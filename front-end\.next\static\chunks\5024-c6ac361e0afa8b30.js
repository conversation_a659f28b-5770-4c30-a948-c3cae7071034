"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5024],{4516:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(19946).A)("MapPin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])},16785:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(19946).A)("Target",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"12",r:"6",key:"1vlfrh"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}]])},17580:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(19946).A)("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]])},33109:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(19946).A)("TrendingUp",[["polyline",{points:"22 7 13.5 15.5 8.5 10.5 2 17",key:"126l90"}],["polyline",{points:"16 7 22 7 22 13",key:"kwv8wd"}]])},40646:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(19946).A)("CircleCheckBig",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},51976:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(19946).A)("Heart",[["path",{d:"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z",key:"c3ymky"}]])},55863:(e,t,r)=>{r.d(t,{C1:()=>w,bL:()=>x});var a=r(12115),n=r(46081),o=r(63655),l=r(95155),i="Progress",[s,c]=(0,n.A)(i),[u,d]=s(i),f=a.forwardRef((e,t)=>{var r,a,n,i;let{__scopeProgress:s,value:c=null,max:d,getValueLabel:f=y,...v}=e;(d||0===d)&&!b(d)&&console.error((r="".concat(d),a="Progress","Invalid prop `max` of value `".concat(r,"` supplied to `").concat(a,"`. Only numbers greater than 0 are valid max values. Defaulting to `").concat(100,"`.")));let p=b(d)?d:100;null===c||g(c,p)||console.error((n="".concat(c),i="Progress","Invalid prop `value` of value `".concat(n,"` supplied to `").concat(i,"`. The `value` prop must be:\n  - a positive number\n  - less than the value passed to `max` (or ").concat(100," if no `max` prop is set)\n  - `null` or `undefined` if the progress is indeterminate.\n\nDefaulting to `null`.")));let x=g(c,p)?c:null,w=h(x)?f(x,p):void 0;return(0,l.jsx)(u,{scope:s,value:x,max:p,children:(0,l.jsx)(o.sG.div,{"aria-valuemax":p,"aria-valuemin":0,"aria-valuenow":h(x)?x:void 0,"aria-valuetext":w,role:"progressbar","data-state":m(x,p),"data-value":null!=x?x:void 0,"data-max":p,...v,ref:t})})});f.displayName=i;var v="ProgressIndicator",p=a.forwardRef((e,t)=>{var r;let{__scopeProgress:a,...n}=e,i=d(v,a);return(0,l.jsx)(o.sG.div,{"data-state":m(i.value,i.max),"data-value":null!==(r=i.value)&&void 0!==r?r:void 0,"data-max":i.max,...n,ref:t})});function y(e,t){return"".concat(Math.round(e/t*100),"%")}function m(e,t){return null==e?"indeterminate":e===t?"complete":"loading"}function h(e){return"number"==typeof e}function b(e){return h(e)&&!isNaN(e)&&e>0}function g(e,t){return h(e)&&!isNaN(e)&&e<=t&&e>=0}p.displayName=v;var x=f,w=p},55868:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(19946).A)("DollarSign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]])},60704:(e,t,r)=>{r.d(t,{B8:()=>F,UC:()=>T,bL:()=>D,l9:()=>M});var a=r(12115),n=r(85185),o=r(46081),l=r(89196),i=r(28905),s=r(63655),c=r(94315),u=r(5845),d=r(61285),f=r(95155),v="Tabs",[p,y]=(0,o.A)(v,[l.RG]),m=(0,l.RG)(),[h,b]=p(v),g=a.forwardRef((e,t)=>{let{__scopeTabs:r,value:a,onValueChange:n,defaultValue:o,orientation:l="horizontal",dir:i,activationMode:v="automatic",...p}=e,y=(0,c.jH)(i),[m,b]=(0,u.i)({prop:a,onChange:n,defaultProp:o});return(0,f.jsx)(h,{scope:r,baseId:(0,d.B)(),value:m,onValueChange:b,orientation:l,dir:y,activationMode:v,children:(0,f.jsx)(s.sG.div,{dir:y,"data-orientation":l,...p,ref:t})})});g.displayName=v;var x="TabsList",w=a.forwardRef((e,t)=>{let{__scopeTabs:r,loop:a=!0,...n}=e,o=b(x,r),i=m(r);return(0,f.jsx)(l.bL,{asChild:!0,...i,orientation:o.orientation,dir:o.dir,loop:a,children:(0,f.jsx)(s.sG.div,{role:"tablist","aria-orientation":o.orientation,...n,ref:t})})});w.displayName=x;var A="TabsTrigger",k=a.forwardRef((e,t)=>{let{__scopeTabs:r,value:a,disabled:o=!1,...i}=e,c=b(A,r),u=m(r),d=I(c.baseId,a),v=j(c.baseId,a),p=a===c.value;return(0,f.jsx)(l.q7,{asChild:!0,...u,focusable:!o,active:p,children:(0,f.jsx)(s.sG.button,{type:"button",role:"tab","aria-selected":p,"aria-controls":v,"data-state":p?"active":"inactive","data-disabled":o?"":void 0,disabled:o,id:d,...i,ref:t,onMouseDown:(0,n.m)(e.onMouseDown,e=>{o||0!==e.button||!1!==e.ctrlKey?e.preventDefault():c.onValueChange(a)}),onKeyDown:(0,n.m)(e.onKeyDown,e=>{[" ","Enter"].includes(e.key)&&c.onValueChange(a)}),onFocus:(0,n.m)(e.onFocus,()=>{let e="manual"!==c.activationMode;p||o||!e||c.onValueChange(a)})})})});k.displayName=A;var R="TabsContent",C=a.forwardRef((e,t)=>{let{__scopeTabs:r,value:n,forceMount:o,children:l,...c}=e,u=b(R,r),d=I(u.baseId,n),v=j(u.baseId,n),p=n===u.value,y=a.useRef(p);return a.useEffect(()=>{let e=requestAnimationFrame(()=>y.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,f.jsx)(i.C,{present:o||p,children:r=>{let{present:a}=r;return(0,f.jsx)(s.sG.div,{"data-state":p?"active":"inactive","data-orientation":u.orientation,role:"tabpanel","aria-labelledby":d,hidden:!a,id:v,tabIndex:0,...c,ref:t,style:{...e.style,animationDuration:y.current?"0s":void 0},children:a&&l})}})});function I(e,t){return"".concat(e,"-trigger-").concat(t)}function j(e,t){return"".concat(e,"-content-").concat(t)}C.displayName=R;var D=g,F=w,M=k,T=C},69037:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(19946).A)("Award",[["path",{d:"m15.477 12.89 1.515 8.526a.5.5 0 0 1-.81.47l-3.58-2.687a1 1 0 0 0-1.197 0l-3.586 2.686a.5.5 0 0 1-.81-.469l1.514-8.526",key:"1yiouv"}],["circle",{cx:"12",cy:"8",r:"6",key:"1vp47v"}]])},69074:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(19946).A)("Calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},89196:(e,t,r)=>{r.d(t,{RG:()=>w,bL:()=>M,q7:()=>T});var a=r(12115),n=r(85185),o=r(82284),l=r(6101),i=r(46081),s=r(61285),c=r(63655),u=r(39033),d=r(5845),f=r(94315),v=r(95155),p="rovingFocusGroup.onEntryFocus",y={bubbles:!1,cancelable:!0},m="RovingFocusGroup",[h,b,g]=(0,o.N)(m),[x,w]=(0,i.A)(m,[g]),[A,k]=x(m),R=a.forwardRef((e,t)=>(0,v.jsx)(h.Provider,{scope:e.__scopeRovingFocusGroup,children:(0,v.jsx)(h.Slot,{scope:e.__scopeRovingFocusGroup,children:(0,v.jsx)(C,{...e,ref:t})})}));R.displayName=m;var C=a.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:r,orientation:o,loop:i=!1,dir:s,currentTabStopId:m,defaultCurrentTabStopId:h,onCurrentTabStopIdChange:g,onEntryFocus:x,preventScrollOnEntryFocus:w=!1,...k}=e,R=a.useRef(null),C=(0,l.s)(t,R),I=(0,f.jH)(s),[j=null,D]=(0,d.i)({prop:m,defaultProp:h,onChange:g}),[M,T]=a.useState(!1),G=(0,u.c)(x),N=b(r),E=a.useRef(!1),[L,P]=a.useState(0);return a.useEffect(()=>{let e=R.current;if(e)return e.addEventListener(p,G),()=>e.removeEventListener(p,G)},[G]),(0,v.jsx)(A,{scope:r,orientation:o,dir:I,loop:i,currentTabStopId:j,onItemFocus:a.useCallback(e=>D(e),[D]),onItemShiftTab:a.useCallback(()=>T(!0),[]),onFocusableItemAdd:a.useCallback(()=>P(e=>e+1),[]),onFocusableItemRemove:a.useCallback(()=>P(e=>e-1),[]),children:(0,v.jsx)(c.sG.div,{tabIndex:M||0===L?-1:0,"data-orientation":o,...k,ref:C,style:{outline:"none",...e.style},onMouseDown:(0,n.m)(e.onMouseDown,()=>{E.current=!0}),onFocus:(0,n.m)(e.onFocus,e=>{let t=!E.current;if(e.target===e.currentTarget&&t&&!M){let t=new CustomEvent(p,y);if(e.currentTarget.dispatchEvent(t),!t.defaultPrevented){let e=N().filter(e=>e.focusable);F([e.find(e=>e.active),e.find(e=>e.id===j),...e].filter(Boolean).map(e=>e.ref.current),w)}}E.current=!1}),onBlur:(0,n.m)(e.onBlur,()=>T(!1))})})}),I="RovingFocusGroupItem",j=a.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:r,focusable:o=!0,active:l=!1,tabStopId:i,...u}=e,d=(0,s.B)(),f=i||d,p=k(I,r),y=p.currentTabStopId===f,m=b(r),{onFocusableItemAdd:g,onFocusableItemRemove:x}=p;return a.useEffect(()=>{if(o)return g(),()=>x()},[o,g,x]),(0,v.jsx)(h.ItemSlot,{scope:r,id:f,focusable:o,active:l,children:(0,v.jsx)(c.sG.span,{tabIndex:y?0:-1,"data-orientation":p.orientation,...u,ref:t,onMouseDown:(0,n.m)(e.onMouseDown,e=>{o?p.onItemFocus(f):e.preventDefault()}),onFocus:(0,n.m)(e.onFocus,()=>p.onItemFocus(f)),onKeyDown:(0,n.m)(e.onKeyDown,e=>{if("Tab"===e.key&&e.shiftKey){p.onItemShiftTab();return}if(e.target!==e.currentTarget)return;let t=function(e,t,r){var a;let n=(a=e.key,"rtl"!==r?a:"ArrowLeft"===a?"ArrowRight":"ArrowRight"===a?"ArrowLeft":a);if(!("vertical"===t&&["ArrowLeft","ArrowRight"].includes(n))&&!("horizontal"===t&&["ArrowUp","ArrowDown"].includes(n)))return D[n]}(e,p.orientation,p.dir);if(void 0!==t){if(e.metaKey||e.ctrlKey||e.altKey||e.shiftKey)return;e.preventDefault();let r=m().filter(e=>e.focusable).map(e=>e.ref.current);if("last"===t)r.reverse();else if("prev"===t||"next"===t){"prev"===t&&r.reverse();let a=r.indexOf(e.currentTarget);r=p.loop?function(e,t){return e.map((r,a)=>e[(t+a)%e.length])}(r,a+1):r.slice(a+1)}setTimeout(()=>F(r))}})})})});j.displayName=I;var D={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function F(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],r=document.activeElement;for(let a of e)if(a===r||(a.focus({preventScroll:t}),document.activeElement!==r))return}var M=R,T=j}}]);