"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/scholarship-application/page",{

/***/ "(app-pages-browser)/./components/scholarship/ScholarshipApplicationPage.tsx":
/*!***************************************************************!*\
  !*** ./components/scholarship/ScholarshipApplicationPage.tsx ***!
  \***************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ScholarshipApplicationPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./components/ui/label.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/textarea */ \"(app-pages-browser)/./components/ui/textarea.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./components/ui/select.tsx\");\n/* harmony import */ var _components_ui_tabs__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/tabs */ \"(app-pages-browser)/./components/ui/tabs.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BookOpen_Building_CheckCircle_CreditCard_FileUp_GraduationCap_Loader2_Mail_MapPin_MessageSquare_Phone_School_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BookOpen,Building,CheckCircle,CreditCard,FileUp,GraduationCap,Loader2,Mail,MapPin,MessageSquare,Phone,School,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/book-open.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BookOpen_Building_CheckCircle_CreditCard_FileUp_GraduationCap_Loader2_Mail_MapPin_MessageSquare_Phone_School_User_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BookOpen,Building,CheckCircle,CreditCard,FileUp,GraduationCap,Loader2,Mail,MapPin,MessageSquare,Phone,School,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/school.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BookOpen_Building_CheckCircle_CreditCard_FileUp_GraduationCap_Loader2_Mail_MapPin_MessageSquare_Phone_School_User_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BookOpen,Building,CheckCircle,CreditCard,FileUp,GraduationCap,Loader2,Mail,MapPin,MessageSquare,Phone,School,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/graduation-cap.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BookOpen_Building_CheckCircle_CreditCard_FileUp_GraduationCap_Loader2_Mail_MapPin_MessageSquare_Phone_School_User_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BookOpen,Building,CheckCircle,CreditCard,FileUp,GraduationCap,Loader2,Mail,MapPin,MessageSquare,Phone,School,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/triangle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BookOpen_Building_CheckCircle_CreditCard_FileUp_GraduationCap_Loader2_Mail_MapPin_MessageSquare_Phone_School_User_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BookOpen,Building,CheckCircle,CreditCard,FileUp,GraduationCap,Loader2,Mail,MapPin,MessageSquare,Phone,School,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BookOpen_Building_CheckCircle_CreditCard_FileUp_GraduationCap_Loader2_Mail_MapPin_MessageSquare_Phone_School_User_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BookOpen,Building,CheckCircle,CreditCard,FileUp,GraduationCap,Loader2,Mail,MapPin,MessageSquare,Phone,School,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/phone.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BookOpen_Building_CheckCircle_CreditCard_FileUp_GraduationCap_Loader2_Mail_MapPin_MessageSquare_Phone_School_User_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BookOpen,Building,CheckCircle,CreditCard,FileUp,GraduationCap,Loader2,Mail,MapPin,MessageSquare,Phone,School,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BookOpen_Building_CheckCircle_CreditCard_FileUp_GraduationCap_Loader2_Mail_MapPin_MessageSquare_Phone_School_User_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BookOpen,Building,CheckCircle,CreditCard,FileUp,GraduationCap,Loader2,Mail,MapPin,MessageSquare,Phone,School,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/building.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BookOpen_Building_CheckCircle_CreditCard_FileUp_GraduationCap_Loader2_Mail_MapPin_MessageSquare_Phone_School_User_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BookOpen,Building,CheckCircle,CreditCard,FileUp,GraduationCap,Loader2,Mail,MapPin,MessageSquare,Phone,School,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/credit-card.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BookOpen_Building_CheckCircle_CreditCard_FileUp_GraduationCap_Loader2_Mail_MapPin_MessageSquare_Phone_School_User_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BookOpen,Building,CheckCircle,CreditCard,FileUp,GraduationCap,Loader2,Mail,MapPin,MessageSquare,Phone,School,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BookOpen_Building_CheckCircle_CreditCard_FileUp_GraduationCap_Loader2_Mail_MapPin_MessageSquare_Phone_School_User_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BookOpen,Building,CheckCircle,CreditCard,FileUp,GraduationCap,Loader2,Mail,MapPin,MessageSquare,Phone,School,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BookOpen_Building_CheckCircle_CreditCard_FileUp_GraduationCap_Loader2_Mail_MapPin_MessageSquare_Phone_School_User_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BookOpen,Building,CheckCircle,CreditCard,FileUp,GraduationCap,Loader2,Mail,MapPin,MessageSquare,Phone,School,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-up.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BookOpen_Building_CheckCircle_CreditCard_FileUp_GraduationCap_Loader2_Mail_MapPin_MessageSquare_Phone_School_User_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BookOpen,Building,CheckCircle,CreditCard,FileUp,GraduationCap,Loader2,Mail,MapPin,MessageSquare,Phone,School,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BookOpen_Building_CheckCircle_CreditCard_FileUp_GraduationCap_Loader2_Mail_MapPin_MessageSquare_Phone_School_User_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BookOpen,Building,CheckCircle,CreditCard,FileUp,GraduationCap,Loader2,Mail,MapPin,MessageSquare,Phone,School,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/message-square.js\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./hooks/use-toast.ts\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./lib/api.ts\");\n/* harmony import */ var _SuccessMessage__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./SuccessMessage */ \"(app-pages-browser)/./components/scholarship/SuccessMessage.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\nfunction ScholarshipApplicationPage() {\n    _s();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams)();\n    const [selectedCategory, setSelectedCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('primary');\n    const [submitting, setSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showSuccess, setShowSuccess] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [scholarshipId, setScholarshipId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_9__.useToast)();\n    // Handle URL parameters for category and scholarship ID\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ScholarshipApplicationPage.useEffect\": ()=>{\n            const categoryParam = searchParams.get('category');\n            const scholarshipIdParam = searchParams.get('scholarship_id');\n            if (categoryParam && [\n                'primary',\n                'secondary',\n                'university'\n            ].includes(categoryParam)) {\n                setSelectedCategory(categoryParam);\n            }\n            if (scholarshipIdParam) {\n                setScholarshipId(scholarshipIdParam);\n            }\n        }\n    }[\"ScholarshipApplicationPage.useEffect\"], [\n        searchParams\n    ]);\n    // Form data for different categories\n    const [primaryFormData, setPrimaryFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        student_full_name: '',\n        age: '',\n        current_class: '',\n        father_name: '',\n        mother_name: '',\n        parent_phone: '',\n        home_address: '',\n        school_name: '',\n        headmaster_name: '',\n        school_account_number: '',\n        reason_for_scholarship: '',\n        current_school_fee: '',\n        supporting_information: ''\n    });\n    const [secondaryFormData, setSecondaryFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        student_full_name: '',\n        age: '',\n        class: '',\n        parent_phone: '',\n        address: '',\n        school_name: '',\n        principal_name: '',\n        principal_account_number: '',\n        reason_for_scholarship: '',\n        school_fee_amount: ''\n    });\n    const [universityFormData, setUniversityFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        full_name: '',\n        age: '',\n        course_of_study: '',\n        current_level: '',\n        phone_number: '',\n        email_address: '',\n        matriculation_number: '',\n        reason_for_scholarship: ''\n    });\n    // File uploads for different categories\n    const [primaryFiles, setPrimaryFiles] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        student_picture: null\n    });\n    const [secondaryFiles, setSecondaryFiles] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        student_picture: null\n    });\n    const [universityFiles, setUniversityFiles] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        student_id_card: null,\n        payment_evidence: null,\n        supporting_documents: []\n    });\n    // Category configuration - Updated to match platform design system\n    const categoryConfig = {\n        primary: {\n            icon: _barrel_optimize_names_AlertTriangle_BookOpen_Building_CheckCircle_CreditCard_FileUp_GraduationCap_Loader2_Mail_MapPin_MessageSquare_Phone_School_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n            title: \"Primary School Scholarship\",\n            description: \"For Primary 1-6 students (Filled by Parent/Guardian)\",\n            color: \"bg-blue-500\",\n            bgColor: \"bg-blue-50\",\n            textColor: \"text-blue-700\",\n            borderColor: \"border-blue-200\"\n        },\n        secondary: {\n            icon: _barrel_optimize_names_AlertTriangle_BookOpen_Building_CheckCircle_CreditCard_FileUp_GraduationCap_Loader2_Mail_MapPin_MessageSquare_Phone_School_User_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n            title: \"Secondary School Scholarship\",\n            description: \"For Secondary school students (Filled by Student)\",\n            color: \"bg-green-500\",\n            bgColor: \"bg-green-50\",\n            textColor: \"text-green-700\",\n            borderColor: \"border-green-200\"\n        },\n        university: {\n            icon: _barrel_optimize_names_AlertTriangle_BookOpen_Building_CheckCircle_CreditCard_FileUp_GraduationCap_Loader2_Mail_MapPin_MessageSquare_Phone_School_User_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"],\n            title: \"University Scholarship\",\n            description: \"For University students (Filled by Student)\",\n            color: \"bg-purple-500\",\n            bgColor: \"bg-purple-50\",\n            textColor: \"text-purple-700\",\n            borderColor: \"border-purple-200\"\n        }\n    };\n    const handleFileChange = (category, field, file)=>{\n        if (category === 'primary') {\n            setPrimaryFiles((prev)=>({\n                    ...prev,\n                    [field]: file\n                }));\n        } else if (category === 'secondary') {\n            setSecondaryFiles((prev)=>({\n                    ...prev,\n                    [field]: file\n                }));\n        } else if (category === 'university') {\n            if (field === 'supporting_documents' && file) {\n                setUniversityFiles((prev)=>({\n                        ...prev,\n                        supporting_documents: [\n                            ...prev.supporting_documents,\n                            file\n                        ]\n                    }));\n            } else {\n                setUniversityFiles((prev)=>({\n                        ...prev,\n                        [field]: file\n                    }));\n            }\n        }\n    };\n    const handleMultipleFileChange = (files)=>{\n        if (files && selectedCategory === 'university') {\n            const fileArray = Array.from(files);\n            setUniversityFiles((prev)=>({\n                    ...prev,\n                    supporting_documents: [\n                        ...prev.supporting_documents,\n                        ...fileArray\n                    ]\n                }));\n        }\n    };\n    const removeFile = (index)=>{\n        setUniversityFiles((prev)=>({\n                ...prev,\n                supporting_documents: prev.supporting_documents.filter((_, i)=>i !== index)\n            }));\n    };\n    const validateForm = ()=>{\n        if (selectedCategory === 'primary') {\n            const required = [\n                'student_full_name',\n                'age',\n                'current_class',\n                'father_name',\n                'mother_name',\n                'parent_phone',\n                'home_address',\n                'school_name',\n                'headmaster_name',\n                'school_account_number',\n                'reason_for_scholarship',\n                'current_school_fee'\n            ];\n            return required.every((field)=>primaryFormData[field].trim() !== '') && primaryFiles.student_picture;\n        } else if (selectedCategory === 'secondary') {\n            const required = [\n                'student_full_name',\n                'age',\n                'class',\n                'parent_phone',\n                'address',\n                'school_name',\n                'principal_name',\n                'principal_account_number',\n                'reason_for_scholarship',\n                'school_fee_amount'\n            ];\n            return required.every((field)=>secondaryFormData[field].trim() !== '') && secondaryFiles.student_picture;\n        } else if (selectedCategory === 'university') {\n            const required = [\n                'full_name',\n                'age',\n                'course_of_study',\n                'current_level',\n                'phone_number',\n                'email_address',\n                'matriculation_number',\n                'reason_for_scholarship'\n            ];\n            return required.every((field)=>universityFormData[field].trim() !== '') && universityFiles.student_id_card && universityFiles.payment_evidence;\n        }\n        return false;\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        if (!validateForm()) {\n            toast({\n                title: \"Validation Error\",\n                description: \"Please fill in all required fields and upload required documents\",\n                variant: \"destructive\"\n            });\n            return;\n        }\n        try {\n            setSubmitting(true);\n            const formData = new FormData();\n            formData.append('category', selectedCategory);\n            // Add form data based on category\n            if (selectedCategory === 'primary') {\n                formData.append('form_data', JSON.stringify(primaryFormData));\n                if (primaryFiles.student_picture) {\n                    formData.append('student_picture', primaryFiles.student_picture);\n                }\n            } else if (selectedCategory === 'secondary') {\n                formData.append('form_data', JSON.stringify(secondaryFormData));\n                if (secondaryFiles.student_picture) {\n                    formData.append('student_picture', secondaryFiles.student_picture);\n                }\n            } else if (selectedCategory === 'university') {\n                formData.append('form_data', JSON.stringify(universityFormData));\n                if (universityFiles.student_id_card) {\n                    formData.append('student_id_card', universityFiles.student_id_card);\n                }\n                if (universityFiles.payment_evidence) {\n                    formData.append('payment_evidence', universityFiles.payment_evidence);\n                }\n                universityFiles.supporting_documents.forEach((file, index)=>{\n                    formData.append(\"supporting_documents[\".concat(index, \"]\"), file);\n                });\n            }\n            // Add scholarship ID if available\n            if (scholarshipId) {\n                formData.append('scholarship_id', scholarshipId);\n            }\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_10__.apiClient.submitScholarshipApplication(formData);\n            if (response.success) {\n                // Show success message\n                setShowSuccess(true);\n                // Reset form\n                if (selectedCategory === 'primary') {\n                    setPrimaryFormData({\n                        student_full_name: '',\n                        age: '',\n                        current_class: '',\n                        father_name: '',\n                        mother_name: '',\n                        parent_phone: '',\n                        home_address: '',\n                        school_name: '',\n                        headmaster_name: '',\n                        school_account_number: '',\n                        reason_for_scholarship: '',\n                        current_school_fee: '',\n                        supporting_information: ''\n                    });\n                    setPrimaryFiles({\n                        student_picture: null\n                    });\n                } else if (selectedCategory === 'secondary') {\n                    setSecondaryFormData({\n                        student_full_name: '',\n                        age: '',\n                        class: '',\n                        parent_phone: '',\n                        address: '',\n                        school_name: '',\n                        principal_name: '',\n                        principal_account_number: '',\n                        reason_for_scholarship: '',\n                        school_fee_amount: ''\n                    });\n                    setSecondaryFiles({\n                        student_picture: null\n                    });\n                } else if (selectedCategory === 'university') {\n                    setUniversityFormData({\n                        full_name: '',\n                        age: '',\n                        course_of_study: '',\n                        current_level: '',\n                        phone_number: '',\n                        email_address: '',\n                        matriculation_number: '',\n                        reason_for_scholarship: ''\n                    });\n                    setUniversityFiles({\n                        student_id_card: null,\n                        payment_evidence: null,\n                        supporting_documents: []\n                    });\n                }\n            } else {\n                toast({\n                    title: \"Error\",\n                    description: response.message || \"Failed to submit application\",\n                    variant: \"destructive\"\n                });\n            }\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error('Error submitting application:', error);\n            toast({\n                title: \"Error\",\n                description: ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || \"Failed to submit application\",\n                variant: \"destructive\"\n            });\n        } finally{\n            setSubmitting(false);\n        }\n    };\n    const handleBackToForm = ()=>{\n        setShowSuccess(false);\n    };\n    const handleGoHome = ()=>{\n        // Navigate to dashboard or home page\n        window.location.href = '/dashboard';\n    };\n    if (showSuccess) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_SuccessMessage__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n            onBackToForm: handleBackToForm,\n            onGoHome: handleGoHome\n        }, void 0, false, {\n            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n            lineNumber: 339,\n            columnNumber: 12\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50 dark:bg-gray-900\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"relative py-12 sm:py-16 md:py-20 bg-gradient-to-br from-green-600 to-green-800 text-white\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container mx-auto px-3 sm:px-4 md:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-w-4xl mx-auto text-center space-y-4 sm:space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-2xl sm:text-3xl md:text-4xl lg:text-5xl xl:text-6xl font-bold tracking-tight leading-tight\",\n                                children: \"Scholarship Application\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                lineNumber: 348,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-base sm:text-lg md:text-xl text-green-100 max-w-3xl mx-auto px-2\",\n                                children: \"Choose your scholarship category and complete the application form\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                lineNumber: 351,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                        lineNumber: 347,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                    lineNumber: 346,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                lineNumber: 345,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-6 sm:py-8 md:py-12 bg-white dark:bg-gray-900\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container mx-auto px-3 sm:px-4 md:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-w-4xl mx-auto\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-4 sm:mb-6 md:mb-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-xl sm:text-2xl md:text-3xl font-bold tracking-tight mb-2 text-center sm:text-left\",\n                                        children: \"Apply for Scholarship\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                        lineNumber: 363,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm sm:text-base text-gray-600 dark:text-gray-400 text-center sm:text-left\",\n                                        children: \"Select your education level and fill out the appropriate form\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                        lineNumber: 364,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                lineNumber: 362,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_8__.Tabs, {\n                                value: selectedCategory,\n                                onValueChange: (value)=>setSelectedCategory(value),\n                                className: \"w-full\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_8__.TabsList, {\n                                        className: \"grid w-full grid-cols-1 sm:grid-cols-3 mb-4 sm:mb-6 h-auto p-1 gap-1 sm:gap-2 bg-gray-100 rounded-lg\",\n                                        children: Object.entries(categoryConfig).map((param)=>{\n                                            let [key, config] = param;\n                                            const IconComponent = config.icon;\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_8__.TabsTrigger, {\n                                                value: key,\n                                                className: \"flex flex-col sm:flex-col items-center p-2 sm:p-3 space-y-1 sm:space-y-2 rounded-md transition-all duration-300 data-[state=active]:bg-white data-[state=active]:shadow-md min-h-[60px] sm:min-h-[80px]\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"p-1.5 sm:p-2 rounded-full bg-green-600 text-white shadow-sm\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(IconComponent, {\n                                                            className: \"h-3 w-3 sm:h-4 sm:w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                            lineNumber: 380,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                        lineNumber: 379,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"font-medium text-xs sm:text-xs text-gray-900 leading-tight\",\n                                                                children: config.title\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                lineNumber: 383,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs sm:text-xs text-gray-600 mt-0.5 hidden sm:block\",\n                                                                children: config.description\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                lineNumber: 384,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                        lineNumber: 382,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, key, true, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                lineNumber: 374,\n                                                columnNumber: 21\n                                            }, this);\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                        lineNumber: 370,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_8__.TabsContent, {\n                                        value: \"primary\",\n                                        className: \"space-y-4 sm:space-y-6 md:space-y-8\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                            onSubmit: handleSubmit,\n                                            className: \"space-y-4 sm:space-y-6 md:space-y-8\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-green-50 p-3 sm:p-4 md:p-6 rounded-lg border border-green-200\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-start sm:items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BookOpen_Building_CheckCircle_CreditCard_FileUp_GraduationCap_Loader2_Mail_MapPin_MessageSquare_Phone_School_User_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                className: \"h-4 w-4 sm:h-5 sm:w-5 text-green-600 mr-2 sm:mr-3 mt-0.5 sm:mt-0 flex-shrink-0\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                lineNumber: 396,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-green-800 font-medium text-sm sm:text-base\",\n                                                                children: \"This form should be filled by a Parent or Guardian on behalf of the student\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                lineNumber: 397,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                        lineNumber: 395,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                    lineNumber: 394,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-4 sm:space-y-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"text-base sm:text-lg font-semibold text-green-800 dark:text-green-200 mb-3 sm:mb-4 flex items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BookOpen_Building_CheckCircle_CreditCard_FileUp_GraduationCap_Loader2_Mail_MapPin_MessageSquare_Phone_School_User_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                            className: \"h-4 w-4 sm:h-5 sm:w-5 mr-2 text-green-600\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                            lineNumber: 407,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        \"Student Information\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                    lineNumber: 406,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"space-y-3 sm:space-y-4\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"space-y-2\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                                                    htmlFor: \"student_full_name\",\n                                                                                    className: \"text-green-800 dark:text-green-200 text-sm sm:text-base\",\n                                                                                    children: \"Student Full Name *\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                    lineNumber: 412,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"relative\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BookOpen_Building_CheckCircle_CreditCard_FileUp_GraduationCap_Loader2_Mail_MapPin_MessageSquare_Phone_School_User_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                                            className: \"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-green-500\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                            lineNumber: 416,\n                                                                                            columnNumber: 29\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                                            id: \"student_full_name\",\n                                                                                            value: primaryFormData.student_full_name,\n                                                                                            onChange: (e)=>setPrimaryFormData((prev)=>({\n                                                                                                        ...prev,\n                                                                                                        student_full_name: e.target.value\n                                                                                                    })),\n                                                                                            placeholder: \"Enter student's full name\",\n                                                                                            className: \"pl-10 rounded-xl border-green-200 dark:border-green-700 focus:border-green-500 text-sm sm:text-base\",\n                                                                                            required: true\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                            lineNumber: 417,\n                                                                                            columnNumber: 29\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                    lineNumber: 415,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                            lineNumber: 411,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"space-y-2\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                                                            htmlFor: \"age\",\n                                                                                            className: \"text-green-800 dark:text-green-200 text-sm sm:text-base\",\n                                                                                            children: \"Age *\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                            lineNumber: 430,\n                                                                                            columnNumber: 29\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                                            id: \"age\",\n                                                                                            type: \"number\",\n                                                                                            value: primaryFormData.age,\n                                                                                            onChange: (e)=>setPrimaryFormData((prev)=>({\n                                                                                                        ...prev,\n                                                                                                        age: e.target.value\n                                                                                                    })),\n                                                                                            placeholder: \"Age\",\n                                                                                            className: \"rounded-xl border-green-200 dark:border-green-700 focus:border-green-500 text-sm sm:text-base\",\n                                                                                            required: true\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                            lineNumber: 433,\n                                                                                            columnNumber: 29\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                    lineNumber: 429,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"space-y-2\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                                                            htmlFor: \"current_class\",\n                                                                                            className: \"text-green-800 dark:text-green-200 text-sm sm:text-base\",\n                                                                                            children: \"Current Class *\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                            lineNumber: 444,\n                                                                                            columnNumber: 29\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.Select, {\n                                                                                            value: primaryFormData.current_class,\n                                                                                            onValueChange: (value)=>setPrimaryFormData((prev)=>({\n                                                                                                        ...prev,\n                                                                                                        current_class: value\n                                                                                                    })),\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectTrigger, {\n                                                                                                    className: \"rounded-xl border-green-200 dark:border-green-700 focus:border-green-500 text-sm sm:text-base\",\n                                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectValue, {\n                                                                                                        placeholder: \"Select class\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                                        lineNumber: 449,\n                                                                                                        columnNumber: 33\n                                                                                                    }, this)\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                                    lineNumber: 448,\n                                                                                                    columnNumber: 31\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectContent, {\n                                                                                                    children: [\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                                                            value: \"Primary 1\",\n                                                                                                            children: \"Primary 1\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                                            lineNumber: 452,\n                                                                                                            columnNumber: 33\n                                                                                                        }, this),\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                                                            value: \"Primary 2\",\n                                                                                                            children: \"Primary 2\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                                            lineNumber: 453,\n                                                                                                            columnNumber: 33\n                                                                                                        }, this),\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                                                            value: \"Primary 3\",\n                                                                                                            children: \"Primary 3\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                                            lineNumber: 454,\n                                                                                                            columnNumber: 33\n                                                                                                        }, this),\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                                                            value: \"Primary 4\",\n                                                                                                            children: \"Primary 4\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                                            lineNumber: 455,\n                                                                                                            columnNumber: 33\n                                                                                                        }, this),\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                                                            value: \"Primary 5\",\n                                                                                                            children: \"Primary 5\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                                            lineNumber: 456,\n                                                                                                            columnNumber: 33\n                                                                                                        }, this),\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                                                            value: \"Primary 6\",\n                                                                                                            children: \"Primary 6\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                                            lineNumber: 457,\n                                                                                                            columnNumber: 33\n                                                                                                        }, this)\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                                    lineNumber: 451,\n                                                                                                    columnNumber: 31\n                                                                                                }, this)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                            lineNumber: 447,\n                                                                                            columnNumber: 29\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                    lineNumber: 443,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                            lineNumber: 428,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                    lineNumber: 410,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                            lineNumber: 405,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"text-lg font-semibold text-green-800 dark:text-green-200 mb-4 flex items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BookOpen_Building_CheckCircle_CreditCard_FileUp_GraduationCap_Loader2_Mail_MapPin_MessageSquare_Phone_School_User_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                            className: \"h-5 w-5 mr-2 text-green-600\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                            lineNumber: 468,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        \"Parent/Guardian Information\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                    lineNumber: 467,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"space-y-4\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"space-y-2\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                                                            htmlFor: \"father_name\",\n                                                                                            className: \"text-green-800 dark:text-green-200\",\n                                                                                            children: \"Father's Name *\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                            lineNumber: 474,\n                                                                                            columnNumber: 29\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"relative\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BookOpen_Building_CheckCircle_CreditCard_FileUp_GraduationCap_Loader2_Mail_MapPin_MessageSquare_Phone_School_User_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                                                    className: \"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-green-500\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                                    lineNumber: 478,\n                                                                                                    columnNumber: 31\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                                                    id: \"father_name\",\n                                                                                                    value: primaryFormData.father_name,\n                                                                                                    onChange: (e)=>setPrimaryFormData((prev)=>({\n                                                                                                                ...prev,\n                                                                                                                father_name: e.target.value\n                                                                                                            })),\n                                                                                                    placeholder: \"Enter father's name\",\n                                                                                                    className: \"pl-10 rounded-xl border-green-200 dark:border-green-700 focus:border-green-500\",\n                                                                                                    required: true\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                                    lineNumber: 479,\n                                                                                                    columnNumber: 31\n                                                                                                }, this)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                            lineNumber: 477,\n                                                                                            columnNumber: 29\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                    lineNumber: 473,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"space-y-2\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                                                            htmlFor: \"mother_name\",\n                                                                                            className: \"text-green-800 dark:text-green-200\",\n                                                                                            children: \"Mother's Name *\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                            lineNumber: 490,\n                                                                                            columnNumber: 29\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                                            id: \"mother_name\",\n                                                                                            value: primaryFormData.mother_name,\n                                                                                            onChange: (e)=>setPrimaryFormData((prev)=>({\n                                                                                                        ...prev,\n                                                                                                        mother_name: e.target.value\n                                                                                                    })),\n                                                                                            placeholder: \"Enter mother's name\",\n                                                                                            className: \"rounded-xl border-green-200 dark:border-green-700 focus:border-green-500\",\n                                                                                            required: true\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                            lineNumber: 493,\n                                                                                            columnNumber: 29\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                    lineNumber: 489,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                            lineNumber: 472,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"space-y-2\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                                                    htmlFor: \"parent_phone\",\n                                                                                    className: \"text-green-800 dark:text-green-200\",\n                                                                                    children: \"Father's or Mother's Phone Number *\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                    lineNumber: 504,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"relative\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BookOpen_Building_CheckCircle_CreditCard_FileUp_GraduationCap_Loader2_Mail_MapPin_MessageSquare_Phone_School_User_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                                            className: \"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-green-500\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                            lineNumber: 508,\n                                                                                            columnNumber: 29\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                                            id: \"parent_phone\",\n                                                                                            type: \"tel\",\n                                                                                            value: primaryFormData.parent_phone,\n                                                                                            onChange: (e)=>setPrimaryFormData((prev)=>({\n                                                                                                        ...prev,\n                                                                                                        parent_phone: e.target.value\n                                                                                                    })),\n                                                                                            placeholder: \"Enter phone number\",\n                                                                                            className: \"pl-10 rounded-xl border-green-200 dark:border-green-700 focus:border-green-500\",\n                                                                                            required: true\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                            lineNumber: 509,\n                                                                                            columnNumber: 29\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                    lineNumber: 507,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                            lineNumber: 503,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                    lineNumber: 471,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                            lineNumber: 466,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                    lineNumber: 404,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-lg font-semibold text-green-800 dark:text-green-200 mb-4 flex items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BookOpen_Building_CheckCircle_CreditCard_FileUp_GraduationCap_Loader2_Mail_MapPin_MessageSquare_Phone_School_User_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                    className: \"h-5 w-5 mr-2 text-green-600\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                    lineNumber: 527,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                \"Address Information\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                            lineNumber: 526,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                                    htmlFor: \"home_address\",\n                                                                    className: \"text-green-800 dark:text-green-200\",\n                                                                    children: \"Home Address *\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                    lineNumber: 531,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"relative\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BookOpen_Building_CheckCircle_CreditCard_FileUp_GraduationCap_Loader2_Mail_MapPin_MessageSquare_Phone_School_User_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                            className: \"absolute left-3 top-3 h-4 w-4 text-green-500\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                            lineNumber: 535,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_6__.Textarea, {\n                                                                            id: \"home_address\",\n                                                                            value: primaryFormData.home_address,\n                                                                            onChange: (e)=>setPrimaryFormData((prev)=>({\n                                                                                        ...prev,\n                                                                                        home_address: e.target.value\n                                                                                    })),\n                                                                            placeholder: \"Enter complete home address\",\n                                                                            className: \"pl-10 rounded-xl border-green-200 dark:border-green-700 focus:border-green-500\",\n                                                                            rows: 3,\n                                                                            required: true\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                            lineNumber: 536,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                    lineNumber: 534,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                            lineNumber: 530,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                    lineNumber: 525,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-lg font-semibold text-green-800 dark:text-green-200 mb-4 flex items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BookOpen_Building_CheckCircle_CreditCard_FileUp_GraduationCap_Loader2_Mail_MapPin_MessageSquare_Phone_School_User_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                    className: \"h-5 w-5 mr-2 text-green-600\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                    lineNumber: 552,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                \"School Information\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                            lineNumber: 551,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"space-y-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                                            htmlFor: \"school_name\",\n                                                                            className: \"text-green-800 dark:text-green-200\",\n                                                                            children: \"Name of the School *\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                            lineNumber: 557,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"relative\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BookOpen_Building_CheckCircle_CreditCard_FileUp_GraduationCap_Loader2_Mail_MapPin_MessageSquare_Phone_School_User_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                                    className: \"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-green-500\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                    lineNumber: 561,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                                    id: \"school_name\",\n                                                                                    value: primaryFormData.school_name,\n                                                                                    onChange: (e)=>setPrimaryFormData((prev)=>({\n                                                                                                ...prev,\n                                                                                                school_name: e.target.value\n                                                                                            })),\n                                                                                    placeholder: \"Enter school name\",\n                                                                                    className: \"pl-10 rounded-xl border-green-200 dark:border-green-700 focus:border-green-500\",\n                                                                                    required: true\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                    lineNumber: 562,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                            lineNumber: 560,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                    lineNumber: 556,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"space-y-2\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                                                    htmlFor: \"headmaster_name\",\n                                                                                    className: \"text-green-800 dark:text-green-200\",\n                                                                                    children: \"Name of Headmaster/Director *\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                    lineNumber: 575,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                                    id: \"headmaster_name\",\n                                                                                    value: primaryFormData.headmaster_name,\n                                                                                    onChange: (e)=>setPrimaryFormData((prev)=>({\n                                                                                                ...prev,\n                                                                                                headmaster_name: e.target.value\n                                                                                            })),\n                                                                                    placeholder: \"Enter headmaster's name\",\n                                                                                    className: \"rounded-xl border-green-200 dark:border-green-700 focus:border-green-500\",\n                                                                                    required: true\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                    lineNumber: 578,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                            lineNumber: 574,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"space-y-2\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                                                    htmlFor: \"school_account_number\",\n                                                                                    className: \"text-green-800 dark:text-green-200\",\n                                                                                    children: \"School Account Number *\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                    lineNumber: 588,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                                    id: \"school_account_number\",\n                                                                                    value: primaryFormData.school_account_number,\n                                                                                    onChange: (e)=>setPrimaryFormData((prev)=>({\n                                                                                                ...prev,\n                                                                                                school_account_number: e.target.value\n                                                                                            })),\n                                                                                    placeholder: \"Enter account number for scholarship payment\",\n                                                                                    className: \"rounded-xl border-green-200 dark:border-green-700 focus:border-green-500\",\n                                                                                    required: true\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                    lineNumber: 591,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                            lineNumber: 587,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                    lineNumber: 573,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                            lineNumber: 555,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                    lineNumber: 550,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-lg font-semibold text-green-800 dark:text-green-200 mb-4 flex items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BookOpen_Building_CheckCircle_CreditCard_FileUp_GraduationCap_Loader2_Mail_MapPin_MessageSquare_Phone_School_User_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                    className: \"h-5 w-5 mr-2 text-green-600\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                    lineNumber: 607,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                \"Financial & Additional Information\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                            lineNumber: 606,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"space-y-2\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                                                    htmlFor: \"current_school_fee\",\n                                                                                    className: \"text-green-800 dark:text-green-200\",\n                                                                                    children: \"Current School Fee Amount *\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                    lineNumber: 613,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"relative\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BookOpen_Building_CheckCircle_CreditCard_FileUp_GraduationCap_Loader2_Mail_MapPin_MessageSquare_Phone_School_User_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                                            className: \"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-green-500\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                            lineNumber: 617,\n                                                                                            columnNumber: 29\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                                            id: \"current_school_fee\",\n                                                                                            type: \"number\",\n                                                                                            value: primaryFormData.current_school_fee,\n                                                                                            onChange: (e)=>setPrimaryFormData((prev)=>({\n                                                                                                        ...prev,\n                                                                                                        current_school_fee: e.target.value\n                                                                                                    })),\n                                                                                            placeholder: \"Enter amount in Naira\",\n                                                                                            className: \"pl-10 rounded-xl border-green-200 dark:border-green-700 focus:border-green-500\",\n                                                                                            required: true\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                            lineNumber: 618,\n                                                                                            columnNumber: 29\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                    lineNumber: 616,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                            lineNumber: 612,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"space-y-2\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                                                    htmlFor: \"student_picture\",\n                                                                                    className: \"text-green-800 dark:text-green-200\",\n                                                                                    children: \"Upload Student Picture *\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                    lineNumber: 630,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                                    id: \"student_picture\",\n                                                                                    type: \"file\",\n                                                                                    accept: \"image/*\",\n                                                                                    onChange: (e)=>{\n                                                                                        var _e_target_files;\n                                                                                        return handleFileChange('primary', 'student_picture', ((_e_target_files = e.target.files) === null || _e_target_files === void 0 ? void 0 : _e_target_files[0]) || null);\n                                                                                    },\n                                                                                    className: \"rounded-xl border-green-200 dark:border-green-700 focus:border-green-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-green-50 file:text-green-700 hover:file:bg-green-100\",\n                                                                                    required: true\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                    lineNumber: 633,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                            lineNumber: 629,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                    lineNumber: 611,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"space-y-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                                            htmlFor: \"reason_for_scholarship\",\n                                                                            className: \"text-green-800 dark:text-green-200\",\n                                                                            children: \"Reason for the Scholarship *\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                            lineNumber: 644,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_6__.Textarea, {\n                                                                            id: \"reason_for_scholarship\",\n                                                                            value: primaryFormData.reason_for_scholarship,\n                                                                            onChange: (e)=>setPrimaryFormData((prev)=>({\n                                                                                        ...prev,\n                                                                                        reason_for_scholarship: e.target.value\n                                                                                    })),\n                                                                            placeholder: \"Please explain why your child needs this scholarship\",\n                                                                            className: \"rounded-xl border-green-200 dark:border-green-700 focus:border-green-500\",\n                                                                            rows: 4,\n                                                                            required: true\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                            lineNumber: 647,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                    lineNumber: 643,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"space-y-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                                            htmlFor: \"supporting_information\",\n                                                                            className: \"text-green-800 dark:text-green-200\",\n                                                                            children: \"Any Other Supporting Information\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                            lineNumber: 658,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_6__.Textarea, {\n                                                                            id: \"supporting_information\",\n                                                                            value: primaryFormData.supporting_information,\n                                                                            onChange: (e)=>setPrimaryFormData((prev)=>({\n                                                                                        ...prev,\n                                                                                        supporting_information: e.target.value\n                                                                                    })),\n                                                                            placeholder: \"Any additional information that supports your application\",\n                                                                            className: \"rounded-xl border-green-200 dark:border-green-700 focus:border-green-500\",\n                                                                            rows: 3\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                            lineNumber: 661,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                    lineNumber: 657,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                            lineNumber: 610,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                    lineNumber: 605,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-center pt-6 sm:pt-8\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                        type: \"submit\",\n                                                        disabled: submitting || !validateForm(),\n                                                        size: \"lg\",\n                                                        className: \"w-full sm:w-auto bg-green-600 hover:bg-green-700 text-white px-6 sm:px-12 py-3 sm:py-4 text-base sm:text-lg font-semibold shadow-lg hover:shadow-xl transition-all duration-300\",\n                                                        children: submitting ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BookOpen_Building_CheckCircle_CreditCard_FileUp_GraduationCap_Loader2_Mail_MapPin_MessageSquare_Phone_School_User_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                    className: \"mr-2 sm:mr-3 h-5 w-5 sm:h-6 sm:w-6 animate-spin\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                    lineNumber: 682,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm sm:text-base\",\n                                                                    children: \"Submitting...\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                    lineNumber: 683,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BookOpen_Building_CheckCircle_CreditCard_FileUp_GraduationCap_Loader2_Mail_MapPin_MessageSquare_Phone_School_User_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                    className: \"mr-2 sm:mr-3 h-5 w-5 sm:h-6 sm:w-6\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                    lineNumber: 687,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm sm:text-base\",\n                                                                    children: \"Submit Primary School Application\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                    lineNumber: 688,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                        lineNumber: 674,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                    lineNumber: 673,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                            lineNumber: 393,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                        lineNumber: 392,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_8__.TabsContent, {\n                                        value: \"secondary\",\n                                        className: \"space-y-4 sm:space-y-6 md:space-y-8\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                            onSubmit: handleSubmit,\n                                            className: \"space-y-4 sm:space-y-6 md:space-y-8\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-green-50 p-3 sm:p-4 md:p-6 rounded-lg border border-green-200\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-start sm:items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BookOpen_Building_CheckCircle_CreditCard_FileUp_GraduationCap_Loader2_Mail_MapPin_MessageSquare_Phone_School_User_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                className: \"h-4 w-4 sm:h-5 sm:w-5 text-green-600 mr-2 sm:mr-3 mt-0.5 sm:mt-0 flex-shrink-0\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                lineNumber: 701,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-green-800 font-medium text-sm sm:text-base\",\n                                                                children: \"This form should be filled by the Student directly\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                lineNumber: 702,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                        lineNumber: 700,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                    lineNumber: 699,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"text-lg font-semibold text-green-800 dark:text-green-200 mb-4 flex items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BookOpen_Building_CheckCircle_CreditCard_FileUp_GraduationCap_Loader2_Mail_MapPin_MessageSquare_Phone_School_User_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                            className: \"h-5 w-5 mr-2 text-green-600\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                            lineNumber: 712,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        \"Student Information\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                    lineNumber: 711,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"space-y-4\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"space-y-2\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                                                    htmlFor: \"sec_student_full_name\",\n                                                                                    className: \"text-green-800 dark:text-green-200\",\n                                                                                    children: \"Student Full Name *\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                    lineNumber: 717,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"relative\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BookOpen_Building_CheckCircle_CreditCard_FileUp_GraduationCap_Loader2_Mail_MapPin_MessageSquare_Phone_School_User_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                                            className: \"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-green-500\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                            lineNumber: 721,\n                                                                                            columnNumber: 29\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                                            id: \"sec_student_full_name\",\n                                                                                            value: secondaryFormData.student_full_name,\n                                                                                            onChange: (e)=>setSecondaryFormData((prev)=>({\n                                                                                                        ...prev,\n                                                                                                        student_full_name: e.target.value\n                                                                                                    })),\n                                                                                            placeholder: \"Enter your full name\",\n                                                                                            className: \"pl-10 rounded-xl border-green-200 dark:border-green-700 focus:border-green-500\",\n                                                                                            required: true\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                            lineNumber: 722,\n                                                                                            columnNumber: 29\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                    lineNumber: 720,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                            lineNumber: 716,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 sm:gap-4\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"space-y-2\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                                                            htmlFor: \"sec_age\",\n                                                                                            className: \"text-green-800 dark:text-green-200\",\n                                                                                            children: \"Age *\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                            lineNumber: 735,\n                                                                                            columnNumber: 29\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                                            id: \"sec_age\",\n                                                                                            type: \"number\",\n                                                                                            value: secondaryFormData.age,\n                                                                                            onChange: (e)=>setSecondaryFormData((prev)=>({\n                                                                                                        ...prev,\n                                                                                                        age: e.target.value\n                                                                                                    })),\n                                                                                            placeholder: \"Your age\",\n                                                                                            className: \"rounded-xl border-green-200 dark:border-green-700 focus:border-green-500\",\n                                                                                            required: true\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                            lineNumber: 738,\n                                                                                            columnNumber: 29\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                    lineNumber: 734,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"space-y-2\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                                                            htmlFor: \"sec_class\",\n                                                                                            className: \"text-green-800 dark:text-green-200\",\n                                                                                            children: \"Class *\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                            lineNumber: 749,\n                                                                                            columnNumber: 29\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.Select, {\n                                                                                            value: secondaryFormData.class,\n                                                                                            onValueChange: (value)=>setSecondaryFormData((prev)=>({\n                                                                                                        ...prev,\n                                                                                                        class: value\n                                                                                                    })),\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectTrigger, {\n                                                                                                    className: \"rounded-xl border-green-200 dark:border-green-700 focus:border-green-500\",\n                                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectValue, {\n                                                                                                        placeholder: \"Select class\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                                        lineNumber: 754,\n                                                                                                        columnNumber: 33\n                                                                                                    }, this)\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                                    lineNumber: 753,\n                                                                                                    columnNumber: 31\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectContent, {\n                                                                                                    children: [\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                                                            value: \"JSS 1\",\n                                                                                                            children: \"JSS 1\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                                            lineNumber: 757,\n                                                                                                            columnNumber: 33\n                                                                                                        }, this),\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                                                            value: \"JSS 2\",\n                                                                                                            children: \"JSS 2\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                                            lineNumber: 758,\n                                                                                                            columnNumber: 33\n                                                                                                        }, this),\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                                                            value: \"JSS 3\",\n                                                                                                            children: \"JSS 3\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                                            lineNumber: 759,\n                                                                                                            columnNumber: 33\n                                                                                                        }, this),\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                                                            value: \"SS 1\",\n                                                                                                            children: \"SS 1\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                                            lineNumber: 760,\n                                                                                                            columnNumber: 33\n                                                                                                        }, this),\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                                                            value: \"SS 2\",\n                                                                                                            children: \"SS 2\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                                            lineNumber: 761,\n                                                                                                            columnNumber: 33\n                                                                                                        }, this),\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                                                            value: \"SS 3\",\n                                                                                                            children: \"SS 3\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                                            lineNumber: 762,\n                                                                                                            columnNumber: 33\n                                                                                                        }, this)\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                                    lineNumber: 756,\n                                                                                                    columnNumber: 31\n                                                                                                }, this)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                            lineNumber: 752,\n                                                                                            columnNumber: 29\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                    lineNumber: 748,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"space-y-2\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                                                            htmlFor: \"sec_parent_phone\",\n                                                                                            className: \"text-green-800 dark:text-green-200\",\n                                                                                            children: \"Parent's Phone Number *\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                            lineNumber: 767,\n                                                                                            columnNumber: 29\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"relative\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BookOpen_Building_CheckCircle_CreditCard_FileUp_GraduationCap_Loader2_Mail_MapPin_MessageSquare_Phone_School_User_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                                                    className: \"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-green-500\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                                    lineNumber: 771,\n                                                                                                    columnNumber: 31\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                                                    id: \"sec_parent_phone\",\n                                                                                                    type: \"tel\",\n                                                                                                    value: secondaryFormData.parent_phone,\n                                                                                                    onChange: (e)=>setSecondaryFormData((prev)=>({\n                                                                                                                ...prev,\n                                                                                                                parent_phone: e.target.value\n                                                                                                            })),\n                                                                                                    placeholder: \"Enter parent's phone number\",\n                                                                                                    className: \"pl-10 rounded-xl border-green-200 dark:border-green-700 focus:border-green-500\",\n                                                                                                    required: true\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                                    lineNumber: 772,\n                                                                                                    columnNumber: 31\n                                                                                                }, this)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                            lineNumber: 770,\n                                                                                            columnNumber: 29\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                    lineNumber: 766,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                            lineNumber: 733,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                    lineNumber: 715,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                            lineNumber: 710,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"text-lg font-semibold text-green-800 dark:text-green-200 mb-4 flex items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BookOpen_Building_CheckCircle_CreditCard_FileUp_GraduationCap_Loader2_Mail_MapPin_MessageSquare_Phone_School_User_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                            className: \"h-5 w-5 mr-2 text-green-600\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                            lineNumber: 790,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        \"Address & School Information\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                    lineNumber: 789,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"space-y-4\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"space-y-2\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                                                    htmlFor: \"sec_address\",\n                                                                                    className: \"text-green-800 dark:text-green-200\",\n                                                                                    children: \"Address *\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                    lineNumber: 795,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"relative\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BookOpen_Building_CheckCircle_CreditCard_FileUp_GraduationCap_Loader2_Mail_MapPin_MessageSquare_Phone_School_User_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                                            className: \"absolute left-3 top-3 h-4 w-4 text-green-500\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                            lineNumber: 799,\n                                                                                            columnNumber: 29\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_6__.Textarea, {\n                                                                                            id: \"sec_address\",\n                                                                                            value: secondaryFormData.address,\n                                                                                            onChange: (e)=>setSecondaryFormData((prev)=>({\n                                                                                                        ...prev,\n                                                                                                        address: e.target.value\n                                                                                                    })),\n                                                                                            placeholder: \"Enter your complete address\",\n                                                                                            className: \"pl-10 rounded-xl border-green-200 dark:border-green-700 focus:border-green-500\",\n                                                                                            rows: 3,\n                                                                                            required: true\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                            lineNumber: 800,\n                                                                                            columnNumber: 29\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                    lineNumber: 798,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                            lineNumber: 794,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"space-y-2\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                                                            htmlFor: \"sec_school_name\",\n                                                                                            className: \"text-green-800 dark:text-green-200\",\n                                                                                            children: \"School Name *\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                            lineNumber: 814,\n                                                                                            columnNumber: 29\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"relative\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BookOpen_Building_CheckCircle_CreditCard_FileUp_GraduationCap_Loader2_Mail_MapPin_MessageSquare_Phone_School_User_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                                                    className: \"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-green-500\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                                    lineNumber: 818,\n                                                                                                    columnNumber: 31\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                                                    id: \"sec_school_name\",\n                                                                                                    value: secondaryFormData.school_name,\n                                                                                                    onChange: (e)=>setSecondaryFormData((prev)=>({\n                                                                                                                ...prev,\n                                                                                                                school_name: e.target.value\n                                                                                                            })),\n                                                                                                    placeholder: \"Enter your school name\",\n                                                                                                    className: \"pl-10 rounded-xl border-green-200 dark:border-green-700 focus:border-green-500\",\n                                                                                                    required: true\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                                    lineNumber: 819,\n                                                                                                    columnNumber: 31\n                                                                                                }, this)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                            lineNumber: 817,\n                                                                                            columnNumber: 29\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                    lineNumber: 813,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"space-y-2\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                                                            htmlFor: \"sec_principal_name\",\n                                                                                            className: \"text-green-800 dark:text-green-200\",\n                                                                                            children: \"School Principal or Director Name *\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                            lineNumber: 831,\n                                                                                            columnNumber: 29\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                                            id: \"sec_principal_name\",\n                                                                                            value: secondaryFormData.principal_name,\n                                                                                            onChange: (e)=>setSecondaryFormData((prev)=>({\n                                                                                                        ...prev,\n                                                                                                        principal_name: e.target.value\n                                                                                                    })),\n                                                                                            placeholder: \"Enter principal's name\",\n                                                                                            className: \"rounded-xl border-green-200 dark:border-green-700 focus:border-green-500\",\n                                                                                            required: true\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                            lineNumber: 834,\n                                                                                            columnNumber: 29\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                    lineNumber: 830,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                            lineNumber: 812,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                    lineNumber: 793,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                            lineNumber: 788,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                    lineNumber: 709,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-lg font-semibold text-green-800 dark:text-green-200 mb-4 flex items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BookOpen_Building_CheckCircle_CreditCard_FileUp_GraduationCap_Loader2_Mail_MapPin_MessageSquare_Phone_School_User_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                    className: \"h-5 w-5 mr-2 text-green-600\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                    lineNumber: 851,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                \"Financial Information\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                            lineNumber: 850,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-4\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"space-y-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                                                htmlFor: \"sec_principal_account_number\",\n                                                                                className: \"text-green-800 dark:text-green-200\",\n                                                                                children: \"Principal or Financial Officer Account Number *\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                lineNumber: 857,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"relative\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BookOpen_Building_CheckCircle_CreditCard_FileUp_GraduationCap_Loader2_Mail_MapPin_MessageSquare_Phone_School_User_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                                        className: \"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-green-500\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                        lineNumber: 861,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                                        id: \"sec_principal_account_number\",\n                                                                                        value: secondaryFormData.principal_account_number,\n                                                                                        onChange: (e)=>setSecondaryFormData((prev)=>({\n                                                                                                    ...prev,\n                                                                                                    principal_account_number: e.target.value\n                                                                                                })),\n                                                                                        placeholder: \"Enter account number for scholarship payment\",\n                                                                                        className: \"pl-10 rounded-xl border-green-200 dark:border-green-700 focus:border-green-500\",\n                                                                                        required: true\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                        lineNumber: 862,\n                                                                                        columnNumber: 29\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                lineNumber: 860,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                        lineNumber: 856,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"space-y-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                                                htmlFor: \"sec_school_fee_amount\",\n                                                                                className: \"text-green-800 dark:text-green-200\",\n                                                                                children: \"School Fee Amount *\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                lineNumber: 874,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                                id: \"sec_school_fee_amount\",\n                                                                                type: \"number\",\n                                                                                value: secondaryFormData.school_fee_amount,\n                                                                                onChange: (e)=>setSecondaryFormData((prev)=>({\n                                                                                            ...prev,\n                                                                                            school_fee_amount: e.target.value\n                                                                                        })),\n                                                                                placeholder: \"Enter amount in Naira\",\n                                                                                className: \"rounded-xl border-green-200 dark:border-green-700 focus:border-green-500\",\n                                                                                required: true\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                lineNumber: 877,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                        lineNumber: 873,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                lineNumber: 855,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                            lineNumber: 854,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                    lineNumber: 849,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-lg font-semibold text-green-800 dark:text-green-200 mb-4 flex items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BookOpen_Building_CheckCircle_CreditCard_FileUp_GraduationCap_Loader2_Mail_MapPin_MessageSquare_Phone_School_User_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                    className: \"h-5 w-5 mr-2 text-green-600\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                    lineNumber: 894,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                \"Picture & Application Reason\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                            lineNumber: 893,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"space-y-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                                            htmlFor: \"sec_student_picture\",\n                                                                            className: \"text-green-800 dark:text-green-200\",\n                                                                            children: \"Upload Student Picture *\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                            lineNumber: 899,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                            id: \"sec_student_picture\",\n                                                                            type: \"file\",\n                                                                            accept: \"image/*\",\n                                                                            onChange: (e)=>{\n                                                                                var _e_target_files;\n                                                                                return handleFileChange('secondary', 'student_picture', ((_e_target_files = e.target.files) === null || _e_target_files === void 0 ? void 0 : _e_target_files[0]) || null);\n                                                                            },\n                                                                            className: \"rounded-xl border-green-200 dark:border-green-700 focus:border-green-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-green-50 file:text-green-700 hover:file:bg-green-100\",\n                                                                            required: true\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                            lineNumber: 902,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                    lineNumber: 898,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"space-y-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                                            htmlFor: \"sec_reason_for_scholarship\",\n                                                                            className: \"text-green-800 dark:text-green-200\",\n                                                                            children: \"Reason for the Scholarship *\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                            lineNumber: 913,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_6__.Textarea, {\n                                                                            id: \"sec_reason_for_scholarship\",\n                                                                            value: secondaryFormData.reason_for_scholarship,\n                                                                            onChange: (e)=>setSecondaryFormData((prev)=>({\n                                                                                        ...prev,\n                                                                                        reason_for_scholarship: e.target.value\n                                                                                    })),\n                                                                            placeholder: \"Please explain why you need this scholarship\",\n                                                                            className: \"rounded-xl border-green-200 dark:border-green-700 focus:border-green-500\",\n                                                                            rows: 4,\n                                                                            required: true\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                            lineNumber: 916,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                    lineNumber: 912,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                            lineNumber: 897,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                    lineNumber: 892,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-center pt-6 sm:pt-8\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                        type: \"submit\",\n                                                        disabled: submitting || !validateForm(),\n                                                        size: \"lg\",\n                                                        className: \"w-full sm:w-auto bg-gradient-to-r from-green-600 via-emerald-600 to-teal-600 hover:from-green-700 hover:via-emerald-700 hover:to-teal-700 text-white px-6 sm:px-12 py-3 sm:py-4 text-base sm:text-lg font-bold shadow-lg hover:shadow-xl transition-all duration-300\",\n                                                        children: submitting ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BookOpen_Building_CheckCircle_CreditCard_FileUp_GraduationCap_Loader2_Mail_MapPin_MessageSquare_Phone_School_User_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                    className: \"mr-2 sm:mr-3 h-5 w-5 sm:h-6 sm:w-6 animate-spin\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                    lineNumber: 938,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm sm:text-base\",\n                                                                    children: \"Submitting...\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                    lineNumber: 939,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BookOpen_Building_CheckCircle_CreditCard_FileUp_GraduationCap_Loader2_Mail_MapPin_MessageSquare_Phone_School_User_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                    className: \"mr-2 sm:mr-3 h-5 w-5 sm:h-6 sm:w-6\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                    lineNumber: 943,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm sm:text-base\",\n                                                                    children: \"Submit Secondary School Application\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                    lineNumber: 944,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                        lineNumber: 930,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                    lineNumber: 929,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                            lineNumber: 698,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                        lineNumber: 697,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_8__.TabsContent, {\n                                        value: \"university\",\n                                        className: \"space-y-4 sm:space-y-6 md:space-y-8\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                            onSubmit: handleSubmit,\n                                            className: \"space-y-4 sm:space-y-6 md:space-y-8\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-green-50 p-3 sm:p-4 md:p-6 rounded-lg border border-green-200\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-start sm:items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BookOpen_Building_CheckCircle_CreditCard_FileUp_GraduationCap_Loader2_Mail_MapPin_MessageSquare_Phone_School_User_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                className: \"h-4 w-4 sm:h-5 sm:w-5 text-green-600 mr-2 sm:mr-3 mt-0.5 sm:mt-0 flex-shrink-0\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                lineNumber: 957,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-green-800 font-medium text-sm sm:text-base\",\n                                                                children: \"This form should be filled by the University Student directly\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                lineNumber: 958,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                        lineNumber: 956,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                    lineNumber: 955,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-lg font-semibold text-green-800 dark:text-green-200 mb-4 flex items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BookOpen_Building_CheckCircle_CreditCard_FileUp_GraduationCap_Loader2_Mail_MapPin_MessageSquare_Phone_School_User_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                    className: \"h-5 w-5 mr-2 text-green-600\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                    lineNumber: 967,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                \"Personal Information\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                            lineNumber: 966,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"space-y-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                                            htmlFor: \"uni_full_name\",\n                                                                            className: \"text-green-800 dark:text-green-200\",\n                                                                            children: \"Full Name *\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                            lineNumber: 972,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"relative\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BookOpen_Building_CheckCircle_CreditCard_FileUp_GraduationCap_Loader2_Mail_MapPin_MessageSquare_Phone_School_User_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                                    className: \"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-green-500\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                    lineNumber: 976,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                                    id: \"uni_full_name\",\n                                                                                    value: universityFormData.full_name,\n                                                                                    onChange: (e)=>setUniversityFormData((prev)=>({\n                                                                                                ...prev,\n                                                                                                full_name: e.target.value\n                                                                                            })),\n                                                                                    placeholder: \"Enter your full name\",\n                                                                                    className: \"pl-10 rounded-xl border-green-200 dark:border-green-700 focus:border-green-500\",\n                                                                                    required: true\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                    lineNumber: 977,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                            lineNumber: 975,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                    lineNumber: 971,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"space-y-2\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                                                    htmlFor: \"uni_age\",\n                                                                                    className: \"text-green-800 dark:text-green-200\",\n                                                                                    children: \"Age *\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                    lineNumber: 990,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                                    id: \"uni_age\",\n                                                                                    type: \"number\",\n                                                                                    value: universityFormData.age,\n                                                                                    onChange: (e)=>setUniversityFormData((prev)=>({\n                                                                                                ...prev,\n                                                                                                age: e.target.value\n                                                                                            })),\n                                                                                    placeholder: \"Your age\",\n                                                                                    className: \"rounded-xl border-green-200 dark:border-green-700 focus:border-green-500\",\n                                                                                    required: true\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                    lineNumber: 993,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                            lineNumber: 989,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"space-y-2\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                                                    htmlFor: \"uni_phone_number\",\n                                                                                    className: \"text-green-800 dark:text-green-200\",\n                                                                                    children: \"Phone Number *\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                    lineNumber: 1005,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"relative\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BookOpen_Building_CheckCircle_CreditCard_FileUp_GraduationCap_Loader2_Mail_MapPin_MessageSquare_Phone_School_User_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                                            className: \"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-green-500\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                            lineNumber: 1009,\n                                                                                            columnNumber: 29\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                                            id: \"uni_phone_number\",\n                                                                                            type: \"tel\",\n                                                                                            value: universityFormData.phone_number,\n                                                                                            onChange: (e)=>setUniversityFormData((prev)=>({\n                                                                                                        ...prev,\n                                                                                                        phone_number: e.target.value\n                                                                                                    })),\n                                                                                            placeholder: \"Enter your phone number\",\n                                                                                            className: \"pl-10 rounded-xl border-green-200 dark:border-green-700 focus:border-green-500\",\n                                                                                            required: true\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                            lineNumber: 1010,\n                                                                                            columnNumber: 29\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                    lineNumber: 1008,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                            lineNumber: 1004,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                    lineNumber: 988,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"space-y-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                                            htmlFor: \"uni_email_address\",\n                                                                            className: \"text-green-800 dark:text-green-200\",\n                                                                            children: \"Email Address *\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                            lineNumber: 1024,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"relative\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BookOpen_Building_CheckCircle_CreditCard_FileUp_GraduationCap_Loader2_Mail_MapPin_MessageSquare_Phone_School_User_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                                    className: \"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-green-500\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                    lineNumber: 1028,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                                    id: \"uni_email_address\",\n                                                                                    type: \"email\",\n                                                                                    value: universityFormData.email_address,\n                                                                                    onChange: (e)=>setUniversityFormData((prev)=>({\n                                                                                                ...prev,\n                                                                                                email_address: e.target.value\n                                                                                            })),\n                                                                                    placeholder: \"Enter your email address\",\n                                                                                    className: \"pl-10 rounded-xl border-green-200 dark:border-green-700 focus:border-green-500\",\n                                                                                    required: true\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                    lineNumber: 1029,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                            lineNumber: 1027,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                    lineNumber: 1023,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                            lineNumber: 970,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                    lineNumber: 965,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-lg font-semibold text-green-800 dark:text-green-200 mb-4 flex items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BookOpen_Building_CheckCircle_CreditCard_FileUp_GraduationCap_Loader2_Mail_MapPin_MessageSquare_Phone_School_User_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                    className: \"h-5 w-5 mr-2 text-green-600\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                    lineNumber: 1046,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                \"Academic Information\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                            lineNumber: 1045,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"space-y-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                                            htmlFor: \"uni_course_of_study\",\n                                                                            className: \"text-green-800 dark:text-green-200\",\n                                                                            children: \"Course of Study *\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                            lineNumber: 1051,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                            id: \"uni_course_of_study\",\n                                                                            value: universityFormData.course_of_study,\n                                                                            onChange: (e)=>setUniversityFormData((prev)=>({\n                                                                                        ...prev,\n                                                                                        course_of_study: e.target.value\n                                                                                    })),\n                                                                            placeholder: \"Enter your course of study\",\n                                                                            className: \"rounded-xl border-green-200 dark:border-green-700 focus:border-green-500\",\n                                                                            required: true\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                            lineNumber: 1054,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                    lineNumber: 1050,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"space-y-2\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                                                    htmlFor: \"uni_current_level\",\n                                                                                    className: \"text-green-800 dark:text-green-200\",\n                                                                                    children: \"Current Level *\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                    lineNumber: 1066,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.Select, {\n                                                                                    value: universityFormData.current_level,\n                                                                                    onValueChange: (value)=>setUniversityFormData((prev)=>({\n                                                                                                ...prev,\n                                                                                                current_level: value\n                                                                                            })),\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectTrigger, {\n                                                                                            className: \"rounded-xl border-green-200 dark:border-green-700 focus:border-green-500\",\n                                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectValue, {\n                                                                                                placeholder: \"Select your current level\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                                lineNumber: 1071,\n                                                                                                columnNumber: 31\n                                                                                            }, this)\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                            lineNumber: 1070,\n                                                                                            columnNumber: 29\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectContent, {\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                                                    value: \"100L\",\n                                                                                                    children: \"100 Level\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                                    lineNumber: 1074,\n                                                                                                    columnNumber: 31\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                                                    value: \"200L\",\n                                                                                                    children: \"200 Level\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                                    lineNumber: 1075,\n                                                                                                    columnNumber: 31\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                                                    value: \"300L\",\n                                                                                                    children: \"300 Level\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                                    lineNumber: 1076,\n                                                                                                    columnNumber: 31\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                                                    value: \"400L\",\n                                                                                                    children: \"400 Level\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                                    lineNumber: 1077,\n                                                                                                    columnNumber: 31\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                                                    value: \"500L\",\n                                                                                                    children: \"500 Level\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                                    lineNumber: 1078,\n                                                                                                    columnNumber: 31\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                                                    value: \"600L\",\n                                                                                                    children: \"600 Level\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                                    lineNumber: 1079,\n                                                                                                    columnNumber: 31\n                                                                                                }, this)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                            lineNumber: 1073,\n                                                                                            columnNumber: 29\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                    lineNumber: 1069,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                            lineNumber: 1065,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"space-y-2\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                                                    htmlFor: \"uni_matriculation_number\",\n                                                                                    className: \"text-green-800 dark:text-green-200\",\n                                                                                    children: \"Matriculation Number *\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                    lineNumber: 1085,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                                    id: \"uni_matriculation_number\",\n                                                                                    value: universityFormData.matriculation_number,\n                                                                                    onChange: (e)=>setUniversityFormData((prev)=>({\n                                                                                                ...prev,\n                                                                                                matriculation_number: e.target.value\n                                                                                            })),\n                                                                                    placeholder: \"Enter your matriculation number\",\n                                                                                    className: \"rounded-xl border-green-200 dark:border-green-700 focus:border-green-500\",\n                                                                                    required: true\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                    lineNumber: 1088,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                            lineNumber: 1084,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                    lineNumber: 1064,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                            lineNumber: 1049,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                    lineNumber: 1044,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-lg font-semibold text-green-800 dark:text-green-200 mb-4 flex items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BookOpen_Building_CheckCircle_CreditCard_FileUp_GraduationCap_Loader2_Mail_MapPin_MessageSquare_Phone_School_User_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                    className: \"h-5 w-5 mr-2 text-green-600\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                    lineNumber: 1104,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                \"Required Documents\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                            lineNumber: 1103,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"space-y-2\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                                                    htmlFor: \"uni_student_id_card\",\n                                                                                    className: \"text-green-800 dark:text-green-200\",\n                                                                                    children: \"Upload Student ID Card *\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                    lineNumber: 1110,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                                    id: \"uni_student_id_card\",\n                                                                                    type: \"file\",\n                                                                                    accept: \"image/*,.pdf\",\n                                                                                    onChange: (e)=>{\n                                                                                        var _e_target_files;\n                                                                                        return handleFileChange('university', 'student_id_card', ((_e_target_files = e.target.files) === null || _e_target_files === void 0 ? void 0 : _e_target_files[0]) || null);\n                                                                                    },\n                                                                                    className: \"rounded-xl border-green-200 dark:border-green-700 focus:border-green-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-green-50 file:text-green-700 hover:file:bg-green-100\",\n                                                                                    required: true\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                    lineNumber: 1113,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                            lineNumber: 1109,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"space-y-2\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                                                    htmlFor: \"uni_payment_evidence\",\n                                                                                    className: \"text-green-800 dark:text-green-200\",\n                                                                                    children: \"Upload Payment Evidence (Remita/Screenshot) *\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                    lineNumber: 1124,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                                    id: \"uni_payment_evidence\",\n                                                                                    type: \"file\",\n                                                                                    accept: \"image/*,.pdf\",\n                                                                                    onChange: (e)=>{\n                                                                                        var _e_target_files;\n                                                                                        return handleFileChange('university', 'payment_evidence', ((_e_target_files = e.target.files) === null || _e_target_files === void 0 ? void 0 : _e_target_files[0]) || null);\n                                                                                    },\n                                                                                    className: \"rounded-xl border-green-200 dark:border-green-700 focus:border-green-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-green-50 file:text-green-700 hover:file:bg-green-100\",\n                                                                                    required: true\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                    lineNumber: 1127,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                            lineNumber: 1123,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                    lineNumber: 1108,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"space-y-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                                            htmlFor: \"uni_supporting_documents\",\n                                                                            className: \"text-green-800 dark:text-green-200\",\n                                                                            children: \"Upload Supporting Documents (PDF, DOCX, JPG)\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                            lineNumber: 1139,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                            id: \"uni_supporting_documents\",\n                                                                            type: \"file\",\n                                                                            accept: \".pdf,.docx,.doc,.jpg,.jpeg,.png\",\n                                                                            multiple: true,\n                                                                            onChange: (e)=>handleMultipleFileChange(e.target.files),\n                                                                            className: \"rounded-xl border-green-200 dark:border-green-700 focus:border-green-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-green-50 file:text-green-700 hover:file:bg-green-100\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                            lineNumber: 1142,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        universityFiles.supporting_documents.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"mt-2 space-y-2\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"text-sm text-green-600\",\n                                                                                    children: \"Selected files:\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                    lineNumber: 1152,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                universityFiles.supporting_documents.map((file, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"flex items-center justify-between bg-green-50 p-2 rounded-xl border border-green-200\",\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                className: \"text-sm text-green-700\",\n                                                                                                children: file.name\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                                lineNumber: 1155,\n                                                                                                columnNumber: 33\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                                                type: \"button\",\n                                                                                                variant: \"ghost\",\n                                                                                                size: \"sm\",\n                                                                                                onClick: ()=>removeFile(index),\n                                                                                                className: \"text-red-600 hover:text-red-800\",\n                                                                                                children: \"Remove\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                                lineNumber: 1156,\n                                                                                                columnNumber: 33\n                                                                                            }, this)\n                                                                                        ]\n                                                                                    }, index, true, {\n                                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                        lineNumber: 1154,\n                                                                                        columnNumber: 31\n                                                                                    }, this))\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                            lineNumber: 1151,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                    lineNumber: 1138,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                            lineNumber: 1107,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                    lineNumber: 1102,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-lg font-semibold text-green-800 dark:text-green-200 mb-4 flex items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BookOpen_Building_CheckCircle_CreditCard_FileUp_GraduationCap_Loader2_Mail_MapPin_MessageSquare_Phone_School_User_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                                    className: \"h-5 w-5 mr-2 text-green-600\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                    lineNumber: 1176,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                \"Application Reason\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                            lineNumber: 1175,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                                    htmlFor: \"uni_reason_for_scholarship\",\n                                                                    className: \"text-green-800 dark:text-green-200\",\n                                                                    children: \"Reason for the Scholarship *\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                    lineNumber: 1180,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_6__.Textarea, {\n                                                                    id: \"uni_reason_for_scholarship\",\n                                                                    value: universityFormData.reason_for_scholarship,\n                                                                    onChange: (e)=>setUniversityFormData((prev)=>({\n                                                                                ...prev,\n                                                                                reason_for_scholarship: e.target.value\n                                                                            })),\n                                                                    placeholder: \"Please explain why you need this scholarship\",\n                                                                    className: \"rounded-xl border-green-200 dark:border-green-700 focus:border-green-500\",\n                                                                    rows: 4,\n                                                                    required: true\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                    lineNumber: 1183,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                            lineNumber: 1179,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                    lineNumber: 1174,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-center pt-6 sm:pt-8\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                        type: \"submit\",\n                                                        disabled: submitting || !validateForm(),\n                                                        size: \"lg\",\n                                                        className: \"w-full sm:w-auto bg-gradient-to-r from-green-600 via-emerald-600 to-teal-600 hover:from-green-700 hover:via-emerald-700 hover:to-teal-700 text-white px-6 sm:px-12 py-3 sm:py-4 text-base sm:text-lg font-bold shadow-lg hover:shadow-xl transition-all duration-300\",\n                                                        children: submitting ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BookOpen_Building_CheckCircle_CreditCard_FileUp_GraduationCap_Loader2_Mail_MapPin_MessageSquare_Phone_School_User_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                    className: \"mr-2 sm:mr-3 h-5 w-5 sm:h-6 sm:w-6 animate-spin\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                    lineNumber: 1204,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm sm:text-base\",\n                                                                    children: \"Submitting...\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                    lineNumber: 1205,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BookOpen_Building_CheckCircle_CreditCard_FileUp_GraduationCap_Loader2_Mail_MapPin_MessageSquare_Phone_School_User_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                    className: \"mr-2 sm:mr-3 h-5 w-5 sm:h-6 sm:w-6\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                    lineNumber: 1209,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm sm:text-base\",\n                                                                    children: \"Submit University Application\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                    lineNumber: 1210,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                        lineNumber: 1196,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                    lineNumber: 1195,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                            lineNumber: 954,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                        lineNumber: 953,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                lineNumber: 369,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                        lineNumber: 361,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                    lineNumber: 360,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                lineNumber: 359,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n        lineNumber: 343,\n        columnNumber: 5\n    }, this);\n}\n_s(ScholarshipApplicationPage, \"YzYPuqCmKkDBdbhfr6Jpe188HFM=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams,\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_9__.useToast\n    ];\n});\n_c = ScholarshipApplicationPage;\nvar _c;\n$RefreshReg$(_c, \"ScholarshipApplicationPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2NvbXBvbmVudHMvc2Nob2xhcnNoaXAvU2Nob2xhcnNoaXBBcHBsaWNhdGlvblBhZ2UudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUVrRDtBQUNEO0FBRUY7QUFDRjtBQUNBO0FBQ007QUFDbUQ7QUFDdkI7QUFrQjFEO0FBQ3VCO0FBQ1A7QUFDUTtBQTJDOUIsU0FBU2tDOztJQUN0QixNQUFNQyxlQUFlaEMsZ0VBQWVBO0lBQ3BDLE1BQU0sQ0FBQ2lDLGtCQUFrQkMsb0JBQW9CLEdBQUdwQywrQ0FBUUEsQ0FBeUM7SUFDakcsTUFBTSxDQUFDcUMsWUFBWUMsY0FBYyxHQUFHdEMsK0NBQVFBLENBQUM7SUFDN0MsTUFBTSxDQUFDdUMsYUFBYUMsZUFBZSxHQUFHeEMsK0NBQVFBLENBQUM7SUFDL0MsTUFBTSxDQUFDeUMsZUFBZUMsaUJBQWlCLEdBQUcxQywrQ0FBUUEsQ0FBZ0I7SUFDbEUsTUFBTSxFQUFFMkMsS0FBSyxFQUFFLEdBQUdiLDBEQUFRQTtJQUUxQix3REFBd0Q7SUFDeEQ3QixnREFBU0E7Z0RBQUM7WUFDUixNQUFNMkMsZ0JBQWdCVixhQUFhVyxHQUFHLENBQUM7WUFDdkMsTUFBTUMscUJBQXFCWixhQUFhVyxHQUFHLENBQUM7WUFFNUMsSUFBSUQsaUJBQWlCO2dCQUFDO2dCQUFXO2dCQUFhO2FBQWEsQ0FBQ0csUUFBUSxDQUFDSCxnQkFBZ0I7Z0JBQ25GUixvQkFBb0JRO1lBQ3RCO1lBRUEsSUFBSUUsb0JBQW9CO2dCQUN0QkosaUJBQWlCSTtZQUNuQjtRQUNGOytDQUFHO1FBQUNaO0tBQWE7SUFFakIscUNBQXFDO0lBQ3JDLE1BQU0sQ0FBQ2MsaUJBQWlCQyxtQkFBbUIsR0FBR2pELCtDQUFRQSxDQUF3QjtRQUM1RWtELG1CQUFtQjtRQUNuQkMsS0FBSztRQUNMQyxlQUFlO1FBQ2ZDLGFBQWE7UUFDYkMsYUFBYTtRQUNiQyxjQUFjO1FBQ2RDLGNBQWM7UUFDZEMsYUFBYTtRQUNiQyxpQkFBaUI7UUFDakJDLHVCQUF1QjtRQUN2QkMsd0JBQXdCO1FBQ3hCQyxvQkFBb0I7UUFDcEJDLHdCQUF3QjtJQUMxQjtJQUVBLE1BQU0sQ0FBQ0MsbUJBQW1CQyxxQkFBcUIsR0FBR2hFLCtDQUFRQSxDQUEwQjtRQUNsRmtELG1CQUFtQjtRQUNuQkMsS0FBSztRQUNMYyxPQUFPO1FBQ1BWLGNBQWM7UUFDZFcsU0FBUztRQUNUVCxhQUFhO1FBQ2JVLGdCQUFnQjtRQUNoQkMsMEJBQTBCO1FBQzFCUix3QkFBd0I7UUFDeEJTLG1CQUFtQjtJQUNyQjtJQUVBLE1BQU0sQ0FBQ0Msb0JBQW9CQyxzQkFBc0IsR0FBR3ZFLCtDQUFRQSxDQUFxQjtRQUMvRXdFLFdBQVc7UUFDWHJCLEtBQUs7UUFDTHNCLGlCQUFpQjtRQUNqQkMsZUFBZTtRQUNmQyxjQUFjO1FBQ2RDLGVBQWU7UUFDZkMsc0JBQXNCO1FBQ3RCakIsd0JBQXdCO0lBQzFCO0lBRUEsd0NBQXdDO0lBQ3hDLE1BQU0sQ0FBQ2tCLGNBQWNDLGdCQUFnQixHQUFHL0UsK0NBQVFBLENBQUM7UUFDL0NnRixpQkFBaUI7SUFDbkI7SUFFQSxNQUFNLENBQUNDLGdCQUFnQkMsa0JBQWtCLEdBQUdsRiwrQ0FBUUEsQ0FBQztRQUNuRGdGLGlCQUFpQjtJQUNuQjtJQUVBLE1BQU0sQ0FBQ0csaUJBQWlCQyxtQkFBbUIsR0FBR3BGLCtDQUFRQSxDQUFDO1FBQ3JEcUYsaUJBQWlCO1FBQ2pCQyxrQkFBa0I7UUFDbEJDLHNCQUFzQixFQUFFO0lBQzFCO0lBRUEsbUVBQW1FO0lBQ25FLE1BQU1DLGlCQUFpQjtRQUNyQkMsU0FBUztZQUNQQyxNQUFNMUUsNE1BQVFBO1lBQ2QyRSxPQUFPO1lBQ1BDLGFBQWE7WUFDYkMsT0FBTztZQUNQQyxTQUFTO1lBQ1RDLFdBQVc7WUFDWEMsYUFBYTtRQUNmO1FBQ0FDLFdBQVc7WUFDVFAsTUFBTXhFLDRNQUFNQTtZQUNaeUUsT0FBTztZQUNQQyxhQUFhO1lBQ2JDLE9BQU87WUFDUEMsU0FBUztZQUNUQyxXQUFXO1lBQ1hDLGFBQWE7UUFDZjtRQUNBRSxZQUFZO1lBQ1ZSLE1BQU16RSw0TUFBYUE7WUFDbkIwRSxPQUFPO1lBQ1BDLGFBQWE7WUFDYkMsT0FBTztZQUNQQyxTQUFTO1lBQ1RDLFdBQVc7WUFDWEMsYUFBYTtRQUNmO0lBQ0Y7SUFFQSxNQUFNRyxtQkFBbUIsQ0FBQ0MsVUFBa0JDLE9BQWVDO1FBQ3pELElBQUlGLGFBQWEsV0FBVztZQUMxQnJCLGdCQUFnQndCLENBQUFBLE9BQVM7b0JBQUUsR0FBR0EsSUFBSTtvQkFBRSxDQUFDRixNQUFNLEVBQUVDO2dCQUFLO1FBQ3BELE9BQU8sSUFBSUYsYUFBYSxhQUFhO1lBQ25DbEIsa0JBQWtCcUIsQ0FBQUEsT0FBUztvQkFBRSxHQUFHQSxJQUFJO29CQUFFLENBQUNGLE1BQU0sRUFBRUM7Z0JBQUs7UUFDdEQsT0FBTyxJQUFJRixhQUFhLGNBQWM7WUFDcEMsSUFBSUMsVUFBVSwwQkFBMEJDLE1BQU07Z0JBQzVDbEIsbUJBQW1CbUIsQ0FBQUEsT0FBUzt3QkFDMUIsR0FBR0EsSUFBSTt3QkFDUGhCLHNCQUFzQjsrQkFBSWdCLEtBQUtoQixvQkFBb0I7NEJBQUVlO3lCQUFLO29CQUM1RDtZQUNGLE9BQU87Z0JBQ0xsQixtQkFBbUJtQixDQUFBQSxPQUFTO3dCQUFFLEdBQUdBLElBQUk7d0JBQUUsQ0FBQ0YsTUFBTSxFQUFFQztvQkFBSztZQUN2RDtRQUNGO0lBQ0Y7SUFFQSxNQUFNRSwyQkFBMkIsQ0FBQ0M7UUFDaEMsSUFBSUEsU0FBU3RFLHFCQUFxQixjQUFjO1lBQzlDLE1BQU11RSxZQUFZQyxNQUFNQyxJQUFJLENBQUNIO1lBQzdCckIsbUJBQW1CbUIsQ0FBQUEsT0FBUztvQkFDMUIsR0FBR0EsSUFBSTtvQkFDUGhCLHNCQUFzQjsyQkFBSWdCLEtBQUtoQixvQkFBb0I7MkJBQUttQjtxQkFBVTtnQkFDcEU7UUFDRjtJQUNGO0lBRUEsTUFBTUcsYUFBYSxDQUFDQztRQUNsQjFCLG1CQUFtQm1CLENBQUFBLE9BQVM7Z0JBQzFCLEdBQUdBLElBQUk7Z0JBQ1BoQixzQkFBc0JnQixLQUFLaEIsb0JBQW9CLENBQUN3QixNQUFNLENBQUMsQ0FBQ0MsR0FBR0MsSUFBTUEsTUFBTUg7WUFDekU7SUFDRjtJQUVBLE1BQU1JLGVBQWU7UUFDbkIsSUFBSS9FLHFCQUFxQixXQUFXO1lBQ2xDLE1BQU1nRixXQUFXO2dCQUFDO2dCQUFxQjtnQkFBTztnQkFBaUI7Z0JBQWU7Z0JBQWU7Z0JBQWdCO2dCQUFnQjtnQkFBZTtnQkFBbUI7Z0JBQXlCO2dCQUEwQjthQUFxQjtZQUN2TyxPQUFPQSxTQUFTQyxLQUFLLENBQUNmLENBQUFBLFFBQVNyRCxlQUFlLENBQUNxRCxNQUFxQyxDQUFDZ0IsSUFBSSxPQUFPLE9BQU92QyxhQUFhRSxlQUFlO1FBQ3JJLE9BQU8sSUFBSTdDLHFCQUFxQixhQUFhO1lBQzNDLE1BQU1nRixXQUFXO2dCQUFDO2dCQUFxQjtnQkFBTztnQkFBUztnQkFBZ0I7Z0JBQVc7Z0JBQWU7Z0JBQWtCO2dCQUE0QjtnQkFBMEI7YUFBb0I7WUFDN0wsT0FBT0EsU0FBU0MsS0FBSyxDQUFDZixDQUFBQSxRQUFTdEMsaUJBQWlCLENBQUNzQyxNQUF1QyxDQUFDZ0IsSUFBSSxPQUFPLE9BQU9wQyxlQUFlRCxlQUFlO1FBQzNJLE9BQU8sSUFBSTdDLHFCQUFxQixjQUFjO1lBQzVDLE1BQU1nRixXQUFXO2dCQUFDO2dCQUFhO2dCQUFPO2dCQUFtQjtnQkFBaUI7Z0JBQWdCO2dCQUFpQjtnQkFBd0I7YUFBeUI7WUFDNUosT0FBT0EsU0FBU0MsS0FBSyxDQUFDZixDQUFBQSxRQUFTL0Isa0JBQWtCLENBQUMrQixNQUFrQyxDQUFDZ0IsSUFBSSxPQUFPLE9BQ3pGbEMsZ0JBQWdCRSxlQUFlLElBQUlGLGdCQUFnQkcsZ0JBQWdCO1FBQzVFO1FBQ0EsT0FBTztJQUNUO0lBRUEsTUFBTWdDLGVBQWUsT0FBT0M7UUFDMUJBLEVBQUVDLGNBQWM7UUFFaEIsSUFBSSxDQUFDTixnQkFBZ0I7WUFDbkJ2RSxNQUFNO2dCQUNKZ0QsT0FBTztnQkFDUEMsYUFBYTtnQkFDYjZCLFNBQVM7WUFDWDtZQUNBO1FBQ0Y7UUFFQSxJQUFJO1lBQ0ZuRixjQUFjO1lBRWQsTUFBTW9GLFdBQVcsSUFBSUM7WUFDckJELFNBQVNFLE1BQU0sQ0FBQyxZQUFZekY7WUFFNUIsa0NBQWtDO1lBQ2xDLElBQUlBLHFCQUFxQixXQUFXO2dCQUNsQ3VGLFNBQVNFLE1BQU0sQ0FBQyxhQUFhQyxLQUFLQyxTQUFTLENBQUM5RTtnQkFDNUMsSUFBSThCLGFBQWFFLGVBQWUsRUFBRTtvQkFDaEMwQyxTQUFTRSxNQUFNLENBQUMsbUJBQW1COUMsYUFBYUUsZUFBZTtnQkFDakU7WUFDRixPQUFPLElBQUk3QyxxQkFBcUIsYUFBYTtnQkFDM0N1RixTQUFTRSxNQUFNLENBQUMsYUFBYUMsS0FBS0MsU0FBUyxDQUFDL0Q7Z0JBQzVDLElBQUlrQixlQUFlRCxlQUFlLEVBQUU7b0JBQ2xDMEMsU0FBU0UsTUFBTSxDQUFDLG1CQUFtQjNDLGVBQWVELGVBQWU7Z0JBQ25FO1lBQ0YsT0FBTyxJQUFJN0MscUJBQXFCLGNBQWM7Z0JBQzVDdUYsU0FBU0UsTUFBTSxDQUFDLGFBQWFDLEtBQUtDLFNBQVMsQ0FBQ3hEO2dCQUM1QyxJQUFJYSxnQkFBZ0JFLGVBQWUsRUFBRTtvQkFDbkNxQyxTQUFTRSxNQUFNLENBQUMsbUJBQW1CekMsZ0JBQWdCRSxlQUFlO2dCQUNwRTtnQkFDQSxJQUFJRixnQkFBZ0JHLGdCQUFnQixFQUFFO29CQUNwQ29DLFNBQVNFLE1BQU0sQ0FBQyxvQkFBb0J6QyxnQkFBZ0JHLGdCQUFnQjtnQkFDdEU7Z0JBQ0FILGdCQUFnQkksb0JBQW9CLENBQUN3QyxPQUFPLENBQUMsQ0FBQ3pCLE1BQU1RO29CQUNsRFksU0FBU0UsTUFBTSxDQUFDLHdCQUE4QixPQUFOZCxPQUFNLE1BQUlSO2dCQUNwRDtZQUNGO1lBRUEsa0NBQWtDO1lBQ2xDLElBQUk3RCxlQUFlO2dCQUNqQmlGLFNBQVNFLE1BQU0sQ0FBQyxrQkFBa0JuRjtZQUNwQztZQUVBLE1BQU11RixXQUFXLE1BQU1qRyxnREFBU0EsQ0FBQ2tHLDRCQUE0QixDQUFDUDtZQUU5RCxJQUFJTSxTQUFTRSxPQUFPLEVBQUU7Z0JBQ3BCLHVCQUF1QjtnQkFDdkIxRixlQUFlO2dCQUVmLGFBQWE7Z0JBQ2IsSUFBSUwscUJBQXFCLFdBQVc7b0JBQ2xDYyxtQkFBbUI7d0JBQ2pCQyxtQkFBbUI7d0JBQUlDLEtBQUs7d0JBQUlDLGVBQWU7d0JBQUlDLGFBQWE7d0JBQUlDLGFBQWE7d0JBQ2pGQyxjQUFjO3dCQUFJQyxjQUFjO3dCQUFJQyxhQUFhO3dCQUFJQyxpQkFBaUI7d0JBQ3RFQyx1QkFBdUI7d0JBQUlDLHdCQUF3Qjt3QkFBSUMsb0JBQW9CO3dCQUMzRUMsd0JBQXdCO29CQUMxQjtvQkFDQWlCLGdCQUFnQjt3QkFBRUMsaUJBQWlCO29CQUFLO2dCQUMxQyxPQUFPLElBQUk3QyxxQkFBcUIsYUFBYTtvQkFDM0M2QixxQkFBcUI7d0JBQ25CZCxtQkFBbUI7d0JBQUlDLEtBQUs7d0JBQUljLE9BQU87d0JBQUlWLGNBQWM7d0JBQUlXLFNBQVM7d0JBQ3RFVCxhQUFhO3dCQUFJVSxnQkFBZ0I7d0JBQUlDLDBCQUEwQjt3QkFDL0RSLHdCQUF3Qjt3QkFBSVMsbUJBQW1CO29CQUNqRDtvQkFDQWEsa0JBQWtCO3dCQUFFRixpQkFBaUI7b0JBQUs7Z0JBQzVDLE9BQU8sSUFBSTdDLHFCQUFxQixjQUFjO29CQUM1Q29DLHNCQUFzQjt3QkFDcEJDLFdBQVc7d0JBQUlyQixLQUFLO3dCQUFJc0IsaUJBQWlCO3dCQUFJQyxlQUFlO3dCQUM1REMsY0FBYzt3QkFBSUMsZUFBZTt3QkFBSUMsc0JBQXNCO3dCQUMzRGpCLHdCQUF3QjtvQkFDMUI7b0JBQ0F3QixtQkFBbUI7d0JBQUVDLGlCQUFpQjt3QkFBTUMsa0JBQWtCO3dCQUFNQyxzQkFBc0IsRUFBRTtvQkFBQztnQkFDL0Y7WUFDRixPQUFPO2dCQUNMNUMsTUFBTTtvQkFDSmdELE9BQU87b0JBQ1BDLGFBQWFvQyxTQUFTRyxPQUFPLElBQUk7b0JBQ2pDVixTQUFTO2dCQUNYO1lBQ0Y7UUFDRixFQUFFLE9BQU9XLE9BQVk7Z0JBSUpBLHNCQUFBQTtZQUhmQyxRQUFRRCxLQUFLLENBQUMsaUNBQWlDQTtZQUMvQ3pGLE1BQU07Z0JBQ0pnRCxPQUFPO2dCQUNQQyxhQUFhd0MsRUFBQUEsa0JBQUFBLE1BQU1KLFFBQVEsY0FBZEksdUNBQUFBLHVCQUFBQSxnQkFBZ0JFLElBQUksY0FBcEJGLDJDQUFBQSxxQkFBc0JELE9BQU8sS0FBSTtnQkFDOUNWLFNBQVM7WUFDWDtRQUNGLFNBQVU7WUFDUm5GLGNBQWM7UUFDaEI7SUFDRjtJQUVBLE1BQU1pRyxtQkFBbUI7UUFDdkIvRixlQUFlO0lBQ2pCO0lBRUEsTUFBTWdHLGVBQWU7UUFDbkIscUNBQXFDO1FBQ3JDQyxPQUFPQyxRQUFRLENBQUNDLElBQUksR0FBRztJQUN6QjtJQUVBLElBQUlwRyxhQUFhO1FBQ2YscUJBQU8sOERBQUNQLHdEQUFjQTtZQUFDNEcsY0FBY0w7WUFBa0JNLFVBQVVMOzs7Ozs7SUFDbkU7SUFFQSxxQkFDRSw4REFBQ007UUFBSUMsV0FBVTs7MEJBRWIsOERBQUNDO2dCQUFRRCxXQUFVOzBCQUNqQiw0RUFBQ0Q7b0JBQUlDLFdBQVU7OEJBQ2IsNEVBQUNEO3dCQUFJQyxXQUFVOzswQ0FDYiw4REFBQ0U7Z0NBQUdGLFdBQVU7MENBQWtHOzs7Ozs7MENBR2hILDhEQUFDRztnQ0FBRUgsV0FBVTswQ0FBd0U7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MEJBUTNGLDhEQUFDQztnQkFBUUQsV0FBVTswQkFDakIsNEVBQUNEO29CQUFJQyxXQUFVOzhCQUNiLDRFQUFDRDt3QkFBSUMsV0FBVTs7MENBQ2IsOERBQUNEO2dDQUFJQyxXQUFVOztrREFDYiw4REFBQ0k7d0NBQUdKLFdBQVU7a0RBQXlGOzs7Ozs7a0RBQ3ZHLDhEQUFDRzt3Q0FBRUgsV0FBVTtrREFBaUY7Ozs7Ozs7Ozs7OzswQ0FLaEcsOERBQUNuSSxxREFBSUE7Z0NBQUN3SSxPQUFPakg7Z0NBQWtCa0gsZUFBZSxDQUFDRCxRQUFVaEgsb0JBQW9CZ0g7Z0NBQWVMLFdBQVU7O2tEQUNwRyw4REFBQ2pJLHlEQUFRQTt3Q0FBQ2lJLFdBQVU7a0RBQ2pCTyxPQUFPQyxPQUFPLENBQUMvRCxnQkFBZ0JnRSxHQUFHLENBQUM7Z0RBQUMsQ0FBQ0MsS0FBS0MsT0FBTzs0Q0FDaEQsTUFBTUMsZ0JBQWdCRCxPQUFPaEUsSUFBSTs0Q0FDakMscUJBQ0UsOERBQUMzRSw0REFBV0E7Z0RBRVZxSSxPQUFPSztnREFDUFYsV0FBVTs7a0VBRVYsOERBQUNEO3dEQUFJQyxXQUFVO2tFQUNiLDRFQUFDWTs0REFBY1osV0FBVTs7Ozs7Ozs7Ozs7a0VBRTNCLDhEQUFDRDt3REFBSUMsV0FBVTs7MEVBQ2IsOERBQUNEO2dFQUFJQyxXQUFVOzBFQUE4RFcsT0FBTy9ELEtBQUs7Ozs7OzswRUFDekYsOERBQUNtRDtnRUFBSUMsV0FBVTswRUFBMkRXLE9BQU85RCxXQUFXOzs7Ozs7Ozs7Ozs7OytDQVR6RjZEOzs7Ozt3Q0FhWDs7Ozs7O2tEQUlGLDhEQUFDNUksNERBQVdBO3dDQUFDdUksT0FBTTt3Q0FBVUwsV0FBVTtrREFDckMsNEVBQUNhOzRDQUFLQyxVQUFVdkM7NENBQWN5QixXQUFVOzs4REFDdEMsOERBQUNEO29EQUFJQyxXQUFVOzhEQUNiLDRFQUFDRDt3REFBSUMsV0FBVTs7MEVBQ2IsOERBQUNuSCw0TUFBYUE7Z0VBQUNtSCxXQUFVOzs7Ozs7MEVBQ3pCLDhEQUFDRztnRUFBRUgsV0FBVTswRUFBa0Q7Ozs7Ozs7Ozs7Ozs7Ozs7OzhEQU9uRSw4REFBQ0Q7b0RBQUlDLFdBQVU7O3NFQUNiLDhEQUFDRDs7OEVBQ0MsOERBQUNnQjtvRUFBR2YsV0FBVTs7c0ZBQ1osOERBQUM1SCw0TUFBSUE7NEVBQUM0SCxXQUFVOzs7Ozs7d0VBQThDOzs7Ozs7OzhFQUdoRSw4REFBQ0Q7b0VBQUlDLFdBQVU7O3NGQUNiLDhEQUFDRDs0RUFBSUMsV0FBVTs7OEZBQ2IsOERBQUMxSSx1REFBS0E7b0ZBQUMwSixTQUFRO29GQUFvQmhCLFdBQVU7OEZBQTBEOzs7Ozs7OEZBR3ZHLDhEQUFDRDtvRkFBSUMsV0FBVTs7c0dBQ2IsOERBQUM1SCw0TUFBSUE7NEZBQUM0SCxXQUFVOzs7Ozs7c0dBQ2hCLDhEQUFDM0ksdURBQUtBOzRGQUNKNEosSUFBRzs0RkFDSFosT0FBT3BHLGdCQUFnQkUsaUJBQWlCOzRGQUN4QytHLFVBQVUsQ0FBQzFDLElBQU10RSxtQkFBbUJzRCxDQUFBQSxPQUFTO3dHQUFFLEdBQUdBLElBQUk7d0dBQUVyRCxtQkFBbUJxRSxFQUFFMkMsTUFBTSxDQUFDZCxLQUFLO29HQUFDOzRGQUMxRmUsYUFBWTs0RkFDWnBCLFdBQVU7NEZBQ1Y1QixRQUFROzs7Ozs7Ozs7Ozs7Ozs7Ozs7c0ZBS2QsOERBQUMyQjs0RUFBSUMsV0FBVTs7OEZBQ2IsOERBQUNEO29GQUFJQyxXQUFVOztzR0FDYiw4REFBQzFJLHVEQUFLQTs0RkFBQzBKLFNBQVE7NEZBQU1oQixXQUFVO3NHQUEwRDs7Ozs7O3NHQUd6Riw4REFBQzNJLHVEQUFLQTs0RkFDSjRKLElBQUc7NEZBQ0hJLE1BQUs7NEZBQ0xoQixPQUFPcEcsZ0JBQWdCRyxHQUFHOzRGQUMxQjhHLFVBQVUsQ0FBQzFDLElBQU10RSxtQkFBbUJzRCxDQUFBQSxPQUFTO3dHQUFFLEdBQUdBLElBQUk7d0dBQUVwRCxLQUFLb0UsRUFBRTJDLE1BQU0sQ0FBQ2QsS0FBSztvR0FBQzs0RkFDNUVlLGFBQVk7NEZBQ1pwQixXQUFVOzRGQUNWNUIsUUFBUTs7Ozs7Ozs7Ozs7OzhGQUdaLDhEQUFDMkI7b0ZBQUlDLFdBQVU7O3NHQUNiLDhEQUFDMUksdURBQUtBOzRGQUFDMEosU0FBUTs0RkFBZ0JoQixXQUFVO3NHQUEwRDs7Ozs7O3NHQUduRyw4REFBQ3hJLHlEQUFNQTs0RkFBQzZJLE9BQU9wRyxnQkFBZ0JJLGFBQWE7NEZBQUVpRyxlQUFlLENBQUNELFFBQVVuRyxtQkFBbUJzRCxDQUFBQSxPQUFTO3dHQUFFLEdBQUdBLElBQUk7d0dBQUVuRCxlQUFlZ0c7b0dBQU07OzhHQUNsSSw4REFBQzFJLGdFQUFhQTtvR0FBQ3FJLFdBQVU7OEdBQ3ZCLDRFQUFDcEksOERBQVdBO3dHQUFDd0osYUFBWTs7Ozs7Ozs7Ozs7OEdBRTNCLDhEQUFDM0osZ0VBQWFBOztzSEFDWiw4REFBQ0MsNkRBQVVBOzRHQUFDMkksT0FBTTtzSEFBWTs7Ozs7O3NIQUM5Qiw4REFBQzNJLDZEQUFVQTs0R0FBQzJJLE9BQU07c0hBQVk7Ozs7OztzSEFDOUIsOERBQUMzSSw2REFBVUE7NEdBQUMySSxPQUFNO3NIQUFZOzs7Ozs7c0hBQzlCLDhEQUFDM0ksNkRBQVVBOzRHQUFDMkksT0FBTTtzSEFBWTs7Ozs7O3NIQUM5Qiw4REFBQzNJLDZEQUFVQTs0R0FBQzJJLE9BQU07c0hBQVk7Ozs7OztzSEFDOUIsOERBQUMzSSw2REFBVUE7NEdBQUMySSxPQUFNO3NIQUFZOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7c0VBUzFDLDhEQUFDTjs7OEVBQ0MsOERBQUNnQjtvRUFBR2YsV0FBVTs7c0ZBQ1osOERBQUM1SCw0TUFBSUE7NEVBQUM0SCxXQUFVOzs7Ozs7d0VBQWdDOzs7Ozs7OzhFQUdsRCw4REFBQ0Q7b0VBQUlDLFdBQVU7O3NGQUNiLDhEQUFDRDs0RUFBSUMsV0FBVTs7OEZBQ2IsOERBQUNEO29GQUFJQyxXQUFVOztzR0FDYiw4REFBQzFJLHVEQUFLQTs0RkFBQzBKLFNBQVE7NEZBQWNoQixXQUFVO3NHQUFxQzs7Ozs7O3NHQUc1RSw4REFBQ0Q7NEZBQUlDLFdBQVU7OzhHQUNiLDhEQUFDNUgsNE1BQUlBO29HQUFDNEgsV0FBVTs7Ozs7OzhHQUNoQiw4REFBQzNJLHVEQUFLQTtvR0FDSjRKLElBQUc7b0dBQ0haLE9BQU9wRyxnQkFBZ0JLLFdBQVc7b0dBQ2xDNEcsVUFBVSxDQUFDMUMsSUFBTXRFLG1CQUFtQnNELENBQUFBLE9BQVM7Z0hBQUUsR0FBR0EsSUFBSTtnSEFBRWxELGFBQWFrRSxFQUFFMkMsTUFBTSxDQUFDZCxLQUFLOzRHQUFDO29HQUNwRmUsYUFBWTtvR0FDWnBCLFdBQVU7b0dBQ1Y1QixRQUFROzs7Ozs7Ozs7Ozs7Ozs7Ozs7OEZBSWQsOERBQUMyQjtvRkFBSUMsV0FBVTs7c0dBQ2IsOERBQUMxSSx1REFBS0E7NEZBQUMwSixTQUFROzRGQUFjaEIsV0FBVTtzR0FBcUM7Ozs7OztzR0FHNUUsOERBQUMzSSx1REFBS0E7NEZBQ0o0SixJQUFHOzRGQUNIWixPQUFPcEcsZ0JBQWdCTSxXQUFXOzRGQUNsQzJHLFVBQVUsQ0FBQzFDLElBQU10RSxtQkFBbUJzRCxDQUFBQSxPQUFTO3dHQUFFLEdBQUdBLElBQUk7d0dBQUVqRCxhQUFhaUUsRUFBRTJDLE1BQU0sQ0FBQ2QsS0FBSztvR0FBQzs0RkFDcEZlLGFBQVk7NEZBQ1pwQixXQUFVOzRGQUNWNUIsUUFBUTs7Ozs7Ozs7Ozs7Ozs7Ozs7O3NGQUlkLDhEQUFDMkI7NEVBQUlDLFdBQVU7OzhGQUNiLDhEQUFDMUksdURBQUtBO29GQUFDMEosU0FBUTtvRkFBZWhCLFdBQVU7OEZBQXFDOzs7Ozs7OEZBRzdFLDhEQUFDRDtvRkFBSUMsV0FBVTs7c0dBQ2IsOERBQUMzSCw0TUFBS0E7NEZBQUMySCxXQUFVOzs7Ozs7c0dBQ2pCLDhEQUFDM0ksdURBQUtBOzRGQUNKNEosSUFBRzs0RkFDSEksTUFBSzs0RkFDTGhCLE9BQU9wRyxnQkFBZ0JPLFlBQVk7NEZBQ25DMEcsVUFBVSxDQUFDMUMsSUFBTXRFLG1CQUFtQnNELENBQUFBLE9BQVM7d0dBQUUsR0FBR0EsSUFBSTt3R0FBRWhELGNBQWNnRSxFQUFFMkMsTUFBTSxDQUFDZCxLQUFLO29HQUFDOzRGQUNyRmUsYUFBWTs0RkFDWnBCLFdBQVU7NEZBQ1Y1QixRQUFROzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OERBU3BCLDhEQUFDMkI7O3NFQUNDLDhEQUFDZ0I7NERBQUdmLFdBQVU7OzhFQUNaLDhEQUFDekgsNE1BQU1BO29FQUFDeUgsV0FBVTs7Ozs7O2dFQUFnQzs7Ozs7OztzRUFHcEQsOERBQUNEOzREQUFJQyxXQUFVOzs4RUFDYiw4REFBQzFJLHVEQUFLQTtvRUFBQzBKLFNBQVE7b0VBQWVoQixXQUFVOzhFQUFxQzs7Ozs7OzhFQUc3RSw4REFBQ0Q7b0VBQUlDLFdBQVU7O3NGQUNiLDhEQUFDekgsNE1BQU1BOzRFQUFDeUgsV0FBVTs7Ozs7O3NGQUNsQiw4REFBQ3pJLDZEQUFRQTs0RUFDUDBKLElBQUc7NEVBQ0haLE9BQU9wRyxnQkFBZ0JRLFlBQVk7NEVBQ25DeUcsVUFBVSxDQUFDMUMsSUFBTXRFLG1CQUFtQnNELENBQUFBLE9BQVM7d0ZBQUUsR0FBR0EsSUFBSTt3RkFBRS9DLGNBQWMrRCxFQUFFMkMsTUFBTSxDQUFDZCxLQUFLO29GQUFDOzRFQUNyRmUsYUFBWTs0RUFDWnBCLFdBQVU7NEVBQ1ZzQixNQUFNOzRFQUNObEQsUUFBUTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzhEQU9oQiw4REFBQzJCOztzRUFDQyw4REFBQ2dCOzREQUFHZixXQUFVOzs4RUFDWiw4REFBQ3hILDRNQUFRQTtvRUFBQ3dILFdBQVU7Ozs7OztnRUFBZ0M7Ozs7Ozs7c0VBR3RELDhEQUFDRDs0REFBSUMsV0FBVTs7OEVBQ2IsOERBQUNEO29FQUFJQyxXQUFVOztzRkFDYiw4REFBQzFJLHVEQUFLQTs0RUFBQzBKLFNBQVE7NEVBQWNoQixXQUFVO3NGQUFxQzs7Ozs7O3NGQUc1RSw4REFBQ0Q7NEVBQUlDLFdBQVU7OzhGQUNiLDhEQUFDeEgsNE1BQVFBO29GQUFDd0gsV0FBVTs7Ozs7OzhGQUNwQiw4REFBQzNJLHVEQUFLQTtvRkFDSjRKLElBQUc7b0ZBQ0haLE9BQU9wRyxnQkFBZ0JTLFdBQVc7b0ZBQ2xDd0csVUFBVSxDQUFDMUMsSUFBTXRFLG1CQUFtQnNELENBQUFBLE9BQVM7Z0dBQUUsR0FBR0EsSUFBSTtnR0FBRTlDLGFBQWE4RCxFQUFFMkMsTUFBTSxDQUFDZCxLQUFLOzRGQUFDO29GQUNwRmUsYUFBWTtvRkFDWnBCLFdBQVU7b0ZBQ1Y1QixRQUFROzs7Ozs7Ozs7Ozs7Ozs7Ozs7OEVBS2QsOERBQUMyQjtvRUFBSUMsV0FBVTs7c0ZBQ2IsOERBQUNEOzRFQUFJQyxXQUFVOzs4RkFDYiw4REFBQzFJLHVEQUFLQTtvRkFBQzBKLFNBQVE7b0ZBQWtCaEIsV0FBVTs4RkFBcUM7Ozs7Ozs4RkFHaEYsOERBQUMzSSx1REFBS0E7b0ZBQ0o0SixJQUFHO29GQUNIWixPQUFPcEcsZ0JBQWdCVSxlQUFlO29GQUN0Q3VHLFVBQVUsQ0FBQzFDLElBQU10RSxtQkFBbUJzRCxDQUFBQSxPQUFTO2dHQUFFLEdBQUdBLElBQUk7Z0dBQUU3QyxpQkFBaUI2RCxFQUFFMkMsTUFBTSxDQUFDZCxLQUFLOzRGQUFDO29GQUN4RmUsYUFBWTtvRkFDWnBCLFdBQVU7b0ZBQ1Y1QixRQUFROzs7Ozs7Ozs7Ozs7c0ZBR1osOERBQUMyQjs0RUFBSUMsV0FBVTs7OEZBQ2IsOERBQUMxSSx1REFBS0E7b0ZBQUMwSixTQUFRO29GQUF3QmhCLFdBQVU7OEZBQXFDOzs7Ozs7OEZBR3RGLDhEQUFDM0ksdURBQUtBO29GQUNKNEosSUFBRztvRkFDSFosT0FBT3BHLGdCQUFnQlcscUJBQXFCO29GQUM1Q3NHLFVBQVUsQ0FBQzFDLElBQU10RSxtQkFBbUJzRCxDQUFBQSxPQUFTO2dHQUFFLEdBQUdBLElBQUk7Z0dBQUU1Qyx1QkFBdUI0RCxFQUFFMkMsTUFBTSxDQUFDZCxLQUFLOzRGQUFDO29GQUM5RmUsYUFBWTtvRkFDWnBCLFdBQVU7b0ZBQ1Y1QixRQUFROzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OERBUWxCLDhEQUFDMkI7O3NFQUNDLDhEQUFDZ0I7NERBQUdmLFdBQVU7OzhFQUNaLDhEQUFDdkgsNE1BQVVBO29FQUFDdUgsV0FBVTs7Ozs7O2dFQUFnQzs7Ozs7OztzRUFHeEQsOERBQUNEOzREQUFJQyxXQUFVOzs4RUFDYiw4REFBQ0Q7b0VBQUlDLFdBQVU7O3NGQUNiLDhEQUFDRDs0RUFBSUMsV0FBVTs7OEZBQ2IsOERBQUMxSSx1REFBS0E7b0ZBQUMwSixTQUFRO29GQUFxQmhCLFdBQVU7OEZBQXFDOzs7Ozs7OEZBR25GLDhEQUFDRDtvRkFBSUMsV0FBVTs7c0dBQ2IsOERBQUN2SCw0TUFBVUE7NEZBQUN1SCxXQUFVOzs7Ozs7c0dBQ3RCLDhEQUFDM0ksdURBQUtBOzRGQUNKNEosSUFBRzs0RkFDSEksTUFBSzs0RkFDTGhCLE9BQU9wRyxnQkFBZ0JhLGtCQUFrQjs0RkFDekNvRyxVQUFVLENBQUMxQyxJQUFNdEUsbUJBQW1Cc0QsQ0FBQUEsT0FBUzt3R0FBRSxHQUFHQSxJQUFJO3dHQUFFMUMsb0JBQW9CMEQsRUFBRTJDLE1BQU0sQ0FBQ2QsS0FBSztvR0FBQzs0RkFDM0ZlLGFBQVk7NEZBQ1pwQixXQUFVOzRGQUNWNUIsUUFBUTs7Ozs7Ozs7Ozs7Ozs7Ozs7O3NGQUlkLDhEQUFDMkI7NEVBQUlDLFdBQVU7OzhGQUNiLDhEQUFDMUksdURBQUtBO29GQUFDMEosU0FBUTtvRkFBa0JoQixXQUFVOzhGQUFxQzs7Ozs7OzhGQUdoRiw4REFBQzNJLHVEQUFLQTtvRkFDSjRKLElBQUc7b0ZBQ0hJLE1BQUs7b0ZBQ0xFLFFBQU87b0ZBQ1BMLFVBQVUsQ0FBQzFDOzRGQUFxREE7K0ZBQS9DcEIsaUJBQWlCLFdBQVcsbUJBQW1Cb0IsRUFBQUEsa0JBQUFBLEVBQUUyQyxNQUFNLENBQUN6RCxLQUFLLGNBQWRjLHNDQUFBQSxlQUFnQixDQUFDLEVBQUUsS0FBSTs7b0ZBQ3ZGd0IsV0FBVTtvRkFDVjVCLFFBQVE7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs4RUFJZCw4REFBQzJCO29FQUFJQyxXQUFVOztzRkFDYiw4REFBQzFJLHVEQUFLQTs0RUFBQzBKLFNBQVE7NEVBQXlCaEIsV0FBVTtzRkFBcUM7Ozs7OztzRkFHdkYsOERBQUN6SSw2REFBUUE7NEVBQ1AwSixJQUFHOzRFQUNIWixPQUFPcEcsZ0JBQWdCWSxzQkFBc0I7NEVBQzdDcUcsVUFBVSxDQUFDMUMsSUFBTXRFLG1CQUFtQnNELENBQUFBLE9BQVM7d0ZBQUUsR0FBR0EsSUFBSTt3RkFBRTNDLHdCQUF3QjJELEVBQUUyQyxNQUFNLENBQUNkLEtBQUs7b0ZBQUM7NEVBQy9GZSxhQUFZOzRFQUNacEIsV0FBVTs0RUFDVnNCLE1BQU07NEVBQ05sRCxRQUFROzs7Ozs7Ozs7Ozs7OEVBR1osOERBQUMyQjtvRUFBSUMsV0FBVTs7c0ZBQ2IsOERBQUMxSSx1REFBS0E7NEVBQUMwSixTQUFROzRFQUF5QmhCLFdBQVU7c0ZBQXFDOzs7Ozs7c0ZBR3ZGLDhEQUFDekksNkRBQVFBOzRFQUNQMEosSUFBRzs0RUFDSFosT0FBT3BHLGdCQUFnQmMsc0JBQXNCOzRFQUM3Q21HLFVBQVUsQ0FBQzFDLElBQU10RSxtQkFBbUJzRCxDQUFBQSxPQUFTO3dGQUFFLEdBQUdBLElBQUk7d0ZBQUV6Qyx3QkFBd0J5RCxFQUFFMkMsTUFBTSxDQUFDZCxLQUFLO29GQUFDOzRFQUMvRmUsYUFBWTs0RUFDWnBCLFdBQVU7NEVBQ1ZzQixNQUFNOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OERBTWQsOERBQUN2QjtvREFBSUMsV0FBVTs4REFDYiw0RUFBQzVJLHlEQUFNQTt3REFDTGlLLE1BQUs7d0RBQ0xHLFVBQVVsSSxjQUFjLENBQUM2RTt3REFDekJzRCxNQUFLO3dEQUNMekIsV0FBVTtrRUFFVDFHLDJCQUNDOzs4RUFDRSw4REFBQ1gsNE1BQU9BO29FQUFDcUgsV0FBVTs7Ozs7OzhFQUNuQiw4REFBQzBCO29FQUFLMUIsV0FBVTs4RUFBdUI7Ozs7Ozs7eUZBR3pDOzs4RUFDRSw4REFBQ3BILDRNQUFXQTtvRUFBQ29ILFdBQVU7Ozs7Ozs4RUFDdkIsOERBQUMwQjtvRUFBSzFCLFdBQVU7OEVBQXVCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztrREFTbkQsOERBQUNsSSw0REFBV0E7d0NBQUN1SSxPQUFNO3dDQUFZTCxXQUFVO2tEQUN2Qyw0RUFBQ2E7NENBQUtDLFVBQVV2Qzs0Q0FBY3lCLFdBQVU7OzhEQUN0Qyw4REFBQ0Q7b0RBQUlDLFdBQVU7OERBQ2IsNEVBQUNEO3dEQUFJQyxXQUFVOzswRUFDYiw4REFBQ25ILDRNQUFhQTtnRUFBQ21ILFdBQVU7Ozs7OzswRUFDekIsOERBQUNHO2dFQUFFSCxXQUFVOzBFQUFrRDs7Ozs7Ozs7Ozs7Ozs7Ozs7OERBT25FLDhEQUFDRDtvREFBSUMsV0FBVTs7c0VBQ2IsOERBQUNEOzs4RUFDQyw4REFBQ2dCO29FQUFHZixXQUFVOztzRkFDWiw4REFBQzVILDRNQUFJQTs0RUFBQzRILFdBQVU7Ozs7Ozt3RUFBZ0M7Ozs7Ozs7OEVBR2xELDhEQUFDRDtvRUFBSUMsV0FBVTs7c0ZBQ2IsOERBQUNEOzRFQUFJQyxXQUFVOzs4RkFDYiw4REFBQzFJLHVEQUFLQTtvRkFBQzBKLFNBQVE7b0ZBQXdCaEIsV0FBVTs4RkFBcUM7Ozs7Ozs4RkFHdEYsOERBQUNEO29GQUFJQyxXQUFVOztzR0FDYiw4REFBQzVILDRNQUFJQTs0RkFBQzRILFdBQVU7Ozs7OztzR0FDaEIsOERBQUMzSSx1REFBS0E7NEZBQ0o0SixJQUFHOzRGQUNIWixPQUFPckYsa0JBQWtCYixpQkFBaUI7NEZBQzFDK0csVUFBVSxDQUFDMUMsSUFBTXZELHFCQUFxQnVDLENBQUFBLE9BQVM7d0dBQUUsR0FBR0EsSUFBSTt3R0FBRXJELG1CQUFtQnFFLEVBQUUyQyxNQUFNLENBQUNkLEtBQUs7b0dBQUM7NEZBQzVGZSxhQUFZOzRGQUNacEIsV0FBVTs0RkFDVjVCLFFBQVE7Ozs7Ozs7Ozs7Ozs7Ozs7OztzRkFLZCw4REFBQzJCOzRFQUFJQyxXQUFVOzs4RkFDYiw4REFBQ0Q7b0ZBQUlDLFdBQVU7O3NHQUNiLDhEQUFDMUksdURBQUtBOzRGQUFDMEosU0FBUTs0RkFBVWhCLFdBQVU7c0dBQXFDOzs7Ozs7c0dBR3hFLDhEQUFDM0ksdURBQUtBOzRGQUNKNEosSUFBRzs0RkFDSEksTUFBSzs0RkFDTGhCLE9BQU9yRixrQkFBa0JaLEdBQUc7NEZBQzVCOEcsVUFBVSxDQUFDMUMsSUFBTXZELHFCQUFxQnVDLENBQUFBLE9BQVM7d0dBQUUsR0FBR0EsSUFBSTt3R0FBRXBELEtBQUtvRSxFQUFFMkMsTUFBTSxDQUFDZCxLQUFLO29HQUFDOzRGQUM5RWUsYUFBWTs0RkFDWnBCLFdBQVU7NEZBQ1Y1QixRQUFROzs7Ozs7Ozs7Ozs7OEZBR1osOERBQUMyQjtvRkFBSUMsV0FBVTs7c0dBQ2IsOERBQUMxSSx1REFBS0E7NEZBQUMwSixTQUFROzRGQUFZaEIsV0FBVTtzR0FBcUM7Ozs7OztzR0FHMUUsOERBQUN4SSx5REFBTUE7NEZBQUM2SSxPQUFPckYsa0JBQWtCRSxLQUFLOzRGQUFFb0YsZUFBZSxDQUFDRCxRQUFVcEYscUJBQXFCdUMsQ0FBQUEsT0FBUzt3R0FBRSxHQUFHQSxJQUFJO3dHQUFFdEMsT0FBT21GO29HQUFNOzs4R0FDdEgsOERBQUMxSSxnRUFBYUE7b0dBQUNxSSxXQUFVOzhHQUN2Qiw0RUFBQ3BJLDhEQUFXQTt3R0FBQ3dKLGFBQVk7Ozs7Ozs7Ozs7OzhHQUUzQiw4REFBQzNKLGdFQUFhQTs7c0hBQ1osOERBQUNDLDZEQUFVQTs0R0FBQzJJLE9BQU07c0hBQVE7Ozs7OztzSEFDMUIsOERBQUMzSSw2REFBVUE7NEdBQUMySSxPQUFNO3NIQUFROzs7Ozs7c0hBQzFCLDhEQUFDM0ksNkRBQVVBOzRHQUFDMkksT0FBTTtzSEFBUTs7Ozs7O3NIQUMxQiw4REFBQzNJLDZEQUFVQTs0R0FBQzJJLE9BQU07c0hBQU87Ozs7OztzSEFDekIsOERBQUMzSSw2REFBVUE7NEdBQUMySSxPQUFNO3NIQUFPOzs7Ozs7c0hBQ3pCLDhEQUFDM0ksNkRBQVVBOzRHQUFDMkksT0FBTTtzSEFBTzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzhGQUkvQiw4REFBQ047b0ZBQUlDLFdBQVU7O3NHQUNiLDhEQUFDMUksdURBQUtBOzRGQUFDMEosU0FBUTs0RkFBbUJoQixXQUFVO3NHQUFxQzs7Ozs7O3NHQUdqRiw4REFBQ0Q7NEZBQUlDLFdBQVU7OzhHQUNiLDhEQUFDM0gsNE1BQUtBO29HQUFDMkgsV0FBVTs7Ozs7OzhHQUNqQiw4REFBQzNJLHVEQUFLQTtvR0FDSjRKLElBQUc7b0dBQ0hJLE1BQUs7b0dBQ0xoQixPQUFPckYsa0JBQWtCUixZQUFZO29HQUNyQzBHLFVBQVUsQ0FBQzFDLElBQU12RCxxQkFBcUJ1QyxDQUFBQSxPQUFTO2dIQUFFLEdBQUdBLElBQUk7Z0hBQUVoRCxjQUFjZ0UsRUFBRTJDLE1BQU0sQ0FBQ2QsS0FBSzs0R0FBQztvR0FDdkZlLGFBQVk7b0dBQ1pwQixXQUFVO29HQUNWNUIsUUFBUTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O3NFQVNwQiw4REFBQzJCOzs4RUFDQyw4REFBQ2dCO29FQUFHZixXQUFVOztzRkFDWiw4REFBQ3pILDRNQUFNQTs0RUFBQ3lILFdBQVU7Ozs7Ozt3RUFBZ0M7Ozs7Ozs7OEVBR3BELDhEQUFDRDtvRUFBSUMsV0FBVTs7c0ZBQ2IsOERBQUNEOzRFQUFJQyxXQUFVOzs4RkFDYiw4REFBQzFJLHVEQUFLQTtvRkFBQzBKLFNBQVE7b0ZBQWNoQixXQUFVOzhGQUFxQzs7Ozs7OzhGQUc1RSw4REFBQ0Q7b0ZBQUlDLFdBQVU7O3NHQUNiLDhEQUFDekgsNE1BQU1BOzRGQUFDeUgsV0FBVTs7Ozs7O3NHQUNsQiw4REFBQ3pJLDZEQUFRQTs0RkFDUDBKLElBQUc7NEZBQ0haLE9BQU9yRixrQkFBa0JHLE9BQU87NEZBQ2hDK0YsVUFBVSxDQUFDMUMsSUFBTXZELHFCQUFxQnVDLENBQUFBLE9BQVM7d0dBQUUsR0FBR0EsSUFBSTt3R0FBRXJDLFNBQVNxRCxFQUFFMkMsTUFBTSxDQUFDZCxLQUFLO29HQUFDOzRGQUNsRmUsYUFBWTs0RkFDWnBCLFdBQVU7NEZBQ1ZzQixNQUFNOzRGQUNObEQsUUFBUTs7Ozs7Ozs7Ozs7Ozs7Ozs7O3NGQUtkLDhEQUFDMkI7NEVBQUlDLFdBQVU7OzhGQUNiLDhEQUFDRDtvRkFBSUMsV0FBVTs7c0dBQ2IsOERBQUMxSSx1REFBS0E7NEZBQUMwSixTQUFROzRGQUFrQmhCLFdBQVU7c0dBQXFDOzs7Ozs7c0dBR2hGLDhEQUFDRDs0RkFBSUMsV0FBVTs7OEdBQ2IsOERBQUN4SCw0TUFBUUE7b0dBQUN3SCxXQUFVOzs7Ozs7OEdBQ3BCLDhEQUFDM0ksdURBQUtBO29HQUNKNEosSUFBRztvR0FDSFosT0FBT3JGLGtCQUFrQk4sV0FBVztvR0FDcEN3RyxVQUFVLENBQUMxQyxJQUFNdkQscUJBQXFCdUMsQ0FBQUEsT0FBUztnSEFBRSxHQUFHQSxJQUFJO2dIQUFFOUMsYUFBYThELEVBQUUyQyxNQUFNLENBQUNkLEtBQUs7NEdBQUM7b0dBQ3RGZSxhQUFZO29HQUNacEIsV0FBVTtvR0FDVjVCLFFBQVE7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs4RkFLZCw4REFBQzJCO29GQUFJQyxXQUFVOztzR0FDYiw4REFBQzFJLHVEQUFLQTs0RkFBQzBKLFNBQVE7NEZBQXFCaEIsV0FBVTtzR0FBcUM7Ozs7OztzR0FHbkYsOERBQUMzSSx1REFBS0E7NEZBQ0o0SixJQUFHOzRGQUNIWixPQUFPckYsa0JBQWtCSSxjQUFjOzRGQUN2QzhGLFVBQVUsQ0FBQzFDLElBQU12RCxxQkFBcUJ1QyxDQUFBQSxPQUFTO3dHQUFFLEdBQUdBLElBQUk7d0dBQUVwQyxnQkFBZ0JvRCxFQUFFMkMsTUFBTSxDQUFDZCxLQUFLO29HQUFDOzRGQUN6RmUsYUFBWTs0RkFDWnBCLFdBQVU7NEZBQ1Y1QixRQUFROzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OERBU3BCLDhEQUFDMkI7O3NFQUNDLDhEQUFDZ0I7NERBQUdmLFdBQVU7OzhFQUNaLDhEQUFDdkgsNE1BQVVBO29FQUFDdUgsV0FBVTs7Ozs7O2dFQUFnQzs7Ozs7OztzRUFHeEQsOERBQUNEOzREQUFJQyxXQUFVO3NFQUNiLDRFQUFDRDtnRUFBSUMsV0FBVTs7a0ZBQ2IsOERBQUNEO3dFQUFJQyxXQUFVOzswRkFDYiw4REFBQzFJLHVEQUFLQTtnRkFBQzBKLFNBQVE7Z0ZBQStCaEIsV0FBVTswRkFBcUM7Ozs7OzswRkFHN0YsOERBQUNEO2dGQUFJQyxXQUFVOztrR0FDYiw4REFBQ3ZILDRNQUFVQTt3RkFBQ3VILFdBQVU7Ozs7OztrR0FDdEIsOERBQUMzSSx1REFBS0E7d0ZBQ0o0SixJQUFHO3dGQUNIWixPQUFPckYsa0JBQWtCSyx3QkFBd0I7d0ZBQ2pENkYsVUFBVSxDQUFDMUMsSUFBTXZELHFCQUFxQnVDLENBQUFBLE9BQVM7b0dBQUUsR0FBR0EsSUFBSTtvR0FBRW5DLDBCQUEwQm1ELEVBQUUyQyxNQUFNLENBQUNkLEtBQUs7Z0dBQUM7d0ZBQ25HZSxhQUFZO3dGQUNacEIsV0FBVTt3RkFDVjVCLFFBQVE7Ozs7Ozs7Ozs7Ozs7Ozs7OztrRkFLZCw4REFBQzJCO3dFQUFJQyxXQUFVOzswRkFDYiw4REFBQzFJLHVEQUFLQTtnRkFBQzBKLFNBQVE7Z0ZBQXdCaEIsV0FBVTswRkFBcUM7Ozs7OzswRkFHdEYsOERBQUMzSSx1REFBS0E7Z0ZBQ0o0SixJQUFHO2dGQUNISSxNQUFLO2dGQUNMaEIsT0FBT3JGLGtCQUFrQk0saUJBQWlCO2dGQUMxQzRGLFVBQVUsQ0FBQzFDLElBQU12RCxxQkFBcUJ1QyxDQUFBQSxPQUFTOzRGQUFFLEdBQUdBLElBQUk7NEZBQUVsQyxtQkFBbUJrRCxFQUFFMkMsTUFBTSxDQUFDZCxLQUFLO3dGQUFDO2dGQUM1RmUsYUFBWTtnRkFDWnBCLFdBQVU7Z0ZBQ1Y1QixRQUFROzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs4REFRbEIsOERBQUMyQjs7c0VBQ0MsOERBQUNnQjs0REFBR2YsV0FBVTs7OEVBQ1osOERBQUN0SCw0TUFBTUE7b0VBQUNzSCxXQUFVOzs7Ozs7Z0VBQWdDOzs7Ozs7O3NFQUdwRCw4REFBQ0Q7NERBQUlDLFdBQVU7OzhFQUNiLDhEQUFDRDtvRUFBSUMsV0FBVTs7c0ZBQ2IsOERBQUMxSSx1REFBS0E7NEVBQUMwSixTQUFROzRFQUFzQmhCLFdBQVU7c0ZBQXFDOzs7Ozs7c0ZBR3BGLDhEQUFDM0ksdURBQUtBOzRFQUNKNEosSUFBRzs0RUFDSEksTUFBSzs0RUFDTEUsUUFBTzs0RUFDUEwsVUFBVSxDQUFDMUM7b0ZBQXVEQTt1RkFBakRwQixpQkFBaUIsYUFBYSxtQkFBbUJvQixFQUFBQSxrQkFBQUEsRUFBRTJDLE1BQU0sQ0FBQ3pELEtBQUssY0FBZGMsc0NBQUFBLGVBQWdCLENBQUMsRUFBRSxLQUFJOzs0RUFDekZ3QixXQUFVOzRFQUNWNUIsUUFBUTs7Ozs7Ozs7Ozs7OzhFQUlaLDhEQUFDMkI7b0VBQUlDLFdBQVU7O3NGQUNiLDhEQUFDMUksdURBQUtBOzRFQUFDMEosU0FBUTs0RUFBNkJoQixXQUFVO3NGQUFxQzs7Ozs7O3NGQUczRiw4REFBQ3pJLDZEQUFRQTs0RUFDUDBKLElBQUc7NEVBQ0haLE9BQU9yRixrQkFBa0JILHNCQUFzQjs0RUFDL0NxRyxVQUFVLENBQUMxQyxJQUFNdkQscUJBQXFCdUMsQ0FBQUEsT0FBUzt3RkFBRSxHQUFHQSxJQUFJO3dGQUFFM0Msd0JBQXdCMkQsRUFBRTJDLE1BQU0sQ0FBQ2QsS0FBSztvRkFBQzs0RUFDakdlLGFBQVk7NEVBQ1pwQixXQUFVOzRFQUNWc0IsTUFBTTs0RUFDTmxELFFBQVE7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs4REFNaEIsOERBQUMyQjtvREFBSUMsV0FBVTs4REFDYiw0RUFBQzVJLHlEQUFNQTt3REFDTGlLLE1BQUs7d0RBQ0xHLFVBQVVsSSxjQUFjLENBQUM2RTt3REFDekJzRCxNQUFLO3dEQUNMekIsV0FBVTtrRUFFVDFHLDJCQUNDOzs4RUFDRSw4REFBQ1gsNE1BQU9BO29FQUFDcUgsV0FBVTs7Ozs7OzhFQUNuQiw4REFBQzBCO29FQUFLMUIsV0FBVTs4RUFBdUI7Ozs7Ozs7eUZBR3pDOzs4RUFDRSw4REFBQ3BILDRNQUFXQTtvRUFBQ29ILFdBQVU7Ozs7Ozs4RUFDdkIsOERBQUMwQjtvRUFBSzFCLFdBQVU7OEVBQXVCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztrREFTbkQsOERBQUNsSSw0REFBV0E7d0NBQUN1SSxPQUFNO3dDQUFhTCxXQUFVO2tEQUN4Qyw0RUFBQ2E7NENBQUtDLFVBQVV2Qzs0Q0FBY3lCLFdBQVU7OzhEQUN0Qyw4REFBQ0Q7b0RBQUlDLFdBQVU7OERBQ2IsNEVBQUNEO3dEQUFJQyxXQUFVOzswRUFDYiw4REFBQ25ILDRNQUFhQTtnRUFBQ21ILFdBQVU7Ozs7OzswRUFDekIsOERBQUNHO2dFQUFFSCxXQUFVOzBFQUFrRDs7Ozs7Ozs7Ozs7Ozs7Ozs7OERBT25FLDhEQUFDRDs7c0VBQ0MsOERBQUNnQjs0REFBR2YsV0FBVTs7OEVBQ1osOERBQUM1SCw0TUFBSUE7b0VBQUM0SCxXQUFVOzs7Ozs7Z0VBQWdDOzs7Ozs7O3NFQUdsRCw4REFBQ0Q7NERBQUlDLFdBQVU7OzhFQUNiLDhEQUFDRDtvRUFBSUMsV0FBVTs7c0ZBQ2IsOERBQUMxSSx1REFBS0E7NEVBQUMwSixTQUFROzRFQUFnQmhCLFdBQVU7c0ZBQXFDOzs7Ozs7c0ZBRzlFLDhEQUFDRDs0RUFBSUMsV0FBVTs7OEZBQ2IsOERBQUM1SCw0TUFBSUE7b0ZBQUM0SCxXQUFVOzs7Ozs7OEZBQ2hCLDhEQUFDM0ksdURBQUtBO29GQUNKNEosSUFBRztvRkFDSFosT0FBTzlFLG1CQUFtQkUsU0FBUztvRkFDbkN5RixVQUFVLENBQUMxQyxJQUFNaEQsc0JBQXNCZ0MsQ0FBQUEsT0FBUztnR0FBRSxHQUFHQSxJQUFJO2dHQUFFL0IsV0FBVytDLEVBQUUyQyxNQUFNLENBQUNkLEtBQUs7NEZBQUM7b0ZBQ3JGZSxhQUFZO29GQUNacEIsV0FBVTtvRkFDVjVCLFFBQVE7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs4RUFLZCw4REFBQzJCO29FQUFJQyxXQUFVOztzRkFDYiw4REFBQ0Q7NEVBQUlDLFdBQVU7OzhGQUNiLDhEQUFDMUksdURBQUtBO29GQUFDMEosU0FBUTtvRkFBVWhCLFdBQVU7OEZBQXFDOzs7Ozs7OEZBR3hFLDhEQUFDM0ksdURBQUtBO29GQUNKNEosSUFBRztvRkFDSEksTUFBSztvRkFDTGhCLE9BQU85RSxtQkFBbUJuQixHQUFHO29GQUM3QjhHLFVBQVUsQ0FBQzFDLElBQU1oRCxzQkFBc0JnQyxDQUFBQSxPQUFTO2dHQUFFLEdBQUdBLElBQUk7Z0dBQUVwRCxLQUFLb0UsRUFBRTJDLE1BQU0sQ0FBQ2QsS0FBSzs0RkFBQztvRkFDL0VlLGFBQVk7b0ZBQ1pwQixXQUFVO29GQUNWNUIsUUFBUTs7Ozs7Ozs7Ozs7O3NGQUlaLDhEQUFDMkI7NEVBQUlDLFdBQVU7OzhGQUNiLDhEQUFDMUksdURBQUtBO29GQUFDMEosU0FBUTtvRkFBbUJoQixXQUFVOzhGQUFxQzs7Ozs7OzhGQUdqRiw4REFBQ0Q7b0ZBQUlDLFdBQVU7O3NHQUNiLDhEQUFDM0gsNE1BQUtBOzRGQUFDMkgsV0FBVTs7Ozs7O3NHQUNqQiw4REFBQzNJLHVEQUFLQTs0RkFDSjRKLElBQUc7NEZBQ0hJLE1BQUs7NEZBQ0xoQixPQUFPOUUsbUJBQW1CSyxZQUFZOzRGQUN0Q3NGLFVBQVUsQ0FBQzFDLElBQU1oRCxzQkFBc0JnQyxDQUFBQSxPQUFTO3dHQUFFLEdBQUdBLElBQUk7d0dBQUU1QixjQUFjNEMsRUFBRTJDLE1BQU0sQ0FBQ2QsS0FBSztvR0FBQzs0RkFDeEZlLGFBQVk7NEZBQ1pwQixXQUFVOzRGQUNWNUIsUUFBUTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzhFQU1oQiw4REFBQzJCO29FQUFJQyxXQUFVOztzRkFDYiw4REFBQzFJLHVEQUFLQTs0RUFBQzBKLFNBQVE7NEVBQW9CaEIsV0FBVTtzRkFBcUM7Ozs7OztzRkFHbEYsOERBQUNEOzRFQUFJQyxXQUFVOzs4RkFDYiw4REFBQzFILDRNQUFJQTtvRkFBQzBILFdBQVU7Ozs7Ozs4RkFDaEIsOERBQUMzSSx1REFBS0E7b0ZBQ0o0SixJQUFHO29GQUNISSxNQUFLO29GQUNMaEIsT0FBTzlFLG1CQUFtQk0sYUFBYTtvRkFDdkNxRixVQUFVLENBQUMxQyxJQUFNaEQsc0JBQXNCZ0MsQ0FBQUEsT0FBUztnR0FBRSxHQUFHQSxJQUFJO2dHQUFFM0IsZUFBZTJDLEVBQUUyQyxNQUFNLENBQUNkLEtBQUs7NEZBQUM7b0ZBQ3pGZSxhQUFZO29GQUNacEIsV0FBVTtvRkFDVjVCLFFBQVE7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs4REFRbEIsOERBQUMyQjs7c0VBQ0MsOERBQUNnQjs0REFBR2YsV0FBVTs7OEVBQ1osOERBQUM5SCw0TUFBYUE7b0VBQUM4SCxXQUFVOzs7Ozs7Z0VBQWdDOzs7Ozs7O3NFQUczRCw4REFBQ0Q7NERBQUlDLFdBQVU7OzhFQUNiLDhEQUFDRDtvRUFBSUMsV0FBVTs7c0ZBQ2IsOERBQUMxSSx1REFBS0E7NEVBQUMwSixTQUFROzRFQUFzQmhCLFdBQVU7c0ZBQXFDOzs7Ozs7c0ZBR3BGLDhEQUFDM0ksdURBQUtBOzRFQUNKNEosSUFBRzs0RUFDSFosT0FBTzlFLG1CQUFtQkcsZUFBZTs0RUFDekN3RixVQUFVLENBQUMxQyxJQUFNaEQsc0JBQXNCZ0MsQ0FBQUEsT0FBUzt3RkFBRSxHQUFHQSxJQUFJO3dGQUFFOUIsaUJBQWlCOEMsRUFBRTJDLE1BQU0sQ0FBQ2QsS0FBSztvRkFBQzs0RUFDM0ZlLGFBQVk7NEVBQ1pwQixXQUFVOzRFQUNWNUIsUUFBUTs7Ozs7Ozs7Ozs7OzhFQUlaLDhEQUFDMkI7b0VBQUlDLFdBQVU7O3NGQUNiLDhEQUFDRDs0RUFBSUMsV0FBVTs7OEZBQ2IsOERBQUMxSSx1REFBS0E7b0ZBQUMwSixTQUFRO29GQUFvQmhCLFdBQVU7OEZBQXFDOzs7Ozs7OEZBR2xGLDhEQUFDeEkseURBQU1BO29GQUFDNkksT0FBTzlFLG1CQUFtQkksYUFBYTtvRkFBRTJFLGVBQWUsQ0FBQ0QsUUFBVTdFLHNCQUFzQmdDLENBQUFBLE9BQVM7Z0dBQUUsR0FBR0EsSUFBSTtnR0FBRTdCLGVBQWUwRTs0RkFBTTs7c0dBQ3hJLDhEQUFDMUksZ0VBQWFBOzRGQUFDcUksV0FBVTtzR0FDdkIsNEVBQUNwSSw4REFBV0E7Z0dBQUN3SixhQUFZOzs7Ozs7Ozs7OztzR0FFM0IsOERBQUMzSixnRUFBYUE7OzhHQUNaLDhEQUFDQyw2REFBVUE7b0dBQUMySSxPQUFNOzhHQUFPOzs7Ozs7OEdBQ3pCLDhEQUFDM0ksNkRBQVVBO29HQUFDMkksT0FBTTs4R0FBTzs7Ozs7OzhHQUN6Qiw4REFBQzNJLDZEQUFVQTtvR0FBQzJJLE9BQU07OEdBQU87Ozs7Ozs4R0FDekIsOERBQUMzSSw2REFBVUE7b0dBQUMySSxPQUFNOzhHQUFPOzs7Ozs7OEdBQ3pCLDhEQUFDM0ksNkRBQVVBO29HQUFDMkksT0FBTTs4R0FBTzs7Ozs7OzhHQUN6Qiw4REFBQzNJLDZEQUFVQTtvR0FBQzJJLE9BQU07OEdBQU87Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztzRkFLL0IsOERBQUNOOzRFQUFJQyxXQUFVOzs4RkFDYiw4REFBQzFJLHVEQUFLQTtvRkFBQzBKLFNBQVE7b0ZBQTJCaEIsV0FBVTs4RkFBcUM7Ozs7Ozs4RkFHekYsOERBQUMzSSx1REFBS0E7b0ZBQ0o0SixJQUFHO29GQUNIWixPQUFPOUUsbUJBQW1CTyxvQkFBb0I7b0ZBQzlDb0YsVUFBVSxDQUFDMUMsSUFBTWhELHNCQUFzQmdDLENBQUFBLE9BQVM7Z0dBQUUsR0FBR0EsSUFBSTtnR0FBRTFCLHNCQUFzQjBDLEVBQUUyQyxNQUFNLENBQUNkLEtBQUs7NEZBQUM7b0ZBQ2hHZSxhQUFZO29GQUNacEIsV0FBVTtvRkFDVjVCLFFBQVE7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs4REFRbEIsOERBQUMyQjs7c0VBQ0MsOERBQUNnQjs0REFBR2YsV0FBVTs7OEVBQ1osOERBQUN0SCw0TUFBTUE7b0VBQUNzSCxXQUFVOzs7Ozs7Z0VBQWdDOzs7Ozs7O3NFQUdwRCw4REFBQ0Q7NERBQUlDLFdBQVU7OzhFQUNiLDhEQUFDRDtvRUFBSUMsV0FBVTs7c0ZBQ2IsOERBQUNEOzRFQUFJQyxXQUFVOzs4RkFDYiw4REFBQzFJLHVEQUFLQTtvRkFBQzBKLFNBQVE7b0ZBQXNCaEIsV0FBVTs4RkFBcUM7Ozs7Ozs4RkFHcEYsOERBQUMzSSx1REFBS0E7b0ZBQ0o0SixJQUFHO29GQUNISSxNQUFLO29GQUNMRSxRQUFPO29GQUNQTCxVQUFVLENBQUMxQzs0RkFBd0RBOytGQUFsRHBCLGlCQUFpQixjQUFjLG1CQUFtQm9CLEVBQUFBLGtCQUFBQSxFQUFFMkMsTUFBTSxDQUFDekQsS0FBSyxjQUFkYyxzQ0FBQUEsZUFBZ0IsQ0FBQyxFQUFFLEtBQUk7O29GQUMxRndCLFdBQVU7b0ZBQ1Y1QixRQUFROzs7Ozs7Ozs7Ozs7c0ZBSVosOERBQUMyQjs0RUFBSUMsV0FBVTs7OEZBQ2IsOERBQUMxSSx1REFBS0E7b0ZBQUMwSixTQUFRO29GQUF1QmhCLFdBQVU7OEZBQXFDOzs7Ozs7OEZBR3JGLDhEQUFDM0ksdURBQUtBO29GQUNKNEosSUFBRztvRkFDSEksTUFBSztvRkFDTEUsUUFBTztvRkFDUEwsVUFBVSxDQUFDMUM7NEZBQXlEQTsrRkFBbkRwQixpQkFBaUIsY0FBYyxvQkFBb0JvQixFQUFBQSxrQkFBQUEsRUFBRTJDLE1BQU0sQ0FBQ3pELEtBQUssY0FBZGMsc0NBQUFBLGVBQWdCLENBQUMsRUFBRSxLQUFJOztvRkFDM0Z3QixXQUFVO29GQUNWNUIsUUFBUTs7Ozs7Ozs7Ozs7Ozs7Ozs7OzhFQUtkLDhEQUFDMkI7b0VBQUlDLFdBQVU7O3NGQUNiLDhEQUFDMUksdURBQUtBOzRFQUFDMEosU0FBUTs0RUFBMkJoQixXQUFVO3NGQUFxQzs7Ozs7O3NGQUd6Riw4REFBQzNJLHVEQUFLQTs0RUFDSjRKLElBQUc7NEVBQ0hJLE1BQUs7NEVBQ0xFLFFBQU87NEVBQ1BJLFFBQVE7NEVBQ1JULFVBQVUsQ0FBQzFDLElBQU1mLHlCQUF5QmUsRUFBRTJDLE1BQU0sQ0FBQ3pELEtBQUs7NEVBQ3hEc0MsV0FBVTs7Ozs7O3dFQUVYNUQsZ0JBQWdCSSxvQkFBb0IsQ0FBQ29GLE1BQU0sR0FBRyxtQkFDN0MsOERBQUM3Qjs0RUFBSUMsV0FBVTs7OEZBQ2IsOERBQUNHO29GQUFFSCxXQUFVOzhGQUF5Qjs7Ozs7O2dGQUNyQzVELGdCQUFnQkksb0JBQW9CLENBQUNpRSxHQUFHLENBQUMsQ0FBQ2xELE1BQU1RLHNCQUMvQyw4REFBQ2dDO3dGQUFnQkMsV0FBVTs7MEdBQ3pCLDhEQUFDMEI7Z0dBQUsxQixXQUFVOzBHQUEwQnpDLEtBQUtzRSxJQUFJOzs7Ozs7MEdBQ25ELDhEQUFDeksseURBQU1BO2dHQUNMaUssTUFBSztnR0FDTDNDLFNBQVE7Z0dBQ1IrQyxNQUFLO2dHQUNMSyxTQUFTLElBQU1oRSxXQUFXQztnR0FDMUJpQyxXQUFVOzBHQUNYOzs7Ozs7O3VGQVJPakM7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzhEQW9CdEIsOERBQUNnQzs7c0VBQ0MsOERBQUNnQjs0REFBR2YsV0FBVTs7OEVBQ1osOERBQUNsSCw0TUFBYUE7b0VBQUNrSCxXQUFVOzs7Ozs7Z0VBQWdDOzs7Ozs7O3NFQUczRCw4REFBQ0Q7NERBQUlDLFdBQVU7OzhFQUNiLDhEQUFDMUksdURBQUtBO29FQUFDMEosU0FBUTtvRUFBNkJoQixXQUFVOzhFQUFxQzs7Ozs7OzhFQUczRiw4REFBQ3pJLDZEQUFRQTtvRUFDUDBKLElBQUc7b0VBQ0haLE9BQU85RSxtQkFBbUJWLHNCQUFzQjtvRUFDaERxRyxVQUFVLENBQUMxQyxJQUFNaEQsc0JBQXNCZ0MsQ0FBQUEsT0FBUztnRkFBRSxHQUFHQSxJQUFJO2dGQUFFM0Msd0JBQXdCMkQsRUFBRTJDLE1BQU0sQ0FBQ2QsS0FBSzs0RUFBQztvRUFDbEdlLGFBQVk7b0VBQ1pwQixXQUFVO29FQUNWc0IsTUFBTTtvRUFDTmxELFFBQVE7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs4REFLZCw4REFBQzJCO29EQUFJQyxXQUFVOzhEQUNiLDRFQUFDNUkseURBQU1BO3dEQUNMaUssTUFBSzt3REFDTEcsVUFBVWxJLGNBQWMsQ0FBQzZFO3dEQUN6QnNELE1BQUs7d0RBQ0x6QixXQUFVO2tFQUVUMUcsMkJBQ0M7OzhFQUNFLDhEQUFDWCw0TUFBT0E7b0VBQUNxSCxXQUFVOzs7Ozs7OEVBQ25CLDhEQUFDMEI7b0VBQUsxQixXQUFVOzhFQUF1Qjs7Ozs7Ozt5RkFHekM7OzhFQUNFLDhEQUFDcEgsNE1BQVdBO29FQUFDb0gsV0FBVTs7Ozs7OzhFQUN2Qiw4REFBQzBCO29FQUFLMUIsV0FBVTs4RUFBdUI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQWFqRTtHQTVuQ3dCOUc7O1FBQ0QvQiw0REFBZUE7UUFLbEI0QixzREFBUUE7OztLQU5KRyIsInNvdXJjZXMiOlsiQzpcXGxhcmFnb25cXHd3d1xcbGFyYXZlbC1hcGktbmdvXFxmcm9udC1lbmRcXGNvbXBvbmVudHNcXHNjaG9sYXJzaGlwXFxTY2hvbGFyc2hpcEFwcGxpY2F0aW9uUGFnZS50c3giXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnXG5cbmltcG9ydCBSZWFjdCwgeyB1c2VTdGF0ZSwgdXNlRWZmZWN0IH0gZnJvbSAncmVhY3QnXG5pbXBvcnQgeyB1c2VTZWFyY2hQYXJhbXMgfSBmcm9tICduZXh0L25hdmlnYXRpb24nXG5cbmltcG9ydCB7IEJ1dHRvbiB9IGZyb20gXCJAL2NvbXBvbmVudHMvdWkvYnV0dG9uXCJcbmltcG9ydCB7IElucHV0IH0gZnJvbSBcIkAvY29tcG9uZW50cy91aS9pbnB1dFwiXG5pbXBvcnQgeyBMYWJlbCB9IGZyb20gXCJAL2NvbXBvbmVudHMvdWkvbGFiZWxcIlxuaW1wb3J0IHsgVGV4dGFyZWEgfSBmcm9tIFwiQC9jb21wb25lbnRzL3VpL3RleHRhcmVhXCJcbmltcG9ydCB7IFNlbGVjdCwgU2VsZWN0Q29udGVudCwgU2VsZWN0SXRlbSwgU2VsZWN0VHJpZ2dlciwgU2VsZWN0VmFsdWUgfSBmcm9tIFwiQC9jb21wb25lbnRzL3VpL3NlbGVjdFwiXG5pbXBvcnQgeyBUYWJzLCBUYWJzQ29udGVudCwgVGFic0xpc3QsIFRhYnNUcmlnZ2VyIH0gZnJvbSBcIkAvY29tcG9uZW50cy91aS90YWJzXCJcbmltcG9ydCB7XG4gIEJvb2tPcGVuLFxuICBHcmFkdWF0aW9uQ2FwLFxuICBTY2hvb2wsXG4gIFVzZXIsXG4gIFBob25lLFxuICBNYWlsLFxuICBNYXBQaW4sXG4gIEJ1aWxkaW5nLFxuICBDcmVkaXRDYXJkLFxuICBDYW1lcmEsXG4gIEZpbGVVcCxcbiAgTG9hZGVyMixcbiAgVXBsb2FkLFxuICBDaGVja0NpcmNsZSxcbiAgQWxlcnRUcmlhbmdsZSxcbiAgTWVzc2FnZVNxdWFyZVxufSBmcm9tICdsdWNpZGUtcmVhY3QnXG5pbXBvcnQgeyB1c2VUb2FzdCB9IGZyb20gXCJAL2hvb2tzL3VzZS10b2FzdFwiXG5pbXBvcnQgeyBhcGlDbGllbnQgfSBmcm9tICdAL2xpYi9hcGknXG5pbXBvcnQgU3VjY2Vzc01lc3NhZ2UgZnJvbSAnLi9TdWNjZXNzTWVzc2FnZSdcblxuLy8gRm9ybSBkYXRhIGludGVyZmFjZXMgZm9yIGRpZmZlcmVudCBjYXRlZ29yaWVzXG5pbnRlcmZhY2UgUHJpbWFyeVNjaG9vbEZvcm1EYXRhIHtcbiAgc3R1ZGVudF9mdWxsX25hbWU6IHN0cmluZ1xuICBhZ2U6IHN0cmluZ1xuICBjdXJyZW50X2NsYXNzOiBzdHJpbmdcbiAgZmF0aGVyX25hbWU6IHN0cmluZ1xuICBtb3RoZXJfbmFtZTogc3RyaW5nXG4gIHBhcmVudF9waG9uZTogc3RyaW5nXG4gIGhvbWVfYWRkcmVzczogc3RyaW5nXG4gIHNjaG9vbF9uYW1lOiBzdHJpbmdcbiAgaGVhZG1hc3Rlcl9uYW1lOiBzdHJpbmdcbiAgc2Nob29sX2FjY291bnRfbnVtYmVyOiBzdHJpbmdcbiAgcmVhc29uX2Zvcl9zY2hvbGFyc2hpcDogc3RyaW5nXG4gIGN1cnJlbnRfc2Nob29sX2ZlZTogc3RyaW5nXG4gIHN1cHBvcnRpbmdfaW5mb3JtYXRpb246IHN0cmluZ1xufVxuXG5pbnRlcmZhY2UgU2Vjb25kYXJ5U2Nob29sRm9ybURhdGEge1xuICBzdHVkZW50X2Z1bGxfbmFtZTogc3RyaW5nXG4gIGFnZTogc3RyaW5nXG4gIGNsYXNzOiBzdHJpbmdcbiAgcGFyZW50X3Bob25lOiBzdHJpbmdcbiAgYWRkcmVzczogc3RyaW5nXG4gIHNjaG9vbF9uYW1lOiBzdHJpbmdcbiAgcHJpbmNpcGFsX25hbWU6IHN0cmluZ1xuICBwcmluY2lwYWxfYWNjb3VudF9udW1iZXI6IHN0cmluZ1xuICByZWFzb25fZm9yX3NjaG9sYXJzaGlwOiBzdHJpbmdcbiAgc2Nob29sX2ZlZV9hbW91bnQ6IHN0cmluZ1xufVxuXG5pbnRlcmZhY2UgVW5pdmVyc2l0eUZvcm1EYXRhIHtcbiAgZnVsbF9uYW1lOiBzdHJpbmdcbiAgYWdlOiBzdHJpbmdcbiAgY291cnNlX29mX3N0dWR5OiBzdHJpbmdcbiAgY3VycmVudF9sZXZlbDogc3RyaW5nXG4gIHBob25lX251bWJlcjogc3RyaW5nXG4gIGVtYWlsX2FkZHJlc3M6IHN0cmluZ1xuICBtYXRyaWN1bGF0aW9uX251bWJlcjogc3RyaW5nXG4gIHJlYXNvbl9mb3Jfc2Nob2xhcnNoaXA6IHN0cmluZ1xufVxuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBTY2hvbGFyc2hpcEFwcGxpY2F0aW9uUGFnZSgpIHtcbiAgY29uc3Qgc2VhcmNoUGFyYW1zID0gdXNlU2VhcmNoUGFyYW1zKClcbiAgY29uc3QgW3NlbGVjdGVkQ2F0ZWdvcnksIHNldFNlbGVjdGVkQ2F0ZWdvcnldID0gdXNlU3RhdGU8J3ByaW1hcnknIHwgJ3NlY29uZGFyeScgfCAndW5pdmVyc2l0eSc+KCdwcmltYXJ5JylcbiAgY29uc3QgW3N1Ym1pdHRpbmcsIHNldFN1Ym1pdHRpbmddID0gdXNlU3RhdGUoZmFsc2UpXG4gIGNvbnN0IFtzaG93U3VjY2Vzcywgc2V0U2hvd1N1Y2Nlc3NdID0gdXNlU3RhdGUoZmFsc2UpXG4gIGNvbnN0IFtzY2hvbGFyc2hpcElkLCBzZXRTY2hvbGFyc2hpcElkXSA9IHVzZVN0YXRlPHN0cmluZyB8IG51bGw+KG51bGwpXG4gIGNvbnN0IHsgdG9hc3QgfSA9IHVzZVRvYXN0KClcblxuICAvLyBIYW5kbGUgVVJMIHBhcmFtZXRlcnMgZm9yIGNhdGVnb3J5IGFuZCBzY2hvbGFyc2hpcCBJRFxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGNvbnN0IGNhdGVnb3J5UGFyYW0gPSBzZWFyY2hQYXJhbXMuZ2V0KCdjYXRlZ29yeScpXG4gICAgY29uc3Qgc2Nob2xhcnNoaXBJZFBhcmFtID0gc2VhcmNoUGFyYW1zLmdldCgnc2Nob2xhcnNoaXBfaWQnKVxuXG4gICAgaWYgKGNhdGVnb3J5UGFyYW0gJiYgWydwcmltYXJ5JywgJ3NlY29uZGFyeScsICd1bml2ZXJzaXR5J10uaW5jbHVkZXMoY2F0ZWdvcnlQYXJhbSkpIHtcbiAgICAgIHNldFNlbGVjdGVkQ2F0ZWdvcnkoY2F0ZWdvcnlQYXJhbSBhcyAncHJpbWFyeScgfCAnc2Vjb25kYXJ5JyB8ICd1bml2ZXJzaXR5JylcbiAgICB9XG5cbiAgICBpZiAoc2Nob2xhcnNoaXBJZFBhcmFtKSB7XG4gICAgICBzZXRTY2hvbGFyc2hpcElkKHNjaG9sYXJzaGlwSWRQYXJhbSlcbiAgICB9XG4gIH0sIFtzZWFyY2hQYXJhbXNdKVxuXG4gIC8vIEZvcm0gZGF0YSBmb3IgZGlmZmVyZW50IGNhdGVnb3JpZXNcbiAgY29uc3QgW3ByaW1hcnlGb3JtRGF0YSwgc2V0UHJpbWFyeUZvcm1EYXRhXSA9IHVzZVN0YXRlPFByaW1hcnlTY2hvb2xGb3JtRGF0YT4oe1xuICAgIHN0dWRlbnRfZnVsbF9uYW1lOiAnJyxcbiAgICBhZ2U6ICcnLFxuICAgIGN1cnJlbnRfY2xhc3M6ICcnLFxuICAgIGZhdGhlcl9uYW1lOiAnJyxcbiAgICBtb3RoZXJfbmFtZTogJycsXG4gICAgcGFyZW50X3Bob25lOiAnJyxcbiAgICBob21lX2FkZHJlc3M6ICcnLFxuICAgIHNjaG9vbF9uYW1lOiAnJyxcbiAgICBoZWFkbWFzdGVyX25hbWU6ICcnLFxuICAgIHNjaG9vbF9hY2NvdW50X251bWJlcjogJycsXG4gICAgcmVhc29uX2Zvcl9zY2hvbGFyc2hpcDogJycsXG4gICAgY3VycmVudF9zY2hvb2xfZmVlOiAnJyxcbiAgICBzdXBwb3J0aW5nX2luZm9ybWF0aW9uOiAnJ1xuICB9KVxuXG4gIGNvbnN0IFtzZWNvbmRhcnlGb3JtRGF0YSwgc2V0U2Vjb25kYXJ5Rm9ybURhdGFdID0gdXNlU3RhdGU8U2Vjb25kYXJ5U2Nob29sRm9ybURhdGE+KHtcbiAgICBzdHVkZW50X2Z1bGxfbmFtZTogJycsXG4gICAgYWdlOiAnJyxcbiAgICBjbGFzczogJycsXG4gICAgcGFyZW50X3Bob25lOiAnJyxcbiAgICBhZGRyZXNzOiAnJyxcbiAgICBzY2hvb2xfbmFtZTogJycsXG4gICAgcHJpbmNpcGFsX25hbWU6ICcnLFxuICAgIHByaW5jaXBhbF9hY2NvdW50X251bWJlcjogJycsXG4gICAgcmVhc29uX2Zvcl9zY2hvbGFyc2hpcDogJycsXG4gICAgc2Nob29sX2ZlZV9hbW91bnQ6ICcnXG4gIH0pXG5cbiAgY29uc3QgW3VuaXZlcnNpdHlGb3JtRGF0YSwgc2V0VW5pdmVyc2l0eUZvcm1EYXRhXSA9IHVzZVN0YXRlPFVuaXZlcnNpdHlGb3JtRGF0YT4oe1xuICAgIGZ1bGxfbmFtZTogJycsXG4gICAgYWdlOiAnJyxcbiAgICBjb3Vyc2Vfb2Zfc3R1ZHk6ICcnLFxuICAgIGN1cnJlbnRfbGV2ZWw6ICcnLFxuICAgIHBob25lX251bWJlcjogJycsXG4gICAgZW1haWxfYWRkcmVzczogJycsXG4gICAgbWF0cmljdWxhdGlvbl9udW1iZXI6ICcnLFxuICAgIHJlYXNvbl9mb3Jfc2Nob2xhcnNoaXA6ICcnXG4gIH0pXG5cbiAgLy8gRmlsZSB1cGxvYWRzIGZvciBkaWZmZXJlbnQgY2F0ZWdvcmllc1xuICBjb25zdCBbcHJpbWFyeUZpbGVzLCBzZXRQcmltYXJ5RmlsZXNdID0gdXNlU3RhdGUoe1xuICAgIHN0dWRlbnRfcGljdHVyZTogbnVsbCBhcyBGaWxlIHwgbnVsbFxuICB9KVxuXG4gIGNvbnN0IFtzZWNvbmRhcnlGaWxlcywgc2V0U2Vjb25kYXJ5RmlsZXNdID0gdXNlU3RhdGUoe1xuICAgIHN0dWRlbnRfcGljdHVyZTogbnVsbCBhcyBGaWxlIHwgbnVsbFxuICB9KVxuXG4gIGNvbnN0IFt1bml2ZXJzaXR5RmlsZXMsIHNldFVuaXZlcnNpdHlGaWxlc10gPSB1c2VTdGF0ZSh7XG4gICAgc3R1ZGVudF9pZF9jYXJkOiBudWxsIGFzIEZpbGUgfCBudWxsLFxuICAgIHBheW1lbnRfZXZpZGVuY2U6IG51bGwgYXMgRmlsZSB8IG51bGwsXG4gICAgc3VwcG9ydGluZ19kb2N1bWVudHM6IFtdIGFzIEZpbGVbXVxuICB9KVxuXG4gIC8vIENhdGVnb3J5IGNvbmZpZ3VyYXRpb24gLSBVcGRhdGVkIHRvIG1hdGNoIHBsYXRmb3JtIGRlc2lnbiBzeXN0ZW1cbiAgY29uc3QgY2F0ZWdvcnlDb25maWcgPSB7XG4gICAgcHJpbWFyeToge1xuICAgICAgaWNvbjogQm9va09wZW4sXG4gICAgICB0aXRsZTogXCJQcmltYXJ5IFNjaG9vbCBTY2hvbGFyc2hpcFwiLFxuICAgICAgZGVzY3JpcHRpb246IFwiRm9yIFByaW1hcnkgMS02IHN0dWRlbnRzIChGaWxsZWQgYnkgUGFyZW50L0d1YXJkaWFuKVwiLFxuICAgICAgY29sb3I6IFwiYmctYmx1ZS01MDBcIixcbiAgICAgIGJnQ29sb3I6IFwiYmctYmx1ZS01MFwiLFxuICAgICAgdGV4dENvbG9yOiBcInRleHQtYmx1ZS03MDBcIixcbiAgICAgIGJvcmRlckNvbG9yOiBcImJvcmRlci1ibHVlLTIwMFwiXG4gICAgfSxcbiAgICBzZWNvbmRhcnk6IHtcbiAgICAgIGljb246IFNjaG9vbCxcbiAgICAgIHRpdGxlOiBcIlNlY29uZGFyeSBTY2hvb2wgU2Nob2xhcnNoaXBcIixcbiAgICAgIGRlc2NyaXB0aW9uOiBcIkZvciBTZWNvbmRhcnkgc2Nob29sIHN0dWRlbnRzIChGaWxsZWQgYnkgU3R1ZGVudClcIixcbiAgICAgIGNvbG9yOiBcImJnLWdyZWVuLTUwMFwiLFxuICAgICAgYmdDb2xvcjogXCJiZy1ncmVlbi01MFwiLFxuICAgICAgdGV4dENvbG9yOiBcInRleHQtZ3JlZW4tNzAwXCIsXG4gICAgICBib3JkZXJDb2xvcjogXCJib3JkZXItZ3JlZW4tMjAwXCJcbiAgICB9LFxuICAgIHVuaXZlcnNpdHk6IHtcbiAgICAgIGljb246IEdyYWR1YXRpb25DYXAsXG4gICAgICB0aXRsZTogXCJVbml2ZXJzaXR5IFNjaG9sYXJzaGlwXCIsXG4gICAgICBkZXNjcmlwdGlvbjogXCJGb3IgVW5pdmVyc2l0eSBzdHVkZW50cyAoRmlsbGVkIGJ5IFN0dWRlbnQpXCIsXG4gICAgICBjb2xvcjogXCJiZy1wdXJwbGUtNTAwXCIsXG4gICAgICBiZ0NvbG9yOiBcImJnLXB1cnBsZS01MFwiLFxuICAgICAgdGV4dENvbG9yOiBcInRleHQtcHVycGxlLTcwMFwiLFxuICAgICAgYm9yZGVyQ29sb3I6IFwiYm9yZGVyLXB1cnBsZS0yMDBcIlxuICAgIH1cbiAgfVxuXG4gIGNvbnN0IGhhbmRsZUZpbGVDaGFuZ2UgPSAoY2F0ZWdvcnk6IHN0cmluZywgZmllbGQ6IHN0cmluZywgZmlsZTogRmlsZSB8IG51bGwpID0+IHtcbiAgICBpZiAoY2F0ZWdvcnkgPT09ICdwcmltYXJ5Jykge1xuICAgICAgc2V0UHJpbWFyeUZpbGVzKHByZXYgPT4gKHsgLi4ucHJldiwgW2ZpZWxkXTogZmlsZSB9KSlcbiAgICB9IGVsc2UgaWYgKGNhdGVnb3J5ID09PSAnc2Vjb25kYXJ5Jykge1xuICAgICAgc2V0U2Vjb25kYXJ5RmlsZXMocHJldiA9PiAoeyAuLi5wcmV2LCBbZmllbGRdOiBmaWxlIH0pKVxuICAgIH0gZWxzZSBpZiAoY2F0ZWdvcnkgPT09ICd1bml2ZXJzaXR5Jykge1xuICAgICAgaWYgKGZpZWxkID09PSAnc3VwcG9ydGluZ19kb2N1bWVudHMnICYmIGZpbGUpIHtcbiAgICAgICAgc2V0VW5pdmVyc2l0eUZpbGVzKHByZXYgPT4gKHsgXG4gICAgICAgICAgLi4ucHJldiwgXG4gICAgICAgICAgc3VwcG9ydGluZ19kb2N1bWVudHM6IFsuLi5wcmV2LnN1cHBvcnRpbmdfZG9jdW1lbnRzLCBmaWxlXSBcbiAgICAgICAgfSkpXG4gICAgICB9IGVsc2Uge1xuICAgICAgICBzZXRVbml2ZXJzaXR5RmlsZXMocHJldiA9PiAoeyAuLi5wcmV2LCBbZmllbGRdOiBmaWxlIH0pKVxuICAgICAgfVxuICAgIH1cbiAgfVxuXG4gIGNvbnN0IGhhbmRsZU11bHRpcGxlRmlsZUNoYW5nZSA9IChmaWxlczogRmlsZUxpc3QgfCBudWxsKSA9PiB7XG4gICAgaWYgKGZpbGVzICYmIHNlbGVjdGVkQ2F0ZWdvcnkgPT09ICd1bml2ZXJzaXR5Jykge1xuICAgICAgY29uc3QgZmlsZUFycmF5ID0gQXJyYXkuZnJvbShmaWxlcylcbiAgICAgIHNldFVuaXZlcnNpdHlGaWxlcyhwcmV2ID0+ICh7IFxuICAgICAgICAuLi5wcmV2LCBcbiAgICAgICAgc3VwcG9ydGluZ19kb2N1bWVudHM6IFsuLi5wcmV2LnN1cHBvcnRpbmdfZG9jdW1lbnRzLCAuLi5maWxlQXJyYXldIFxuICAgICAgfSkpXG4gICAgfVxuICB9XG5cbiAgY29uc3QgcmVtb3ZlRmlsZSA9IChpbmRleDogbnVtYmVyKSA9PiB7XG4gICAgc2V0VW5pdmVyc2l0eUZpbGVzKHByZXYgPT4gKHtcbiAgICAgIC4uLnByZXYsXG4gICAgICBzdXBwb3J0aW5nX2RvY3VtZW50czogcHJldi5zdXBwb3J0aW5nX2RvY3VtZW50cy5maWx0ZXIoKF8sIGkpID0+IGkgIT09IGluZGV4KVxuICAgIH0pKVxuICB9XG5cbiAgY29uc3QgdmFsaWRhdGVGb3JtID0gKCkgPT4ge1xuICAgIGlmIChzZWxlY3RlZENhdGVnb3J5ID09PSAncHJpbWFyeScpIHtcbiAgICAgIGNvbnN0IHJlcXVpcmVkID0gWydzdHVkZW50X2Z1bGxfbmFtZScsICdhZ2UnLCAnY3VycmVudF9jbGFzcycsICdmYXRoZXJfbmFtZScsICdtb3RoZXJfbmFtZScsICdwYXJlbnRfcGhvbmUnLCAnaG9tZV9hZGRyZXNzJywgJ3NjaG9vbF9uYW1lJywgJ2hlYWRtYXN0ZXJfbmFtZScsICdzY2hvb2xfYWNjb3VudF9udW1iZXInLCAncmVhc29uX2Zvcl9zY2hvbGFyc2hpcCcsICdjdXJyZW50X3NjaG9vbF9mZWUnXVxuICAgICAgcmV0dXJuIHJlcXVpcmVkLmV2ZXJ5KGZpZWxkID0+IHByaW1hcnlGb3JtRGF0YVtmaWVsZCBhcyBrZXlvZiBQcmltYXJ5U2Nob29sRm9ybURhdGFdLnRyaW0oKSAhPT0gJycpICYmIHByaW1hcnlGaWxlcy5zdHVkZW50X3BpY3R1cmVcbiAgICB9IGVsc2UgaWYgKHNlbGVjdGVkQ2F0ZWdvcnkgPT09ICdzZWNvbmRhcnknKSB7XG4gICAgICBjb25zdCByZXF1aXJlZCA9IFsnc3R1ZGVudF9mdWxsX25hbWUnLCAnYWdlJywgJ2NsYXNzJywgJ3BhcmVudF9waG9uZScsICdhZGRyZXNzJywgJ3NjaG9vbF9uYW1lJywgJ3ByaW5jaXBhbF9uYW1lJywgJ3ByaW5jaXBhbF9hY2NvdW50X251bWJlcicsICdyZWFzb25fZm9yX3NjaG9sYXJzaGlwJywgJ3NjaG9vbF9mZWVfYW1vdW50J11cbiAgICAgIHJldHVybiByZXF1aXJlZC5ldmVyeShmaWVsZCA9PiBzZWNvbmRhcnlGb3JtRGF0YVtmaWVsZCBhcyBrZXlvZiBTZWNvbmRhcnlTY2hvb2xGb3JtRGF0YV0udHJpbSgpICE9PSAnJykgJiYgc2Vjb25kYXJ5RmlsZXMuc3R1ZGVudF9waWN0dXJlXG4gICAgfSBlbHNlIGlmIChzZWxlY3RlZENhdGVnb3J5ID09PSAndW5pdmVyc2l0eScpIHtcbiAgICAgIGNvbnN0IHJlcXVpcmVkID0gWydmdWxsX25hbWUnLCAnYWdlJywgJ2NvdXJzZV9vZl9zdHVkeScsICdjdXJyZW50X2xldmVsJywgJ3Bob25lX251bWJlcicsICdlbWFpbF9hZGRyZXNzJywgJ21hdHJpY3VsYXRpb25fbnVtYmVyJywgJ3JlYXNvbl9mb3Jfc2Nob2xhcnNoaXAnXVxuICAgICAgcmV0dXJuIHJlcXVpcmVkLmV2ZXJ5KGZpZWxkID0+IHVuaXZlcnNpdHlGb3JtRGF0YVtmaWVsZCBhcyBrZXlvZiBVbml2ZXJzaXR5Rm9ybURhdGFdLnRyaW0oKSAhPT0gJycpICYmIFxuICAgICAgICAgICAgIHVuaXZlcnNpdHlGaWxlcy5zdHVkZW50X2lkX2NhcmQgJiYgdW5pdmVyc2l0eUZpbGVzLnBheW1lbnRfZXZpZGVuY2VcbiAgICB9XG4gICAgcmV0dXJuIGZhbHNlXG4gIH1cblxuICBjb25zdCBoYW5kbGVTdWJtaXQgPSBhc3luYyAoZTogUmVhY3QuRm9ybUV2ZW50KSA9PiB7XG4gICAgZS5wcmV2ZW50RGVmYXVsdCgpXG4gICAgXG4gICAgaWYgKCF2YWxpZGF0ZUZvcm0oKSkge1xuICAgICAgdG9hc3Qoe1xuICAgICAgICB0aXRsZTogXCJWYWxpZGF0aW9uIEVycm9yXCIsXG4gICAgICAgIGRlc2NyaXB0aW9uOiBcIlBsZWFzZSBmaWxsIGluIGFsbCByZXF1aXJlZCBmaWVsZHMgYW5kIHVwbG9hZCByZXF1aXJlZCBkb2N1bWVudHNcIixcbiAgICAgICAgdmFyaWFudDogXCJkZXN0cnVjdGl2ZVwiXG4gICAgICB9KVxuICAgICAgcmV0dXJuXG4gICAgfVxuXG4gICAgdHJ5IHtcbiAgICAgIHNldFN1Ym1pdHRpbmcodHJ1ZSlcbiAgICAgIFxuICAgICAgY29uc3QgZm9ybURhdGEgPSBuZXcgRm9ybURhdGEoKVxuICAgICAgZm9ybURhdGEuYXBwZW5kKCdjYXRlZ29yeScsIHNlbGVjdGVkQ2F0ZWdvcnkpXG4gICAgICBcbiAgICAgIC8vIEFkZCBmb3JtIGRhdGEgYmFzZWQgb24gY2F0ZWdvcnlcbiAgICAgIGlmIChzZWxlY3RlZENhdGVnb3J5ID09PSAncHJpbWFyeScpIHtcbiAgICAgICAgZm9ybURhdGEuYXBwZW5kKCdmb3JtX2RhdGEnLCBKU09OLnN0cmluZ2lmeShwcmltYXJ5Rm9ybURhdGEpKVxuICAgICAgICBpZiAocHJpbWFyeUZpbGVzLnN0dWRlbnRfcGljdHVyZSkge1xuICAgICAgICAgIGZvcm1EYXRhLmFwcGVuZCgnc3R1ZGVudF9waWN0dXJlJywgcHJpbWFyeUZpbGVzLnN0dWRlbnRfcGljdHVyZSlcbiAgICAgICAgfVxuICAgICAgfSBlbHNlIGlmIChzZWxlY3RlZENhdGVnb3J5ID09PSAnc2Vjb25kYXJ5Jykge1xuICAgICAgICBmb3JtRGF0YS5hcHBlbmQoJ2Zvcm1fZGF0YScsIEpTT04uc3RyaW5naWZ5KHNlY29uZGFyeUZvcm1EYXRhKSlcbiAgICAgICAgaWYgKHNlY29uZGFyeUZpbGVzLnN0dWRlbnRfcGljdHVyZSkge1xuICAgICAgICAgIGZvcm1EYXRhLmFwcGVuZCgnc3R1ZGVudF9waWN0dXJlJywgc2Vjb25kYXJ5RmlsZXMuc3R1ZGVudF9waWN0dXJlKVxuICAgICAgICB9XG4gICAgICB9IGVsc2UgaWYgKHNlbGVjdGVkQ2F0ZWdvcnkgPT09ICd1bml2ZXJzaXR5Jykge1xuICAgICAgICBmb3JtRGF0YS5hcHBlbmQoJ2Zvcm1fZGF0YScsIEpTT04uc3RyaW5naWZ5KHVuaXZlcnNpdHlGb3JtRGF0YSkpXG4gICAgICAgIGlmICh1bml2ZXJzaXR5RmlsZXMuc3R1ZGVudF9pZF9jYXJkKSB7XG4gICAgICAgICAgZm9ybURhdGEuYXBwZW5kKCdzdHVkZW50X2lkX2NhcmQnLCB1bml2ZXJzaXR5RmlsZXMuc3R1ZGVudF9pZF9jYXJkKVxuICAgICAgICB9XG4gICAgICAgIGlmICh1bml2ZXJzaXR5RmlsZXMucGF5bWVudF9ldmlkZW5jZSkge1xuICAgICAgICAgIGZvcm1EYXRhLmFwcGVuZCgncGF5bWVudF9ldmlkZW5jZScsIHVuaXZlcnNpdHlGaWxlcy5wYXltZW50X2V2aWRlbmNlKVxuICAgICAgICB9XG4gICAgICAgIHVuaXZlcnNpdHlGaWxlcy5zdXBwb3J0aW5nX2RvY3VtZW50cy5mb3JFYWNoKChmaWxlLCBpbmRleCkgPT4ge1xuICAgICAgICAgIGZvcm1EYXRhLmFwcGVuZChgc3VwcG9ydGluZ19kb2N1bWVudHNbJHtpbmRleH1dYCwgZmlsZSlcbiAgICAgICAgfSlcbiAgICAgIH1cblxuICAgICAgLy8gQWRkIHNjaG9sYXJzaGlwIElEIGlmIGF2YWlsYWJsZVxuICAgICAgaWYgKHNjaG9sYXJzaGlwSWQpIHtcbiAgICAgICAgZm9ybURhdGEuYXBwZW5kKCdzY2hvbGFyc2hpcF9pZCcsIHNjaG9sYXJzaGlwSWQpXG4gICAgICB9XG5cbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgYXBpQ2xpZW50LnN1Ym1pdFNjaG9sYXJzaGlwQXBwbGljYXRpb24oZm9ybURhdGEpXG5cbiAgICAgIGlmIChyZXNwb25zZS5zdWNjZXNzKSB7XG4gICAgICAgIC8vIFNob3cgc3VjY2VzcyBtZXNzYWdlXG4gICAgICAgIHNldFNob3dTdWNjZXNzKHRydWUpXG5cbiAgICAgICAgLy8gUmVzZXQgZm9ybVxuICAgICAgICBpZiAoc2VsZWN0ZWRDYXRlZ29yeSA9PT0gJ3ByaW1hcnknKSB7XG4gICAgICAgICAgc2V0UHJpbWFyeUZvcm1EYXRhKHtcbiAgICAgICAgICAgIHN0dWRlbnRfZnVsbF9uYW1lOiAnJywgYWdlOiAnJywgY3VycmVudF9jbGFzczogJycsIGZhdGhlcl9uYW1lOiAnJywgbW90aGVyX25hbWU6ICcnLFxuICAgICAgICAgICAgcGFyZW50X3Bob25lOiAnJywgaG9tZV9hZGRyZXNzOiAnJywgc2Nob29sX25hbWU6ICcnLCBoZWFkbWFzdGVyX25hbWU6ICcnLFxuICAgICAgICAgICAgc2Nob29sX2FjY291bnRfbnVtYmVyOiAnJywgcmVhc29uX2Zvcl9zY2hvbGFyc2hpcDogJycsIGN1cnJlbnRfc2Nob29sX2ZlZTogJycsXG4gICAgICAgICAgICBzdXBwb3J0aW5nX2luZm9ybWF0aW9uOiAnJ1xuICAgICAgICAgIH0pXG4gICAgICAgICAgc2V0UHJpbWFyeUZpbGVzKHsgc3R1ZGVudF9waWN0dXJlOiBudWxsIH0pXG4gICAgICAgIH0gZWxzZSBpZiAoc2VsZWN0ZWRDYXRlZ29yeSA9PT0gJ3NlY29uZGFyeScpIHtcbiAgICAgICAgICBzZXRTZWNvbmRhcnlGb3JtRGF0YSh7XG4gICAgICAgICAgICBzdHVkZW50X2Z1bGxfbmFtZTogJycsIGFnZTogJycsIGNsYXNzOiAnJywgcGFyZW50X3Bob25lOiAnJywgYWRkcmVzczogJycsXG4gICAgICAgICAgICBzY2hvb2xfbmFtZTogJycsIHByaW5jaXBhbF9uYW1lOiAnJywgcHJpbmNpcGFsX2FjY291bnRfbnVtYmVyOiAnJyxcbiAgICAgICAgICAgIHJlYXNvbl9mb3Jfc2Nob2xhcnNoaXA6ICcnLCBzY2hvb2xfZmVlX2Ftb3VudDogJydcbiAgICAgICAgICB9KVxuICAgICAgICAgIHNldFNlY29uZGFyeUZpbGVzKHsgc3R1ZGVudF9waWN0dXJlOiBudWxsIH0pXG4gICAgICAgIH0gZWxzZSBpZiAoc2VsZWN0ZWRDYXRlZ29yeSA9PT0gJ3VuaXZlcnNpdHknKSB7XG4gICAgICAgICAgc2V0VW5pdmVyc2l0eUZvcm1EYXRhKHtcbiAgICAgICAgICAgIGZ1bGxfbmFtZTogJycsIGFnZTogJycsIGNvdXJzZV9vZl9zdHVkeTogJycsIGN1cnJlbnRfbGV2ZWw6ICcnLFxuICAgICAgICAgICAgcGhvbmVfbnVtYmVyOiAnJywgZW1haWxfYWRkcmVzczogJycsIG1hdHJpY3VsYXRpb25fbnVtYmVyOiAnJyxcbiAgICAgICAgICAgIHJlYXNvbl9mb3Jfc2Nob2xhcnNoaXA6ICcnXG4gICAgICAgICAgfSlcbiAgICAgICAgICBzZXRVbml2ZXJzaXR5RmlsZXMoeyBzdHVkZW50X2lkX2NhcmQ6IG51bGwsIHBheW1lbnRfZXZpZGVuY2U6IG51bGwsIHN1cHBvcnRpbmdfZG9jdW1lbnRzOiBbXSB9KVxuICAgICAgICB9XG4gICAgICB9IGVsc2Uge1xuICAgICAgICB0b2FzdCh7XG4gICAgICAgICAgdGl0bGU6IFwiRXJyb3JcIixcbiAgICAgICAgICBkZXNjcmlwdGlvbjogcmVzcG9uc2UubWVzc2FnZSB8fCBcIkZhaWxlZCB0byBzdWJtaXQgYXBwbGljYXRpb25cIixcbiAgICAgICAgICB2YXJpYW50OiBcImRlc3RydWN0aXZlXCJcbiAgICAgICAgfSlcbiAgICAgIH1cbiAgICB9IGNhdGNoIChlcnJvcjogYW55KSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBzdWJtaXR0aW5nIGFwcGxpY2F0aW9uOicsIGVycm9yKVxuICAgICAgdG9hc3Qoe1xuICAgICAgICB0aXRsZTogXCJFcnJvclwiLFxuICAgICAgICBkZXNjcmlwdGlvbjogZXJyb3IucmVzcG9uc2U/LmRhdGE/Lm1lc3NhZ2UgfHwgXCJGYWlsZWQgdG8gc3VibWl0IGFwcGxpY2F0aW9uXCIsXG4gICAgICAgIHZhcmlhbnQ6IFwiZGVzdHJ1Y3RpdmVcIlxuICAgICAgfSlcbiAgICB9IGZpbmFsbHkge1xuICAgICAgc2V0U3VibWl0dGluZyhmYWxzZSlcbiAgICB9XG4gIH1cblxuICBjb25zdCBoYW5kbGVCYWNrVG9Gb3JtID0gKCkgPT4ge1xuICAgIHNldFNob3dTdWNjZXNzKGZhbHNlKVxuICB9XG5cbiAgY29uc3QgaGFuZGxlR29Ib21lID0gKCkgPT4ge1xuICAgIC8vIE5hdmlnYXRlIHRvIGRhc2hib2FyZCBvciBob21lIHBhZ2VcbiAgICB3aW5kb3cubG9jYXRpb24uaHJlZiA9ICcvZGFzaGJvYXJkJ1xuICB9XG5cbiAgaWYgKHNob3dTdWNjZXNzKSB7XG4gICAgcmV0dXJuIDxTdWNjZXNzTWVzc2FnZSBvbkJhY2tUb0Zvcm09e2hhbmRsZUJhY2tUb0Zvcm19IG9uR29Ib21lPXtoYW5kbGVHb0hvbWV9IC8+XG4gIH1cblxuICByZXR1cm4gKFxuICAgIDxkaXYgY2xhc3NOYW1lPVwibWluLWgtc2NyZWVuIGJnLWdyYXktNTAgZGFyazpiZy1ncmF5LTkwMFwiPlxuICAgICAgey8qIEhlcm8gU2VjdGlvbiAqL31cbiAgICAgIDxzZWN0aW9uIGNsYXNzTmFtZT1cInJlbGF0aXZlIHB5LTEyIHNtOnB5LTE2IG1kOnB5LTIwIGJnLWdyYWRpZW50LXRvLWJyIGZyb20tZ3JlZW4tNjAwIHRvLWdyZWVuLTgwMCB0ZXh0LXdoaXRlXCI+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiY29udGFpbmVyIG14LWF1dG8gcHgtMyBzbTpweC00IG1kOnB4LTYgbGc6cHgtOFwiPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWF4LXctNHhsIG14LWF1dG8gdGV4dC1jZW50ZXIgc3BhY2UteS00IHNtOnNwYWNlLXktNlwiPlxuICAgICAgICAgICAgPGgxIGNsYXNzTmFtZT1cInRleHQtMnhsIHNtOnRleHQtM3hsIG1kOnRleHQtNHhsIGxnOnRleHQtNXhsIHhsOnRleHQtNnhsIGZvbnQtYm9sZCB0cmFja2luZy10aWdodCBsZWFkaW5nLXRpZ2h0XCI+XG4gICAgICAgICAgICAgIFNjaG9sYXJzaGlwIEFwcGxpY2F0aW9uXG4gICAgICAgICAgICA8L2gxPlxuICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1iYXNlIHNtOnRleHQtbGcgbWQ6dGV4dC14bCB0ZXh0LWdyZWVuLTEwMCBtYXgtdy0zeGwgbXgtYXV0byBweC0yXCI+XG4gICAgICAgICAgICAgIENob29zZSB5b3VyIHNjaG9sYXJzaGlwIGNhdGVnb3J5IGFuZCBjb21wbGV0ZSB0aGUgYXBwbGljYXRpb24gZm9ybVxuICAgICAgICAgICAgPC9wPlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvc2VjdGlvbj5cblxuICAgICAgey8qIE1haW4gQ29udGVudCAqL31cbiAgICAgIDxzZWN0aW9uIGNsYXNzTmFtZT1cInB5LTYgc206cHktOCBtZDpweS0xMiBiZy13aGl0ZSBkYXJrOmJnLWdyYXktOTAwXCI+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiY29udGFpbmVyIG14LWF1dG8gcHgtMyBzbTpweC00IG1kOnB4LTYgbGc6cHgtOFwiPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWF4LXctNHhsIG14LWF1dG9cIj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWItNCBzbTptYi02IG1kOm1iLThcIj5cbiAgICAgICAgICAgICAgPGgyIGNsYXNzTmFtZT1cInRleHQteGwgc206dGV4dC0yeGwgbWQ6dGV4dC0zeGwgZm9udC1ib2xkIHRyYWNraW5nLXRpZ2h0IG1iLTIgdGV4dC1jZW50ZXIgc206dGV4dC1sZWZ0XCI+QXBwbHkgZm9yIFNjaG9sYXJzaGlwPC9oMj5cbiAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSBzbTp0ZXh0LWJhc2UgdGV4dC1ncmF5LTYwMCBkYXJrOnRleHQtZ3JheS00MDAgdGV4dC1jZW50ZXIgc206dGV4dC1sZWZ0XCI+XG4gICAgICAgICAgICAgICAgU2VsZWN0IHlvdXIgZWR1Y2F0aW9uIGxldmVsIGFuZCBmaWxsIG91dCB0aGUgYXBwcm9wcmlhdGUgZm9ybVxuICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIHsvKiBDYXRlZ29yeSBTZWxlY3Rpb24gKi99XG4gICAgICAgICAgICA8VGFicyB2YWx1ZT17c2VsZWN0ZWRDYXRlZ29yeX0gb25WYWx1ZUNoYW5nZT17KHZhbHVlKSA9PiBzZXRTZWxlY3RlZENhdGVnb3J5KHZhbHVlIGFzIGFueSl9IGNsYXNzTmFtZT1cInctZnVsbFwiPlxuICAgICAgICAgICAgICA8VGFic0xpc3QgY2xhc3NOYW1lPVwiZ3JpZCB3LWZ1bGwgZ3JpZC1jb2xzLTEgc206Z3JpZC1jb2xzLTMgbWItNCBzbTptYi02IGgtYXV0byBwLTEgZ2FwLTEgc206Z2FwLTIgYmctZ3JheS0xMDAgcm91bmRlZC1sZ1wiPlxuICAgICAgICAgICAgICAgIHtPYmplY3QuZW50cmllcyhjYXRlZ29yeUNvbmZpZykubWFwKChba2V5LCBjb25maWddKSA9PiB7XG4gICAgICAgICAgICAgICAgICBjb25zdCBJY29uQ29tcG9uZW50ID0gY29uZmlnLmljb25cbiAgICAgICAgICAgICAgICAgIHJldHVybiAoXG4gICAgICAgICAgICAgICAgICAgIDxUYWJzVHJpZ2dlclxuICAgICAgICAgICAgICAgICAgICAgIGtleT17a2V5fVxuICAgICAgICAgICAgICAgICAgICAgIHZhbHVlPXtrZXl9XG4gICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiZmxleCBmbGV4LWNvbCBzbTpmbGV4LWNvbCBpdGVtcy1jZW50ZXIgcC0yIHNtOnAtMyBzcGFjZS15LTEgc206c3BhY2UteS0yIHJvdW5kZWQtbWQgdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tMzAwIGRhdGEtW3N0YXRlPWFjdGl2ZV06Ymctd2hpdGUgZGF0YS1bc3RhdGU9YWN0aXZlXTpzaGFkb3ctbWQgbWluLWgtWzYwcHhdIHNtOm1pbi1oLVs4MHB4XVwiXG4gICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInAtMS41IHNtOnAtMiByb3VuZGVkLWZ1bGwgYmctZ3JlZW4tNjAwIHRleHQtd2hpdGUgc2hhZG93LXNtXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICA8SWNvbkNvbXBvbmVudCBjbGFzc05hbWU9XCJoLTMgdy0zIHNtOmgtNCBzbTp3LTRcIiAvPlxuICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZm9udC1tZWRpdW0gdGV4dC14cyBzbTp0ZXh0LXhzIHRleHQtZ3JheS05MDAgbGVhZGluZy10aWdodFwiPntjb25maWcudGl0bGV9PC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQteHMgc206dGV4dC14cyB0ZXh0LWdyYXktNjAwIG10LTAuNSBoaWRkZW4gc206YmxvY2tcIj57Y29uZmlnLmRlc2NyaXB0aW9ufTwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICA8L1RhYnNUcmlnZ2VyPlxuICAgICAgICAgICAgICAgICAgKVxuICAgICAgICAgICAgICAgIH0pfVxuICAgICAgICAgICAgICA8L1RhYnNMaXN0PlxuXG4gICAgICAgICAgICAgIHsvKiBQcmltYXJ5IFNjaG9vbCBGb3JtICovfVxuICAgICAgICAgICAgICA8VGFic0NvbnRlbnQgdmFsdWU9XCJwcmltYXJ5XCIgY2xhc3NOYW1lPVwic3BhY2UteS00IHNtOnNwYWNlLXktNiBtZDpzcGFjZS15LThcIj5cbiAgICAgICAgICAgICAgICA8Zm9ybSBvblN1Ym1pdD17aGFuZGxlU3VibWl0fSBjbGFzc05hbWU9XCJzcGFjZS15LTQgc206c3BhY2UteS02IG1kOnNwYWNlLXktOFwiPlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy1ncmVlbi01MCBwLTMgc206cC00IG1kOnAtNiByb3VuZGVkLWxnIGJvcmRlciBib3JkZXItZ3JlZW4tMjAwXCI+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1zdGFydCBzbTppdGVtcy1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgICAgICAgICA8QWxlcnRUcmlhbmdsZSBjbGFzc05hbWU9XCJoLTQgdy00IHNtOmgtNSBzbTp3LTUgdGV4dC1ncmVlbi02MDAgbXItMiBzbTptci0zIG10LTAuNSBzbTptdC0wIGZsZXgtc2hyaW5rLTBcIiAvPlxuICAgICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtZ3JlZW4tODAwIGZvbnQtbWVkaXVtIHRleHQtc20gc206dGV4dC1iYXNlXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICBUaGlzIGZvcm0gc2hvdWxkIGJlIGZpbGxlZCBieSBhIFBhcmVudCBvciBHdWFyZGlhbiBvbiBiZWhhbGYgb2YgdGhlIHN0dWRlbnRcbiAgICAgICAgICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgICAgIHsvKiBTdHVkZW50IEluZm9ybWF0aW9uICovfVxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTQgc206c3BhY2UteS02XCI+XG4gICAgICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQtYmFzZSBzbTp0ZXh0LWxnIGZvbnQtc2VtaWJvbGQgdGV4dC1ncmVlbi04MDAgZGFyazp0ZXh0LWdyZWVuLTIwMCBtYi0zIHNtOm1iLTQgZmxleCBpdGVtcy1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxVc2VyIGNsYXNzTmFtZT1cImgtNCB3LTQgc206aC01IHNtOnctNSBtci0yIHRleHQtZ3JlZW4tNjAwXCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgIFN0dWRlbnQgSW5mb3JtYXRpb25cbiAgICAgICAgICAgICAgICAgICAgICA8L2gzPlxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS0zIHNtOnNwYWNlLXktNFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTJcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPExhYmVsIGh0bWxGb3I9XCJzdHVkZW50X2Z1bGxfbmFtZVwiIGNsYXNzTmFtZT1cInRleHQtZ3JlZW4tODAwIGRhcms6dGV4dC1ncmVlbi0yMDAgdGV4dC1zbSBzbTp0ZXh0LWJhc2VcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBTdHVkZW50IEZ1bGwgTmFtZSAqXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvTGFiZWw+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicmVsYXRpdmVcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8VXNlciBjbGFzc05hbWU9XCJhYnNvbHV0ZSBsZWZ0LTMgdG9wLTEvMiB0cmFuc2Zvcm0gLXRyYW5zbGF0ZS15LTEvMiBoLTQgdy00IHRleHQtZ3JlZW4tNTAwXCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8SW5wdXRcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGlkPVwic3R1ZGVudF9mdWxsX25hbWVcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdmFsdWU9e3ByaW1hcnlGb3JtRGF0YS5zdHVkZW50X2Z1bGxfbmFtZX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gc2V0UHJpbWFyeUZvcm1EYXRhKHByZXYgPT4gKHsgLi4ucHJldiwgc3R1ZGVudF9mdWxsX25hbWU6IGUudGFyZ2V0LnZhbHVlIH0pKX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiRW50ZXIgc3R1ZGVudCdzIGZ1bGwgbmFtZVwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJwbC0xMCByb3VuZGVkLXhsIGJvcmRlci1ncmVlbi0yMDAgZGFyazpib3JkZXItZ3JlZW4tNzAwIGZvY3VzOmJvcmRlci1ncmVlbi01MDAgdGV4dC1zbSBzbTp0ZXh0LWJhc2VcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcmVxdWlyZWRcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTEgc206Z3JpZC1jb2xzLTIgZ2FwLTMgc206Z2FwLTRcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTJcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8TGFiZWwgaHRtbEZvcj1cImFnZVwiIGNsYXNzTmFtZT1cInRleHQtZ3JlZW4tODAwIGRhcms6dGV4dC1ncmVlbi0yMDAgdGV4dC1zbSBzbTp0ZXh0LWJhc2VcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIEFnZSAqXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9MYWJlbD5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8SW5wdXRcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGlkPVwiYWdlXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHR5cGU9XCJudW1iZXJcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdmFsdWU9e3ByaW1hcnlGb3JtRGF0YS5hZ2V9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHNldFByaW1hcnlGb3JtRGF0YShwcmV2ID0+ICh7IC4uLnByZXYsIGFnZTogZS50YXJnZXQudmFsdWUgfSkpfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCJBZ2VcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwicm91bmRlZC14bCBib3JkZXItZ3JlZW4tMjAwIGRhcms6Ym9yZGVyLWdyZWVuLTcwMCBmb2N1czpib3JkZXItZ3JlZW4tNTAwIHRleHQtc20gc206dGV4dC1iYXNlXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHJlcXVpcmVkXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS0yXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPExhYmVsIGh0bWxGb3I9XCJjdXJyZW50X2NsYXNzXCIgY2xhc3NOYW1lPVwidGV4dC1ncmVlbi04MDAgZGFyazp0ZXh0LWdyZWVuLTIwMCB0ZXh0LXNtIHNtOnRleHQtYmFzZVwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgQ3VycmVudCBDbGFzcyAqXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9MYWJlbD5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8U2VsZWN0IHZhbHVlPXtwcmltYXJ5Rm9ybURhdGEuY3VycmVudF9jbGFzc30gb25WYWx1ZUNoYW5nZT17KHZhbHVlKSA9PiBzZXRQcmltYXJ5Rm9ybURhdGEocHJldiA9PiAoeyAuLi5wcmV2LCBjdXJyZW50X2NsYXNzOiB2YWx1ZSB9KSl9PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPFNlbGVjdFRyaWdnZXIgY2xhc3NOYW1lPVwicm91bmRlZC14bCBib3JkZXItZ3JlZW4tMjAwIGRhcms6Ym9yZGVyLWdyZWVuLTcwMCBmb2N1czpib3JkZXItZ3JlZW4tNTAwIHRleHQtc20gc206dGV4dC1iYXNlXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxTZWxlY3RWYWx1ZSBwbGFjZWhvbGRlcj1cIlNlbGVjdCBjbGFzc1wiIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L1NlbGVjdFRyaWdnZXI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8U2VsZWN0Q29udGVudD5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPFNlbGVjdEl0ZW0gdmFsdWU9XCJQcmltYXJ5IDFcIj5QcmltYXJ5IDE8L1NlbGVjdEl0ZW0+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxTZWxlY3RJdGVtIHZhbHVlPVwiUHJpbWFyeSAyXCI+UHJpbWFyeSAyPC9TZWxlY3RJdGVtPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8U2VsZWN0SXRlbSB2YWx1ZT1cIlByaW1hcnkgM1wiPlByaW1hcnkgMzwvU2VsZWN0SXRlbT5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPFNlbGVjdEl0ZW0gdmFsdWU9XCJQcmltYXJ5IDRcIj5QcmltYXJ5IDQ8L1NlbGVjdEl0ZW0+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxTZWxlY3RJdGVtIHZhbHVlPVwiUHJpbWFyeSA1XCI+UHJpbWFyeSA1PC9TZWxlY3RJdGVtPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8U2VsZWN0SXRlbSB2YWx1ZT1cIlByaW1hcnkgNlwiPlByaW1hcnkgNjwvU2VsZWN0SXRlbT5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvU2VsZWN0Q29udGVudD5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L1NlbGVjdD5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgICAgICAgey8qIFBhcmVudCBJbmZvcm1hdGlvbiAqL31cbiAgICAgICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LXNlbWlib2xkIHRleHQtZ3JlZW4tODAwIGRhcms6dGV4dC1ncmVlbi0yMDAgbWItNCBmbGV4IGl0ZW1zLWNlbnRlclwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgPFVzZXIgY2xhc3NOYW1lPVwiaC01IHctNSBtci0yIHRleHQtZ3JlZW4tNjAwXCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgIFBhcmVudC9HdWFyZGlhbiBJbmZvcm1hdGlvblxuICAgICAgICAgICAgICAgICAgICAgIDwvaDM+XG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTRcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMSBzbTpncmlkLWNvbHMtMiBnYXAtMyBzbTpnYXAtNFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktMlwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxMYWJlbCBodG1sRm9yPVwiZmF0aGVyX25hbWVcIiBjbGFzc05hbWU9XCJ0ZXh0LWdyZWVuLTgwMCBkYXJrOnRleHQtZ3JlZW4tMjAwXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBGYXRoZXIncyBOYW1lICpcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L0xhYmVsPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicmVsYXRpdmVcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxVc2VyIGNsYXNzTmFtZT1cImFic29sdXRlIGxlZnQtMyB0b3AtMS8yIHRyYW5zZm9ybSAtdHJhbnNsYXRlLXktMS8yIGgtNCB3LTQgdGV4dC1ncmVlbi01MDBcIiAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPElucHV0XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGlkPVwiZmF0aGVyX25hbWVcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB2YWx1ZT17cHJpbWFyeUZvcm1EYXRhLmZhdGhlcl9uYW1lfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHNldFByaW1hcnlGb3JtRGF0YShwcmV2ID0+ICh7IC4uLnByZXYsIGZhdGhlcl9uYW1lOiBlLnRhcmdldC52YWx1ZSB9KSl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiRW50ZXIgZmF0aGVyJ3MgbmFtZVwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInBsLTEwIHJvdW5kZWQteGwgYm9yZGVyLWdyZWVuLTIwMCBkYXJrOmJvcmRlci1ncmVlbi03MDAgZm9jdXM6Ym9yZGVyLWdyZWVuLTUwMFwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHJlcXVpcmVkXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTJcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8TGFiZWwgaHRtbEZvcj1cIm1vdGhlcl9uYW1lXCIgY2xhc3NOYW1lPVwidGV4dC1ncmVlbi04MDAgZGFyazp0ZXh0LWdyZWVuLTIwMFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgTW90aGVyJ3MgTmFtZSAqXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9MYWJlbD5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8SW5wdXRcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGlkPVwibW90aGVyX25hbWVcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdmFsdWU9e3ByaW1hcnlGb3JtRGF0YS5tb3RoZXJfbmFtZX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gc2V0UHJpbWFyeUZvcm1EYXRhKHByZXYgPT4gKHsgLi4ucHJldiwgbW90aGVyX25hbWU6IGUudGFyZ2V0LnZhbHVlIH0pKX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiRW50ZXIgbW90aGVyJ3MgbmFtZVwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJyb3VuZGVkLXhsIGJvcmRlci1ncmVlbi0yMDAgZGFyazpib3JkZXItZ3JlZW4tNzAwIGZvY3VzOmJvcmRlci1ncmVlbi01MDBcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcmVxdWlyZWRcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTJcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPExhYmVsIGh0bWxGb3I9XCJwYXJlbnRfcGhvbmVcIiBjbGFzc05hbWU9XCJ0ZXh0LWdyZWVuLTgwMCBkYXJrOnRleHQtZ3JlZW4tMjAwXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgRmF0aGVyJ3Mgb3IgTW90aGVyJ3MgUGhvbmUgTnVtYmVyICpcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9MYWJlbD5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJyZWxhdGl2ZVwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxQaG9uZSBjbGFzc05hbWU9XCJhYnNvbHV0ZSBsZWZ0LTMgdG9wLTEvMiB0cmFuc2Zvcm0gLXRyYW5zbGF0ZS15LTEvMiBoLTQgdy00IHRleHQtZ3JlZW4tNTAwXCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8SW5wdXRcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGlkPVwicGFyZW50X3Bob25lXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHR5cGU9XCJ0ZWxcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdmFsdWU9e3ByaW1hcnlGb3JtRGF0YS5wYXJlbnRfcGhvbmV9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHNldFByaW1hcnlGb3JtRGF0YShwcmV2ID0+ICh7IC4uLnByZXYsIHBhcmVudF9waG9uZTogZS50YXJnZXQudmFsdWUgfSkpfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCJFbnRlciBwaG9uZSBudW1iZXJcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwicGwtMTAgcm91bmRlZC14bCBib3JkZXItZ3JlZW4tMjAwIGRhcms6Ym9yZGVyLWdyZWVuLTcwMCBmb2N1czpib3JkZXItZ3JlZW4tNTAwXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHJlcXVpcmVkXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgICAgey8qIEFkZHJlc3MgSW5mb3JtYXRpb24gKi99XG4gICAgICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LXNlbWlib2xkIHRleHQtZ3JlZW4tODAwIGRhcms6dGV4dC1ncmVlbi0yMDAgbWItNCBmbGV4IGl0ZW1zLWNlbnRlclwiPlxuICAgICAgICAgICAgICAgICAgICAgIDxNYXBQaW4gY2xhc3NOYW1lPVwiaC01IHctNSBtci0yIHRleHQtZ3JlZW4tNjAwXCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICBBZGRyZXNzIEluZm9ybWF0aW9uXG4gICAgICAgICAgICAgICAgICAgIDwvaDM+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS0yXCI+XG4gICAgICAgICAgICAgICAgICAgICAgPExhYmVsIGh0bWxGb3I9XCJob21lX2FkZHJlc3NcIiBjbGFzc05hbWU9XCJ0ZXh0LWdyZWVuLTgwMCBkYXJrOnRleHQtZ3JlZW4tMjAwXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICBIb21lIEFkZHJlc3MgKlxuICAgICAgICAgICAgICAgICAgICAgIDwvTGFiZWw+XG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJyZWxhdGl2ZVwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgPE1hcFBpbiBjbGFzc05hbWU9XCJhYnNvbHV0ZSBsZWZ0LTMgdG9wLTMgaC00IHctNCB0ZXh0LWdyZWVuLTUwMFwiIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICA8VGV4dGFyZWFcbiAgICAgICAgICAgICAgICAgICAgICAgICAgaWQ9XCJob21lX2FkZHJlc3NcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICB2YWx1ZT17cHJpbWFyeUZvcm1EYXRhLmhvbWVfYWRkcmVzc31cbiAgICAgICAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBzZXRQcmltYXJ5Rm9ybURhdGEocHJldiA9PiAoeyAuLi5wcmV2LCBob21lX2FkZHJlc3M6IGUudGFyZ2V0LnZhbHVlIH0pKX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCJFbnRlciBjb21wbGV0ZSBob21lIGFkZHJlc3NcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJwbC0xMCByb3VuZGVkLXhsIGJvcmRlci1ncmVlbi0yMDAgZGFyazpib3JkZXItZ3JlZW4tNzAwIGZvY3VzOmJvcmRlci1ncmVlbi01MDBcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICByb3dzPXszfVxuICAgICAgICAgICAgICAgICAgICAgICAgICByZXF1aXJlZFxuICAgICAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgICAgey8qIFNjaG9vbCBJbmZvcm1hdGlvbiAqL31cbiAgICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZvbnQtc2VtaWJvbGQgdGV4dC1ncmVlbi04MDAgZGFyazp0ZXh0LWdyZWVuLTIwMCBtYi00IGZsZXggaXRlbXMtY2VudGVyXCI+XG4gICAgICAgICAgICAgICAgICAgICAgPEJ1aWxkaW5nIGNsYXNzTmFtZT1cImgtNSB3LTUgbXItMiB0ZXh0LWdyZWVuLTYwMFwiIC8+XG4gICAgICAgICAgICAgICAgICAgICAgU2Nob29sIEluZm9ybWF0aW9uXG4gICAgICAgICAgICAgICAgICAgIDwvaDM+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS00XCI+XG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTJcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxMYWJlbCBodG1sRm9yPVwic2Nob29sX25hbWVcIiBjbGFzc05hbWU9XCJ0ZXh0LWdyZWVuLTgwMCBkYXJrOnRleHQtZ3JlZW4tMjAwXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIE5hbWUgb2YgdGhlIFNjaG9vbCAqXG4gICAgICAgICAgICAgICAgICAgICAgICA8L0xhYmVsPlxuICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJyZWxhdGl2ZVwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8QnVpbGRpbmcgY2xhc3NOYW1lPVwiYWJzb2x1dGUgbGVmdC0zIHRvcC0xLzIgdHJhbnNmb3JtIC10cmFuc2xhdGUteS0xLzIgaC00IHctNCB0ZXh0LWdyZWVuLTUwMFwiIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxJbnB1dFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGlkPVwic2Nob29sX25hbWVcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHZhbHVlPXtwcmltYXJ5Rm9ybURhdGEuc2Nob29sX25hbWV9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBzZXRQcmltYXJ5Rm9ybURhdGEocHJldiA9PiAoeyAuLi5wcmV2LCBzY2hvb2xfbmFtZTogZS50YXJnZXQudmFsdWUgfSkpfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiRW50ZXIgc2Nob29sIG5hbWVcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInBsLTEwIHJvdW5kZWQteGwgYm9yZGVyLWdyZWVuLTIwMCBkYXJrOmJvcmRlci1ncmVlbi03MDAgZm9jdXM6Ym9yZGVyLWdyZWVuLTUwMFwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgcmVxdWlyZWRcbiAgICAgICAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy0xIHNtOmdyaWQtY29scy0yIGdhcC0zIHNtOmdhcC00XCI+XG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktMlwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8TGFiZWwgaHRtbEZvcj1cImhlYWRtYXN0ZXJfbmFtZVwiIGNsYXNzTmFtZT1cInRleHQtZ3JlZW4tODAwIGRhcms6dGV4dC1ncmVlbi0yMDBcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBOYW1lIG9mIEhlYWRtYXN0ZXIvRGlyZWN0b3IgKlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8L0xhYmVsPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8SW5wdXRcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBpZD1cImhlYWRtYXN0ZXJfbmFtZVwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgdmFsdWU9e3ByaW1hcnlGb3JtRGF0YS5oZWFkbWFzdGVyX25hbWV9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBzZXRQcmltYXJ5Rm9ybURhdGEocHJldiA9PiAoeyAuLi5wcmV2LCBoZWFkbWFzdGVyX25hbWU6IGUudGFyZ2V0LnZhbHVlIH0pKX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIkVudGVyIGhlYWRtYXN0ZXIncyBuYW1lXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJyb3VuZGVkLXhsIGJvcmRlci1ncmVlbi0yMDAgZGFyazpib3JkZXItZ3JlZW4tNzAwIGZvY3VzOmJvcmRlci1ncmVlbi01MDBcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHJlcXVpcmVkXG4gICAgICAgICAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS0yXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxMYWJlbCBodG1sRm9yPVwic2Nob29sX2FjY291bnRfbnVtYmVyXCIgY2xhc3NOYW1lPVwidGV4dC1ncmVlbi04MDAgZGFyazp0ZXh0LWdyZWVuLTIwMFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIFNjaG9vbCBBY2NvdW50IE51bWJlciAqXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvTGFiZWw+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxJbnB1dFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGlkPVwic2Nob29sX2FjY291bnRfbnVtYmVyXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB2YWx1ZT17cHJpbWFyeUZvcm1EYXRhLnNjaG9vbF9hY2NvdW50X251bWJlcn1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHNldFByaW1hcnlGb3JtRGF0YShwcmV2ID0+ICh7IC4uLnByZXYsIHNjaG9vbF9hY2NvdW50X251bWJlcjogZS50YXJnZXQudmFsdWUgfSkpfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiRW50ZXIgYWNjb3VudCBudW1iZXIgZm9yIHNjaG9sYXJzaGlwIHBheW1lbnRcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInJvdW5kZWQteGwgYm9yZGVyLWdyZWVuLTIwMCBkYXJrOmJvcmRlci1ncmVlbi03MDAgZm9jdXM6Ym9yZGVyLWdyZWVuLTUwMFwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgcmVxdWlyZWRcbiAgICAgICAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgICAgICB7LyogRmluYW5jaWFsICYgQWRkaXRpb25hbCBJbmZvcm1hdGlvbiAqL31cbiAgICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZvbnQtc2VtaWJvbGQgdGV4dC1ncmVlbi04MDAgZGFyazp0ZXh0LWdyZWVuLTIwMCBtYi00IGZsZXggaXRlbXMtY2VudGVyXCI+XG4gICAgICAgICAgICAgICAgICAgICAgPENyZWRpdENhcmQgY2xhc3NOYW1lPVwiaC01IHctNSBtci0yIHRleHQtZ3JlZW4tNjAwXCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICBGaW5hbmNpYWwgJiBBZGRpdGlvbmFsIEluZm9ybWF0aW9uXG4gICAgICAgICAgICAgICAgICAgIDwvaDM+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS00XCI+XG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy0xIHNtOmdyaWQtY29scy0yIGdhcC0zIHNtOmdhcC00XCI+XG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktMlwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8TGFiZWwgaHRtbEZvcj1cImN1cnJlbnRfc2Nob29sX2ZlZVwiIGNsYXNzTmFtZT1cInRleHQtZ3JlZW4tODAwIGRhcms6dGV4dC1ncmVlbi0yMDBcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBDdXJyZW50IFNjaG9vbCBGZWUgQW1vdW50ICpcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9MYWJlbD5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJyZWxhdGl2ZVwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxDcmVkaXRDYXJkIGNsYXNzTmFtZT1cImFic29sdXRlIGxlZnQtMyB0b3AtMS8yIHRyYW5zZm9ybSAtdHJhbnNsYXRlLXktMS8yIGgtNCB3LTQgdGV4dC1ncmVlbi01MDBcIiAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxJbnB1dFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaWQ9XCJjdXJyZW50X3NjaG9vbF9mZWVcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdHlwZT1cIm51bWJlclwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICB2YWx1ZT17cHJpbWFyeUZvcm1EYXRhLmN1cnJlbnRfc2Nob29sX2ZlZX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gc2V0UHJpbWFyeUZvcm1EYXRhKHByZXYgPT4gKHsgLi4ucHJldiwgY3VycmVudF9zY2hvb2xfZmVlOiBlLnRhcmdldC52YWx1ZSB9KSl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIkVudGVyIGFtb3VudCBpbiBOYWlyYVwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJwbC0xMCByb3VuZGVkLXhsIGJvcmRlci1ncmVlbi0yMDAgZGFyazpib3JkZXItZ3JlZW4tNzAwIGZvY3VzOmJvcmRlci1ncmVlbi01MDBcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcmVxdWlyZWRcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTJcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPExhYmVsIGh0bWxGb3I9XCJzdHVkZW50X3BpY3R1cmVcIiBjbGFzc05hbWU9XCJ0ZXh0LWdyZWVuLTgwMCBkYXJrOnRleHQtZ3JlZW4tMjAwXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgVXBsb2FkIFN0dWRlbnQgUGljdHVyZSAqXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvTGFiZWw+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxJbnB1dFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGlkPVwic3R1ZGVudF9waWN0dXJlXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB0eXBlPVwiZmlsZVwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgYWNjZXB0PVwiaW1hZ2UvKlwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBoYW5kbGVGaWxlQ2hhbmdlKCdwcmltYXJ5JywgJ3N0dWRlbnRfcGljdHVyZScsIGUudGFyZ2V0LmZpbGVzPy5bMF0gfHwgbnVsbCl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwicm91bmRlZC14bCBib3JkZXItZ3JlZW4tMjAwIGRhcms6Ym9yZGVyLWdyZWVuLTcwMCBmb2N1czpib3JkZXItZ3JlZW4tNTAwIGZpbGU6bXItNCBmaWxlOnB5LTIgZmlsZTpweC00IGZpbGU6cm91bmRlZC1mdWxsIGZpbGU6Ym9yZGVyLTAgZmlsZTp0ZXh0LXNtIGZpbGU6Zm9udC1zZW1pYm9sZCBmaWxlOmJnLWdyZWVuLTUwIGZpbGU6dGV4dC1ncmVlbi03MDAgaG92ZXI6ZmlsZTpiZy1ncmVlbi0xMDBcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHJlcXVpcmVkXG4gICAgICAgICAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktMlwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgPExhYmVsIGh0bWxGb3I9XCJyZWFzb25fZm9yX3NjaG9sYXJzaGlwXCIgY2xhc3NOYW1lPVwidGV4dC1ncmVlbi04MDAgZGFyazp0ZXh0LWdyZWVuLTIwMFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICBSZWFzb24gZm9yIHRoZSBTY2hvbGFyc2hpcCAqXG4gICAgICAgICAgICAgICAgICAgICAgICA8L0xhYmVsPlxuICAgICAgICAgICAgICAgICAgICAgICAgPFRleHRhcmVhXG4gICAgICAgICAgICAgICAgICAgICAgICAgIGlkPVwicmVhc29uX2Zvcl9zY2hvbGFyc2hpcFwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgIHZhbHVlPXtwcmltYXJ5Rm9ybURhdGEucmVhc29uX2Zvcl9zY2hvbGFyc2hpcH1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBzZXRQcmltYXJ5Rm9ybURhdGEocHJldiA9PiAoeyAuLi5wcmV2LCByZWFzb25fZm9yX3NjaG9sYXJzaGlwOiBlLnRhcmdldC52YWx1ZSB9KSl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiUGxlYXNlIGV4cGxhaW4gd2h5IHlvdXIgY2hpbGQgbmVlZHMgdGhpcyBzY2hvbGFyc2hpcFwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInJvdW5kZWQteGwgYm9yZGVyLWdyZWVuLTIwMCBkYXJrOmJvcmRlci1ncmVlbi03MDAgZm9jdXM6Ym9yZGVyLWdyZWVuLTUwMFwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgIHJvd3M9ezR9XG4gICAgICAgICAgICAgICAgICAgICAgICAgIHJlcXVpcmVkXG4gICAgICAgICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS0yXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICA8TGFiZWwgaHRtbEZvcj1cInN1cHBvcnRpbmdfaW5mb3JtYXRpb25cIiBjbGFzc05hbWU9XCJ0ZXh0LWdyZWVuLTgwMCBkYXJrOnRleHQtZ3JlZW4tMjAwXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIEFueSBPdGhlciBTdXBwb3J0aW5nIEluZm9ybWF0aW9uXG4gICAgICAgICAgICAgICAgICAgICAgICA8L0xhYmVsPlxuICAgICAgICAgICAgICAgICAgICAgICAgPFRleHRhcmVhXG4gICAgICAgICAgICAgICAgICAgICAgICAgIGlkPVwic3VwcG9ydGluZ19pbmZvcm1hdGlvblwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgIHZhbHVlPXtwcmltYXJ5Rm9ybURhdGEuc3VwcG9ydGluZ19pbmZvcm1hdGlvbn1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBzZXRQcmltYXJ5Rm9ybURhdGEocHJldiA9PiAoeyAuLi5wcmV2LCBzdXBwb3J0aW5nX2luZm9ybWF0aW9uOiBlLnRhcmdldC52YWx1ZSB9KSl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiQW55IGFkZGl0aW9uYWwgaW5mb3JtYXRpb24gdGhhdCBzdXBwb3J0cyB5b3VyIGFwcGxpY2F0aW9uXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwicm91bmRlZC14bCBib3JkZXItZ3JlZW4tMjAwIGRhcms6Ym9yZGVyLWdyZWVuLTcwMCBmb2N1czpib3JkZXItZ3JlZW4tNTAwXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgcm93cz17M31cbiAgICAgICAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBqdXN0aWZ5LWNlbnRlciBwdC02IHNtOnB0LThcIj5cbiAgICAgICAgICAgICAgICAgICAgPEJ1dHRvblxuICAgICAgICAgICAgICAgICAgICAgIHR5cGU9XCJzdWJtaXRcIlxuICAgICAgICAgICAgICAgICAgICAgIGRpc2FibGVkPXtzdWJtaXR0aW5nIHx8ICF2YWxpZGF0ZUZvcm0oKX1cbiAgICAgICAgICAgICAgICAgICAgICBzaXplPVwibGdcIlxuICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctZnVsbCBzbTp3LWF1dG8gYmctZ3JlZW4tNjAwIGhvdmVyOmJnLWdyZWVuLTcwMCB0ZXh0LXdoaXRlIHB4LTYgc206cHgtMTIgcHktMyBzbTpweS00IHRleHQtYmFzZSBzbTp0ZXh0LWxnIGZvbnQtc2VtaWJvbGQgc2hhZG93LWxnIGhvdmVyOnNoYWRvdy14bCB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi0zMDBcIlxuICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAge3N1Ym1pdHRpbmcgPyAoXG4gICAgICAgICAgICAgICAgICAgICAgICA8PlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8TG9hZGVyMiBjbGFzc05hbWU9XCJtci0yIHNtOm1yLTMgaC01IHctNSBzbTpoLTYgc206dy02IGFuaW1hdGUtc3BpblwiIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtc20gc206dGV4dC1iYXNlXCI+U3VibWl0dGluZy4uLjwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvPlxuICAgICAgICAgICAgICAgICAgICAgICkgOiAoXG4gICAgICAgICAgICAgICAgICAgICAgICA8PlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8Q2hlY2tDaXJjbGUgY2xhc3NOYW1lPVwibXItMiBzbTptci0zIGgtNSB3LTUgc206aC02IHNtOnctNlwiIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtc20gc206dGV4dC1iYXNlXCI+U3VibWl0IFByaW1hcnkgU2Nob29sIEFwcGxpY2F0aW9uPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgICAgPC8+XG4gICAgICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8L2Zvcm0+XG4gICAgICAgICAgICAgIDwvVGFic0NvbnRlbnQ+XG5cbiAgICAgICAgICAgICAgey8qIFNlY29uZGFyeSBTY2hvb2wgRm9ybSAqL31cbiAgICAgICAgICAgICAgPFRhYnNDb250ZW50IHZhbHVlPVwic2Vjb25kYXJ5XCIgY2xhc3NOYW1lPVwic3BhY2UteS00IHNtOnNwYWNlLXktNiBtZDpzcGFjZS15LThcIj5cbiAgICAgICAgICAgICAgICA8Zm9ybSBvblN1Ym1pdD17aGFuZGxlU3VibWl0fSBjbGFzc05hbWU9XCJzcGFjZS15LTQgc206c3BhY2UteS02IG1kOnNwYWNlLXktOFwiPlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy1ncmVlbi01MCBwLTMgc206cC00IG1kOnAtNiByb3VuZGVkLWxnIGJvcmRlciBib3JkZXItZ3JlZW4tMjAwXCI+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1zdGFydCBzbTppdGVtcy1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgICAgICAgICA8QWxlcnRUcmlhbmdsZSBjbGFzc05hbWU9XCJoLTQgdy00IHNtOmgtNSBzbTp3LTUgdGV4dC1ncmVlbi02MDAgbXItMiBzbTptci0zIG10LTAuNSBzbTptdC0wIGZsZXgtc2hyaW5rLTBcIiAvPlxuICAgICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtZ3JlZW4tODAwIGZvbnQtbWVkaXVtIHRleHQtc20gc206dGV4dC1iYXNlXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICBUaGlzIGZvcm0gc2hvdWxkIGJlIGZpbGxlZCBieSB0aGUgU3R1ZGVudCBkaXJlY3RseVxuICAgICAgICAgICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgICAgey8qIFN0dWRlbnQgSW5mb3JtYXRpb24gKi99XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktNlwiPlxuICAgICAgICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZvbnQtc2VtaWJvbGQgdGV4dC1ncmVlbi04MDAgZGFyazp0ZXh0LWdyZWVuLTIwMCBtYi00IGZsZXggaXRlbXMtY2VudGVyXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICA8VXNlciBjbGFzc05hbWU9XCJoLTUgdy01IG1yLTIgdGV4dC1ncmVlbi02MDBcIiAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgU3R1ZGVudCBJbmZvcm1hdGlvblxuICAgICAgICAgICAgICAgICAgICAgIDwvaDM+XG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTRcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS0yXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxMYWJlbCBodG1sRm9yPVwic2VjX3N0dWRlbnRfZnVsbF9uYW1lXCIgY2xhc3NOYW1lPVwidGV4dC1ncmVlbi04MDAgZGFyazp0ZXh0LWdyZWVuLTIwMFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIFN0dWRlbnQgRnVsbCBOYW1lICpcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9MYWJlbD5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJyZWxhdGl2ZVwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxVc2VyIGNsYXNzTmFtZT1cImFic29sdXRlIGxlZnQtMyB0b3AtMS8yIHRyYW5zZm9ybSAtdHJhbnNsYXRlLXktMS8yIGgtNCB3LTQgdGV4dC1ncmVlbi01MDBcIiAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxJbnB1dFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaWQ9XCJzZWNfc3R1ZGVudF9mdWxsX25hbWVcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdmFsdWU9e3NlY29uZGFyeUZvcm1EYXRhLnN0dWRlbnRfZnVsbF9uYW1lfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBzZXRTZWNvbmRhcnlGb3JtRGF0YShwcmV2ID0+ICh7IC4uLnByZXYsIHN0dWRlbnRfZnVsbF9uYW1lOiBlLnRhcmdldC52YWx1ZSB9KSl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIkVudGVyIHlvdXIgZnVsbCBuYW1lXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInBsLTEwIHJvdW5kZWQteGwgYm9yZGVyLWdyZWVuLTIwMCBkYXJrOmJvcmRlci1ncmVlbi03MDAgZm9jdXM6Ym9yZGVyLWdyZWVuLTUwMFwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICByZXF1aXJlZFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMSBzbTpncmlkLWNvbHMtMiBsZzpncmlkLWNvbHMtMyBnYXAtMyBzbTpnYXAtNFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktMlwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxMYWJlbCBodG1sRm9yPVwic2VjX2FnZVwiIGNsYXNzTmFtZT1cInRleHQtZ3JlZW4tODAwIGRhcms6dGV4dC1ncmVlbi0yMDBcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIEFnZSAqXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9MYWJlbD5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8SW5wdXRcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGlkPVwic2VjX2FnZVwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICB0eXBlPVwibnVtYmVyXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHZhbHVlPXtzZWNvbmRhcnlGb3JtRGF0YS5hZ2V9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHNldFNlY29uZGFyeUZvcm1EYXRhKHByZXYgPT4gKHsgLi4ucHJldiwgYWdlOiBlLnRhcmdldC52YWx1ZSB9KSl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIllvdXIgYWdlXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInJvdW5kZWQteGwgYm9yZGVyLWdyZWVuLTIwMCBkYXJrOmJvcmRlci1ncmVlbi03MDAgZm9jdXM6Ym9yZGVyLWdyZWVuLTUwMFwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICByZXF1aXJlZFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktMlwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxMYWJlbCBodG1sRm9yPVwic2VjX2NsYXNzXCIgY2xhc3NOYW1lPVwidGV4dC1ncmVlbi04MDAgZGFyazp0ZXh0LWdyZWVuLTIwMFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgQ2xhc3MgKlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvTGFiZWw+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPFNlbGVjdCB2YWx1ZT17c2Vjb25kYXJ5Rm9ybURhdGEuY2xhc3N9IG9uVmFsdWVDaGFuZ2U9eyh2YWx1ZSkgPT4gc2V0U2Vjb25kYXJ5Rm9ybURhdGEocHJldiA9PiAoeyAuLi5wcmV2LCBjbGFzczogdmFsdWUgfSkpfT5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxTZWxlY3RUcmlnZ2VyIGNsYXNzTmFtZT1cInJvdW5kZWQteGwgYm9yZGVyLWdyZWVuLTIwMCBkYXJrOmJvcmRlci1ncmVlbi03MDAgZm9jdXM6Ym9yZGVyLWdyZWVuLTUwMFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8U2VsZWN0VmFsdWUgcGxhY2Vob2xkZXI9XCJTZWxlY3QgY2xhc3NcIiAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9TZWxlY3RUcmlnZ2VyPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPFNlbGVjdENvbnRlbnQ+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxTZWxlY3RJdGVtIHZhbHVlPVwiSlNTIDFcIj5KU1MgMTwvU2VsZWN0SXRlbT5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPFNlbGVjdEl0ZW0gdmFsdWU9XCJKU1MgMlwiPkpTUyAyPC9TZWxlY3RJdGVtPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8U2VsZWN0SXRlbSB2YWx1ZT1cIkpTUyAzXCI+SlNTIDM8L1NlbGVjdEl0ZW0+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxTZWxlY3RJdGVtIHZhbHVlPVwiU1MgMVwiPlNTIDE8L1NlbGVjdEl0ZW0+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxTZWxlY3RJdGVtIHZhbHVlPVwiU1MgMlwiPlNTIDI8L1NlbGVjdEl0ZW0+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxTZWxlY3RJdGVtIHZhbHVlPVwiU1MgM1wiPlNTIDM8L1NlbGVjdEl0ZW0+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L1NlbGVjdENvbnRlbnQ+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9TZWxlY3Q+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktMlwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxMYWJlbCBodG1sRm9yPVwic2VjX3BhcmVudF9waG9uZVwiIGNsYXNzTmFtZT1cInRleHQtZ3JlZW4tODAwIGRhcms6dGV4dC1ncmVlbi0yMDBcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIFBhcmVudCdzIFBob25lIE51bWJlciAqXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9MYWJlbD5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInJlbGF0aXZlXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8UGhvbmUgY2xhc3NOYW1lPVwiYWJzb2x1dGUgbGVmdC0zIHRvcC0xLzIgdHJhbnNmb3JtIC10cmFuc2xhdGUteS0xLzIgaC00IHctNCB0ZXh0LWdyZWVuLTUwMFwiIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8SW5wdXRcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaWQ9XCJzZWNfcGFyZW50X3Bob25lXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdHlwZT1cInRlbFwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHZhbHVlPXtzZWNvbmRhcnlGb3JtRGF0YS5wYXJlbnRfcGhvbmV9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gc2V0U2Vjb25kYXJ5Rm9ybURhdGEocHJldiA9PiAoeyAuLi5wcmV2LCBwYXJlbnRfcGhvbmU6IGUudGFyZ2V0LnZhbHVlIH0pKX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCJFbnRlciBwYXJlbnQncyBwaG9uZSBudW1iZXJcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJwbC0xMCByb3VuZGVkLXhsIGJvcmRlci1ncmVlbi0yMDAgZGFyazpib3JkZXItZ3JlZW4tNzAwIGZvY3VzOmJvcmRlci1ncmVlbi01MDBcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICByZXF1aXJlZFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgICAgICAgey8qIEFkZHJlc3MgYW5kIFNjaG9vbCBJbmZvcm1hdGlvbiAqL31cbiAgICAgICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LXNlbWlib2xkIHRleHQtZ3JlZW4tODAwIGRhcms6dGV4dC1ncmVlbi0yMDAgbWItNCBmbGV4IGl0ZW1zLWNlbnRlclwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgPE1hcFBpbiBjbGFzc05hbWU9XCJoLTUgdy01IG1yLTIgdGV4dC1ncmVlbi02MDBcIiAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgQWRkcmVzcyAmIFNjaG9vbCBJbmZvcm1hdGlvblxuICAgICAgICAgICAgICAgICAgICAgIDwvaDM+XG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTRcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS0yXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxMYWJlbCBodG1sRm9yPVwic2VjX2FkZHJlc3NcIiBjbGFzc05hbWU9XCJ0ZXh0LWdyZWVuLTgwMCBkYXJrOnRleHQtZ3JlZW4tMjAwXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgQWRkcmVzcyAqXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvTGFiZWw+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicmVsYXRpdmVcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8TWFwUGluIGNsYXNzTmFtZT1cImFic29sdXRlIGxlZnQtMyB0b3AtMyBoLTQgdy00IHRleHQtZ3JlZW4tNTAwXCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8VGV4dGFyZWFcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGlkPVwic2VjX2FkZHJlc3NcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdmFsdWU9e3NlY29uZGFyeUZvcm1EYXRhLmFkZHJlc3N9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHNldFNlY29uZGFyeUZvcm1EYXRhKHByZXYgPT4gKHsgLi4ucHJldiwgYWRkcmVzczogZS50YXJnZXQudmFsdWUgfSkpfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCJFbnRlciB5b3VyIGNvbXBsZXRlIGFkZHJlc3NcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwicGwtMTAgcm91bmRlZC14bCBib3JkZXItZ3JlZW4tMjAwIGRhcms6Ym9yZGVyLWdyZWVuLTcwMCBmb2N1czpib3JkZXItZ3JlZW4tNTAwXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHJvd3M9ezN9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICByZXF1aXJlZFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMSBzbTpncmlkLWNvbHMtMiBnYXAtMyBzbTpnYXAtNFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktMlwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxMYWJlbCBodG1sRm9yPVwic2VjX3NjaG9vbF9uYW1lXCIgY2xhc3NOYW1lPVwidGV4dC1ncmVlbi04MDAgZGFyazp0ZXh0LWdyZWVuLTIwMFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgU2Nob29sIE5hbWUgKlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvTGFiZWw+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJyZWxhdGl2ZVwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPEJ1aWxkaW5nIGNsYXNzTmFtZT1cImFic29sdXRlIGxlZnQtMyB0b3AtMS8yIHRyYW5zZm9ybSAtdHJhbnNsYXRlLXktMS8yIGgtNCB3LTQgdGV4dC1ncmVlbi01MDBcIiAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPElucHV0XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGlkPVwic2VjX3NjaG9vbF9uYW1lXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdmFsdWU9e3NlY29uZGFyeUZvcm1EYXRhLnNjaG9vbF9uYW1lfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHNldFNlY29uZGFyeUZvcm1EYXRhKHByZXYgPT4gKHsgLi4ucHJldiwgc2Nob29sX25hbWU6IGUudGFyZ2V0LnZhbHVlIH0pKX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCJFbnRlciB5b3VyIHNjaG9vbCBuYW1lXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwicGwtMTAgcm91bmRlZC14bCBib3JkZXItZ3JlZW4tMjAwIGRhcms6Ym9yZGVyLWdyZWVuLTcwMCBmb2N1czpib3JkZXItZ3JlZW4tNTAwXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcmVxdWlyZWRcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS0yXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPExhYmVsIGh0bWxGb3I9XCJzZWNfcHJpbmNpcGFsX25hbWVcIiBjbGFzc05hbWU9XCJ0ZXh0LWdyZWVuLTgwMCBkYXJrOnRleHQtZ3JlZW4tMjAwXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBTY2hvb2wgUHJpbmNpcGFsIG9yIERpcmVjdG9yIE5hbWUgKlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvTGFiZWw+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPElucHV0XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBpZD1cInNlY19wcmluY2lwYWxfbmFtZVwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICB2YWx1ZT17c2Vjb25kYXJ5Rm9ybURhdGEucHJpbmNpcGFsX25hbWV9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHNldFNlY29uZGFyeUZvcm1EYXRhKHByZXYgPT4gKHsgLi4ucHJldiwgcHJpbmNpcGFsX25hbWU6IGUudGFyZ2V0LnZhbHVlIH0pKX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiRW50ZXIgcHJpbmNpcGFsJ3MgbmFtZVwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJyb3VuZGVkLXhsIGJvcmRlci1ncmVlbi0yMDAgZGFyazpib3JkZXItZ3JlZW4tNzAwIGZvY3VzOmJvcmRlci1ncmVlbi01MDBcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcmVxdWlyZWRcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgICAgICB7LyogRmluYW5jaWFsIEluZm9ybWF0aW9uICovfVxuICAgICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQtbGcgZm9udC1zZW1pYm9sZCB0ZXh0LWdyZWVuLTgwMCBkYXJrOnRleHQtZ3JlZW4tMjAwIG1iLTQgZmxleCBpdGVtcy1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgICAgICAgICA8Q3JlZGl0Q2FyZCBjbGFzc05hbWU9XCJoLTUgdy01IG1yLTIgdGV4dC1ncmVlbi02MDBcIiAvPlxuICAgICAgICAgICAgICAgICAgICAgIEZpbmFuY2lhbCBJbmZvcm1hdGlvblxuICAgICAgICAgICAgICAgICAgICA8L2gzPlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktNFwiPlxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMSBzbTpncmlkLWNvbHMtMiBnYXAtMyBzbTpnYXAtNFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTJcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPExhYmVsIGh0bWxGb3I9XCJzZWNfcHJpbmNpcGFsX2FjY291bnRfbnVtYmVyXCIgY2xhc3NOYW1lPVwidGV4dC1ncmVlbi04MDAgZGFyazp0ZXh0LWdyZWVuLTIwMFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIFByaW5jaXBhbCBvciBGaW5hbmNpYWwgT2ZmaWNlciBBY2NvdW50IE51bWJlciAqXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvTGFiZWw+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicmVsYXRpdmVcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8Q3JlZGl0Q2FyZCBjbGFzc05hbWU9XCJhYnNvbHV0ZSBsZWZ0LTMgdG9wLTEvMiB0cmFuc2Zvcm0gLXRyYW5zbGF0ZS15LTEvMiBoLTQgdy00IHRleHQtZ3JlZW4tNTAwXCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8SW5wdXRcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGlkPVwic2VjX3ByaW5jaXBhbF9hY2NvdW50X251bWJlclwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICB2YWx1ZT17c2Vjb25kYXJ5Rm9ybURhdGEucHJpbmNpcGFsX2FjY291bnRfbnVtYmVyfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBzZXRTZWNvbmRhcnlGb3JtRGF0YShwcmV2ID0+ICh7IC4uLnByZXYsIHByaW5jaXBhbF9hY2NvdW50X251bWJlcjogZS50YXJnZXQudmFsdWUgfSkpfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCJFbnRlciBhY2NvdW50IG51bWJlciBmb3Igc2Nob2xhcnNoaXAgcGF5bWVudFwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJwbC0xMCByb3VuZGVkLXhsIGJvcmRlci1ncmVlbi0yMDAgZGFyazpib3JkZXItZ3JlZW4tNzAwIGZvY3VzOmJvcmRlci1ncmVlbi01MDBcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcmVxdWlyZWRcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktMlwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8TGFiZWwgaHRtbEZvcj1cInNlY19zY2hvb2xfZmVlX2Ftb3VudFwiIGNsYXNzTmFtZT1cInRleHQtZ3JlZW4tODAwIGRhcms6dGV4dC1ncmVlbi0yMDBcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBTY2hvb2wgRmVlIEFtb3VudCAqXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvTGFiZWw+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxJbnB1dFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGlkPVwic2VjX3NjaG9vbF9mZWVfYW1vdW50XCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB0eXBlPVwibnVtYmVyXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB2YWx1ZT17c2Vjb25kYXJ5Rm9ybURhdGEuc2Nob29sX2ZlZV9hbW91bnR9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBzZXRTZWNvbmRhcnlGb3JtRGF0YShwcmV2ID0+ICh7IC4uLnByZXYsIHNjaG9vbF9mZWVfYW1vdW50OiBlLnRhcmdldC52YWx1ZSB9KSl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCJFbnRlciBhbW91bnQgaW4gTmFpcmFcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInJvdW5kZWQteGwgYm9yZGVyLWdyZWVuLTIwMCBkYXJrOmJvcmRlci1ncmVlbi03MDAgZm9jdXM6Ym9yZGVyLWdyZWVuLTUwMFwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgcmVxdWlyZWRcbiAgICAgICAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgICAgICB7LyogUGljdHVyZSBhbmQgQXBwbGljYXRpb24gUmVhc29uICovfVxuICAgICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQtbGcgZm9udC1zZW1pYm9sZCB0ZXh0LWdyZWVuLTgwMCBkYXJrOnRleHQtZ3JlZW4tMjAwIG1iLTQgZmxleCBpdGVtcy1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgICAgICAgICA8RmlsZVVwIGNsYXNzTmFtZT1cImgtNSB3LTUgbXItMiB0ZXh0LWdyZWVuLTYwMFwiIC8+XG4gICAgICAgICAgICAgICAgICAgICAgUGljdHVyZSAmIEFwcGxpY2F0aW9uIFJlYXNvblxuICAgICAgICAgICAgICAgICAgICA8L2gzPlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktNFwiPlxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS0yXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICA8TGFiZWwgaHRtbEZvcj1cInNlY19zdHVkZW50X3BpY3R1cmVcIiBjbGFzc05hbWU9XCJ0ZXh0LWdyZWVuLTgwMCBkYXJrOnRleHQtZ3JlZW4tMjAwXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIFVwbG9hZCBTdHVkZW50IFBpY3R1cmUgKlxuICAgICAgICAgICAgICAgICAgICAgICAgPC9MYWJlbD5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxJbnB1dFxuICAgICAgICAgICAgICAgICAgICAgICAgICBpZD1cInNlY19zdHVkZW50X3BpY3R1cmVcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICB0eXBlPVwiZmlsZVwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgIGFjY2VwdD1cImltYWdlLypcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IGhhbmRsZUZpbGVDaGFuZ2UoJ3NlY29uZGFyeScsICdzdHVkZW50X3BpY3R1cmUnLCBlLnRhcmdldC5maWxlcz8uWzBdIHx8IG51bGwpfVxuICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJyb3VuZGVkLXhsIGJvcmRlci1ncmVlbi0yMDAgZGFyazpib3JkZXItZ3JlZW4tNzAwIGZvY3VzOmJvcmRlci1ncmVlbi01MDAgZmlsZTptci00IGZpbGU6cHktMiBmaWxlOnB4LTQgZmlsZTpyb3VuZGVkLWZ1bGwgZmlsZTpib3JkZXItMCBmaWxlOnRleHQtc20gZmlsZTpmb250LXNlbWlib2xkIGZpbGU6YmctZ3JlZW4tNTAgZmlsZTp0ZXh0LWdyZWVuLTcwMCBob3ZlcjpmaWxlOmJnLWdyZWVuLTEwMFwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgIHJlcXVpcmVkXG4gICAgICAgICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTJcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxMYWJlbCBodG1sRm9yPVwic2VjX3JlYXNvbl9mb3Jfc2Nob2xhcnNoaXBcIiBjbGFzc05hbWU9XCJ0ZXh0LWdyZWVuLTgwMCBkYXJrOnRleHQtZ3JlZW4tMjAwXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIFJlYXNvbiBmb3IgdGhlIFNjaG9sYXJzaGlwICpcbiAgICAgICAgICAgICAgICAgICAgICAgIDwvTGFiZWw+XG4gICAgICAgICAgICAgICAgICAgICAgICA8VGV4dGFyZWFcbiAgICAgICAgICAgICAgICAgICAgICAgICAgaWQ9XCJzZWNfcmVhc29uX2Zvcl9zY2hvbGFyc2hpcFwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgIHZhbHVlPXtzZWNvbmRhcnlGb3JtRGF0YS5yZWFzb25fZm9yX3NjaG9sYXJzaGlwfVxuICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHNldFNlY29uZGFyeUZvcm1EYXRhKHByZXYgPT4gKHsgLi4ucHJldiwgcmVhc29uX2Zvcl9zY2hvbGFyc2hpcDogZS50YXJnZXQudmFsdWUgfSkpfVxuICAgICAgICAgICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIlBsZWFzZSBleHBsYWluIHdoeSB5b3UgbmVlZCB0aGlzIHNjaG9sYXJzaGlwXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwicm91bmRlZC14bCBib3JkZXItZ3JlZW4tMjAwIGRhcms6Ym9yZGVyLWdyZWVuLTcwMCBmb2N1czpib3JkZXItZ3JlZW4tNTAwXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgcm93cz17NH1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgcmVxdWlyZWRcbiAgICAgICAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBqdXN0aWZ5LWNlbnRlciBwdC02IHNtOnB0LThcIj5cbiAgICAgICAgICAgICAgICAgICAgPEJ1dHRvblxuICAgICAgICAgICAgICAgICAgICAgIHR5cGU9XCJzdWJtaXRcIlxuICAgICAgICAgICAgICAgICAgICAgIGRpc2FibGVkPXtzdWJtaXR0aW5nIHx8ICF2YWxpZGF0ZUZvcm0oKX1cbiAgICAgICAgICAgICAgICAgICAgICBzaXplPVwibGdcIlxuICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctZnVsbCBzbTp3LWF1dG8gYmctZ3JhZGllbnQtdG8tciBmcm9tLWdyZWVuLTYwMCB2aWEtZW1lcmFsZC02MDAgdG8tdGVhbC02MDAgaG92ZXI6ZnJvbS1ncmVlbi03MDAgaG92ZXI6dmlhLWVtZXJhbGQtNzAwIGhvdmVyOnRvLXRlYWwtNzAwIHRleHQtd2hpdGUgcHgtNiBzbTpweC0xMiBweS0zIHNtOnB5LTQgdGV4dC1iYXNlIHNtOnRleHQtbGcgZm9udC1ib2xkIHNoYWRvdy1sZyBob3ZlcjpzaGFkb3cteGwgdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tMzAwXCJcbiAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgIHtzdWJtaXR0aW5nID8gKFxuICAgICAgICAgICAgICAgICAgICAgICAgPD5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPExvYWRlcjIgY2xhc3NOYW1lPVwibXItMiBzbTptci0zIGgtNSB3LTUgc206aC02IHNtOnctNiBhbmltYXRlLXNwaW5cIiAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXNtIHNtOnRleHQtYmFzZVwiPlN1Ym1pdHRpbmcuLi48L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgICA8Lz5cbiAgICAgICAgICAgICAgICAgICAgICApIDogKFxuICAgICAgICAgICAgICAgICAgICAgICAgPD5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPENoZWNrQ2lyY2xlIGNsYXNzTmFtZT1cIm1yLTIgc206bXItMyBoLTUgdy01IHNtOmgtNiBzbTp3LTZcIiAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXNtIHNtOnRleHQtYmFzZVwiPlN1Ym1pdCBTZWNvbmRhcnkgU2Nob29sIEFwcGxpY2F0aW9uPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgICAgPC8+XG4gICAgICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8L2Zvcm0+XG4gICAgICAgICAgICAgIDwvVGFic0NvbnRlbnQ+XG5cbiAgICAgICAgICAgICAgey8qIFVuaXZlcnNpdHkgRm9ybSAqL31cbiAgICAgICAgICAgICAgPFRhYnNDb250ZW50IHZhbHVlPVwidW5pdmVyc2l0eVwiIGNsYXNzTmFtZT1cInNwYWNlLXktNCBzbTpzcGFjZS15LTYgbWQ6c3BhY2UteS04XCI+XG4gICAgICAgICAgICAgICAgPGZvcm0gb25TdWJtaXQ9e2hhbmRsZVN1Ym1pdH0gY2xhc3NOYW1lPVwic3BhY2UteS00IHNtOnNwYWNlLXktNiBtZDpzcGFjZS15LThcIj5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctZ3JlZW4tNTAgcC0zIHNtOnAtNCBtZDpwLTYgcm91bmRlZC1sZyBib3JkZXIgYm9yZGVyLWdyZWVuLTIwMFwiPlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtc3RhcnQgc206aXRlbXMtY2VudGVyXCI+XG4gICAgICAgICAgICAgICAgICAgICAgPEFsZXJ0VHJpYW5nbGUgY2xhc3NOYW1lPVwiaC00IHctNCBzbTpoLTUgc206dy01IHRleHQtZ3JlZW4tNjAwIG1yLTIgc206bXItMyBtdC0wLjUgc206bXQtMCBmbGV4LXNocmluay0wXCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWdyZWVuLTgwMCBmb250LW1lZGl1bSB0ZXh0LXNtIHNtOnRleHQtYmFzZVwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgVGhpcyBmb3JtIHNob3VsZCBiZSBmaWxsZWQgYnkgdGhlIFVuaXZlcnNpdHkgU3R1ZGVudCBkaXJlY3RseVxuICAgICAgICAgICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgICAgey8qIFBlcnNvbmFsIEluZm9ybWF0aW9uICovfVxuICAgICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQtbGcgZm9udC1zZW1pYm9sZCB0ZXh0LWdyZWVuLTgwMCBkYXJrOnRleHQtZ3JlZW4tMjAwIG1iLTQgZmxleCBpdGVtcy1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgICAgICAgICA8VXNlciBjbGFzc05hbWU9XCJoLTUgdy01IG1yLTIgdGV4dC1ncmVlbi02MDBcIiAvPlxuICAgICAgICAgICAgICAgICAgICAgIFBlcnNvbmFsIEluZm9ybWF0aW9uXG4gICAgICAgICAgICAgICAgICAgIDwvaDM+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS00XCI+XG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTJcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxMYWJlbCBodG1sRm9yPVwidW5pX2Z1bGxfbmFtZVwiIGNsYXNzTmFtZT1cInRleHQtZ3JlZW4tODAwIGRhcms6dGV4dC1ncmVlbi0yMDBcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgRnVsbCBOYW1lICpcbiAgICAgICAgICAgICAgICAgICAgICAgIDwvTGFiZWw+XG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInJlbGF0aXZlXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxVc2VyIGNsYXNzTmFtZT1cImFic29sdXRlIGxlZnQtMyB0b3AtMS8yIHRyYW5zZm9ybSAtdHJhbnNsYXRlLXktMS8yIGgtNCB3LTQgdGV4dC1ncmVlbi01MDBcIiAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8SW5wdXRcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBpZD1cInVuaV9mdWxsX25hbWVcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHZhbHVlPXt1bml2ZXJzaXR5Rm9ybURhdGEuZnVsbF9uYW1lfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gc2V0VW5pdmVyc2l0eUZvcm1EYXRhKHByZXYgPT4gKHsgLi4ucHJldiwgZnVsbF9uYW1lOiBlLnRhcmdldC52YWx1ZSB9KSl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCJFbnRlciB5b3VyIGZ1bGwgbmFtZVwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwicGwtMTAgcm91bmRlZC14bCBib3JkZXItZ3JlZW4tMjAwIGRhcms6Ym9yZGVyLWdyZWVuLTcwMCBmb2N1czpib3JkZXItZ3JlZW4tNTAwXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICByZXF1aXJlZFxuICAgICAgICAgICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTEgc206Z3JpZC1jb2xzLTIgZ2FwLTMgc206Z2FwLTRcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS0yXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxMYWJlbCBodG1sRm9yPVwidW5pX2FnZVwiIGNsYXNzTmFtZT1cInRleHQtZ3JlZW4tODAwIGRhcms6dGV4dC1ncmVlbi0yMDBcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBBZ2UgKlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8L0xhYmVsPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8SW5wdXRcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBpZD1cInVuaV9hZ2VcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHR5cGU9XCJudW1iZXJcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHZhbHVlPXt1bml2ZXJzaXR5Rm9ybURhdGEuYWdlfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gc2V0VW5pdmVyc2l0eUZvcm1EYXRhKHByZXYgPT4gKHsgLi4ucHJldiwgYWdlOiBlLnRhcmdldC52YWx1ZSB9KSl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCJZb3VyIGFnZVwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwicm91bmRlZC14bCBib3JkZXItZ3JlZW4tMjAwIGRhcms6Ym9yZGVyLWdyZWVuLTcwMCBmb2N1czpib3JkZXItZ3JlZW4tNTAwXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICByZXF1aXJlZFxuICAgICAgICAgICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS0yXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxMYWJlbCBodG1sRm9yPVwidW5pX3Bob25lX251bWJlclwiIGNsYXNzTmFtZT1cInRleHQtZ3JlZW4tODAwIGRhcms6dGV4dC1ncmVlbi0yMDBcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBQaG9uZSBOdW1iZXIgKlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8L0xhYmVsPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInJlbGF0aXZlXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPFBob25lIGNsYXNzTmFtZT1cImFic29sdXRlIGxlZnQtMyB0b3AtMS8yIHRyYW5zZm9ybSAtdHJhbnNsYXRlLXktMS8yIGgtNCB3LTQgdGV4dC1ncmVlbi01MDBcIiAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxJbnB1dFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaWQ9XCJ1bmlfcGhvbmVfbnVtYmVyXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHR5cGU9XCJ0ZWxcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdmFsdWU9e3VuaXZlcnNpdHlGb3JtRGF0YS5waG9uZV9udW1iZXJ9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHNldFVuaXZlcnNpdHlGb3JtRGF0YShwcmV2ID0+ICh7IC4uLnByZXYsIHBob25lX251bWJlcjogZS50YXJnZXQudmFsdWUgfSkpfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCJFbnRlciB5b3VyIHBob25lIG51bWJlclwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJwbC0xMCByb3VuZGVkLXhsIGJvcmRlci1ncmVlbi0yMDAgZGFyazpib3JkZXItZ3JlZW4tNzAwIGZvY3VzOmJvcmRlci1ncmVlbi01MDBcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcmVxdWlyZWRcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTJcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxMYWJlbCBodG1sRm9yPVwidW5pX2VtYWlsX2FkZHJlc3NcIiBjbGFzc05hbWU9XCJ0ZXh0LWdyZWVuLTgwMCBkYXJrOnRleHQtZ3JlZW4tMjAwXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIEVtYWlsIEFkZHJlc3MgKlxuICAgICAgICAgICAgICAgICAgICAgICAgPC9MYWJlbD5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicmVsYXRpdmVcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPE1haWwgY2xhc3NOYW1lPVwiYWJzb2x1dGUgbGVmdC0zIHRvcC0xLzIgdHJhbnNmb3JtIC10cmFuc2xhdGUteS0xLzIgaC00IHctNCB0ZXh0LWdyZWVuLTUwMFwiIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxJbnB1dFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGlkPVwidW5pX2VtYWlsX2FkZHJlc3NcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHR5cGU9XCJlbWFpbFwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgdmFsdWU9e3VuaXZlcnNpdHlGb3JtRGF0YS5lbWFpbF9hZGRyZXNzfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gc2V0VW5pdmVyc2l0eUZvcm1EYXRhKHByZXYgPT4gKHsgLi4ucHJldiwgZW1haWxfYWRkcmVzczogZS50YXJnZXQudmFsdWUgfSkpfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiRW50ZXIgeW91ciBlbWFpbCBhZGRyZXNzXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJwbC0xMCByb3VuZGVkLXhsIGJvcmRlci1ncmVlbi0yMDAgZGFyazpib3JkZXItZ3JlZW4tNzAwIGZvY3VzOmJvcmRlci1ncmVlbi01MDBcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHJlcXVpcmVkXG4gICAgICAgICAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgICAgey8qIEFjYWRlbWljIEluZm9ybWF0aW9uICovfVxuICAgICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQtbGcgZm9udC1zZW1pYm9sZCB0ZXh0LWdyZWVuLTgwMCBkYXJrOnRleHQtZ3JlZW4tMjAwIG1iLTQgZmxleCBpdGVtcy1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgICAgICAgICA8R3JhZHVhdGlvbkNhcCBjbGFzc05hbWU9XCJoLTUgdy01IG1yLTIgdGV4dC1ncmVlbi02MDBcIiAvPlxuICAgICAgICAgICAgICAgICAgICAgIEFjYWRlbWljIEluZm9ybWF0aW9uXG4gICAgICAgICAgICAgICAgICAgIDwvaDM+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS00XCI+XG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTJcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxMYWJlbCBodG1sRm9yPVwidW5pX2NvdXJzZV9vZl9zdHVkeVwiIGNsYXNzTmFtZT1cInRleHQtZ3JlZW4tODAwIGRhcms6dGV4dC1ncmVlbi0yMDBcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgQ291cnNlIG9mIFN0dWR5ICpcbiAgICAgICAgICAgICAgICAgICAgICAgIDwvTGFiZWw+XG4gICAgICAgICAgICAgICAgICAgICAgICA8SW5wdXRcbiAgICAgICAgICAgICAgICAgICAgICAgICAgaWQ9XCJ1bmlfY291cnNlX29mX3N0dWR5XCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgdmFsdWU9e3VuaXZlcnNpdHlGb3JtRGF0YS5jb3Vyc2Vfb2Zfc3R1ZHl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gc2V0VW5pdmVyc2l0eUZvcm1EYXRhKHByZXYgPT4gKHsgLi4ucHJldiwgY291cnNlX29mX3N0dWR5OiBlLnRhcmdldC52YWx1ZSB9KSl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiRW50ZXIgeW91ciBjb3Vyc2Ugb2Ygc3R1ZHlcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJyb3VuZGVkLXhsIGJvcmRlci1ncmVlbi0yMDAgZGFyazpib3JkZXItZ3JlZW4tNzAwIGZvY3VzOmJvcmRlci1ncmVlbi01MDBcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICByZXF1aXJlZFxuICAgICAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMSBzbTpncmlkLWNvbHMtMiBnYXAtMyBzbTpnYXAtNFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTJcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPExhYmVsIGh0bWxGb3I9XCJ1bmlfY3VycmVudF9sZXZlbFwiIGNsYXNzTmFtZT1cInRleHQtZ3JlZW4tODAwIGRhcms6dGV4dC1ncmVlbi0yMDBcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBDdXJyZW50IExldmVsICpcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9MYWJlbD5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPFNlbGVjdCB2YWx1ZT17dW5pdmVyc2l0eUZvcm1EYXRhLmN1cnJlbnRfbGV2ZWx9IG9uVmFsdWVDaGFuZ2U9eyh2YWx1ZSkgPT4gc2V0VW5pdmVyc2l0eUZvcm1EYXRhKHByZXYgPT4gKHsgLi4ucHJldiwgY3VycmVudF9sZXZlbDogdmFsdWUgfSkpfT5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8U2VsZWN0VHJpZ2dlciBjbGFzc05hbWU9XCJyb3VuZGVkLXhsIGJvcmRlci1ncmVlbi0yMDAgZGFyazpib3JkZXItZ3JlZW4tNzAwIGZvY3VzOmJvcmRlci1ncmVlbi01MDBcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxTZWxlY3RWYWx1ZSBwbGFjZWhvbGRlcj1cIlNlbGVjdCB5b3VyIGN1cnJlbnQgbGV2ZWxcIiAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvU2VsZWN0VHJpZ2dlcj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8U2VsZWN0Q29udGVudD5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxTZWxlY3RJdGVtIHZhbHVlPVwiMTAwTFwiPjEwMCBMZXZlbDwvU2VsZWN0SXRlbT5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxTZWxlY3RJdGVtIHZhbHVlPVwiMjAwTFwiPjIwMCBMZXZlbDwvU2VsZWN0SXRlbT5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxTZWxlY3RJdGVtIHZhbHVlPVwiMzAwTFwiPjMwMCBMZXZlbDwvU2VsZWN0SXRlbT5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxTZWxlY3RJdGVtIHZhbHVlPVwiNDAwTFwiPjQwMCBMZXZlbDwvU2VsZWN0SXRlbT5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxTZWxlY3RJdGVtIHZhbHVlPVwiNTAwTFwiPjUwMCBMZXZlbDwvU2VsZWN0SXRlbT5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxTZWxlY3RJdGVtIHZhbHVlPVwiNjAwTFwiPjYwMCBMZXZlbDwvU2VsZWN0SXRlbT5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L1NlbGVjdENvbnRlbnQ+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvU2VsZWN0PlxuICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS0yXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxMYWJlbCBodG1sRm9yPVwidW5pX21hdHJpY3VsYXRpb25fbnVtYmVyXCIgY2xhc3NOYW1lPVwidGV4dC1ncmVlbi04MDAgZGFyazp0ZXh0LWdyZWVuLTIwMFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIE1hdHJpY3VsYXRpb24gTnVtYmVyICpcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9MYWJlbD5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPElucHV0XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgaWQ9XCJ1bmlfbWF0cmljdWxhdGlvbl9udW1iZXJcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHZhbHVlPXt1bml2ZXJzaXR5Rm9ybURhdGEubWF0cmljdWxhdGlvbl9udW1iZXJ9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBzZXRVbml2ZXJzaXR5Rm9ybURhdGEocHJldiA9PiAoeyAuLi5wcmV2LCBtYXRyaWN1bGF0aW9uX251bWJlcjogZS50YXJnZXQudmFsdWUgfSkpfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiRW50ZXIgeW91ciBtYXRyaWN1bGF0aW9uIG51bWJlclwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwicm91bmRlZC14bCBib3JkZXItZ3JlZW4tMjAwIGRhcms6Ym9yZGVyLWdyZWVuLTcwMCBmb2N1czpib3JkZXItZ3JlZW4tNTAwXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICByZXF1aXJlZFxuICAgICAgICAgICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgICAgIHsvKiBSZXF1aXJlZCBEb2N1bWVudHMgKi99XG4gICAgICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LXNlbWlib2xkIHRleHQtZ3JlZW4tODAwIGRhcms6dGV4dC1ncmVlbi0yMDAgbWItNCBmbGV4IGl0ZW1zLWNlbnRlclwiPlxuICAgICAgICAgICAgICAgICAgICAgIDxGaWxlVXAgY2xhc3NOYW1lPVwiaC01IHctNSBtci0yIHRleHQtZ3JlZW4tNjAwXCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICBSZXF1aXJlZCBEb2N1bWVudHNcbiAgICAgICAgICAgICAgICAgICAgPC9oMz5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTRcIj5cbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTEgc206Z3JpZC1jb2xzLTIgZ2FwLTMgc206Z2FwLTRcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS0yXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxMYWJlbCBodG1sRm9yPVwidW5pX3N0dWRlbnRfaWRfY2FyZFwiIGNsYXNzTmFtZT1cInRleHQtZ3JlZW4tODAwIGRhcms6dGV4dC1ncmVlbi0yMDBcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBVcGxvYWQgU3R1ZGVudCBJRCBDYXJkICpcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9MYWJlbD5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPElucHV0XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgaWQ9XCJ1bmlfc3R1ZGVudF9pZF9jYXJkXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB0eXBlPVwiZmlsZVwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgYWNjZXB0PVwiaW1hZ2UvKiwucGRmXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IGhhbmRsZUZpbGVDaGFuZ2UoJ3VuaXZlcnNpdHknLCAnc3R1ZGVudF9pZF9jYXJkJywgZS50YXJnZXQuZmlsZXM/LlswXSB8fCBudWxsKX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJyb3VuZGVkLXhsIGJvcmRlci1ncmVlbi0yMDAgZGFyazpib3JkZXItZ3JlZW4tNzAwIGZvY3VzOmJvcmRlci1ncmVlbi01MDAgZmlsZTptci00IGZpbGU6cHktMiBmaWxlOnB4LTQgZmlsZTpyb3VuZGVkLWZ1bGwgZmlsZTpib3JkZXItMCBmaWxlOnRleHQtc20gZmlsZTpmb250LXNlbWlib2xkIGZpbGU6YmctZ3JlZW4tNTAgZmlsZTp0ZXh0LWdyZWVuLTcwMCBob3ZlcjpmaWxlOmJnLWdyZWVuLTEwMFwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgcmVxdWlyZWRcbiAgICAgICAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktMlwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8TGFiZWwgaHRtbEZvcj1cInVuaV9wYXltZW50X2V2aWRlbmNlXCIgY2xhc3NOYW1lPVwidGV4dC1ncmVlbi04MDAgZGFyazp0ZXh0LWdyZWVuLTIwMFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIFVwbG9hZCBQYXltZW50IEV2aWRlbmNlIChSZW1pdGEvU2NyZWVuc2hvdCkgKlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8L0xhYmVsPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8SW5wdXRcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBpZD1cInVuaV9wYXltZW50X2V2aWRlbmNlXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB0eXBlPVwiZmlsZVwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgYWNjZXB0PVwiaW1hZ2UvKiwucGRmXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IGhhbmRsZUZpbGVDaGFuZ2UoJ3VuaXZlcnNpdHknLCAncGF5bWVudF9ldmlkZW5jZScsIGUudGFyZ2V0LmZpbGVzPy5bMF0gfHwgbnVsbCl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwicm91bmRlZC14bCBib3JkZXItZ3JlZW4tMjAwIGRhcms6Ym9yZGVyLWdyZWVuLTcwMCBmb2N1czpib3JkZXItZ3JlZW4tNTAwIGZpbGU6bXItNCBmaWxlOnB5LTIgZmlsZTpweC00IGZpbGU6cm91bmRlZC1mdWxsIGZpbGU6Ym9yZGVyLTAgZmlsZTp0ZXh0LXNtIGZpbGU6Zm9udC1zZW1pYm9sZCBmaWxlOmJnLWdyZWVuLTUwIGZpbGU6dGV4dC1ncmVlbi03MDAgaG92ZXI6ZmlsZTpiZy1ncmVlbi0xMDBcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHJlcXVpcmVkXG4gICAgICAgICAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS0yXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICA8TGFiZWwgaHRtbEZvcj1cInVuaV9zdXBwb3J0aW5nX2RvY3VtZW50c1wiIGNsYXNzTmFtZT1cInRleHQtZ3JlZW4tODAwIGRhcms6dGV4dC1ncmVlbi0yMDBcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgVXBsb2FkIFN1cHBvcnRpbmcgRG9jdW1lbnRzIChQREYsIERPQ1gsIEpQRylcbiAgICAgICAgICAgICAgICAgICAgICAgIDwvTGFiZWw+XG4gICAgICAgICAgICAgICAgICAgICAgICA8SW5wdXRcbiAgICAgICAgICAgICAgICAgICAgICAgICAgaWQ9XCJ1bmlfc3VwcG9ydGluZ19kb2N1bWVudHNcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICB0eXBlPVwiZmlsZVwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgIGFjY2VwdD1cIi5wZGYsLmRvY3gsLmRvYywuanBnLC5qcGVnLC5wbmdcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICBtdWx0aXBsZVxuICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IGhhbmRsZU11bHRpcGxlRmlsZUNoYW5nZShlLnRhcmdldC5maWxlcyl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInJvdW5kZWQteGwgYm9yZGVyLWdyZWVuLTIwMCBkYXJrOmJvcmRlci1ncmVlbi03MDAgZm9jdXM6Ym9yZGVyLWdyZWVuLTUwMCBmaWxlOm1yLTQgZmlsZTpweS0yIGZpbGU6cHgtNCBmaWxlOnJvdW5kZWQtZnVsbCBmaWxlOmJvcmRlci0wIGZpbGU6dGV4dC1zbSBmaWxlOmZvbnQtc2VtaWJvbGQgZmlsZTpiZy1ncmVlbi01MCBmaWxlOnRleHQtZ3JlZW4tNzAwIGhvdmVyOmZpbGU6YmctZ3JlZW4tMTAwXCJcbiAgICAgICAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICB7dW5pdmVyc2l0eUZpbGVzLnN1cHBvcnRpbmdfZG9jdW1lbnRzLmxlbmd0aCA+IDAgJiYgKFxuICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm10LTIgc3BhY2UteS0yXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyZWVuLTYwMFwiPlNlbGVjdGVkIGZpbGVzOjwvcD5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB7dW5pdmVyc2l0eUZpbGVzLnN1cHBvcnRpbmdfZG9jdW1lbnRzLm1hcCgoZmlsZSwgaW5kZXgpID0+IChcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYga2V5PXtpbmRleH0gY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuIGJnLWdyZWVuLTUwIHAtMiByb3VuZGVkLXhsIGJvcmRlciBib3JkZXItZ3JlZW4tMjAwXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmVlbi03MDBcIj57ZmlsZS5uYW1lfTwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPEJ1dHRvblxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHR5cGU9XCJidXR0b25cIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHZhcmlhbnQ9XCJnaG9zdFwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc2l6ZT1cInNtXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiByZW1vdmVGaWxlKGluZGV4KX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ0ZXh0LXJlZC02MDAgaG92ZXI6dGV4dC1yZWQtODAwXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIFJlbW92ZVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICkpfVxuICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgICAgIHsvKiBBcHBsaWNhdGlvbiBSZWFzb24gKi99XG4gICAgICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LXNlbWlib2xkIHRleHQtZ3JlZW4tODAwIGRhcms6dGV4dC1ncmVlbi0yMDAgbWItNCBmbGV4IGl0ZW1zLWNlbnRlclwiPlxuICAgICAgICAgICAgICAgICAgICAgIDxNZXNzYWdlU3F1YXJlIGNsYXNzTmFtZT1cImgtNSB3LTUgbXItMiB0ZXh0LWdyZWVuLTYwMFwiIC8+XG4gICAgICAgICAgICAgICAgICAgICAgQXBwbGljYXRpb24gUmVhc29uXG4gICAgICAgICAgICAgICAgICAgIDwvaDM+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS0yXCI+XG4gICAgICAgICAgICAgICAgICAgICAgPExhYmVsIGh0bWxGb3I9XCJ1bmlfcmVhc29uX2Zvcl9zY2hvbGFyc2hpcFwiIGNsYXNzTmFtZT1cInRleHQtZ3JlZW4tODAwIGRhcms6dGV4dC1ncmVlbi0yMDBcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIFJlYXNvbiBmb3IgdGhlIFNjaG9sYXJzaGlwICpcbiAgICAgICAgICAgICAgICAgICAgICA8L0xhYmVsPlxuICAgICAgICAgICAgICAgICAgICAgIDxUZXh0YXJlYVxuICAgICAgICAgICAgICAgICAgICAgICAgaWQ9XCJ1bmlfcmVhc29uX2Zvcl9zY2hvbGFyc2hpcFwiXG4gICAgICAgICAgICAgICAgICAgICAgICB2YWx1ZT17dW5pdmVyc2l0eUZvcm1EYXRhLnJlYXNvbl9mb3Jfc2Nob2xhcnNoaXB9XG4gICAgICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHNldFVuaXZlcnNpdHlGb3JtRGF0YShwcmV2ID0+ICh7IC4uLnByZXYsIHJlYXNvbl9mb3Jfc2Nob2xhcnNoaXA6IGUudGFyZ2V0LnZhbHVlIH0pKX1cbiAgICAgICAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiUGxlYXNlIGV4cGxhaW4gd2h5IHlvdSBuZWVkIHRoaXMgc2Nob2xhcnNoaXBcIlxuICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwicm91bmRlZC14bCBib3JkZXItZ3JlZW4tMjAwIGRhcms6Ym9yZGVyLWdyZWVuLTcwMCBmb2N1czpib3JkZXItZ3JlZW4tNTAwXCJcbiAgICAgICAgICAgICAgICAgICAgICAgIHJvd3M9ezR9XG4gICAgICAgICAgICAgICAgICAgICAgICByZXF1aXJlZFxuICAgICAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBqdXN0aWZ5LWNlbnRlciBwdC02IHNtOnB0LThcIj5cbiAgICAgICAgICAgICAgICAgICAgPEJ1dHRvblxuICAgICAgICAgICAgICAgICAgICAgIHR5cGU9XCJzdWJtaXRcIlxuICAgICAgICAgICAgICAgICAgICAgIGRpc2FibGVkPXtzdWJtaXR0aW5nIHx8ICF2YWxpZGF0ZUZvcm0oKX1cbiAgICAgICAgICAgICAgICAgICAgICBzaXplPVwibGdcIlxuICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctZnVsbCBzbTp3LWF1dG8gYmctZ3JhZGllbnQtdG8tciBmcm9tLWdyZWVuLTYwMCB2aWEtZW1lcmFsZC02MDAgdG8tdGVhbC02MDAgaG92ZXI6ZnJvbS1ncmVlbi03MDAgaG92ZXI6dmlhLWVtZXJhbGQtNzAwIGhvdmVyOnRvLXRlYWwtNzAwIHRleHQtd2hpdGUgcHgtNiBzbTpweC0xMiBweS0zIHNtOnB5LTQgdGV4dC1iYXNlIHNtOnRleHQtbGcgZm9udC1ib2xkIHNoYWRvdy1sZyBob3ZlcjpzaGFkb3cteGwgdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tMzAwXCJcbiAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgIHtzdWJtaXR0aW5nID8gKFxuICAgICAgICAgICAgICAgICAgICAgICAgPD5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPExvYWRlcjIgY2xhc3NOYW1lPVwibXItMiBzbTptci0zIGgtNSB3LTUgc206aC02IHNtOnctNiBhbmltYXRlLXNwaW5cIiAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXNtIHNtOnRleHQtYmFzZVwiPlN1Ym1pdHRpbmcuLi48L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgICA8Lz5cbiAgICAgICAgICAgICAgICAgICAgICApIDogKFxuICAgICAgICAgICAgICAgICAgICAgICAgPD5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPENoZWNrQ2lyY2xlIGNsYXNzTmFtZT1cIm1yLTIgc206bXItMyBoLTUgdy01IHNtOmgtNiBzbTp3LTZcIiAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXNtIHNtOnRleHQtYmFzZVwiPlN1Ym1pdCBVbml2ZXJzaXR5IEFwcGxpY2F0aW9uPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgICAgPC8+XG4gICAgICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8L2Zvcm0+XG4gICAgICAgICAgICAgIDwvVGFic0NvbnRlbnQ+XG4gICAgICAgICAgICA8L1RhYnM+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9zZWN0aW9uPlxuICAgIDwvZGl2PlxuICApXG59XG4iXSwibmFtZXMiOlsiUmVhY3QiLCJ1c2VTdGF0ZSIsInVzZUVmZmVjdCIsInVzZVNlYXJjaFBhcmFtcyIsIkJ1dHRvbiIsIklucHV0IiwiTGFiZWwiLCJUZXh0YXJlYSIsIlNlbGVjdCIsIlNlbGVjdENvbnRlbnQiLCJTZWxlY3RJdGVtIiwiU2VsZWN0VHJpZ2dlciIsIlNlbGVjdFZhbHVlIiwiVGFicyIsIlRhYnNDb250ZW50IiwiVGFic0xpc3QiLCJUYWJzVHJpZ2dlciIsIkJvb2tPcGVuIiwiR3JhZHVhdGlvbkNhcCIsIlNjaG9vbCIsIlVzZXIiLCJQaG9uZSIsIk1haWwiLCJNYXBQaW4iLCJCdWlsZGluZyIsIkNyZWRpdENhcmQiLCJGaWxlVXAiLCJMb2FkZXIyIiwiQ2hlY2tDaXJjbGUiLCJBbGVydFRyaWFuZ2xlIiwiTWVzc2FnZVNxdWFyZSIsInVzZVRvYXN0IiwiYXBpQ2xpZW50IiwiU3VjY2Vzc01lc3NhZ2UiLCJTY2hvbGFyc2hpcEFwcGxpY2F0aW9uUGFnZSIsInNlYXJjaFBhcmFtcyIsInNlbGVjdGVkQ2F0ZWdvcnkiLCJzZXRTZWxlY3RlZENhdGVnb3J5Iiwic3VibWl0dGluZyIsInNldFN1Ym1pdHRpbmciLCJzaG93U3VjY2VzcyIsInNldFNob3dTdWNjZXNzIiwic2Nob2xhcnNoaXBJZCIsInNldFNjaG9sYXJzaGlwSWQiLCJ0b2FzdCIsImNhdGVnb3J5UGFyYW0iLCJnZXQiLCJzY2hvbGFyc2hpcElkUGFyYW0iLCJpbmNsdWRlcyIsInByaW1hcnlGb3JtRGF0YSIsInNldFByaW1hcnlGb3JtRGF0YSIsInN0dWRlbnRfZnVsbF9uYW1lIiwiYWdlIiwiY3VycmVudF9jbGFzcyIsImZhdGhlcl9uYW1lIiwibW90aGVyX25hbWUiLCJwYXJlbnRfcGhvbmUiLCJob21lX2FkZHJlc3MiLCJzY2hvb2xfbmFtZSIsImhlYWRtYXN0ZXJfbmFtZSIsInNjaG9vbF9hY2NvdW50X251bWJlciIsInJlYXNvbl9mb3Jfc2Nob2xhcnNoaXAiLCJjdXJyZW50X3NjaG9vbF9mZWUiLCJzdXBwb3J0aW5nX2luZm9ybWF0aW9uIiwic2Vjb25kYXJ5Rm9ybURhdGEiLCJzZXRTZWNvbmRhcnlGb3JtRGF0YSIsImNsYXNzIiwiYWRkcmVzcyIsInByaW5jaXBhbF9uYW1lIiwicHJpbmNpcGFsX2FjY291bnRfbnVtYmVyIiwic2Nob29sX2ZlZV9hbW91bnQiLCJ1bml2ZXJzaXR5Rm9ybURhdGEiLCJzZXRVbml2ZXJzaXR5Rm9ybURhdGEiLCJmdWxsX25hbWUiLCJjb3Vyc2Vfb2Zfc3R1ZHkiLCJjdXJyZW50X2xldmVsIiwicGhvbmVfbnVtYmVyIiwiZW1haWxfYWRkcmVzcyIsIm1hdHJpY3VsYXRpb25fbnVtYmVyIiwicHJpbWFyeUZpbGVzIiwic2V0UHJpbWFyeUZpbGVzIiwic3R1ZGVudF9waWN0dXJlIiwic2Vjb25kYXJ5RmlsZXMiLCJzZXRTZWNvbmRhcnlGaWxlcyIsInVuaXZlcnNpdHlGaWxlcyIsInNldFVuaXZlcnNpdHlGaWxlcyIsInN0dWRlbnRfaWRfY2FyZCIsInBheW1lbnRfZXZpZGVuY2UiLCJzdXBwb3J0aW5nX2RvY3VtZW50cyIsImNhdGVnb3J5Q29uZmlnIiwicHJpbWFyeSIsImljb24iLCJ0aXRsZSIsImRlc2NyaXB0aW9uIiwiY29sb3IiLCJiZ0NvbG9yIiwidGV4dENvbG9yIiwiYm9yZGVyQ29sb3IiLCJzZWNvbmRhcnkiLCJ1bml2ZXJzaXR5IiwiaGFuZGxlRmlsZUNoYW5nZSIsImNhdGVnb3J5IiwiZmllbGQiLCJmaWxlIiwicHJldiIsImhhbmRsZU11bHRpcGxlRmlsZUNoYW5nZSIsImZpbGVzIiwiZmlsZUFycmF5IiwiQXJyYXkiLCJmcm9tIiwicmVtb3ZlRmlsZSIsImluZGV4IiwiZmlsdGVyIiwiXyIsImkiLCJ2YWxpZGF0ZUZvcm0iLCJyZXF1aXJlZCIsImV2ZXJ5IiwidHJpbSIsImhhbmRsZVN1Ym1pdCIsImUiLCJwcmV2ZW50RGVmYXVsdCIsInZhcmlhbnQiLCJmb3JtRGF0YSIsIkZvcm1EYXRhIiwiYXBwZW5kIiwiSlNPTiIsInN0cmluZ2lmeSIsImZvckVhY2giLCJyZXNwb25zZSIsInN1Ym1pdFNjaG9sYXJzaGlwQXBwbGljYXRpb24iLCJzdWNjZXNzIiwibWVzc2FnZSIsImVycm9yIiwiY29uc29sZSIsImRhdGEiLCJoYW5kbGVCYWNrVG9Gb3JtIiwiaGFuZGxlR29Ib21lIiwid2luZG93IiwibG9jYXRpb24iLCJocmVmIiwib25CYWNrVG9Gb3JtIiwib25Hb0hvbWUiLCJkaXYiLCJjbGFzc05hbWUiLCJzZWN0aW9uIiwiaDEiLCJwIiwiaDIiLCJ2YWx1ZSIsIm9uVmFsdWVDaGFuZ2UiLCJPYmplY3QiLCJlbnRyaWVzIiwibWFwIiwia2V5IiwiY29uZmlnIiwiSWNvbkNvbXBvbmVudCIsImZvcm0iLCJvblN1Ym1pdCIsImgzIiwiaHRtbEZvciIsImlkIiwib25DaGFuZ2UiLCJ0YXJnZXQiLCJwbGFjZWhvbGRlciIsInR5cGUiLCJyb3dzIiwiYWNjZXB0IiwiZGlzYWJsZWQiLCJzaXplIiwic3BhbiIsIm11bHRpcGxlIiwibGVuZ3RoIiwibmFtZSIsIm9uQ2xpY2siXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/scholarship/ScholarshipApplicationPage.tsx\n"));

/***/ })

});