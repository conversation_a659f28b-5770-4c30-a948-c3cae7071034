"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/scholarship-application/page",{

/***/ "(app-pages-browser)/./components/scholarship/ScholarshipApplicationPage.tsx":
/*!***************************************************************!*\
  !*** ./components/scholarship/ScholarshipApplicationPage.tsx ***!
  \***************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ScholarshipApplicationPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./components/ui/label.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/textarea */ \"(app-pages-browser)/./components/ui/textarea.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./components/ui/select.tsx\");\n/* harmony import */ var _components_ui_tabs__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/tabs */ \"(app-pages-browser)/./components/ui/tabs.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BookOpen_Building_CheckCircle_CreditCard_FileUp_GraduationCap_Loader2_Mail_MapPin_MessageSquare_Phone_School_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BookOpen,Building,CheckCircle,CreditCard,FileUp,GraduationCap,Loader2,Mail,MapPin,MessageSquare,Phone,School,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/book-open.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BookOpen_Building_CheckCircle_CreditCard_FileUp_GraduationCap_Loader2_Mail_MapPin_MessageSquare_Phone_School_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BookOpen,Building,CheckCircle,CreditCard,FileUp,GraduationCap,Loader2,Mail,MapPin,MessageSquare,Phone,School,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/school.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BookOpen_Building_CheckCircle_CreditCard_FileUp_GraduationCap_Loader2_Mail_MapPin_MessageSquare_Phone_School_User_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BookOpen,Building,CheckCircle,CreditCard,FileUp,GraduationCap,Loader2,Mail,MapPin,MessageSquare,Phone,School,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/graduation-cap.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BookOpen_Building_CheckCircle_CreditCard_FileUp_GraduationCap_Loader2_Mail_MapPin_MessageSquare_Phone_School_User_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BookOpen,Building,CheckCircle,CreditCard,FileUp,GraduationCap,Loader2,Mail,MapPin,MessageSquare,Phone,School,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/triangle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BookOpen_Building_CheckCircle_CreditCard_FileUp_GraduationCap_Loader2_Mail_MapPin_MessageSquare_Phone_School_User_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BookOpen,Building,CheckCircle,CreditCard,FileUp,GraduationCap,Loader2,Mail,MapPin,MessageSquare,Phone,School,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BookOpen_Building_CheckCircle_CreditCard_FileUp_GraduationCap_Loader2_Mail_MapPin_MessageSquare_Phone_School_User_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BookOpen,Building,CheckCircle,CreditCard,FileUp,GraduationCap,Loader2,Mail,MapPin,MessageSquare,Phone,School,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/phone.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BookOpen_Building_CheckCircle_CreditCard_FileUp_GraduationCap_Loader2_Mail_MapPin_MessageSquare_Phone_School_User_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BookOpen,Building,CheckCircle,CreditCard,FileUp,GraduationCap,Loader2,Mail,MapPin,MessageSquare,Phone,School,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BookOpen_Building_CheckCircle_CreditCard_FileUp_GraduationCap_Loader2_Mail_MapPin_MessageSquare_Phone_School_User_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BookOpen,Building,CheckCircle,CreditCard,FileUp,GraduationCap,Loader2,Mail,MapPin,MessageSquare,Phone,School,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/building.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BookOpen_Building_CheckCircle_CreditCard_FileUp_GraduationCap_Loader2_Mail_MapPin_MessageSquare_Phone_School_User_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BookOpen,Building,CheckCircle,CreditCard,FileUp,GraduationCap,Loader2,Mail,MapPin,MessageSquare,Phone,School,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/credit-card.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BookOpen_Building_CheckCircle_CreditCard_FileUp_GraduationCap_Loader2_Mail_MapPin_MessageSquare_Phone_School_User_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BookOpen,Building,CheckCircle,CreditCard,FileUp,GraduationCap,Loader2,Mail,MapPin,MessageSquare,Phone,School,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BookOpen_Building_CheckCircle_CreditCard_FileUp_GraduationCap_Loader2_Mail_MapPin_MessageSquare_Phone_School_User_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BookOpen,Building,CheckCircle,CreditCard,FileUp,GraduationCap,Loader2,Mail,MapPin,MessageSquare,Phone,School,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BookOpen_Building_CheckCircle_CreditCard_FileUp_GraduationCap_Loader2_Mail_MapPin_MessageSquare_Phone_School_User_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BookOpen,Building,CheckCircle,CreditCard,FileUp,GraduationCap,Loader2,Mail,MapPin,MessageSquare,Phone,School,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-up.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BookOpen_Building_CheckCircle_CreditCard_FileUp_GraduationCap_Loader2_Mail_MapPin_MessageSquare_Phone_School_User_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BookOpen,Building,CheckCircle,CreditCard,FileUp,GraduationCap,Loader2,Mail,MapPin,MessageSquare,Phone,School,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BookOpen_Building_CheckCircle_CreditCard_FileUp_GraduationCap_Loader2_Mail_MapPin_MessageSquare_Phone_School_User_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BookOpen,Building,CheckCircle,CreditCard,FileUp,GraduationCap,Loader2,Mail,MapPin,MessageSquare,Phone,School,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/message-square.js\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./hooks/use-toast.ts\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./lib/api.ts\");\n/* harmony import */ var _SuccessMessage__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./SuccessMessage */ \"(app-pages-browser)/./components/scholarship/SuccessMessage.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\nfunction ScholarshipApplicationPage() {\n    _s();\n    const [selectedCategory, setSelectedCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('primary');\n    const [submitting, setSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showSuccess, setShowSuccess] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_8__.useToast)();\n    // Form data for different categories\n    const [primaryFormData, setPrimaryFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        student_full_name: '',\n        age: '',\n        current_class: '',\n        father_name: '',\n        mother_name: '',\n        parent_phone: '',\n        home_address: '',\n        school_name: '',\n        headmaster_name: '',\n        school_account_number: '',\n        reason_for_scholarship: '',\n        current_school_fee: '',\n        supporting_information: ''\n    });\n    const [secondaryFormData, setSecondaryFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        student_full_name: '',\n        age: '',\n        class: '',\n        parent_phone: '',\n        address: '',\n        school_name: '',\n        principal_name: '',\n        principal_account_number: '',\n        reason_for_scholarship: '',\n        school_fee_amount: ''\n    });\n    const [universityFormData, setUniversityFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        full_name: '',\n        age: '',\n        course_of_study: '',\n        current_level: '',\n        phone_number: '',\n        email_address: '',\n        matriculation_number: '',\n        reason_for_scholarship: ''\n    });\n    // File uploads for different categories\n    const [primaryFiles, setPrimaryFiles] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        student_picture: null\n    });\n    const [secondaryFiles, setSecondaryFiles] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        student_picture: null\n    });\n    const [universityFiles, setUniversityFiles] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        student_id_card: null,\n        payment_evidence: null,\n        supporting_documents: []\n    });\n    // Category configuration - Updated to match platform design system\n    const categoryConfig = {\n        primary: {\n            icon: _barrel_optimize_names_AlertTriangle_BookOpen_Building_CheckCircle_CreditCard_FileUp_GraduationCap_Loader2_Mail_MapPin_MessageSquare_Phone_School_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n            title: \"Primary School Scholarship\",\n            description: \"For Primary 1-6 students (Filled by Parent/Guardian)\",\n            color: \"bg-blue-500\",\n            bgColor: \"bg-blue-50\",\n            textColor: \"text-blue-700\",\n            borderColor: \"border-blue-200\"\n        },\n        secondary: {\n            icon: _barrel_optimize_names_AlertTriangle_BookOpen_Building_CheckCircle_CreditCard_FileUp_GraduationCap_Loader2_Mail_MapPin_MessageSquare_Phone_School_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n            title: \"Secondary School Scholarship\",\n            description: \"For Secondary school students (Filled by Student)\",\n            color: \"bg-green-500\",\n            bgColor: \"bg-green-50\",\n            textColor: \"text-green-700\",\n            borderColor: \"border-green-200\"\n        },\n        university: {\n            icon: _barrel_optimize_names_AlertTriangle_BookOpen_Building_CheckCircle_CreditCard_FileUp_GraduationCap_Loader2_Mail_MapPin_MessageSquare_Phone_School_User_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n            title: \"University Scholarship\",\n            description: \"For University students (Filled by Student)\",\n            color: \"bg-purple-500\",\n            bgColor: \"bg-purple-50\",\n            textColor: \"text-purple-700\",\n            borderColor: \"border-purple-200\"\n        }\n    };\n    const handleFileChange = (category, field, file)=>{\n        if (category === 'primary') {\n            setPrimaryFiles((prev)=>({\n                    ...prev,\n                    [field]: file\n                }));\n        } else if (category === 'secondary') {\n            setSecondaryFiles((prev)=>({\n                    ...prev,\n                    [field]: file\n                }));\n        } else if (category === 'university') {\n            if (field === 'supporting_documents' && file) {\n                setUniversityFiles((prev)=>({\n                        ...prev,\n                        supporting_documents: [\n                            ...prev.supporting_documents,\n                            file\n                        ]\n                    }));\n            } else {\n                setUniversityFiles((prev)=>({\n                        ...prev,\n                        [field]: file\n                    }));\n            }\n        }\n    };\n    const handleMultipleFileChange = (files)=>{\n        if (files && selectedCategory === 'university') {\n            const fileArray = Array.from(files);\n            setUniversityFiles((prev)=>({\n                    ...prev,\n                    supporting_documents: [\n                        ...prev.supporting_documents,\n                        ...fileArray\n                    ]\n                }));\n        }\n    };\n    const removeFile = (index)=>{\n        setUniversityFiles((prev)=>({\n                ...prev,\n                supporting_documents: prev.supporting_documents.filter((_, i)=>i !== index)\n            }));\n    };\n    const validateForm = ()=>{\n        if (selectedCategory === 'primary') {\n            const required = [\n                'student_full_name',\n                'age',\n                'current_class',\n                'father_name',\n                'mother_name',\n                'parent_phone',\n                'home_address',\n                'school_name',\n                'headmaster_name',\n                'school_account_number',\n                'reason_for_scholarship',\n                'current_school_fee'\n            ];\n            return required.every((field)=>primaryFormData[field].trim() !== '') && primaryFiles.student_picture;\n        } else if (selectedCategory === 'secondary') {\n            const required = [\n                'student_full_name',\n                'age',\n                'class',\n                'parent_phone',\n                'address',\n                'school_name',\n                'principal_name',\n                'principal_account_number',\n                'reason_for_scholarship',\n                'school_fee_amount'\n            ];\n            return required.every((field)=>secondaryFormData[field].trim() !== '') && secondaryFiles.student_picture;\n        } else if (selectedCategory === 'university') {\n            const required = [\n                'full_name',\n                'age',\n                'course_of_study',\n                'current_level',\n                'phone_number',\n                'email_address',\n                'matriculation_number',\n                'reason_for_scholarship'\n            ];\n            return required.every((field)=>universityFormData[field].trim() !== '') && universityFiles.student_id_card && universityFiles.payment_evidence;\n        }\n        return false;\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        if (!validateForm()) {\n            toast({\n                title: \"Validation Error\",\n                description: \"Please fill in all required fields and upload required documents\",\n                variant: \"destructive\"\n            });\n            return;\n        }\n        try {\n            setSubmitting(true);\n            const formData = new FormData();\n            formData.append('category', selectedCategory);\n            // Add form data based on category\n            if (selectedCategory === 'primary') {\n                formData.append('form_data', JSON.stringify(primaryFormData));\n                if (primaryFiles.student_picture) {\n                    formData.append('student_picture', primaryFiles.student_picture);\n                }\n            } else if (selectedCategory === 'secondary') {\n                formData.append('form_data', JSON.stringify(secondaryFormData));\n                if (secondaryFiles.student_picture) {\n                    formData.append('student_picture', secondaryFiles.student_picture);\n                }\n            } else if (selectedCategory === 'university') {\n                formData.append('form_data', JSON.stringify(universityFormData));\n                if (universityFiles.student_id_card) {\n                    formData.append('student_id_card', universityFiles.student_id_card);\n                }\n                if (universityFiles.payment_evidence) {\n                    formData.append('payment_evidence', universityFiles.payment_evidence);\n                }\n                universityFiles.supporting_documents.forEach((file, index)=>{\n                    formData.append(\"supporting_documents[\".concat(index, \"]\"), file);\n                });\n            }\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_9__.apiClient.post('/scholarships/apply', formData, {\n                headers: {\n                    'Content-Type': 'multipart/form-data'\n                }\n            });\n            if (response.success) {\n                // Show success message\n                setShowSuccess(true);\n                // Reset form\n                if (selectedCategory === 'primary') {\n                    setPrimaryFormData({\n                        student_full_name: '',\n                        age: '',\n                        current_class: '',\n                        father_name: '',\n                        mother_name: '',\n                        parent_phone: '',\n                        home_address: '',\n                        school_name: '',\n                        headmaster_name: '',\n                        school_account_number: '',\n                        reason_for_scholarship: '',\n                        current_school_fee: '',\n                        supporting_information: ''\n                    });\n                    setPrimaryFiles({\n                        student_picture: null\n                    });\n                } else if (selectedCategory === 'secondary') {\n                    setSecondaryFormData({\n                        student_full_name: '',\n                        age: '',\n                        class: '',\n                        parent_phone: '',\n                        address: '',\n                        school_name: '',\n                        principal_name: '',\n                        principal_account_number: '',\n                        reason_for_scholarship: '',\n                        school_fee_amount: ''\n                    });\n                    setSecondaryFiles({\n                        student_picture: null\n                    });\n                } else if (selectedCategory === 'university') {\n                    setUniversityFormData({\n                        full_name: '',\n                        age: '',\n                        course_of_study: '',\n                        current_level: '',\n                        phone_number: '',\n                        email_address: '',\n                        matriculation_number: '',\n                        reason_for_scholarship: ''\n                    });\n                    setUniversityFiles({\n                        student_id_card: null,\n                        payment_evidence: null,\n                        supporting_documents: []\n                    });\n                }\n            } else {\n                toast({\n                    title: \"Error\",\n                    description: response.message || \"Failed to submit application\",\n                    variant: \"destructive\"\n                });\n            }\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error('Error submitting application:', error);\n            toast({\n                title: \"Error\",\n                description: ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || \"Failed to submit application\",\n                variant: \"destructive\"\n            });\n        } finally{\n            setSubmitting(false);\n        }\n    };\n    const handleBackToForm = ()=>{\n        setShowSuccess(false);\n    };\n    const handleGoHome = ()=>{\n        // Navigate to dashboard or home page\n        window.location.href = '/dashboard';\n    };\n    if (showSuccess) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_SuccessMessage__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n            onBackToForm: handleBackToForm,\n            onGoHome: handleGoHome\n        }, void 0, false, {\n            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n            lineNumber: 321,\n            columnNumber: 12\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50 dark:bg-gray-900\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"relative py-12 sm:py-16 md:py-20 bg-gradient-to-br from-green-600 to-green-800 text-white\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container mx-auto px-3 sm:px-4 md:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-w-4xl mx-auto text-center space-y-4 sm:space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-2xl sm:text-3xl md:text-4xl lg:text-5xl xl:text-6xl font-bold tracking-tight leading-tight\",\n                                children: \"Scholarship Application\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                lineNumber: 330,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-base sm:text-lg md:text-xl text-green-100 max-w-3xl mx-auto px-2\",\n                                children: \"Choose your scholarship category and complete the application form\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                lineNumber: 333,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                        lineNumber: 329,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                    lineNumber: 328,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                lineNumber: 327,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-6 sm:py-8 md:py-12 bg-white dark:bg-gray-900\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container mx-auto px-3 sm:px-4 md:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-w-4xl mx-auto\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-4 sm:mb-6 md:mb-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-xl sm:text-2xl md:text-3xl font-bold tracking-tight mb-2 text-center sm:text-left\",\n                                        children: \"Apply for Scholarship\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                        lineNumber: 345,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm sm:text-base text-gray-600 dark:text-gray-400 text-center sm:text-left\",\n                                        children: \"Select your education level and fill out the appropriate form\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                        lineNumber: 346,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                lineNumber: 344,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_7__.Tabs, {\n                                value: selectedCategory,\n                                onValueChange: (value)=>setSelectedCategory(value),\n                                className: \"w-full\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_7__.TabsList, {\n                                        className: \"grid w-full grid-cols-1 md:grid-cols-3 mb-6 h-auto p-1 gap-1 bg-gray-100 rounded-lg\",\n                                        children: Object.entries(categoryConfig).map((param)=>{\n                                            let [key, config] = param;\n                                            const IconComponent = config.icon;\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_7__.TabsTrigger, {\n                                                value: key,\n                                                className: \"flex flex-col items-center p-3 space-y-2 rounded-md transition-all duration-300 data-[state=active]:bg-white data-[state=active]:shadow-md\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"p-2 rounded-full bg-green-600 text-white shadow-sm\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(IconComponent, {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                            lineNumber: 362,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                        lineNumber: 361,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"font-medium text-xs text-gray-900 leading-tight\",\n                                                                children: config.title\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                lineNumber: 365,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs text-gray-600 mt-0.5\",\n                                                                children: config.description\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                lineNumber: 366,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                        lineNumber: 364,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, key, true, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                lineNumber: 356,\n                                                columnNumber: 21\n                                            }, this);\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                        lineNumber: 352,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_7__.TabsContent, {\n                                        value: \"primary\",\n                                        className: \"space-y-8\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                            onSubmit: handleSubmit,\n                                            className: \"space-y-8\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-green-50 p-6 rounded-lg border border-green-200\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BookOpen_Building_CheckCircle_CreditCard_FileUp_GraduationCap_Loader2_Mail_MapPin_MessageSquare_Phone_School_User_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                className: \"h-5 w-5 text-green-600 mr-3\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                lineNumber: 378,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-green-800 font-medium\",\n                                                                children: \"This form should be filled by a Parent or Guardian on behalf of the student\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                lineNumber: 379,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                        lineNumber: 377,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                    lineNumber: 376,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"text-lg font-semibold text-green-800 dark:text-green-200 mb-4 flex items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BookOpen_Building_CheckCircle_CreditCard_FileUp_GraduationCap_Loader2_Mail_MapPin_MessageSquare_Phone_School_User_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                            className: \"h-5 w-5 mr-2 text-green-600\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                            lineNumber: 389,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        \"Student Information\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                    lineNumber: 388,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"space-y-4\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"space-y-2\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                                                    htmlFor: \"student_full_name\",\n                                                                                    className: \"text-green-800 dark:text-green-200\",\n                                                                                    children: \"Student Full Name *\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                    lineNumber: 394,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"relative\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BookOpen_Building_CheckCircle_CreditCard_FileUp_GraduationCap_Loader2_Mail_MapPin_MessageSquare_Phone_School_User_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                                            className: \"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-green-500\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                            lineNumber: 398,\n                                                                                            columnNumber: 29\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                                                            id: \"student_full_name\",\n                                                                                            value: primaryFormData.student_full_name,\n                                                                                            onChange: (e)=>setPrimaryFormData((prev)=>({\n                                                                                                        ...prev,\n                                                                                                        student_full_name: e.target.value\n                                                                                                    })),\n                                                                                            placeholder: \"Enter student's full name\",\n                                                                                            className: \"pl-10 rounded-xl border-green-200 dark:border-green-700 focus:border-green-500\",\n                                                                                            required: true\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                            lineNumber: 399,\n                                                                                            columnNumber: 29\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                    lineNumber: 397,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                            lineNumber: 393,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"space-y-2\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                                                            htmlFor: \"age\",\n                                                                                            className: \"text-green-800 dark:text-green-200\",\n                                                                                            children: \"Age *\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                            lineNumber: 412,\n                                                                                            columnNumber: 29\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                                                            id: \"age\",\n                                                                                            type: \"number\",\n                                                                                            value: primaryFormData.age,\n                                                                                            onChange: (e)=>setPrimaryFormData((prev)=>({\n                                                                                                        ...prev,\n                                                                                                        age: e.target.value\n                                                                                                    })),\n                                                                                            placeholder: \"Age\",\n                                                                                            className: \"rounded-xl border-green-200 dark:border-green-700 focus:border-green-500\",\n                                                                                            required: true\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                            lineNumber: 415,\n                                                                                            columnNumber: 29\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                    lineNumber: 411,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"space-y-2\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                                                            htmlFor: \"current_class\",\n                                                                                            className: \"text-green-800 dark:text-green-200\",\n                                                                                            children: \"Current Class *\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                            lineNumber: 426,\n                                                                                            columnNumber: 29\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.Select, {\n                                                                                            value: primaryFormData.current_class,\n                                                                                            onValueChange: (value)=>setPrimaryFormData((prev)=>({\n                                                                                                        ...prev,\n                                                                                                        current_class: value\n                                                                                                    })),\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectTrigger, {\n                                                                                                    className: \"rounded-xl border-green-200 dark:border-green-700 focus:border-green-500\",\n                                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectValue, {\n                                                                                                        placeholder: \"Select class\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                                        lineNumber: 431,\n                                                                                                        columnNumber: 33\n                                                                                                    }, this)\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                                    lineNumber: 430,\n                                                                                                    columnNumber: 31\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectContent, {\n                                                                                                    children: [\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                                                                            value: \"Primary 1\",\n                                                                                                            children: \"Primary 1\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                                            lineNumber: 434,\n                                                                                                            columnNumber: 33\n                                                                                                        }, this),\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                                                                            value: \"Primary 2\",\n                                                                                                            children: \"Primary 2\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                                            lineNumber: 435,\n                                                                                                            columnNumber: 33\n                                                                                                        }, this),\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                                                                            value: \"Primary 3\",\n                                                                                                            children: \"Primary 3\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                                            lineNumber: 436,\n                                                                                                            columnNumber: 33\n                                                                                                        }, this),\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                                                                            value: \"Primary 4\",\n                                                                                                            children: \"Primary 4\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                                            lineNumber: 437,\n                                                                                                            columnNumber: 33\n                                                                                                        }, this),\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                                                                            value: \"Primary 5\",\n                                                                                                            children: \"Primary 5\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                                            lineNumber: 438,\n                                                                                                            columnNumber: 33\n                                                                                                        }, this),\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                                                                            value: \"Primary 6\",\n                                                                                                            children: \"Primary 6\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                                            lineNumber: 439,\n                                                                                                            columnNumber: 33\n                                                                                                        }, this)\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                                    lineNumber: 433,\n                                                                                                    columnNumber: 31\n                                                                                                }, this)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                            lineNumber: 429,\n                                                                                            columnNumber: 29\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                    lineNumber: 425,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                            lineNumber: 410,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                    lineNumber: 392,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                            lineNumber: 387,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"text-lg font-semibold text-green-800 dark:text-green-200 mb-4 flex items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BookOpen_Building_CheckCircle_CreditCard_FileUp_GraduationCap_Loader2_Mail_MapPin_MessageSquare_Phone_School_User_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                            className: \"h-5 w-5 mr-2 text-green-600\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                            lineNumber: 450,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        \"Parent/Guardian Information\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                    lineNumber: 449,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"space-y-4\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"space-y-2\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                                                            htmlFor: \"father_name\",\n                                                                                            className: \"text-green-800 dark:text-green-200\",\n                                                                                            children: \"Father's Name *\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                            lineNumber: 456,\n                                                                                            columnNumber: 29\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"relative\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BookOpen_Building_CheckCircle_CreditCard_FileUp_GraduationCap_Loader2_Mail_MapPin_MessageSquare_Phone_School_User_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                                                    className: \"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-green-500\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                                    lineNumber: 460,\n                                                                                                    columnNumber: 31\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                                                                    id: \"father_name\",\n                                                                                                    value: primaryFormData.father_name,\n                                                                                                    onChange: (e)=>setPrimaryFormData((prev)=>({\n                                                                                                                ...prev,\n                                                                                                                father_name: e.target.value\n                                                                                                            })),\n                                                                                                    placeholder: \"Enter father's name\",\n                                                                                                    className: \"pl-10 rounded-xl border-green-200 dark:border-green-700 focus:border-green-500\",\n                                                                                                    required: true\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                                    lineNumber: 461,\n                                                                                                    columnNumber: 31\n                                                                                                }, this)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                            lineNumber: 459,\n                                                                                            columnNumber: 29\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                    lineNumber: 455,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"space-y-2\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                                                            htmlFor: \"mother_name\",\n                                                                                            className: \"text-green-800 dark:text-green-200\",\n                                                                                            children: \"Mother's Name *\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                            lineNumber: 472,\n                                                                                            columnNumber: 29\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                                                            id: \"mother_name\",\n                                                                                            value: primaryFormData.mother_name,\n                                                                                            onChange: (e)=>setPrimaryFormData((prev)=>({\n                                                                                                        ...prev,\n                                                                                                        mother_name: e.target.value\n                                                                                                    })),\n                                                                                            placeholder: \"Enter mother's name\",\n                                                                                            className: \"rounded-xl border-green-200 dark:border-green-700 focus:border-green-500\",\n                                                                                            required: true\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                            lineNumber: 475,\n                                                                                            columnNumber: 29\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                    lineNumber: 471,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                            lineNumber: 454,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"space-y-2\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                                                    htmlFor: \"parent_phone\",\n                                                                                    className: \"text-green-800 dark:text-green-200\",\n                                                                                    children: \"Father's or Mother's Phone Number *\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                    lineNumber: 486,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"relative\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BookOpen_Building_CheckCircle_CreditCard_FileUp_GraduationCap_Loader2_Mail_MapPin_MessageSquare_Phone_School_User_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                                            className: \"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-green-500\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                            lineNumber: 490,\n                                                                                            columnNumber: 29\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                                                            id: \"parent_phone\",\n                                                                                            type: \"tel\",\n                                                                                            value: primaryFormData.parent_phone,\n                                                                                            onChange: (e)=>setPrimaryFormData((prev)=>({\n                                                                                                        ...prev,\n                                                                                                        parent_phone: e.target.value\n                                                                                                    })),\n                                                                                            placeholder: \"Enter phone number\",\n                                                                                            className: \"pl-10 rounded-xl border-green-200 dark:border-green-700 focus:border-green-500\",\n                                                                                            required: true\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                            lineNumber: 491,\n                                                                                            columnNumber: 29\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                    lineNumber: 489,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                            lineNumber: 485,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                    lineNumber: 453,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                            lineNumber: 448,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                    lineNumber: 386,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-lg font-semibold text-green-800 dark:text-green-200 mb-4 flex items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BookOpen_Building_CheckCircle_CreditCard_FileUp_GraduationCap_Loader2_Mail_MapPin_MessageSquare_Phone_School_User_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                    className: \"h-5 w-5 mr-2 text-green-600\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                    lineNumber: 509,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                \"Address Information\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                            lineNumber: 508,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                                    htmlFor: \"home_address\",\n                                                                    className: \"text-green-800 dark:text-green-200\",\n                                                                    children: \"Home Address *\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                    lineNumber: 513,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"relative\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BookOpen_Building_CheckCircle_CreditCard_FileUp_GraduationCap_Loader2_Mail_MapPin_MessageSquare_Phone_School_User_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                            className: \"absolute left-3 top-3 h-4 w-4 text-green-500\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                            lineNumber: 517,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_5__.Textarea, {\n                                                                            id: \"home_address\",\n                                                                            value: primaryFormData.home_address,\n                                                                            onChange: (e)=>setPrimaryFormData((prev)=>({\n                                                                                        ...prev,\n                                                                                        home_address: e.target.value\n                                                                                    })),\n                                                                            placeholder: \"Enter complete home address\",\n                                                                            className: \"pl-10 rounded-xl border-green-200 dark:border-green-700 focus:border-green-500\",\n                                                                            rows: 3,\n                                                                            required: true\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                            lineNumber: 518,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                    lineNumber: 516,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                            lineNumber: 512,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                    lineNumber: 507,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-lg font-semibold text-green-800 dark:text-green-200 mb-4 flex items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BookOpen_Building_CheckCircle_CreditCard_FileUp_GraduationCap_Loader2_Mail_MapPin_MessageSquare_Phone_School_User_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                    className: \"h-5 w-5 mr-2 text-green-600\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                    lineNumber: 534,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                \"School Information\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                            lineNumber: 533,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"space-y-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                                            htmlFor: \"school_name\",\n                                                                            className: \"text-green-800 dark:text-green-200\",\n                                                                            children: \"Name of the School *\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                            lineNumber: 539,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"relative\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BookOpen_Building_CheckCircle_CreditCard_FileUp_GraduationCap_Loader2_Mail_MapPin_MessageSquare_Phone_School_User_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                                    className: \"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-green-500\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                    lineNumber: 543,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                                                    id: \"school_name\",\n                                                                                    value: primaryFormData.school_name,\n                                                                                    onChange: (e)=>setPrimaryFormData((prev)=>({\n                                                                                                ...prev,\n                                                                                                school_name: e.target.value\n                                                                                            })),\n                                                                                    placeholder: \"Enter school name\",\n                                                                                    className: \"pl-10 rounded-xl border-green-200 dark:border-green-700 focus:border-green-500\",\n                                                                                    required: true\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                    lineNumber: 544,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                            lineNumber: 542,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                    lineNumber: 538,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"space-y-2\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                                                    htmlFor: \"headmaster_name\",\n                                                                                    className: \"text-green-800 dark:text-green-200\",\n                                                                                    children: \"Name of Headmaster/Director *\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                    lineNumber: 557,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                                                    id: \"headmaster_name\",\n                                                                                    value: primaryFormData.headmaster_name,\n                                                                                    onChange: (e)=>setPrimaryFormData((prev)=>({\n                                                                                                ...prev,\n                                                                                                headmaster_name: e.target.value\n                                                                                            })),\n                                                                                    placeholder: \"Enter headmaster's name\",\n                                                                                    className: \"rounded-xl border-green-200 dark:border-green-700 focus:border-green-500\",\n                                                                                    required: true\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                    lineNumber: 560,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                            lineNumber: 556,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"space-y-2\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                                                    htmlFor: \"school_account_number\",\n                                                                                    className: \"text-green-800 dark:text-green-200\",\n                                                                                    children: \"School Account Number *\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                    lineNumber: 570,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                                                    id: \"school_account_number\",\n                                                                                    value: primaryFormData.school_account_number,\n                                                                                    onChange: (e)=>setPrimaryFormData((prev)=>({\n                                                                                                ...prev,\n                                                                                                school_account_number: e.target.value\n                                                                                            })),\n                                                                                    placeholder: \"Enter account number for scholarship payment\",\n                                                                                    className: \"rounded-xl border-green-200 dark:border-green-700 focus:border-green-500\",\n                                                                                    required: true\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                    lineNumber: 573,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                            lineNumber: 569,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                    lineNumber: 555,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                            lineNumber: 537,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                    lineNumber: 532,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-lg font-semibold text-green-800 dark:text-green-200 mb-4 flex items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BookOpen_Building_CheckCircle_CreditCard_FileUp_GraduationCap_Loader2_Mail_MapPin_MessageSquare_Phone_School_User_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                    className: \"h-5 w-5 mr-2 text-green-600\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                    lineNumber: 589,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                \"Financial & Additional Information\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                            lineNumber: 588,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"space-y-2\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                                                    htmlFor: \"current_school_fee\",\n                                                                                    className: \"text-green-800 dark:text-green-200\",\n                                                                                    children: \"Current School Fee Amount *\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                    lineNumber: 595,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"relative\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BookOpen_Building_CheckCircle_CreditCard_FileUp_GraduationCap_Loader2_Mail_MapPin_MessageSquare_Phone_School_User_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                                            className: \"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-green-500\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                            lineNumber: 599,\n                                                                                            columnNumber: 29\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                                                            id: \"current_school_fee\",\n                                                                                            type: \"number\",\n                                                                                            value: primaryFormData.current_school_fee,\n                                                                                            onChange: (e)=>setPrimaryFormData((prev)=>({\n                                                                                                        ...prev,\n                                                                                                        current_school_fee: e.target.value\n                                                                                                    })),\n                                                                                            placeholder: \"Enter amount in Naira\",\n                                                                                            className: \"pl-10 rounded-xl border-green-200 dark:border-green-700 focus:border-green-500\",\n                                                                                            required: true\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                            lineNumber: 600,\n                                                                                            columnNumber: 29\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                    lineNumber: 598,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                            lineNumber: 594,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"space-y-2\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                                                    htmlFor: \"student_picture\",\n                                                                                    className: \"text-green-800 dark:text-green-200\",\n                                                                                    children: \"Upload Student Picture *\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                    lineNumber: 612,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                                                    id: \"student_picture\",\n                                                                                    type: \"file\",\n                                                                                    accept: \"image/*\",\n                                                                                    onChange: (e)=>{\n                                                                                        var _e_target_files;\n                                                                                        return handleFileChange('primary', 'student_picture', ((_e_target_files = e.target.files) === null || _e_target_files === void 0 ? void 0 : _e_target_files[0]) || null);\n                                                                                    },\n                                                                                    className: \"rounded-xl border-green-200 dark:border-green-700 focus:border-green-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-green-50 file:text-green-700 hover:file:bg-green-100\",\n                                                                                    required: true\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                    lineNumber: 615,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                            lineNumber: 611,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                    lineNumber: 593,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"space-y-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                                            htmlFor: \"reason_for_scholarship\",\n                                                                            className: \"text-green-800 dark:text-green-200\",\n                                                                            children: \"Reason for the Scholarship *\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                            lineNumber: 626,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_5__.Textarea, {\n                                                                            id: \"reason_for_scholarship\",\n                                                                            value: primaryFormData.reason_for_scholarship,\n                                                                            onChange: (e)=>setPrimaryFormData((prev)=>({\n                                                                                        ...prev,\n                                                                                        reason_for_scholarship: e.target.value\n                                                                                    })),\n                                                                            placeholder: \"Please explain why your child needs this scholarship\",\n                                                                            className: \"rounded-xl border-green-200 dark:border-green-700 focus:border-green-500\",\n                                                                            rows: 4,\n                                                                            required: true\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                            lineNumber: 629,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                    lineNumber: 625,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"space-y-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                                            htmlFor: \"supporting_information\",\n                                                                            className: \"text-green-800 dark:text-green-200\",\n                                                                            children: \"Any Other Supporting Information\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                            lineNumber: 640,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_5__.Textarea, {\n                                                                            id: \"supporting_information\",\n                                                                            value: primaryFormData.supporting_information,\n                                                                            onChange: (e)=>setPrimaryFormData((prev)=>({\n                                                                                        ...prev,\n                                                                                        supporting_information: e.target.value\n                                                                                    })),\n                                                                            placeholder: \"Any additional information that supports your application\",\n                                                                            className: \"rounded-xl border-green-200 dark:border-green-700 focus:border-green-500\",\n                                                                            rows: 3\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                            lineNumber: 643,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                    lineNumber: 639,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                            lineNumber: 592,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                    lineNumber: 587,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-center pt-8\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                        type: \"submit\",\n                                                        disabled: submitting || !validateForm(),\n                                                        size: \"lg\",\n                                                        className: \"bg-green-600 hover:bg-green-700 text-white px-12 py-4 text-lg font-semibold shadow-lg hover:shadow-xl transition-all duration-300\",\n                                                        children: submitting ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BookOpen_Building_CheckCircle_CreditCard_FileUp_GraduationCap_Loader2_Mail_MapPin_MessageSquare_Phone_School_User_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                    className: \"mr-3 h-6 w-6 animate-spin\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                    lineNumber: 664,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                \"Submitting Application...\"\n                                                            ]\n                                                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BookOpen_Building_CheckCircle_CreditCard_FileUp_GraduationCap_Loader2_Mail_MapPin_MessageSquare_Phone_School_User_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                    className: \"mr-3 h-6 w-6\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                    lineNumber: 669,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                \"Submit Primary School Application\"\n                                                            ]\n                                                        }, void 0, true)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                        lineNumber: 656,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                    lineNumber: 655,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                            lineNumber: 375,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                        lineNumber: 374,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_7__.TabsContent, {\n                                        value: \"secondary\",\n                                        className: \"space-y-8\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                            onSubmit: handleSubmit,\n                                            className: \"space-y-8\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-green-50 p-6 rounded-lg border border-green-200\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BookOpen_Building_CheckCircle_CreditCard_FileUp_GraduationCap_Loader2_Mail_MapPin_MessageSquare_Phone_School_User_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                className: \"h-5 w-5 text-green-600 mr-3\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                lineNumber: 683,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-green-800 font-medium\",\n                                                                children: \"This form should be filled by the Student directly\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                lineNumber: 684,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                        lineNumber: 682,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                    lineNumber: 681,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"text-lg font-semibold text-green-800 dark:text-green-200 mb-4 flex items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BookOpen_Building_CheckCircle_CreditCard_FileUp_GraduationCap_Loader2_Mail_MapPin_MessageSquare_Phone_School_User_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                            className: \"h-5 w-5 mr-2 text-green-600\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                            lineNumber: 694,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        \"Student Information\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                    lineNumber: 693,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"space-y-4\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"space-y-2\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                                                    htmlFor: \"sec_student_full_name\",\n                                                                                    className: \"text-green-800 dark:text-green-200\",\n                                                                                    children: \"Student Full Name *\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                    lineNumber: 699,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"relative\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BookOpen_Building_CheckCircle_CreditCard_FileUp_GraduationCap_Loader2_Mail_MapPin_MessageSquare_Phone_School_User_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                                            className: \"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-green-500\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                            lineNumber: 703,\n                                                                                            columnNumber: 29\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                                                            id: \"sec_student_full_name\",\n                                                                                            value: secondaryFormData.student_full_name,\n                                                                                            onChange: (e)=>setSecondaryFormData((prev)=>({\n                                                                                                        ...prev,\n                                                                                                        student_full_name: e.target.value\n                                                                                                    })),\n                                                                                            placeholder: \"Enter your full name\",\n                                                                                            className: \"pl-10 rounded-xl border-green-200 dark:border-green-700 focus:border-green-500\",\n                                                                                            required: true\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                            lineNumber: 704,\n                                                                                            columnNumber: 29\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                    lineNumber: 702,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                            lineNumber: 698,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"space-y-2\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                                                            htmlFor: \"sec_age\",\n                                                                                            className: \"text-green-800 dark:text-green-200\",\n                                                                                            children: \"Age *\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                            lineNumber: 717,\n                                                                                            columnNumber: 29\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                                                            id: \"sec_age\",\n                                                                                            type: \"number\",\n                                                                                            value: secondaryFormData.age,\n                                                                                            onChange: (e)=>setSecondaryFormData((prev)=>({\n                                                                                                        ...prev,\n                                                                                                        age: e.target.value\n                                                                                                    })),\n                                                                                            placeholder: \"Your age\",\n                                                                                            className: \"rounded-xl border-green-200 dark:border-green-700 focus:border-green-500\",\n                                                                                            required: true\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                            lineNumber: 720,\n                                                                                            columnNumber: 29\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                    lineNumber: 716,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"space-y-2\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                                                            htmlFor: \"sec_class\",\n                                                                                            className: \"text-green-800 dark:text-green-200\",\n                                                                                            children: \"Class *\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                            lineNumber: 731,\n                                                                                            columnNumber: 29\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.Select, {\n                                                                                            value: secondaryFormData.class,\n                                                                                            onValueChange: (value)=>setSecondaryFormData((prev)=>({\n                                                                                                        ...prev,\n                                                                                                        class: value\n                                                                                                    })),\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectTrigger, {\n                                                                                                    className: \"rounded-xl border-green-200 dark:border-green-700 focus:border-green-500\",\n                                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectValue, {\n                                                                                                        placeholder: \"Select class\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                                        lineNumber: 736,\n                                                                                                        columnNumber: 33\n                                                                                                    }, this)\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                                    lineNumber: 735,\n                                                                                                    columnNumber: 31\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectContent, {\n                                                                                                    children: [\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                                                                            value: \"JSS 1\",\n                                                                                                            children: \"JSS 1\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                                            lineNumber: 739,\n                                                                                                            columnNumber: 33\n                                                                                                        }, this),\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                                                                            value: \"JSS 2\",\n                                                                                                            children: \"JSS 2\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                                            lineNumber: 740,\n                                                                                                            columnNumber: 33\n                                                                                                        }, this),\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                                                                            value: \"JSS 3\",\n                                                                                                            children: \"JSS 3\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                                            lineNumber: 741,\n                                                                                                            columnNumber: 33\n                                                                                                        }, this),\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                                                                            value: \"SS 1\",\n                                                                                                            children: \"SS 1\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                                            lineNumber: 742,\n                                                                                                            columnNumber: 33\n                                                                                                        }, this),\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                                                                            value: \"SS 2\",\n                                                                                                            children: \"SS 2\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                                            lineNumber: 743,\n                                                                                                            columnNumber: 33\n                                                                                                        }, this),\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                                                                            value: \"SS 3\",\n                                                                                                            children: \"SS 3\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                                            lineNumber: 744,\n                                                                                                            columnNumber: 33\n                                                                                                        }, this)\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                                    lineNumber: 738,\n                                                                                                    columnNumber: 31\n                                                                                                }, this)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                            lineNumber: 734,\n                                                                                            columnNumber: 29\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                    lineNumber: 730,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"space-y-2\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                                                            htmlFor: \"sec_parent_phone\",\n                                                                                            className: \"text-green-800 dark:text-green-200\",\n                                                                                            children: \"Parent's Phone Number *\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                            lineNumber: 749,\n                                                                                            columnNumber: 29\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"relative\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BookOpen_Building_CheckCircle_CreditCard_FileUp_GraduationCap_Loader2_Mail_MapPin_MessageSquare_Phone_School_User_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                                                    className: \"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-green-500\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                                    lineNumber: 753,\n                                                                                                    columnNumber: 31\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                                                                    id: \"sec_parent_phone\",\n                                                                                                    type: \"tel\",\n                                                                                                    value: secondaryFormData.parent_phone,\n                                                                                                    onChange: (e)=>setSecondaryFormData((prev)=>({\n                                                                                                                ...prev,\n                                                                                                                parent_phone: e.target.value\n                                                                                                            })),\n                                                                                                    placeholder: \"Enter parent's phone number\",\n                                                                                                    className: \"pl-10 rounded-xl border-green-200 dark:border-green-700 focus:border-green-500\",\n                                                                                                    required: true\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                                    lineNumber: 754,\n                                                                                                    columnNumber: 31\n                                                                                                }, this)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                            lineNumber: 752,\n                                                                                            columnNumber: 29\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                    lineNumber: 748,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                            lineNumber: 715,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                    lineNumber: 697,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                            lineNumber: 692,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"text-lg font-semibold text-green-800 dark:text-green-200 mb-4 flex items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BookOpen_Building_CheckCircle_CreditCard_FileUp_GraduationCap_Loader2_Mail_MapPin_MessageSquare_Phone_School_User_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                            className: \"h-5 w-5 mr-2 text-green-600\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                            lineNumber: 772,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        \"Address & School Information\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                    lineNumber: 771,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"space-y-4\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"space-y-2\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                                                    htmlFor: \"sec_address\",\n                                                                                    className: \"text-green-800 dark:text-green-200\",\n                                                                                    children: \"Address *\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                    lineNumber: 777,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"relative\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BookOpen_Building_CheckCircle_CreditCard_FileUp_GraduationCap_Loader2_Mail_MapPin_MessageSquare_Phone_School_User_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                                            className: \"absolute left-3 top-3 h-4 w-4 text-green-500\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                            lineNumber: 781,\n                                                                                            columnNumber: 29\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_5__.Textarea, {\n                                                                                            id: \"sec_address\",\n                                                                                            value: secondaryFormData.address,\n                                                                                            onChange: (e)=>setSecondaryFormData((prev)=>({\n                                                                                                        ...prev,\n                                                                                                        address: e.target.value\n                                                                                                    })),\n                                                                                            placeholder: \"Enter your complete address\",\n                                                                                            className: \"pl-10 rounded-xl border-green-200 dark:border-green-700 focus:border-green-500\",\n                                                                                            rows: 3,\n                                                                                            required: true\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                            lineNumber: 782,\n                                                                                            columnNumber: 29\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                    lineNumber: 780,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                            lineNumber: 776,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"space-y-2\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                                                            htmlFor: \"sec_school_name\",\n                                                                                            className: \"text-green-800 dark:text-green-200\",\n                                                                                            children: \"School Name *\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                            lineNumber: 796,\n                                                                                            columnNumber: 29\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"relative\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BookOpen_Building_CheckCircle_CreditCard_FileUp_GraduationCap_Loader2_Mail_MapPin_MessageSquare_Phone_School_User_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                                                    className: \"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-green-500\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                                    lineNumber: 800,\n                                                                                                    columnNumber: 31\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                                                                    id: \"sec_school_name\",\n                                                                                                    value: secondaryFormData.school_name,\n                                                                                                    onChange: (e)=>setSecondaryFormData((prev)=>({\n                                                                                                                ...prev,\n                                                                                                                school_name: e.target.value\n                                                                                                            })),\n                                                                                                    placeholder: \"Enter your school name\",\n                                                                                                    className: \"pl-10 rounded-xl border-green-200 dark:border-green-700 focus:border-green-500\",\n                                                                                                    required: true\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                                    lineNumber: 801,\n                                                                                                    columnNumber: 31\n                                                                                                }, this)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                            lineNumber: 799,\n                                                                                            columnNumber: 29\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                    lineNumber: 795,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"space-y-2\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                                                            htmlFor: \"sec_principal_name\",\n                                                                                            className: \"text-green-800 dark:text-green-200\",\n                                                                                            children: \"School Principal or Director Name *\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                            lineNumber: 813,\n                                                                                            columnNumber: 29\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                                                            id: \"sec_principal_name\",\n                                                                                            value: secondaryFormData.principal_name,\n                                                                                            onChange: (e)=>setSecondaryFormData((prev)=>({\n                                                                                                        ...prev,\n                                                                                                        principal_name: e.target.value\n                                                                                                    })),\n                                                                                            placeholder: \"Enter principal's name\",\n                                                                                            className: \"rounded-xl border-green-200 dark:border-green-700 focus:border-green-500\",\n                                                                                            required: true\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                            lineNumber: 816,\n                                                                                            columnNumber: 29\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                    lineNumber: 812,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                            lineNumber: 794,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                    lineNumber: 775,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                            lineNumber: 770,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                    lineNumber: 691,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-lg font-semibold text-green-800 dark:text-green-200 mb-4 flex items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BookOpen_Building_CheckCircle_CreditCard_FileUp_GraduationCap_Loader2_Mail_MapPin_MessageSquare_Phone_School_User_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                    className: \"h-5 w-5 mr-2 text-green-600\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                    lineNumber: 833,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                \"Financial Information\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                            lineNumber: 832,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-4\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"space-y-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                                                htmlFor: \"sec_principal_account_number\",\n                                                                                className: \"text-green-800 dark:text-green-200\",\n                                                                                children: \"Principal or Financial Officer Account Number *\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                lineNumber: 839,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"relative\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BookOpen_Building_CheckCircle_CreditCard_FileUp_GraduationCap_Loader2_Mail_MapPin_MessageSquare_Phone_School_User_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                                        className: \"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-green-500\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                        lineNumber: 843,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                                                        id: \"sec_principal_account_number\",\n                                                                                        value: secondaryFormData.principal_account_number,\n                                                                                        onChange: (e)=>setSecondaryFormData((prev)=>({\n                                                                                                    ...prev,\n                                                                                                    principal_account_number: e.target.value\n                                                                                                })),\n                                                                                        placeholder: \"Enter account number for scholarship payment\",\n                                                                                        className: \"pl-10 rounded-xl border-green-200 dark:border-green-700 focus:border-green-500\",\n                                                                                        required: true\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                        lineNumber: 844,\n                                                                                        columnNumber: 29\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                lineNumber: 842,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                        lineNumber: 838,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"space-y-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                                                htmlFor: \"sec_school_fee_amount\",\n                                                                                className: \"text-green-800 dark:text-green-200\",\n                                                                                children: \"School Fee Amount *\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                lineNumber: 856,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                                                id: \"sec_school_fee_amount\",\n                                                                                type: \"number\",\n                                                                                value: secondaryFormData.school_fee_amount,\n                                                                                onChange: (e)=>setSecondaryFormData((prev)=>({\n                                                                                            ...prev,\n                                                                                            school_fee_amount: e.target.value\n                                                                                        })),\n                                                                                placeholder: \"Enter amount in Naira\",\n                                                                                className: \"rounded-xl border-green-200 dark:border-green-700 focus:border-green-500\",\n                                                                                required: true\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                lineNumber: 859,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                        lineNumber: 855,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                lineNumber: 837,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                            lineNumber: 836,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                    lineNumber: 831,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-lg font-semibold text-green-800 dark:text-green-200 mb-4 flex items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BookOpen_Building_CheckCircle_CreditCard_FileUp_GraduationCap_Loader2_Mail_MapPin_MessageSquare_Phone_School_User_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                    className: \"h-5 w-5 mr-2 text-green-600\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                    lineNumber: 876,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                \"Picture & Application Reason\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                            lineNumber: 875,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"space-y-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                                            htmlFor: \"sec_student_picture\",\n                                                                            className: \"text-green-800 dark:text-green-200\",\n                                                                            children: \"Upload Student Picture *\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                            lineNumber: 881,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                                            id: \"sec_student_picture\",\n                                                                            type: \"file\",\n                                                                            accept: \"image/*\",\n                                                                            onChange: (e)=>{\n                                                                                var _e_target_files;\n                                                                                return handleFileChange('secondary', 'student_picture', ((_e_target_files = e.target.files) === null || _e_target_files === void 0 ? void 0 : _e_target_files[0]) || null);\n                                                                            },\n                                                                            className: \"rounded-xl border-green-200 dark:border-green-700 focus:border-green-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-green-50 file:text-green-700 hover:file:bg-green-100\",\n                                                                            required: true\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                            lineNumber: 884,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                    lineNumber: 880,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"space-y-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                                            htmlFor: \"sec_reason_for_scholarship\",\n                                                                            className: \"text-green-800 dark:text-green-200\",\n                                                                            children: \"Reason for the Scholarship *\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                            lineNumber: 895,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_5__.Textarea, {\n                                                                            id: \"sec_reason_for_scholarship\",\n                                                                            value: secondaryFormData.reason_for_scholarship,\n                                                                            onChange: (e)=>setSecondaryFormData((prev)=>({\n                                                                                        ...prev,\n                                                                                        reason_for_scholarship: e.target.value\n                                                                                    })),\n                                                                            placeholder: \"Please explain why you need this scholarship\",\n                                                                            className: \"rounded-xl border-green-200 dark:border-green-700 focus:border-green-500\",\n                                                                            rows: 4,\n                                                                            required: true\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                            lineNumber: 898,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                    lineNumber: 894,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                            lineNumber: 879,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                    lineNumber: 874,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-center pt-8\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                        type: \"submit\",\n                                                        disabled: submitting || !validateForm(),\n                                                        size: \"lg\",\n                                                        className: \"bg-gradient-to-r from-green-600 via-emerald-600 to-teal-600 hover:from-green-700 hover:via-emerald-700 hover:to-teal-700 text-white px-12 py-4 text-lg font-bold shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105\",\n                                                        children: submitting ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BookOpen_Building_CheckCircle_CreditCard_FileUp_GraduationCap_Loader2_Mail_MapPin_MessageSquare_Phone_School_User_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                    className: \"mr-3 h-6 w-6 animate-spin\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                    lineNumber: 920,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                \"Submitting Application...\"\n                                                            ]\n                                                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BookOpen_Building_CheckCircle_CreditCard_FileUp_GraduationCap_Loader2_Mail_MapPin_MessageSquare_Phone_School_User_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                    className: \"mr-3 h-6 w-6\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                    lineNumber: 925,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                \"Submit Secondary School Application\"\n                                                            ]\n                                                        }, void 0, true)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                        lineNumber: 912,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                    lineNumber: 911,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                            lineNumber: 680,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                        lineNumber: 679,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_7__.TabsContent, {\n                                        value: \"university\",\n                                        className: \"space-y-8\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                            onSubmit: handleSubmit,\n                                            className: \"space-y-8\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-purple-50 p-6 rounded-lg border-2 border-purple-200 shadow-sm\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BookOpen_Building_CheckCircle_CreditCard_FileUp_GraduationCap_Loader2_Mail_MapPin_MessageSquare_Phone_School_User_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                className: \"h-6 w-6 text-purple-600 mr-3\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                lineNumber: 939,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-purple-800 font-semibold text-lg\",\n                                                                children: \"This form should be filled by the University Student directly\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                lineNumber: 940,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                        lineNumber: 938,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                    lineNumber: 937,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-lg font-semibold text-green-800 dark:text-green-200 mb-4 flex items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BookOpen_Building_CheckCircle_CreditCard_FileUp_GraduationCap_Loader2_Mail_MapPin_MessageSquare_Phone_School_User_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                    className: \"h-5 w-5 mr-2 text-green-600\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                    lineNumber: 949,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                \"Personal Information\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                            lineNumber: 948,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"space-y-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                                            htmlFor: \"uni_full_name\",\n                                                                            className: \"text-green-800 dark:text-green-200\",\n                                                                            children: \"Full Name *\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                            lineNumber: 954,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"relative\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BookOpen_Building_CheckCircle_CreditCard_FileUp_GraduationCap_Loader2_Mail_MapPin_MessageSquare_Phone_School_User_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                                    className: \"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-green-500\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                    lineNumber: 958,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                                                    id: \"uni_full_name\",\n                                                                                    value: universityFormData.full_name,\n                                                                                    onChange: (e)=>setUniversityFormData((prev)=>({\n                                                                                                ...prev,\n                                                                                                full_name: e.target.value\n                                                                                            })),\n                                                                                    placeholder: \"Enter your full name\",\n                                                                                    className: \"pl-10 rounded-xl border-green-200 dark:border-green-700 focus:border-green-500\",\n                                                                                    required: true\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                    lineNumber: 959,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                            lineNumber: 957,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                    lineNumber: 953,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"space-y-2\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                                                    htmlFor: \"uni_age\",\n                                                                                    className: \"text-green-800 dark:text-green-200\",\n                                                                                    children: \"Age *\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                    lineNumber: 972,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                                                    id: \"uni_age\",\n                                                                                    type: \"number\",\n                                                                                    value: universityFormData.age,\n                                                                                    onChange: (e)=>setUniversityFormData((prev)=>({\n                                                                                                ...prev,\n                                                                                                age: e.target.value\n                                                                                            })),\n                                                                                    placeholder: \"Your age\",\n                                                                                    className: \"rounded-xl border-green-200 dark:border-green-700 focus:border-green-500\",\n                                                                                    required: true\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                    lineNumber: 975,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                            lineNumber: 971,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"space-y-2\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                                                    htmlFor: \"uni_phone_number\",\n                                                                                    className: \"text-green-800 dark:text-green-200\",\n                                                                                    children: \"Phone Number *\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                    lineNumber: 987,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"relative\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BookOpen_Building_CheckCircle_CreditCard_FileUp_GraduationCap_Loader2_Mail_MapPin_MessageSquare_Phone_School_User_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                                            className: \"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-green-500\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                            lineNumber: 991,\n                                                                                            columnNumber: 29\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                                                            id: \"uni_phone_number\",\n                                                                                            type: \"tel\",\n                                                                                            value: universityFormData.phone_number,\n                                                                                            onChange: (e)=>setUniversityFormData((prev)=>({\n                                                                                                        ...prev,\n                                                                                                        phone_number: e.target.value\n                                                                                                    })),\n                                                                                            placeholder: \"Enter your phone number\",\n                                                                                            className: \"pl-10 rounded-xl border-green-200 dark:border-green-700 focus:border-green-500\",\n                                                                                            required: true\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                            lineNumber: 992,\n                                                                                            columnNumber: 29\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                    lineNumber: 990,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                            lineNumber: 986,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                    lineNumber: 970,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"space-y-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                                            htmlFor: \"uni_email_address\",\n                                                                            className: \"text-green-800 dark:text-green-200\",\n                                                                            children: \"Email Address *\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                            lineNumber: 1006,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"relative\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BookOpen_Building_CheckCircle_CreditCard_FileUp_GraduationCap_Loader2_Mail_MapPin_MessageSquare_Phone_School_User_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                                    className: \"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-green-500\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                    lineNumber: 1010,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                                                    id: \"uni_email_address\",\n                                                                                    type: \"email\",\n                                                                                    value: universityFormData.email_address,\n                                                                                    onChange: (e)=>setUniversityFormData((prev)=>({\n                                                                                                ...prev,\n                                                                                                email_address: e.target.value\n                                                                                            })),\n                                                                                    placeholder: \"Enter your email address\",\n                                                                                    className: \"pl-10 rounded-xl border-green-200 dark:border-green-700 focus:border-green-500\",\n                                                                                    required: true\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                    lineNumber: 1011,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                            lineNumber: 1009,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                    lineNumber: 1005,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                            lineNumber: 952,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                    lineNumber: 947,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-lg font-semibold text-green-800 dark:text-green-200 mb-4 flex items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BookOpen_Building_CheckCircle_CreditCard_FileUp_GraduationCap_Loader2_Mail_MapPin_MessageSquare_Phone_School_User_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                    className: \"h-5 w-5 mr-2 text-green-600\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                    lineNumber: 1028,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                \"Academic Information\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                            lineNumber: 1027,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"space-y-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                                            htmlFor: \"uni_course_of_study\",\n                                                                            className: \"text-green-800 dark:text-green-200\",\n                                                                            children: \"Course of Study *\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                            lineNumber: 1033,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                                            id: \"uni_course_of_study\",\n                                                                            value: universityFormData.course_of_study,\n                                                                            onChange: (e)=>setUniversityFormData((prev)=>({\n                                                                                        ...prev,\n                                                                                        course_of_study: e.target.value\n                                                                                    })),\n                                                                            placeholder: \"Enter your course of study\",\n                                                                            className: \"rounded-xl border-green-200 dark:border-green-700 focus:border-green-500\",\n                                                                            required: true\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                            lineNumber: 1036,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                    lineNumber: 1032,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"space-y-2\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                                                    htmlFor: \"uni_current_level\",\n                                                                                    className: \"text-green-800 dark:text-green-200\",\n                                                                                    children: \"Current Level *\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                    lineNumber: 1048,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.Select, {\n                                                                                    value: universityFormData.current_level,\n                                                                                    onValueChange: (value)=>setUniversityFormData((prev)=>({\n                                                                                                ...prev,\n                                                                                                current_level: value\n                                                                                            })),\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectTrigger, {\n                                                                                            className: \"rounded-xl border-green-200 dark:border-green-700 focus:border-green-500\",\n                                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectValue, {\n                                                                                                placeholder: \"Select your current level\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                                lineNumber: 1053,\n                                                                                                columnNumber: 31\n                                                                                            }, this)\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                            lineNumber: 1052,\n                                                                                            columnNumber: 29\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectContent, {\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                                                                    value: \"100L\",\n                                                                                                    children: \"100 Level\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                                    lineNumber: 1056,\n                                                                                                    columnNumber: 31\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                                                                    value: \"200L\",\n                                                                                                    children: \"200 Level\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                                    lineNumber: 1057,\n                                                                                                    columnNumber: 31\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                                                                    value: \"300L\",\n                                                                                                    children: \"300 Level\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                                    lineNumber: 1058,\n                                                                                                    columnNumber: 31\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                                                                    value: \"400L\",\n                                                                                                    children: \"400 Level\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                                    lineNumber: 1059,\n                                                                                                    columnNumber: 31\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                                                                    value: \"500L\",\n                                                                                                    children: \"500 Level\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                                    lineNumber: 1060,\n                                                                                                    columnNumber: 31\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                                                                    value: \"600L\",\n                                                                                                    children: \"600 Level\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                                    lineNumber: 1061,\n                                                                                                    columnNumber: 31\n                                                                                                }, this)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                            lineNumber: 1055,\n                                                                                            columnNumber: 29\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                    lineNumber: 1051,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                            lineNumber: 1047,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"space-y-2\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                                                    htmlFor: \"uni_matriculation_number\",\n                                                                                    className: \"text-green-800 dark:text-green-200\",\n                                                                                    children: \"Matriculation Number *\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                    lineNumber: 1067,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                                                    id: \"uni_matriculation_number\",\n                                                                                    value: universityFormData.matriculation_number,\n                                                                                    onChange: (e)=>setUniversityFormData((prev)=>({\n                                                                                                ...prev,\n                                                                                                matriculation_number: e.target.value\n                                                                                            })),\n                                                                                    placeholder: \"Enter your matriculation number\",\n                                                                                    className: \"rounded-xl border-green-200 dark:border-green-700 focus:border-green-500\",\n                                                                                    required: true\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                    lineNumber: 1070,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                            lineNumber: 1066,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                    lineNumber: 1046,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                            lineNumber: 1031,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                    lineNumber: 1026,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-lg font-semibold text-green-800 dark:text-green-200 mb-4 flex items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BookOpen_Building_CheckCircle_CreditCard_FileUp_GraduationCap_Loader2_Mail_MapPin_MessageSquare_Phone_School_User_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                    className: \"h-5 w-5 mr-2 text-green-600\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                    lineNumber: 1086,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                \"Required Documents\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                            lineNumber: 1085,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"space-y-2\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                                                    htmlFor: \"uni_student_id_card\",\n                                                                                    className: \"text-green-800 dark:text-green-200\",\n                                                                                    children: \"Upload Student ID Card *\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                    lineNumber: 1092,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                                                    id: \"uni_student_id_card\",\n                                                                                    type: \"file\",\n                                                                                    accept: \"image/*,.pdf\",\n                                                                                    onChange: (e)=>{\n                                                                                        var _e_target_files;\n                                                                                        return handleFileChange('university', 'student_id_card', ((_e_target_files = e.target.files) === null || _e_target_files === void 0 ? void 0 : _e_target_files[0]) || null);\n                                                                                    },\n                                                                                    className: \"rounded-xl border-green-200 dark:border-green-700 focus:border-green-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-green-50 file:text-green-700 hover:file:bg-green-100\",\n                                                                                    required: true\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                    lineNumber: 1095,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                            lineNumber: 1091,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"space-y-2\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                                                    htmlFor: \"uni_payment_evidence\",\n                                                                                    className: \"text-green-800 dark:text-green-200\",\n                                                                                    children: \"Upload Payment Evidence (Remita/Screenshot) *\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                    lineNumber: 1106,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                                                    id: \"uni_payment_evidence\",\n                                                                                    type: \"file\",\n                                                                                    accept: \"image/*,.pdf\",\n                                                                                    onChange: (e)=>{\n                                                                                        var _e_target_files;\n                                                                                        return handleFileChange('university', 'payment_evidence', ((_e_target_files = e.target.files) === null || _e_target_files === void 0 ? void 0 : _e_target_files[0]) || null);\n                                                                                    },\n                                                                                    className: \"rounded-xl border-green-200 dark:border-green-700 focus:border-green-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-green-50 file:text-green-700 hover:file:bg-green-100\",\n                                                                                    required: true\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                    lineNumber: 1109,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                            lineNumber: 1105,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                    lineNumber: 1090,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"space-y-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                                            htmlFor: \"uni_supporting_documents\",\n                                                                            className: \"text-green-800 dark:text-green-200\",\n                                                                            children: \"Upload Supporting Documents (PDF, DOCX, JPG)\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                            lineNumber: 1121,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                                            id: \"uni_supporting_documents\",\n                                                                            type: \"file\",\n                                                                            accept: \".pdf,.docx,.doc,.jpg,.jpeg,.png\",\n                                                                            multiple: true,\n                                                                            onChange: (e)=>handleMultipleFileChange(e.target.files),\n                                                                            className: \"rounded-xl border-green-200 dark:border-green-700 focus:border-green-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-green-50 file:text-green-700 hover:file:bg-green-100\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                            lineNumber: 1124,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        universityFiles.supporting_documents.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"mt-2 space-y-2\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"text-sm text-green-600\",\n                                                                                    children: \"Selected files:\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                    lineNumber: 1134,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                universityFiles.supporting_documents.map((file, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"flex items-center justify-between bg-green-50 p-2 rounded-xl border border-green-200\",\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                className: \"text-sm text-green-700\",\n                                                                                                children: file.name\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                                lineNumber: 1137,\n                                                                                                columnNumber: 33\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                                                type: \"button\",\n                                                                                                variant: \"ghost\",\n                                                                                                size: \"sm\",\n                                                                                                onClick: ()=>removeFile(index),\n                                                                                                className: \"text-red-600 hover:text-red-800\",\n                                                                                                children: \"Remove\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                                lineNumber: 1138,\n                                                                                                columnNumber: 33\n                                                                                            }, this)\n                                                                                        ]\n                                                                                    }, index, true, {\n                                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                        lineNumber: 1136,\n                                                                                        columnNumber: 31\n                                                                                    }, this))\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                            lineNumber: 1133,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                    lineNumber: 1120,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                            lineNumber: 1089,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                    lineNumber: 1084,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-lg font-semibold text-green-800 dark:text-green-200 mb-4 flex items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BookOpen_Building_CheckCircle_CreditCard_FileUp_GraduationCap_Loader2_Mail_MapPin_MessageSquare_Phone_School_User_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                    className: \"h-5 w-5 mr-2 text-green-600\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                    lineNumber: 1158,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                \"Application Reason\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                            lineNumber: 1157,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                                    htmlFor: \"uni_reason_for_scholarship\",\n                                                                    className: \"text-green-800 dark:text-green-200\",\n                                                                    children: \"Reason for the Scholarship *\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                    lineNumber: 1162,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_5__.Textarea, {\n                                                                    id: \"uni_reason_for_scholarship\",\n                                                                    value: universityFormData.reason_for_scholarship,\n                                                                    onChange: (e)=>setUniversityFormData((prev)=>({\n                                                                                ...prev,\n                                                                                reason_for_scholarship: e.target.value\n                                                                            })),\n                                                                    placeholder: \"Please explain why you need this scholarship\",\n                                                                    className: \"rounded-xl border-green-200 dark:border-green-700 focus:border-green-500\",\n                                                                    rows: 4,\n                                                                    required: true\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                    lineNumber: 1165,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                            lineNumber: 1161,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                    lineNumber: 1156,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-center pt-8\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                        type: \"submit\",\n                                                        disabled: submitting || !validateForm(),\n                                                        size: \"lg\",\n                                                        className: \"bg-gradient-to-r from-purple-600 via-violet-600 to-indigo-600 hover:from-purple-700 hover:via-violet-700 hover:to-indigo-700 text-white px-12 py-4 text-lg font-bold shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105\",\n                                                        children: submitting ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BookOpen_Building_CheckCircle_CreditCard_FileUp_GraduationCap_Loader2_Mail_MapPin_MessageSquare_Phone_School_User_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                    className: \"mr-3 h-6 w-6 animate-spin\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                    lineNumber: 1186,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                \"Submitting Application...\"\n                                                            ]\n                                                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BookOpen_Building_CheckCircle_CreditCard_FileUp_GraduationCap_Loader2_Mail_MapPin_MessageSquare_Phone_School_User_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                    className: \"mr-3 h-6 w-6\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                    lineNumber: 1191,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                \"Submit University Application\"\n                                                            ]\n                                                        }, void 0, true)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                        lineNumber: 1178,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                    lineNumber: 1177,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                            lineNumber: 936,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                        lineNumber: 935,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                lineNumber: 351,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                        lineNumber: 343,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                    lineNumber: 342,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                lineNumber: 341,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n        lineNumber: 325,\n        columnNumber: 5\n    }, this);\n}\n_s(ScholarshipApplicationPage, \"Sti8wAfvn58MX1lRHs4cA5vIto0=\", false, function() {\n    return [\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_8__.useToast\n    ];\n});\n_c = ScholarshipApplicationPage;\nvar _c;\n$RefreshReg$(_c, \"ScholarshipApplicationPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/scholarship/ScholarshipApplicationPage.tsx\n"));

/***/ })

});