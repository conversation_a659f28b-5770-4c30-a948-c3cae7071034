"use strict";exports.id=666,exports.ids=[666],exports.modules={91666:(e,s,a)=>{a.r(s),a.d(s,{default:()=>H});var t=a(60687),r=a(43210),l=a(59556),c=a(55192),i=a(24934),n=a(59821),d=a(85910),m=a(78122),o=a(16103),x=a(31158),h=a(58869),u=a(41550),p=a(48340),j=a(40228),g=a(94424),f=a(4403),N=a(40093);function w({user:e}){let[s,a]=(0,r.useState)(!1),[d,w]=(0,r.useState)(!1),[y,v]=(0,r.useState)(null),b=(0,r.useRef)(null),{settings:_}=(0,N.t)(),C=_?.app_name||"Laravel NGO Foundation",A=async()=>{try{a(!0);let e=await l.uE.getIdCard();e.success&&e.data&&v(e.data)}catch(e){console.error("Failed to fetch ID card data:",e)}finally{a(!1)}},S=async()=>{try{w(!0),(await l.uE.generateQrCode()).success&&await A()}catch(e){console.error("Failed to generate QR code:",e)}finally{w(!1)}},D=async()=>{if(b.current&&y)try{a(!0);let e=(await (0,g.default)(b.current,{scale:2,useCORS:!0,allowTaint:!0,backgroundColor:"#ffffff"})).toDataURL("image/png"),s=new f.Ay({orientation:"portrait",unit:"mm",format:"a4"});s.addImage(e,"PNG",62.2,50,85.6,53.98),s.save(`${y.short_id}_${C.replace(/\s+/g,"_")}_ID_Card.pdf`)}catch(e){console.error("Failed to download ID card:",e)}finally{a(!1)}},R=y||{short_id:e.short_id||"Loading...",full_name:`${e.first_name} ${e.last_name}`,email:e.email,phone_number:e.phone_number,role:e.role,qr_code_url:e.qr_code_url,profile_picture_url:e.profile_picture_url,organization:C,member_since:e.created_at?new Date(e.created_at).toLocaleDateString("en-US",{year:"numeric",month:"long"}):"Unknown",address:e.address?`${e.city}, ${e.state}, ${e.country}`:null};return(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsxs)("h2",{className:"text-2xl font-bold",children:["Your ",C," ID Card"]}),(0,t.jsxs)("div",{className:"flex gap-2",children:[(0,t.jsxs)(i.$,{onClick:S,disabled:d,variant:"outline",size:"sm",children:[d?(0,t.jsx)(m.A,{className:"h-4 w-4 animate-spin mr-2"}):(0,t.jsx)(o.A,{className:"h-4 w-4 mr-2"}),"Generate New QR"]}),(0,t.jsxs)(i.$,{onClick:D,disabled:s||!y,size:"sm",children:[s?(0,t.jsx)(m.A,{className:"h-4 w-4 animate-spin mr-2"}):(0,t.jsx)(x.A,{className:"h-4 w-4 mr-2"}),"Download PDF"]})]})]}),(0,t.jsx)("div",{className:"flex justify-center",children:(0,t.jsxs)("div",{ref:b,className:"w-[340px] h-[214px] bg-gradient-to-br from-green-600 to-green-800 rounded-lg shadow-lg overflow-hidden relative",style:{aspectRatio:"85.6/53.98"},children:[(0,t.jsxs)("div",{className:"bg-white/20 backdrop-blur-sm px-4 py-2",children:[(0,t.jsx)("h3",{className:"text-white font-bold text-lg",children:R.organization}),(0,t.jsx)("p",{className:"text-white/90 text-sm",children:"Member ID Card"})]}),(0,t.jsxs)("div",{className:"flex p-4 h-full",children:[(0,t.jsxs)("div",{className:"flex-1 space-y-3",children:[(0,t.jsx)("div",{className:"w-16 h-16 bg-white rounded-full flex items-center justify-center overflow-hidden",children:R.profile_picture_url?(0,t.jsx)("img",{src:R.profile_picture_url,alt:"Profile",className:"w-full h-full object-cover"}):(0,t.jsx)(h.A,{className:"h-8 w-8 text-gray-400"})}),(0,t.jsxs)("div",{className:"space-y-1",children:[(0,t.jsx)("h4",{className:"text-white font-bold text-sm",children:R.full_name}),(0,t.jsxs)("p",{className:"text-white/90 text-xs",children:["ID: ",R.short_id]}),(0,t.jsx)(n.E,{variant:"secondary",className:"text-xs",children:R.role})]}),(0,t.jsxs)("div",{className:"space-y-1 text-white/80 text-xs",children:[(0,t.jsxs)("div",{className:"flex items-center gap-1",children:[(0,t.jsx)(u.A,{className:"h-3 w-3"}),(0,t.jsx)("span",{className:"truncate",children:R.email})]}),R.phone_number&&(0,t.jsxs)("div",{className:"flex items-center gap-1",children:[(0,t.jsx)(p.A,{className:"h-3 w-3"}),(0,t.jsx)("span",{children:R.phone_number})]}),(0,t.jsxs)("div",{className:"flex items-center gap-1",children:[(0,t.jsx)(j.A,{className:"h-3 w-3"}),(0,t.jsxs)("span",{children:["Since ",R.member_since]})]})]})]}),(0,t.jsxs)("div",{className:"flex flex-col items-center justify-center",children:[(0,t.jsx)("div",{className:"w-20 h-20 bg-white rounded p-1",children:R.qr_code_url?(0,t.jsx)("img",{src:R.qr_code_url,alt:"QR Code",className:"w-full h-full object-contain"}):(0,t.jsx)("div",{className:"w-full h-full bg-gray-200 rounded flex items-center justify-center",children:(0,t.jsx)(o.A,{className:"h-8 w-8 text-gray-400"})})}),(0,t.jsx)("p",{className:"text-white/70 text-xs mt-1",children:"Scan for details"})]})]}),(0,t.jsx)("div",{className:"absolute bottom-0 left-0 right-0 bg-white/10 backdrop-blur-sm px-4 py-1",children:(0,t.jsx)("p",{className:"text-white/70 text-xs text-center",children:_?.organization_address||"foundation.org"})})]})}),(0,t.jsxs)(c.Zp,{children:[(0,t.jsx)(c.aR,{children:(0,t.jsxs)(c.ZB,{className:"flex items-center gap-2",children:[(0,t.jsx)(h.A,{className:"h-5 w-5"}),"ID Card Information"]})}),(0,t.jsxs)(c.Wu,{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"text-sm font-medium text-gray-500",children:"Member ID"}),(0,t.jsx)("p",{className:"text-lg font-mono",children:R.short_id})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"text-sm font-medium text-gray-500",children:"Full Name"}),(0,t.jsx)("p",{children:R.full_name})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"text-sm font-medium text-gray-500",children:"Role"}),(0,t.jsx)("p",{className:"capitalize",children:R.role})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"text-sm font-medium text-gray-500",children:"Member Since"}),(0,t.jsx)("p",{children:R.member_since})]}),(0,t.jsxs)("div",{className:"md:col-span-2",children:[(0,t.jsx)("label",{className:"text-sm font-medium text-gray-500",children:"Email"}),(0,t.jsx)("p",{children:R.email})]}),R.phone_number&&(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"text-sm font-medium text-gray-500",children:"Phone"}),(0,t.jsx)("p",{children:R.phone_number})]}),R.address&&(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"text-sm font-medium text-gray-500",children:"Address"}),(0,t.jsx)("p",{children:R.address})]})]}),(0,t.jsx)("div",{className:"border-t pt-4",children:(0,t.jsxs)("p",{className:"text-sm text-gray-600",children:["This QR code contains your member information and can be scanned to verify your identity as a member of ",C,". Keep this ID card safe and contact support if you need to regenerate your QR code."]})})]})]})]})}var y=a(68988),v=a(39390),b=a(15616),_=a(5336),C=a(93613),A=a(41862),S=a(8819),D=a(64021),R=a(12597),$=a(13861),P=a(51361),k=a(16023),I=a(63974),F=a(24224),B=a(96241);let L=(0,F.F)("relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground",{variants:{variant:{default:"bg-background text-foreground",destructive:"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive"}},defaultVariants:{variant:"default"}}),Z=r.forwardRef(({className:e,variant:s,...a},r)=>(0,t.jsx)("div",{ref:r,role:"alert",className:(0,B.cn)(L({variant:s}),e),...a}));Z.displayName="Alert",r.forwardRef(({className:e,...s},a)=>(0,t.jsx)("h5",{ref:a,className:(0,B.cn)("mb-1 font-medium leading-none tracking-tight",e),...s})).displayName="AlertTitle";let z=r.forwardRef(({className:e,...s},a)=>(0,t.jsx)("div",{ref:a,className:(0,B.cn)("text-sm [&_p]:leading-relaxed",e),...s}));function E({user:e,onUserUpdate:s}){let[a,m]=(0,r.useState)({first_name:e.first_name,last_name:e.last_name,phone_number:e.phone_number||"",date_of_birth:e.date_of_birth||"",gender:e.gender||"",address:e.address||"",city:e.city||"",state:e.state||"",country:e.country||"Nigeria"}),[o,u]=(0,r.useState)({current_password:"",new_password:"",new_password_confirmation:""}),[p,j]=(0,r.useState)(!1),[g,f]=(0,r.useState)(!1),[w,F]=(0,r.useState)(!1),[B,L]=(0,r.useState)(!1),[E,M]=(0,r.useState)(null),[T,q]=(0,r.useState)(null),[J,U]=(0,r.useState)(null),[W,O]=(0,r.useState)(!1),[Q,G]=(0,r.useState)(!1),[X,Y]=(0,r.useState)(!1),V=(0,r.useRef)(null),{settings:H}=(0,N.t)(),K=async e=>{e.preventDefault(),j(!0),M(null);try{let e=await l.uE.updateProfile(a);if(e.success){M({type:"success",text:"Profile updated successfully!"}),s&&e.user&&s(e.user);let a=JSON.parse(localStorage.getItem("user")||"{}");localStorage.setItem("user",JSON.stringify({...a,...e.user}))}else M({type:"error",text:e.message||"Failed to update profile"})}catch(e){console.error("Profile update error:",e),M({type:"error",text:"An error occurred while updating profile"})}finally{j(!1)}},ee=async e=>{if(e.preventDefault(),f(!0),q(null),o.new_password!==o.new_password_confirmation){q({type:"error",text:"New passwords do not match"}),f(!1);return}try{let e=await l.uE.request("/profile/password",{method:"PUT",body:JSON.stringify(o)});e.success?(q({type:"success",text:"Password changed successfully!"}),u({current_password:"",new_password:"",new_password_confirmation:""})):q({type:"error",text:e.message||"Failed to change password"})}catch(e){console.error("Password update error:",e),q({type:"error",text:"An error occurred while changing password"})}finally{f(!1)}},es=async e=>{let a=e.target.files?.[0];if(a){if(!a.type.startsWith("image/")){U({type:"error",text:"Please select an image file"});return}if(a.size>2097152){U({type:"error",text:"Image size must be less than 2MB"});return}F(!0),U(null);try{let e=new FormData;e.append("avatar",a);let t=await fetch("http://localhost:8000/api/v1/profile/avatar",{method:"POST",headers:{Authorization:`Bearer ${localStorage.getItem("authToken")}`,Accept:"application/json"},body:e}),r=await t.json();if(r.success){U({type:"success",text:"Avatar updated successfully!"});let e={...JSON.parse(localStorage.getItem("user")||"{}"),profile_picture_url:r.avatar_url};localStorage.setItem("user",JSON.stringify(e)),s&&s(e),window.location.reload()}else U({type:"error",text:r.message||"Failed to upload avatar"})}catch(e){console.error("Avatar upload error:",e),U({type:"error",text:"An error occurred while uploading avatar"})}finally{F(!1)}}},ea=async()=>{L(!0);try{let e=await l.uE.request("/profile/generate-qr",{method:"POST"});if(e.success){U({type:"success",text:"QR Code regenerated successfully!"});let a={...JSON.parse(localStorage.getItem("user")||"{}"),qr_code_url:e.data.qr_code_url};localStorage.setItem("user",JSON.stringify(a)),s&&s(a),window.location.reload()}else U({type:"error",text:e.message||"Failed to regenerate QR code"})}catch(e){console.error("QR code generation error:",e),U({type:"error",text:"An error occurred while generating QR code"})}finally{L(!1)}},et=async()=>{try{let s=localStorage.getItem("authToken");if(!s){U({type:"error",text:"Please login to download ID card"});return}let a=await fetch("http://localhost:8000/api/v1/api/v1/profile/id-card/pdf",{method:"GET",headers:{Authorization:`Bearer ${s}`,Accept:"application/pdf"}});if(!a.ok)throw Error("Failed to download ID card");let t=await a.blob(),r=window.URL.createObjectURL(t),l=document.createElement("a");l.href=r;let c=H?.app_name||"Laravel_NGO";l.download=`${e.first_name}_${e.last_name}_${c.replace(/\s+/g,"_")}_ID_Card.pdf`,document.body.appendChild(l),l.click(),window.URL.revokeObjectURL(r),document.body.removeChild(l),U({type:"success",text:"ID card downloaded successfully!"})}catch(e){console.error("Failed to download ID card:",e),U({type:"error",text:"Failed to download ID card"})}};return(0,t.jsxs)("div",{className:"space-y-6 p-4 sm:p-6",children:[(0,t.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h2",{className:"text-2xl font-bold text-gray-900",children:"Profile Settings"}),(0,t.jsx)("p",{className:"text-gray-600",children:"Manage your account information and preferences"})]}),(0,t.jsxs)(n.E,{variant:"outline",className:"w-fit",children:["ID: ",e.short_id]})]}),(0,t.jsxs)(d.tU,{defaultValue:"profile",className:"space-y-6",children:[(0,t.jsxs)(d.j7,{className:"grid w-full grid-cols-4",children:[(0,t.jsx)(d.Xi,{value:"profile",children:"Profile"}),(0,t.jsx)(d.Xi,{value:"security",children:"Security"}),(0,t.jsx)(d.Xi,{value:"avatar",children:"Avatar"}),(0,t.jsx)(d.Xi,{value:"qrcode",children:"QR Code"})]}),(0,t.jsx)(d.av,{value:"profile",className:"space-y-6",children:(0,t.jsxs)(c.Zp,{children:[(0,t.jsxs)(c.aR,{children:[(0,t.jsxs)(c.ZB,{className:"flex items-center gap-2",children:[(0,t.jsx)(h.A,{className:"h-5 w-5"}),"Personal Information"]}),(0,t.jsx)(c.BT,{children:"Update your personal details and contact information"})]}),(0,t.jsxs)(c.Wu,{children:[E&&(0,t.jsxs)(Z,{className:`mb-6 ${"success"===E.type?"border-green-200 bg-green-50":"border-red-200 bg-red-50"}`,children:["success"===E.type?(0,t.jsx)(_.A,{className:"h-4 w-4 text-green-600"}):(0,t.jsx)(C.A,{className:"h-4 w-4 text-red-600"}),(0,t.jsx)(z,{className:"success"===E.type?"text-green-800":"text-red-800",children:E.text})]}),(0,t.jsxs)("form",{onSubmit:K,className:"space-y-6",children:[(0,t.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(v.J,{htmlFor:"first_name",children:"First Name *"}),(0,t.jsx)(y.p,{id:"first_name",value:a.first_name,onChange:e=>m(s=>({...s,first_name:e.target.value})),required:!0})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(v.J,{htmlFor:"last_name",children:"Last Name *"}),(0,t.jsx)(y.p,{id:"last_name",value:a.last_name,onChange:e=>m(s=>({...s,last_name:e.target.value})),required:!0})]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(v.J,{htmlFor:"phone_number",children:"Phone Number"}),(0,t.jsx)(y.p,{id:"phone_number",type:"tel",value:a.phone_number,onChange:e=>m(s=>({...s,phone_number:e.target.value})),placeholder:"+234"})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(v.J,{htmlFor:"date_of_birth",children:"Date of Birth"}),(0,t.jsx)(y.p,{id:"date_of_birth",type:"date",value:a.date_of_birth,onChange:e=>m(s=>({...s,date_of_birth:e.target.value}))})]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(v.J,{htmlFor:"gender",children:"Gender"}),(0,t.jsxs)(I.l6,{value:a.gender,onValueChange:e=>m(s=>({...s,gender:e})),children:[(0,t.jsx)(I.bq,{children:(0,t.jsx)(I.yv,{placeholder:"Select gender"})}),(0,t.jsxs)(I.gC,{children:[(0,t.jsx)(I.eb,{value:"male",children:"Male"}),(0,t.jsx)(I.eb,{value:"female",children:"Female"}),(0,t.jsx)(I.eb,{value:"other",children:"Other"})]})]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(v.J,{htmlFor:"address",children:"Address"}),(0,t.jsx)(b.T,{id:"address",value:a.address,onChange:e=>m(s=>({...s,address:e.target.value})),placeholder:"Enter your full address",rows:3})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-3 gap-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(v.J,{htmlFor:"city",children:"City"}),(0,t.jsx)(y.p,{id:"city",value:a.city,onChange:e=>m(s=>({...s,city:e.target.value})),placeholder:"e.g., Lagos"})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(v.J,{htmlFor:"state",children:"State"}),(0,t.jsx)(y.p,{id:"state",value:a.state,onChange:e=>m(s=>({...s,state:e.target.value})),placeholder:"e.g., Lagos State"})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(v.J,{htmlFor:"country",children:"Country"}),(0,t.jsx)(y.p,{id:"country",value:a.country,onChange:e=>m(s=>({...s,country:e.target.value})),placeholder:"e.g., Nigeria"})]})]}),(0,t.jsx)(i.$,{type:"submit",disabled:p,className:"w-full sm:w-auto",children:p?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(A.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Updating..."]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(S.A,{className:"mr-2 h-4 w-4"}),"Save Changes"]})})]})]})]})}),(0,t.jsx)(d.av,{value:"security",className:"space-y-6",children:(0,t.jsxs)(c.Zp,{children:[(0,t.jsxs)(c.aR,{children:[(0,t.jsxs)(c.ZB,{className:"flex items-center gap-2",children:[(0,t.jsx)(D.A,{className:"h-5 w-5"}),"Change Password"]}),(0,t.jsx)(c.BT,{children:"Update your password to keep your account secure"})]}),(0,t.jsxs)(c.Wu,{children:[T&&(0,t.jsxs)(Z,{className:`mb-6 ${"success"===T.type?"border-green-200 bg-green-50":"border-red-200 bg-red-50"}`,children:["success"===T.type?(0,t.jsx)(_.A,{className:"h-4 w-4 text-green-600"}):(0,t.jsx)(C.A,{className:"h-4 w-4 text-red-600"}),(0,t.jsx)(z,{className:"success"===T.type?"text-green-800":"text-red-800",children:T.text})]}),(0,t.jsxs)("form",{onSubmit:ee,className:"space-y-6",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(v.J,{htmlFor:"current_password",children:"Current Password *"}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(y.p,{id:"current_password",type:W?"text":"password",value:o.current_password,onChange:e=>u(s=>({...s,current_password:e.target.value})),required:!0}),(0,t.jsx)(i.$,{type:"button",variant:"ghost",size:"sm",className:"absolute right-0 top-0 h-full px-3",onClick:()=>O(!W),children:W?(0,t.jsx)(R.A,{className:"h-4 w-4"}):(0,t.jsx)($.A,{className:"h-4 w-4"})})]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(v.J,{htmlFor:"new_password",children:"New Password *"}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(y.p,{id:"new_password",type:Q?"text":"password",value:o.new_password,onChange:e=>u(s=>({...s,new_password:e.target.value})),required:!0,minLength:8}),(0,t.jsx)(i.$,{type:"button",variant:"ghost",size:"sm",className:"absolute right-0 top-0 h-full px-3",onClick:()=>G(!Q),children:Q?(0,t.jsx)(R.A,{className:"h-4 w-4"}):(0,t.jsx)($.A,{className:"h-4 w-4"})})]}),(0,t.jsx)("p",{className:"text-sm text-gray-500",children:"Password must be at least 8 characters long"})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(v.J,{htmlFor:"new_password_confirmation",children:"Confirm New Password *"}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(y.p,{id:"new_password_confirmation",type:X?"text":"password",value:o.new_password_confirmation,onChange:e=>u(s=>({...s,new_password_confirmation:e.target.value})),required:!0,minLength:8}),(0,t.jsx)(i.$,{type:"button",variant:"ghost",size:"sm",className:"absolute right-0 top-0 h-full px-3",onClick:()=>Y(!X),children:X?(0,t.jsx)(R.A,{className:"h-4 w-4"}):(0,t.jsx)($.A,{className:"h-4 w-4"})})]})]}),(0,t.jsx)(i.$,{type:"submit",disabled:g,className:"w-full sm:w-auto",children:g?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(A.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Updating..."]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(D.A,{className:"mr-2 h-4 w-4"}),"Change Password"]})})]})]})]})}),(0,t.jsx)(d.av,{value:"avatar",className:"space-y-6",children:(0,t.jsxs)(c.Zp,{children:[(0,t.jsxs)(c.aR,{children:[(0,t.jsxs)(c.ZB,{className:"flex items-center gap-2",children:[(0,t.jsx)(P.A,{className:"h-5 w-5"}),"Profile Picture"]}),(0,t.jsx)(c.BT,{children:"Upload a new profile picture (max 2MB, JPEG/PNG)"})]}),(0,t.jsxs)(c.Wu,{children:[J&&(0,t.jsxs)(Z,{className:`mb-6 ${"success"===J.type?"border-green-200 bg-green-50":"border-red-200 bg-red-50"}`,children:["success"===J.type?(0,t.jsx)(_.A,{className:"h-4 w-4 text-green-600"}):(0,t.jsx)(C.A,{className:"h-4 w-4 text-red-600"}),(0,t.jsx)(z,{className:"success"===J.type?"text-green-800":"text-red-800",children:J.text})]}),(0,t.jsxs)("div",{className:"flex flex-col sm:flex-row items-center space-y-4 sm:space-y-0 sm:space-x-6",children:[(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)("div",{className:"w-24 h-24 sm:w-32 sm:h-32 bg-gray-200 rounded-full flex items-center justify-center overflow-hidden",children:e.profile_picture_url?(0,t.jsx)("img",{src:e.profile_picture_url,alt:"Profile",className:"w-full h-full object-cover",onError:e=>{e.target.src="/img/default-avatar.svg"}}):(0,t.jsx)(h.A,{className:"h-12 w-12 sm:h-16 sm:w-16 text-gray-400"})}),w&&(0,t.jsx)("div",{className:"absolute inset-0 bg-black bg-opacity-50 rounded-full flex items-center justify-center",children:(0,t.jsx)(A.A,{className:"h-6 w-6 text-white animate-spin"})})]}),(0,t.jsxs)("div",{className:"flex flex-col space-y-2",children:[(0,t.jsx)("input",{ref:V,type:"file",accept:"image/*",onChange:es,className:"hidden"}),(0,t.jsxs)(i.$,{onClick:()=>V.current?.click(),disabled:w,variant:"outline",className:"w-full sm:w-auto",children:[(0,t.jsx)(k.A,{className:"mr-2 h-4 w-4"}),"Upload New Picture"]}),(0,t.jsx)("p",{className:"text-sm text-gray-500 text-center sm:text-left",children:"JPEG, PNG up to 2MB"})]})]})]})]})}),(0,t.jsx)(d.av,{value:"qrcode",className:"space-y-6",children:(0,t.jsxs)(c.Zp,{children:[(0,t.jsxs)(c.aR,{children:[(0,t.jsxs)(c.ZB,{className:"flex items-center gap-2",children:[(0,t.jsx)("svg",{className:"h-5 w-5",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 4v1m6 11h2m-6 0h-2v4m0-11v3m0 0h.01M12 12h4.01M16 16h4.01M16 12h.01M12 8h.01M16 8h.01"})}),"QR Code & ID Card"]}),(0,t.jsx)(c.BT,{children:"Your unique QR code for identification and digital ID card"})]}),(0,t.jsx)(c.Wu,{children:(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("h3",{className:"text-lg font-semibold mb-4",children:"Your QR Code"}),(0,t.jsx)("div",{className:"bg-white p-4 rounded-lg border inline-block",children:e.qr_code_url?(0,t.jsx)("img",{src:e.qr_code_url,alt:`QR Code for ${e.first_name} ${e.last_name}`,className:"w-48 h-48 object-contain",onError:e=>{let s=e.target;s.style.display="none";let a=s.parentElement;if(a&&!a.querySelector(".qr-error")){let e=document.createElement("div");e.className="qr-error w-48 h-48 bg-gray-100 flex items-center justify-center text-gray-500 text-sm",e.textContent="QR Code not available",a.appendChild(e)}}}):(0,t.jsx)("div",{className:"w-48 h-48 bg-gray-100 flex items-center justify-center rounded-lg",children:(0,t.jsx)("span",{className:"text-gray-500",children:"No QR Code"})})}),(0,t.jsxs)("div",{className:"mt-4",children:[(0,t.jsxs)("p",{className:"text-sm text-gray-600 mb-2",children:["ID: ",e.short_id]}),(0,t.jsxs)(i.$,{onClick:ea,variant:"outline",size:"sm",disabled:B,children:[B?(0,t.jsx)(A.A,{className:"mr-2 h-4 w-4 animate-spin"}):(0,t.jsx)("svg",{className:"mr-2 h-4 w-4",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"})}),"Regenerate QR Code"]})]})]}),(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("h3",{className:"text-lg font-semibold mb-4",children:"Digital ID Card"}),(0,t.jsxs)("div",{className:"bg-gradient-to-r from-green-500 to-green-600 p-6 rounded-lg text-white max-w-sm mx-auto",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,t.jsx)("div",{className:"w-12 h-12 bg-white rounded-full flex items-center justify-center overflow-hidden",children:e.profile_picture_url?(0,t.jsx)("img",{src:e.profile_picture_url,alt:"Profile",className:"w-10 h-10 rounded-full object-cover",onError:e=>{e.target.src="/img/default-avatar.svg"}}):(0,t.jsx)(h.A,{className:"h-6 w-6 text-green-600"})}),(0,t.jsxs)("div",{className:"text-right",children:[(0,t.jsx)("p",{className:"text-xs opacity-80",children:"Member ID"}),(0,t.jsx)("p",{className:"font-mono text-sm",children:e.short_id})]})]}),(0,t.jsxs)("div",{className:"text-left",children:[(0,t.jsxs)("h4",{className:"font-bold text-lg",children:[e.first_name," ",e.last_name]}),(0,t.jsx)("p",{className:"text-sm opacity-90",children:e.email}),(0,t.jsxs)("p",{className:"text-xs mt-2 opacity-80",children:["Member since ",new Date().getFullYear()]})]})]}),(0,t.jsx)("div",{className:"mt-4",children:(0,t.jsxs)(i.$,{variant:"outline",size:"sm",onClick:et,children:[(0,t.jsx)(x.A,{className:"mr-2 h-4 w-4"}),"Download ID Card"]})})]})]})})]})})]})]})}z.displayName="AlertDescription";var M=a(67760),T=a(80428),q=a(25541),J=a(42538),U=a(27351),W=a(41312),O=a(79410),Q=a(84027),G=a(28947),X=a(82080),Y=a(85814),V=a.n(Y);function H(){let[e,s]=(0,r.useState)(null),[a,l]=(0,r.useState)([]),[m,o]=(0,r.useState)([]),[x,u]=(0,r.useState)([]),[p,g]=(0,r.useState)([]),[f,N]=(0,r.useState)([]),[y,v]=(0,r.useState)(!0),b=a.reduce((e,s)=>e+s.amount,0);return y?(0,t.jsx)("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-green-600 mx-auto"}),(0,t.jsx)("p",{className:"mt-4 text-gray-600",children:"Loading dashboard..."})]})}):e?(0,t.jsx)("div",{className:"min-h-screen bg-gray-50",children:(0,t.jsxs)("div",{className:"max-w-7xl mx-auto",children:[(0,t.jsx)("div",{className:"bg-white rounded-none sm:rounded-lg shadow-sm p-4 sm:p-6 mx-0 sm:mx-6 mt-0 sm:mt-6",children:(0,t.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-3 sm:space-x-4",children:[(0,t.jsx)("div",{className:"h-12 w-12 sm:h-16 sm:w-16 bg-green-600 rounded-full flex items-center justify-center",children:e.profile_picture_url?(0,t.jsx)("img",{src:e.profile_picture_url,alt:"Profile",className:"h-full w-full rounded-full object-cover"}):(0,t.jsx)(h.A,{className:"h-6 w-6 sm:h-8 sm:w-8 text-white"})}),(0,t.jsxs)("div",{children:[(0,t.jsxs)("h1",{className:"text-xl sm:text-2xl font-bold text-gray-900",children:["Welcome back, ",e.first_name,"!"]}),(0,t.jsx)("p",{className:"text-sm sm:text-base text-gray-600",children:"Community Member Dashboard"}),(0,t.jsx)("p",{className:"text-xs sm:text-sm text-green-600",children:"Thank you for being part of our mission to empower communities"})]})]}),(0,t.jsxs)("div",{className:"flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-3",children:[(0,t.jsx)(i.$,{variant:"outline",size:"sm",asChild:!0,className:"w-full sm:w-auto",children:(0,t.jsxs)(V(),{href:"/donate",children:[(0,t.jsx)(M.A,{className:"h-4 w-4 mr-2"}),"Make a Donation"]})}),(0,t.jsx)(i.$,{size:"sm",asChild:!0,className:"w-full sm:w-auto",children:(0,t.jsx)(V(),{href:"/about",children:"Learn More"})})]})]})}),(0,t.jsxs)("div",{className:"grid grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-6 px-4 sm:px-6 mt-4 sm:mt-6",children:[(0,t.jsxs)(c.Zp,{className:"p-3 sm:p-4",children:[(0,t.jsxs)(c.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2 px-0",children:[(0,t.jsx)(c.ZB,{className:"text-xs sm:text-sm font-medium",children:"Total Donated"}),(0,t.jsx)(T.A,{className:"h-3 w-3 sm:h-4 sm:w-4 text-muted-foreground"})]}),(0,t.jsxs)(c.Wu,{className:"px-0",children:[(0,t.jsxs)("div",{className:"text-lg sm:text-2xl font-bold",children:["₦",b.toLocaleString()]}),(0,t.jsx)("p",{className:"text-xs text-muted-foreground",children:"Your contribution"})]})]}),(0,t.jsxs)(c.Zp,{className:"p-3 sm:p-4",children:[(0,t.jsxs)(c.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2 px-0",children:[(0,t.jsx)(c.ZB,{className:"text-xs sm:text-sm font-medium",children:"Donations"}),(0,t.jsx)(M.A,{className:"h-3 w-3 sm:h-4 sm:w-4 text-muted-foreground"})]}),(0,t.jsxs)(c.Wu,{className:"px-0",children:[(0,t.jsx)("div",{className:"text-lg sm:text-2xl font-bold text-green-600",children:a.length}),(0,t.jsx)("p",{className:"text-xs text-muted-foreground",children:"Times donated"})]})]}),(0,t.jsxs)(c.Zp,{className:"p-3 sm:p-4",children:[(0,t.jsxs)(c.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2 px-0",children:[(0,t.jsx)(c.ZB,{className:"text-xs sm:text-sm font-medium",children:"Events"}),(0,t.jsx)(j.A,{className:"h-3 w-3 sm:h-4 sm:w-4 text-muted-foreground"})]}),(0,t.jsxs)(c.Wu,{className:"px-0",children:[(0,t.jsx)("div",{className:"text-lg sm:text-2xl font-bold text-blue-600",children:m.length}),(0,t.jsx)("p",{className:"text-xs text-muted-foreground",children:"Events registered"})]})]}),(0,t.jsxs)(c.Zp,{className:"p-3 sm:p-4",children:[(0,t.jsxs)(c.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2 px-0",children:[(0,t.jsx)(c.ZB,{className:"text-xs sm:text-sm font-medium",children:"Impact"}),(0,t.jsx)(q.A,{className:"h-3 w-3 sm:h-4 sm:w-4 text-muted-foreground"})]}),(0,t.jsxs)(c.Wu,{className:"px-0",children:[(0,t.jsx)("div",{className:"text-lg sm:text-2xl font-bold text-purple-600",children:Math.floor(b/1e3)}),(0,t.jsx)("p",{className:"text-xs text-muted-foreground",children:"Lives impacted"})]})]})]}),(0,t.jsx)("div",{className:"px-4 sm:px-6 mt-4 sm:mt-6",children:(0,t.jsxs)(c.Zp,{className:"bg-gradient-to-r from-green-50 to-blue-50 border-green-200",children:[(0,t.jsxs)(c.aR,{className:"pb-4",children:[(0,t.jsxs)(c.ZB,{className:"flex items-center text-green-700 text-lg sm:text-xl",children:[(0,t.jsx)(J.A,{className:"h-5 w-5 mr-2"}),"Get More Involved"]}),(0,t.jsx)(c.BT,{className:"text-sm sm:text-base",children:"Discover ways to make a bigger impact in your community"})]}),(0,t.jsx)(c.Wu,{children:(0,t.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-3 gap-4",children:[(0,t.jsxs)("div",{className:"text-center p-4 bg-white rounded-lg shadow-sm",children:[(0,t.jsx)(U.A,{className:"h-6 w-6 sm:h-8 sm:w-8 text-green-600 mx-auto mb-2"}),(0,t.jsx)("h4",{className:"font-medium mb-2 text-sm sm:text-base",children:"Become a Student"}),(0,t.jsx)("p",{className:"text-xs sm:text-sm text-gray-600 mb-3",children:"Apply for scholarships and access educational resources"}),(0,t.jsx)(i.$,{size:"sm",variant:"outline",asChild:!0,className:"w-full",children:(0,t.jsx)(V(),{href:"/auth/register?type=student",children:"Apply Now"})})]}),(0,t.jsxs)("div",{className:"text-center p-4 bg-white rounded-lg shadow-sm",children:[(0,t.jsx)(W.A,{className:"h-6 w-6 sm:h-8 sm:w-8 text-blue-600 mx-auto mb-2"}),(0,t.jsx)("h4",{className:"font-medium mb-2 text-sm sm:text-base",children:"Volunteer With Us"}),(0,t.jsx)("p",{className:"text-xs sm:text-sm text-gray-600 mb-3",children:"Contribute your time and skills to support our mission"}),(0,t.jsx)(i.$,{size:"sm",variant:"outline",asChild:!0,className:"w-full",children:(0,t.jsx)(V(),{href:"/auth/register?type=volunteer",children:"Join Us"})})]}),(0,t.jsxs)("div",{className:"text-center p-4 bg-white rounded-lg shadow-sm",children:[(0,t.jsx)(O.A,{className:"h-6 w-6 sm:h-8 sm:w-8 text-purple-600 mx-auto mb-2"}),(0,t.jsx)("h4",{className:"font-medium mb-2 text-sm sm:text-base",children:"Partner Organization"}),(0,t.jsx)("p",{className:"text-xs sm:text-sm text-gray-600 mb-3",children:"Collaborate with us to expand educational opportunities"}),(0,t.jsx)(i.$,{size:"sm",variant:"outline",asChild:!0,className:"w-full",children:(0,t.jsx)(V(),{href:"/auth/register?type=partner",children:"Partner"})})]})]})})]})}),(0,t.jsx)("div",{className:"px-4 sm:px-6 mt-4 sm:mt-6 pb-6",children:(0,t.jsxs)(d.tU,{defaultValue:"overview",className:"space-y-6",children:[(0,t.jsxs)(d.j7,{className:"grid w-full grid-cols-3 sm:grid-cols-6 gap-1",children:[(0,t.jsx)(d.Xi,{value:"overview",className:"text-xs sm:text-sm",children:"Overview"}),(0,t.jsx)(d.Xi,{value:"donations",className:"text-xs sm:text-sm",children:"Donations"}),(0,t.jsx)(d.Xi,{value:"events",className:"text-xs sm:text-sm",children:"Events"}),(0,t.jsx)(d.Xi,{value:"programs",className:"text-xs sm:text-sm",children:"Programs"}),(0,t.jsx)(d.Xi,{value:"id-card",className:"text-xs sm:text-sm",children:"ID Card"}),(0,t.jsxs)(d.Xi,{value:"profile",className:"text-xs sm:text-sm",children:[(0,t.jsx)(Q.A,{className:"h-3 w-3 sm:h-4 sm:w-4 sm:mr-1"}),(0,t.jsx)("span",{className:"hidden sm:inline",children:"Profile"})]})]}),(0,t.jsxs)(d.av,{value:"overview",className:"space-y-4 sm:space-y-6",children:[(0,t.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-4 sm:gap-6",children:[(0,t.jsxs)(c.Zp,{children:[(0,t.jsx)(c.aR,{children:(0,t.jsxs)(c.ZB,{className:"flex items-center text-base sm:text-lg",children:[(0,t.jsx)(M.A,{className:"h-4 w-4 sm:h-5 sm:w-5 mr-2"}),"Recent Donations"]})}),(0,t.jsxs)(c.Wu,{className:"space-y-4",children:[a.slice(0,3).map(e=>(0,t.jsxs)("div",{className:"flex items-center justify-between p-3 border rounded-lg",children:[(0,t.jsxs)("div",{className:"min-w-0 flex-1",children:[(0,t.jsxs)("p",{className:"font-medium text-sm sm:text-base",children:["₦",e.amount.toLocaleString()]}),(0,t.jsxs)("p",{className:"text-xs sm:text-sm text-gray-600 truncate",children:[e.donation_type||"General"," • ",new Date(e.created_at).toLocaleDateString()]})]}),(0,t.jsx)(n.E,{variant:"secondary",className:"text-xs",children:e.currency})]},e.id)),0===a.length&&(0,t.jsxs)("div",{className:"text-center py-8",children:[(0,t.jsx)(M.A,{className:"h-8 w-8 sm:h-12 sm:w-12 text-gray-400 mx-auto mb-4"}),(0,t.jsx)("p",{className:"text-gray-500 mb-4 text-sm sm:text-base",children:"No donations yet"}),(0,t.jsx)(i.$,{asChild:!0,size:"sm",className:"w-full sm:w-auto",children:(0,t.jsx)(V(),{href:"/donate",children:"Make Your First Donation"})})]})]})]}),(0,t.jsxs)(c.Zp,{children:[(0,t.jsx)(c.aR,{children:(0,t.jsxs)(c.ZB,{className:"flex items-center text-base sm:text-lg",children:[(0,t.jsx)(G.A,{className:"h-4 w-4 sm:h-5 sm:w-5 mr-2"}),"Active Campaigns"]})}),(0,t.jsxs)(c.Wu,{className:"space-y-4",children:[f.map(e=>(0,t.jsxs)("div",{className:"p-3 border rounded-lg",children:[(0,t.jsx)("h4",{className:"font-medium text-sm sm:text-base",children:e.title}),(0,t.jsx)("p",{className:"text-xs sm:text-sm text-gray-600 mt-1 line-clamp-2",children:e.description}),(0,t.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between mt-2 space-y-2 sm:space-y-0",children:[(0,t.jsxs)("span",{className:"text-xs sm:text-sm text-green-600",children:["Goal: ₦",e.goal?.toLocaleString()]}),(0,t.jsx)(i.$,{variant:"outline",size:"sm",asChild:!0,className:"w-full sm:w-auto",children:(0,t.jsx)(V(),{href:`/donate?campaign=${e.id}`,children:"Donate"})})]})]},e.id)),0===f.length&&(0,t.jsx)("p",{className:"text-gray-500 text-center py-4 text-sm",children:"No active campaigns"})]})]})]}),(0,t.jsxs)(c.Zp,{children:[(0,t.jsx)(c.aR,{children:(0,t.jsxs)(c.ZB,{className:"flex items-center text-base sm:text-lg",children:[(0,t.jsx)(X.A,{className:"h-4 w-4 sm:h-5 sm:w-5 mr-2"}),"Our Programs"]})}),(0,t.jsx)(c.Wu,{children:(0,t.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4",children:[p.map(e=>(0,t.jsxs)("div",{className:"p-4 border rounded-lg",children:[(0,t.jsx)("h4",{className:"font-medium text-sm sm:text-base",children:e.title}),(0,t.jsx)("p",{className:"text-xs sm:text-sm text-gray-600 mt-2 line-clamp-3",children:e.description}),(0,t.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between mt-3 space-y-2 sm:space-y-0",children:[(0,t.jsx)(n.E,{variant:"outline",className:"text-xs w-fit",children:e.status}),(0,t.jsx)(i.$,{variant:"outline",size:"sm",asChild:!0,className:"w-full sm:w-auto",children:(0,t.jsx)(V(),{href:`/projects/${e.slug}`,children:"Learn More"})})]})]},e.id)),0===p.length&&(0,t.jsxs)("div",{className:"col-span-full text-center py-8",children:[(0,t.jsx)(X.A,{className:"h-8 w-8 sm:h-12 sm:w-12 text-gray-400 mx-auto mb-4"}),(0,t.jsx)("p",{className:"text-gray-500 text-sm sm:text-base",children:"No programs available"})]})]})})]})]}),(0,t.jsx)(d.av,{value:"donations",className:"space-y-6",children:(0,t.jsxs)(c.Zp,{children:[(0,t.jsxs)(c.aR,{children:[(0,t.jsx)(c.ZB,{className:"text-base sm:text-lg",children:"My Donation History"}),(0,t.jsx)(c.BT,{className:"text-sm sm:text-base",children:"Track your contributions and see your impact"})]}),(0,t.jsx)(c.Wu,{children:(0,t.jsxs)("div",{className:"space-y-4",children:[a.map(e=>(0,t.jsxs)("div",{className:"border rounded-lg p-4",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,t.jsxs)("h4",{className:"font-semibold text-sm sm:text-base",children:["₦",e.amount.toLocaleString()]}),(0,t.jsx)(n.E,{variant:"secondary",className:"text-xs",children:e.payment_status})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-2 sm:gap-4 text-xs sm:text-sm text-gray-600",children:[(0,t.jsxs)("p",{children:["Type: ",e.donation_type||"General"]}),(0,t.jsxs)("p",{children:["Date: ",new Date(e.created_at).toLocaleDateString()]}),(0,t.jsxs)("p",{children:["Currency: ",e.currency]}),(0,t.jsxs)("p",{children:["Method: ",e.payment_method||"Online"]})]}),(0,t.jsx)("div",{className:"mt-3",children:(0,t.jsx)(i.$,{variant:"outline",size:"sm",className:"w-full sm:w-auto",children:"View Receipt"})})]},e.id)),0===a.length&&(0,t.jsxs)("div",{className:"text-center py-8",children:[(0,t.jsx)(T.A,{className:"h-8 w-8 sm:h-12 sm:w-12 text-gray-400 mx-auto mb-4"}),(0,t.jsx)("p",{className:"text-gray-500 mb-4 text-sm sm:text-base",children:"No donations recorded"}),(0,t.jsx)(i.$,{asChild:!0,className:"w-full sm:w-auto",children:(0,t.jsx)(V(),{href:"/donate",children:"Make Your First Donation"})})]})]})})]})}),(0,t.jsx)(d.av,{value:"events",className:"space-y-6",children:(0,t.jsxs)(c.Zp,{children:[(0,t.jsxs)(c.aR,{children:[(0,t.jsx)(c.ZB,{className:"text-base sm:text-lg",children:"Community Events"}),(0,t.jsx)(c.BT,{className:"text-sm sm:text-base",children:"Join events and connect with your community"})]}),(0,t.jsx)(c.Wu,{children:(0,t.jsxs)("div",{className:"space-y-4",children:[x.map(e=>(0,t.jsxs)("div",{className:"border rounded-lg p-4",children:[(0,t.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between mb-2 space-y-2 sm:space-y-0",children:[(0,t.jsx)("h4",{className:"font-semibold text-sm sm:text-base",children:e.title}),(0,t.jsx)(i.$,{variant:"outline",size:"sm",className:"w-full sm:w-auto",children:"Register"})]}),(0,t.jsxs)("div",{className:"text-xs sm:text-sm text-gray-600 space-y-1",children:[(0,t.jsxs)("p",{children:["Date: ",new Date(e.start_datetime).toLocaleDateString()]}),(0,t.jsxs)("p",{children:["Type: ",e.event_type]}),e.location&&(0,t.jsxs)("p",{children:["Location: ",e.location]})]})]},e.id)),0===x.length&&(0,t.jsx)("p",{className:"text-gray-500 text-center py-4 text-sm",children:"No upcoming events"})]})})]})}),(0,t.jsx)(d.av,{value:"programs",className:"space-y-6",children:(0,t.jsxs)(c.Zp,{children:[(0,t.jsxs)(c.aR,{children:[(0,t.jsx)(c.ZB,{className:"text-base sm:text-lg",children:"Our Impact Programs"}),(0,t.jsx)(c.BT,{className:"text-sm sm:text-base",children:"Learn about our initiatives and how you can contribute"})]}),(0,t.jsx)(c.Wu,{children:(0,t.jsxs)("div",{className:"space-y-4",children:[p.map(e=>(0,t.jsxs)("div",{className:"border rounded-lg p-4",children:[(0,t.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-start sm:justify-between mb-2 space-y-2 sm:space-y-0",children:[(0,t.jsx)("h4",{className:"font-semibold text-sm sm:text-base",children:e.title}),(0,t.jsx)(n.E,{variant:"outline",className:"text-xs w-fit",children:e.status})]}),(0,t.jsx)("p",{className:"text-xs sm:text-sm text-gray-600 mb-3",children:e.description}),(0,t.jsxs)("div",{className:"flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-2",children:[(0,t.jsx)(i.$,{variant:"outline",size:"sm",asChild:!0,className:"w-full sm:w-auto",children:(0,t.jsx)(V(),{href:`/projects/${e.slug}`,children:"Learn More"})}),(0,t.jsx)(i.$,{size:"sm",asChild:!0,className:"w-full sm:w-auto",children:(0,t.jsx)(V(),{href:`/donate?program=${e.id}`,children:"Support This Program"})})]})]},e.id)),0===p.length&&(0,t.jsxs)("div",{className:"text-center py-8",children:[(0,t.jsx)(X.A,{className:"h-8 w-8 sm:h-12 sm:w-12 text-gray-400 mx-auto mb-4"}),(0,t.jsx)("p",{className:"text-gray-500 mb-4 text-sm sm:text-base",children:"No programs available"}),(0,t.jsx)(i.$,{asChild:!0,className:"w-full sm:w-auto",children:(0,t.jsx)(V(),{href:"/projects",children:"Explore Programs"})})]})]})})]})}),(0,t.jsx)(d.av,{value:"id-card",className:"space-y-6",children:(0,t.jsx)(w,{user:e})}),(0,t.jsx)(d.av,{value:"profile",className:"space-y-6",children:(0,t.jsx)("div",{className:"bg-white rounded-lg shadow-sm",children:(0,t.jsx)(E,{user:e,onUserUpdate:e=>{s(s=>({...s,...e}))}})})})]})})]})}):(0,t.jsx)("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:(0,t.jsx)("div",{className:"text-center",children:(0,t.jsx)("p",{className:"text-gray-600",children:"Unable to load user data"})})})}}};