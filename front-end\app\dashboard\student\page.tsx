"use client"

import { useEffect, useState } from "react"
import { apiClient, extractArrayData } from "@/lib/api"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import {
  GraduationCap,
  BookOpen,
  Calendar,
  FileText,
  Award,
  Clock,
  TrendingUp,
  User,
  Mail,
  Phone,
  MapPin,
  Plus,
  Bell,
  Target,
  School,
  DollarSign,
  CheckCircle,
  AlertCircle,
  Search,
  Filter
} from "lucide-react"
import Link from "next/link"
import { useAuth } from '@/hooks/useAuth'
import { useToast } from "@/hooks/use-toast"
import UniversityApplicationManagement from '@/components/dashboard/UniversityApplicationManagement'

interface User {
  id: number
  first_name: string
  last_name: string
  email: string
  phone_number?: string
  preferences?: any
}

// Removed StudentDashboardProps as Next.js pages don't take props

interface ScholarshipApplication {
  id: number
  scholarship: {
    title: string
    amount: number
  }
  status: string
  applied_at: string
}

interface Event {
  id: number
  title: string
  start_datetime: string
  event_type: string
}

export default function StudentDashboard() {
  const { user: authUser } = useAuth()
  const { toast } = useToast()

  const [user, setUser] = useState<User | null>(null)
  const [scholarshipApplications, setScholarshipApplications] = useState<ScholarshipApplication[]>([])
  const [upcomingEvents, setUpcomingEvents] = useState<Event[]>([])
  const [availableScholarships, setAvailableScholarships] = useState([])
  const [universityScholarships, setUniversityScholarships] = useState([])
  const [applicationStats, setApplicationStats] = useState<any>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const fetchDashboardData = async () => {
      try {
        // Fetch student-specific data
        const [userResponse, scholarshipsResponse, eventsResponse, availableScholarshipsResponse] = await Promise.all([
          apiClient.getProfile(),
          apiClient.getMyScholarshipApplications(),
          apiClient.getUpcomingEvents(),
          apiClient.getScholarships()
        ])

        if (userResponse.success) {
          setUser(userResponse.data)
        }

        if (scholarshipsResponse.success) {
          setScholarshipApplications(extractArrayData(scholarshipsResponse))
        }

        if (eventsResponse.success) {
          const eventsData = extractArrayData(eventsResponse)
          setUpcomingEvents(eventsData.slice(0, 5))
        }

        if (availableScholarshipsResponse.success) {
          const availableData = extractArrayData(availableScholarshipsResponse)
          setAvailableScholarships(availableData.slice(0, 3))

          // Filter university scholarships for students
          const universityData = availableData.filter((scholarship: any) =>
            scholarship.category === 'university'
          )
          setUniversityScholarships(universityData.slice(0, 5))
        }

        // Fetch application statistics
        await fetchApplicationStats()

      } catch (error) {
        console.error('Failed to fetch dashboard data:', error)
        toast({
          title: "Error",
          description: "Failed to load dashboard data. Please refresh the page.",
          variant: "destructive",
        })
      } finally {
        setLoading(false)
      }
    }

    const fetchApplicationStats = async () => {
      try {
        const response = await apiClient.get('/api/v1/student-scholarships/statistics')
        if (response.data.success) {
          setApplicationStats(response.data.data)
        }
      } catch (error) {
        console.error('Failed to fetch application statistics:', error)
        toast({
          title: "Error",
          description: "Failed to load application statistics",
          variant: "destructive",
        })
      }
    }

    fetchDashboardData()
  }, [])

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'approved': return 'bg-green-500'
      case 'rejected': return 'bg-red-500'
      case 'under_review': return 'bg-blue-500'
      case 'pending': return 'bg-yellow-500'
      default: return 'bg-gray-500'
    }
  }

  const studentInfo = user?.preferences?.student_data || {}

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading dashboard...</p>
        </div>
      </div>
    )
  }

  if (!user) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <p className="text-gray-600">Unable to load user data</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-7xl mx-auto space-y-6">
        {/* Header */}
        <div className="bg-white rounded-lg shadow-sm p-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="h-16 w-16 bg-green-600 rounded-full flex items-center justify-center">
                <GraduationCap className="h-8 w-8 text-white" />
              </div>
              <div>
                <h1 className="text-2xl font-bold text-gray-900">
                  Welcome back, {user.first_name}!
                </h1>
                <p className="text-gray-600">Student Dashboard</p>
                {studentInfo.current_level && (
                  <p className="text-sm text-green-600">
                    {studentInfo.current_level} • {studentInfo.institution}
                  </p>
                )}
              </div>
            </div>
            <Button asChild>
              <Link href="/scholarships">
                <Plus className="h-4 w-4 mr-2" />
                Apply for Scholarships
              </Link>
            </Button>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Applications</CardTitle>
              <FileText className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{scholarshipApplications.length}</div>
              <p className="text-xs text-muted-foreground">
                Scholarship applications
              </p>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Approved</CardTitle>
              <Award className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">
                {scholarshipApplications.filter(app => app.status === 'approved').length}
              </div>
              <p className="text-xs text-muted-foreground">
                Approved scholarships
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Pending</CardTitle>
              <Clock className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-yellow-600">
                {scholarshipApplications.filter(app => 
                  app.status === 'pending' || app.status === 'under_review'
                ).length}
              </div>
              <p className="text-xs text-muted-foreground">
                Under review
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Available</CardTitle>
              <Target className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-blue-600">
                {availableScholarships.length}
              </div>
              <p className="text-xs text-muted-foreground">
                New opportunities
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Main Content */}
        <Tabs defaultValue="overview" className="space-y-6">
          <TabsList className="grid w-full grid-cols-7">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="scholarships">My Applications</TabsTrigger>
            <TabsTrigger value="university">University Scholarships</TabsTrigger>
            <TabsTrigger value="university-apps">University Applications</TabsTrigger>
            <TabsTrigger value="events">Events</TabsTrigger>
            <TabsTrigger value="resources">Resources</TabsTrigger>
            <TabsTrigger value="profile">Profile</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Recent Applications */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <FileText className="h-5 w-5 mr-2" />
                    Recent Applications
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  {scholarshipApplications.slice(0, 3).map((application) => (
                    <div key={application.id} className="flex items-center justify-between p-3 border rounded-lg">
                      <div>
                        <p className="font-medium">{application.scholarship.title}</p>
                        <p className="text-sm text-gray-600">
                          ₦{application.scholarship.amount.toLocaleString()}
                        </p>
                      </div>
                      <Badge className={getStatusColor(application.status)}>
                        {application.status.replace('_', ' ')}
                      </Badge>
                    </div>
                  ))}
                  {scholarshipApplications.length === 0 && (
                    <p className="text-gray-500 text-center py-4">No applications yet</p>
                  )}
                </CardContent>
              </Card>

              {/* Available Scholarships */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <Award className="h-5 w-5 mr-2" />
                    Available Scholarships
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  {availableScholarships.slice(0, 3).map((scholarship: any) => (
                    <div key={scholarship.id} className="p-3 border rounded-lg">
                      <h4 className="font-medium">{scholarship.title}</h4>
                      <p className="text-sm text-gray-600 mt-1">
                        Amount: ₦{scholarship.amount?.toLocaleString()}
                      </p>
                      <p className="text-xs text-gray-500 mt-2">
                        Deadline: {new Date(scholarship.application_deadline).toLocaleDateString()}
                      </p>
                    </div>
                  ))}
                  <Button variant="outline" className="w-full" asChild>
                    <Link href="/scholarships">View All Scholarships</Link>
                  </Button>
                </CardContent>
              </Card>
            </div>

            {/* Upcoming Events */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Calendar className="h-5 w-5 mr-2" />
                  Upcoming Events
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {upcomingEvents.map((event) => (
                    <div key={event.id} className="flex items-center justify-between p-3 border rounded-lg">
                      <div>
                        <p className="font-medium">{event.title}</p>
                        <p className="text-sm text-gray-600">
                          {new Date(event.start_datetime).toLocaleDateString()} • {event.event_type}
                        </p>
                      </div>
                      <Button variant="outline" size="sm">View Details</Button>
                    </div>
                  ))}
                  {upcomingEvents.length === 0 && (
                    <p className="text-gray-500 text-center py-4">No upcoming events</p>
                  )}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="scholarships" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>My Scholarship Applications</CardTitle>
                <CardDescription>
                  Track the status of your scholarship applications
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {scholarshipApplications.map((application) => (
                    <div key={application.id} className="border rounded-lg p-4">
                      <div className="flex items-center justify-between mb-2">
                        <h4 className="font-semibold">{application.scholarship.title}</h4>
                        <Badge className={getStatusColor(application.status)}>
                          {application.status.replace('_', ' ')}
                        </Badge>
                      </div>
                      <div className="grid grid-cols-2 gap-4 text-sm text-gray-600">
                        <p>Amount: ₦{application.scholarship.amount.toLocaleString()}</p>
                        <p>Applied: {new Date(application.applied_at).toLocaleDateString()}</p>
                      </div>
                    </div>
                  ))}
                  {scholarshipApplications.length === 0 && (
                    <div className="text-center py-8">
                      <Award className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                      <p className="text-gray-500 mb-4">You haven't applied for any scholarships yet</p>
                      <Button asChild>
                        <Link href="/scholarships">Browse Scholarships</Link>
                      </Button>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="university" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              {/* University Scholarship Statistics */}
              <div className="lg:col-span-1">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center">
                      <School className="h-5 w-5 mr-2" />
                      University Stats
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-600">Available</span>
                      <span className="font-semibold">{universityScholarships.length}</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-600">Applied</span>
                      <span className="font-semibold">
                        {scholarshipApplications.filter(app =>
                          availableScholarships.find((s: any) => s.id === app.scholarship.id && s.category === 'university')
                        ).length}
                      </span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-600">Success Rate</span>
                      <span className="font-semibold text-green-600">
                        {applicationStats?.success_rate || '0%'}
                      </span>
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Available University Scholarships */}
              <div className="lg:col-span-2">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center justify-between">
                      <span className="flex items-center">
                        <GraduationCap className="h-5 w-5 mr-2" />
                        University Scholarships
                      </span>
                      <Button variant="outline" size="sm" asChild>
                        <Link href="/scholarships?category=university">
                          <Search className="h-4 w-4 mr-2" />
                          Browse All
                        </Link>
                      </Button>
                    </CardTitle>
                    <CardDescription>
                      Scholarships specifically for university students
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {universityScholarships.map((scholarship: any) => (
                        <div key={scholarship.id} className="border rounded-lg p-4 hover:shadow-md transition-shadow">
                          <div className="flex items-start justify-between mb-3">
                            <div className="flex-1">
                              <h4 className="font-semibold text-lg mb-1">{scholarship.title}</h4>
                              <p className="text-sm text-gray-600 mb-2">{scholarship.description}</p>
                              <div className="flex items-center gap-4 text-sm text-gray-500">
                                <span className="flex items-center">
                                  <DollarSign className="h-4 w-4 mr-1" />
                                  ₦{scholarship.amount?.toLocaleString()}
                                </span>
                                <span className="flex items-center">
                                  <Calendar className="h-4 w-4 mr-1" />
                                  {new Date(scholarship.application_deadline).toLocaleDateString()}
                                </span>
                              </div>
                            </div>
                            <div className="flex flex-col gap-2">
                              <Badge variant="outline">University</Badge>
                              <Button size="sm" asChild>
                                <Link href={`/scholarships/${scholarship.id}/apply`}>
                                  Apply Now
                                </Link>
                              </Button>
                            </div>
                          </div>
                        </div>
                      ))}
                      {universityScholarships.length === 0 && (
                        <div className="text-center py-8">
                          <GraduationCap className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                          <p className="text-gray-500 mb-4">No university scholarships available at the moment</p>
                          <Button variant="outline" asChild>
                            <Link href="/scholarships">
                              <Bell className="h-4 w-4 mr-2" />
                              Get Notified
                            </Link>
                          </Button>
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="university-apps" className="space-y-6">
            <UniversityApplicationManagement />
          </TabsContent>

          <TabsContent value="events" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Educational Events</CardTitle>
                <CardDescription>
                  Join workshops, seminars, and networking events
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {upcomingEvents.map((event) => (
                    <div key={event.id} className="border rounded-lg p-4">
                      <div className="flex items-center justify-between mb-2">
                        <h4 className="font-semibold">{event.title}</h4>
                        <Button variant="outline" size="sm">Register</Button>
                      </div>
                      <div className="text-sm text-gray-600">
                        <p>Date: {new Date(event.start_datetime).toLocaleDateString()}</p>
                        <p>Type: {event.event_type}</p>
                      </div>
                    </div>
                  ))}
                  {upcomingEvents.length === 0 && (
                    <p className="text-gray-500 text-center py-4">No upcoming events</p>
                  )}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="resources" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Educational Resources */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <BookOpen className="h-5 w-5 mr-2" />
                    Educational Resources
                  </CardTitle>
                  <CardDescription>
                    Access study materials and academic support
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-3">
                    <div className="p-3 border rounded-lg hover:bg-gray-50 cursor-pointer">
                      <h4 className="font-medium">Study Guides</h4>
                      <p className="text-sm text-gray-600">Comprehensive study materials for various subjects</p>
                    </div>
                    <div className="p-3 border rounded-lg hover:bg-gray-50 cursor-pointer">
                      <h4 className="font-medium">Online Courses</h4>
                      <p className="text-sm text-gray-600">Free online courses to enhance your skills</p>
                    </div>
                    <div className="p-3 border rounded-lg hover:bg-gray-50 cursor-pointer">
                      <h4 className="font-medium">Career Guidance</h4>
                      <p className="text-sm text-gray-600">Professional development and career planning</p>
                    </div>
                    <div className="p-3 border rounded-lg hover:bg-gray-50 cursor-pointer">
                      <h4 className="font-medium">Mentorship Program</h4>
                      <p className="text-sm text-gray-600">Connect with experienced professionals</p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Application Tips */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <Target className="h-5 w-5 mr-2" />
                    Application Tips
                  </CardTitle>
                  <CardDescription>
                    Improve your scholarship application success rate
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-3">
                    <div className="flex items-start gap-3">
                      <CheckCircle className="h-5 w-5 text-green-600 mt-0.5" />
                      <div>
                        <h4 className="font-medium text-sm">Complete Your Profile</h4>
                        <p className="text-xs text-gray-600">Ensure all academic information is up to date</p>
                      </div>
                    </div>
                    <div className="flex items-start gap-3">
                      <CheckCircle className="h-5 w-5 text-green-600 mt-0.5" />
                      <div>
                        <h4 className="font-medium text-sm">Write Strong Essays</h4>
                        <p className="text-xs text-gray-600">Craft compelling personal statements</p>
                      </div>
                    </div>
                    <div className="flex items-start gap-3">
                      <CheckCircle className="h-5 w-5 text-green-600 mt-0.5" />
                      <div>
                        <h4 className="font-medium text-sm">Meet Deadlines</h4>
                        <p className="text-xs text-gray-600">Submit applications well before deadlines</p>
                      </div>
                    </div>
                    <div className="flex items-start gap-3">
                      <CheckCircle className="h-5 w-5 text-green-600 mt-0.5" />
                      <div>
                        <h4 className="font-medium text-sm">Gather References</h4>
                        <p className="text-xs text-gray-600">Secure strong recommendation letters</p>
                      </div>
                    </div>
                  </div>
                  <Button variant="outline" className="w-full">
                    <FileText className="h-4 w-4 mr-2" />
                    Download Application Guide
                  </Button>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="profile" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Student Profile</CardTitle>
                <CardDescription>
                  Manage your academic and personal information
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Full Name</label>
                    <p className="text-sm text-gray-600">{user.first_name} {user.last_name}</p>
                  </div>
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Email</label>
                    <p className="text-sm text-gray-600">{user.email}</p>
                  </div>
                  {studentInfo.current_level && (
                    <div className="space-y-2">
                      <label className="text-sm font-medium">Academic Level</label>
                      <p className="text-sm text-gray-600">{studentInfo.current_level}</p>
                    </div>
                  )}
                  {studentInfo.institution && (
                    <div className="space-y-2">
                      <label className="text-sm font-medium">Institution</label>
                      <p className="text-sm text-gray-600">{studentInfo.institution}</p>
                    </div>
                  )}
                  {studentInfo.field_of_study && (
                    <div className="space-y-2">
                      <label className="text-sm font-medium">Field of Study</label>
                      <p className="text-sm text-gray-600">{studentInfo.field_of_study}</p>
                    </div>
                  )}
                  {studentInfo.gpa && (
                    <div className="space-y-2">
                      <label className="text-sm font-medium">GPA</label>
                      <p className="text-sm text-gray-600">{studentInfo.gpa}</p>
                    </div>
                  )}
                </div>
                <Button variant="outline">Edit Profile</Button>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  )
} 