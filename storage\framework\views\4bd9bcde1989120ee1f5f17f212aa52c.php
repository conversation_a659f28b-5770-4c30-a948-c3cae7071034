

<?php $__env->startSection('title', 'Edit Scholarship'); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">Edit Scholarship</h1>
        <div>
            <a href="<?php echo e(route('admin.scholarships.show', $scholarship->id)); ?>" class="btn btn-secondary me-2">
                <i class="fas fa-arrow-left me-2"></i>Back to Details
            </a>
            <a href="<?php echo e(route('admin.scholarships.index')); ?>" class="btn btn-outline-secondary">
                <i class="fas fa-list me-2"></i>All Scholarships
            </a>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-8">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Scholarship Information</h6>
                </div>
                <div class="card-body">
                    <form action="<?php echo e(route('admin.scholarships.update', $scholarship->id)); ?>" method="POST" enctype="multipart/form-data">
                        <?php echo csrf_field(); ?>
                        <?php echo method_field('PUT'); ?>

                        <div class="row">
                            <div class="col-md-12 mb-3">
                                <label for="title" class="form-label">Scholarship Title <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="title" name="title" value="<?php echo e(old('title', $scholarship->title)); ?>" required>
                                <div class="form-text">Enter a clear and descriptive title for the scholarship</div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="amount" class="form-label">Scholarship Amount (₦) <span class="text-danger">*</span></label>
                                <input type="number" class="form-control" id="amount" name="amount" step="0.01" min="0" value="<?php echo e(old('amount', $scholarship->amount)); ?>" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="category" class="form-label">Education Category <span class="text-danger">*</span></label>
                                <select class="form-select" id="category" name="category" required onchange="updateCategoryInfo()">
                                    <option value="">Select Education Level</option>
                                    <option value="primary" <?php echo e(old('category', $scholarship->category) === 'primary' ? 'selected' : ''); ?>>Primary School (Ages 5-13)</option>
                                    <option value="secondary" <?php echo e(old('category', $scholarship->category) === 'secondary' ? 'selected' : ''); ?>>Secondary School (Ages 12-20)</option>
                                    <option value="university" <?php echo e(old('category', $scholarship->category) === 'university' ? 'selected' : ''); ?>>University (Ages 16-35)</option>
                                </select>
                                <div class="form-text">Choose the education level this scholarship targets</div>
                            </div>
                        </div>

                        <!-- Category Information Alert -->
                        <div id="categoryInfo" class="alert alert-info <?php echo e(old('category', $scholarship->category) ? '' : 'd-none'); ?> mb-3">
                            <div id="categoryInfoContent"></div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="application_deadline" class="form-label">Application Deadline <span class="text-danger">*</span></label>
                                <input type="datetime-local" class="form-control" id="application_deadline" name="application_deadline"
                                       value="<?php echo e(old('application_deadline', $scholarship->application_deadline ? $scholarship->application_deadline->format('Y-m-d\TH:i') : '')); ?>" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="application_start_date" class="form-label">Application Start Date</label>
                                <input type="datetime-local" class="form-control" id="application_start_date" name="application_start_date"
                                       value="<?php echo e(old('application_start_date', $scholarship->application_start_date ? $scholarship->application_start_date->format('Y-m-d\TH:i') : '')); ?>">
                                <div class="form-text">Leave empty to start immediately</div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="max_applicants" class="form-label">Maximum Applicants</label>
                                <input type="number" class="form-control" id="max_applicants" name="max_applicants" min="1" value="<?php echo e(old('max_applicants', $scholarship->max_applicants)); ?>">
                                <div class="form-text">Leave empty for unlimited applications</div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="contact_email" class="form-label">Contact Email</label>
                                <input type="email" class="form-control" id="contact_email" name="contact_email" value="<?php echo e(old('contact_email', $scholarship->contact_email)); ?>">
                                <div class="form-text">Email for scholarship inquiries</div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="description" class="form-label">Description <span class="text-danger">*</span></label>
                            <textarea class="form-control" id="description" name="description" rows="4" required><?php echo e(old('description', $scholarship->description)); ?></textarea>
                            <div class="form-text">Provide detailed information about the scholarship</div>
                        </div>

                        <div class="mb-3">
                            <label for="eligibility_criteria" class="form-label">Eligibility Criteria <span class="text-danger">*</span></label>
                            <textarea class="form-control" id="eligibility_criteria" name="eligibility_criteria" rows="4" required><?php echo e(old('eligibility_criteria', $scholarship->eligibility_criteria)); ?></textarea>
                            <div class="form-text">List the requirements and criteria for applicants</div>
                        </div>

                        <div class="mb-3">
                            <label for="required_documents" class="form-label">Required Documents</label>
                            <textarea class="form-control" id="required_documents" name="required_documents" rows="3"><?php echo e(old('required_documents', $scholarship->required_documents)); ?></textarea>
                            <div class="form-text">List documents that applicants need to submit</div>
                        </div>

                        <!-- Custom Fields Management -->
                        <div class="mb-4">
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <h6 class="mb-0">Custom Application Fields</h6>
                                <button type="button" class="btn btn-sm btn-outline-primary" onclick="addCustomField()">
                                    <i class="fas fa-plus me-1"></i>Add Field
                                </button>
                            </div>
                            <div id="customFieldsContainer" class="border rounded p-3 bg-light">
                                <?php if($scholarship->customFields && $scholarship->customFields->count() > 0): ?>
                                    <?php $__currentLoopData = $scholarship->customFields; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $field): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <div class="custom-field-item border rounded p-3 mb-3 bg-white" data-field-id="<?php echo e($field->id); ?>">
                                            <div class="d-flex justify-content-between align-items-center mb-2">
                                                <h6 class="mb-0"><?php echo e($field->field_label); ?></h6>
                                                <div>
                                                    <button type="button" class="btn btn-sm btn-outline-secondary me-1" onclick="moveFieldUp(<?php echo e($field->id); ?>)">
                                                        <i class="fas fa-arrow-up"></i>
                                                    </button>
                                                    <button type="button" class="btn btn-sm btn-outline-secondary me-1" onclick="moveFieldDown(<?php echo e($field->id); ?>)">
                                                        <i class="fas fa-arrow-down"></i>
                                                    </button>
                                                    <button type="button" class="btn btn-sm btn-outline-danger" onclick="removeCustomField(<?php echo e($field->id); ?>)">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </div>
                                            </div>
                                            <div class="row">
                                                <div class="col-md-6">
                                                    <label class="form-label">Field Name</label>
                                                    <input type="text" class="form-control" name="custom_fields[<?php echo e($field->id); ?>][field_name]" value="<?php echo e($field->field_name); ?>">
                                                </div>
                                                <div class="col-md-6">
                                                    <label class="form-label">Display Label</label>
                                                    <input type="text" class="form-control" name="custom_fields[<?php echo e($field->id); ?>][field_label]" value="<?php echo e($field->field_label); ?>">
                                                </div>
                                            </div>
                                            <div class="row mt-2">
                                                <div class="col-md-4">
                                                    <label class="form-label">Field Type</label>
                                                    <select class="form-select" name="custom_fields[<?php echo e($field->id); ?>][field_type]">
                                                        <option value="text" <?php echo e($field->field_type === 'text' ? 'selected' : ''); ?>>Text</option>
                                                        <option value="textarea" <?php echo e($field->field_type === 'textarea' ? 'selected' : ''); ?>>Textarea</option>
                                                        <option value="email" <?php echo e($field->field_type === 'email' ? 'selected' : ''); ?>>Email</option>
                                                        <option value="tel" <?php echo e($field->field_type === 'tel' ? 'selected' : ''); ?>>Phone</option>
                                                        <option value="number" <?php echo e($field->field_type === 'number' ? 'selected' : ''); ?>>Number</option>
                                                        <option value="date" <?php echo e($field->field_type === 'date' ? 'selected' : ''); ?>>Date</option>
                                                        <option value="file" <?php echo e($field->field_type === 'file' ? 'selected' : ''); ?>>File Upload</option>
                                                        <option value="select" <?php echo e($field->field_type === 'select' ? 'selected' : ''); ?>>Dropdown</option>
                                                    </select>
                                                </div>
                                                <div class="col-md-4">
                                                    <label class="form-label">Validation</label>
                                                    <div class="form-check">
                                                        <input class="form-check-input" type="checkbox" name="custom_fields[<?php echo e($field->id); ?>][is_required]" value="1" <?php echo e($field->is_required ? 'checked' : ''); ?>>
                                                        <label class="form-check-label">Required</label>
                                                    </div>
                                                </div>
                                                <div class="col-md-4">
                                                    <label class="form-label">Display Order</label>
                                                    <input type="number" class="form-control" name="custom_fields[<?php echo e($field->id); ?>][display_order]" value="<?php echo e($field->display_order); ?>">
                                                </div>
                                            </div>
                                            <div class="mt-2">
                                                <label class="form-label">Help Text</label>
                                                <input type="text" class="form-control" name="custom_fields[<?php echo e($field->id); ?>][help_text]" value="<?php echo e($field->help_text); ?>" placeholder="Optional help text for applicants">
                                            </div>
                                            <input type="hidden" name="custom_fields[<?php echo e($field->id); ?>][id]" value="<?php echo e($field->id); ?>">
                                        </div>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                <?php else: ?>
                                    <p class="text-muted mb-0">No custom fields added yet. Click "Add Field" to create application-specific fields.</p>
                                <?php endif; ?>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="image" class="form-label">Scholarship Image</label>
                            <input type="file" class="form-control" id="image" name="image" accept="image/*">
                            <?php if($scholarship->image): ?>
                                <div class="mt-2">
                                    <img src="<?php echo e(asset('storage/' . $scholarship->image)); ?>" alt="Current Image" class="img-thumbnail" style="max-width: 200px;">
                                    <div class="form-text">Current image. Upload a new one to replace it.</div>
                                </div>
                            <?php endif; ?>
                            <div class="form-text">Upload an image to represent this scholarship (optional)</div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="status" class="form-label">Status</label>
                                <select class="form-select" id="status" name="status">
                                    <option value="draft" <?php echo e(old('status', $scholarship->status) === 'draft' ? 'selected' : ''); ?>>Draft</option>
                                    <option value="active" <?php echo e(old('status', $scholarship->status) === 'active' ? 'selected' : ''); ?>>Active</option>
                                    <option value="inactive" <?php echo e(old('status', $scholarship->status) === 'inactive' ? 'selected' : ''); ?>>Inactive</option>
                                </select>
                            </div>
                            <div class="col-md-6 mb-3">
                                <div class="form-check mt-4">
                                    <input class="form-check-input" type="checkbox" id="is_featured" name="is_featured" value="1" <?php echo e(old('is_featured', $scholarship->is_featured) ? 'checked' : ''); ?>>
                                    <label class="form-check-label" for="is_featured">
                                        Featured Scholarship
                                    </label>
                                    <div class="form-text">Featured scholarships appear prominently on the website</div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="is_open" name="is_open" value="1" <?php echo e(old('is_open', $scholarship->is_open) ? 'checked' : ''); ?>>
                                    <label class="form-check-label" for="is_open">
                                        Open for Applications
                                    </label>
                                    <div class="form-text">Allow students to apply for this scholarship</div>
                                </div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="template_id" class="form-label">Save as Template</label>
                                <div class="input-group">
                                    <input type="text" class="form-control" id="template_name" name="template_name" placeholder="Template name (optional)">
                                    <button type="button" class="btn btn-outline-secondary" onclick="saveAsTemplate()">
                                        <i class="fas fa-save"></i>
                                    </button>
                                </div>
                                <div class="form-text">Save current configuration as a reusable template</div>
                            </div>
                        </div>

                        <div class="d-flex justify-content-between">
                            <a href="<?php echo e(route('admin.scholarships.index')); ?>" class="btn btn-secondary">
                                <i class="fas fa-arrow-left me-2"></i>Back to Scholarships
                            </a>
                            <div>
                                <button type="submit" name="action" value="draft" class="btn btn-outline-primary me-2">
                                    <i class="fas fa-save me-2"></i>Save as Draft
                                </button>
                                <button type="submit" name="action" value="update" class="btn btn-primary">
                                    <i class="fas fa-check me-2"></i>Update Scholarship
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-info">Quick Actions</h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="<?php echo e(route('scholarships.show', $scholarship->slug)); ?>" target="_blank" class="btn btn-outline-info">
                            <i class="fas fa-external-link-alt me-2"></i>Preview Public Page
                        </a>
                        <a href="<?php echo e(route('admin.scholarships.applications', $scholarship->id)); ?>" class="btn btn-outline-success">
                            <i class="fas fa-file-alt me-2"></i>View Applications (<?php echo e($scholarship->applications->count() ?? 0); ?>)
                        </a>
                        <button type="button" class="btn btn-outline-warning" onclick="duplicateScholarship()">
                            <i class="fas fa-copy me-2"></i>Duplicate Scholarship
                        </button>
                        <button type="button" class="btn btn-outline-danger" onclick="deleteScholarship()">
                            <i class="fas fa-trash me-2"></i>Delete Scholarship
                        </button>
                    </div>
                </div>
            </div>

            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Scholarship Statistics</h6>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-6">
                            <div class="border-end">
                                <h4 class="text-primary"><?php echo e($scholarship->applications->count() ?? 0); ?></h4>
                                <small class="text-muted">Applications</small>
                            </div>
                        </div>
                        <div class="col-6">
                            <h4 class="text-success"><?php echo e($scholarship->applications->where('status', 'approved')->count() ?? 0); ?></h4>
                            <small class="text-muted">Approved</small>
                        </div>
                    </div>
                    <hr>
                    <div class="row text-center">
                        <div class="col-6">
                            <h5 class="text-warning"><?php echo e($scholarship->applications->where('status', 'pending')->count() ?? 0); ?></h5>
                            <small class="text-muted">Pending</small>
                        </div>
                        <div class="col-6">
                            <h5 class="text-danger"><?php echo e($scholarship->applications->where('status', 'rejected')->count() ?? 0); ?></h5>
                            <small class="text-muted">Rejected</small>
                        </div>
                    </div>
                </div>
            </div>

            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Scholarship Tips</h6>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <h6><i class="fas fa-lightbulb me-2"></i>Tips for Success</h6>
                        <ul class="mb-0 small">
                            <li>Choose the correct education category</li>
                            <li>Set realistic application deadlines</li>
                            <li>Define clear eligibility criteria</li>
                            <li>Add custom fields for specific requirements</li>
                            <li>Test the application flow before publishing</li>
                        </ul>
                    </div>
                    <div class="alert alert-warning mt-3">
                        <h6><i class="fas fa-exclamation-triangle me-2"></i>Category Guidelines</h6>
                        <ul class="mb-0 small">
                            <li><strong>Primary:</strong> Parent/guardian applies for child</li>
                            <li><strong>Secondary:</strong> Student applies with school details</li>
                            <li><strong>University:</strong> Student applies with matric number</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
let customFieldCounter = <?php echo e($scholarship->customFields ? $scholarship->customFields->max('id') + 1 : 1000); ?>;

document.addEventListener('DOMContentLoaded', function() {
    // Set minimum date to today
    const today = new Date().toISOString().slice(0, 16);
    document.getElementById('application_deadline').setAttribute('min', today);
    if (document.getElementById('application_start_date')) {
        document.getElementById('application_start_date').setAttribute('min', today);
    }

    // Initialize category info if category is already selected
    updateCategoryInfo();
});

function updateCategoryInfo() {
    const category = document.getElementById('category').value;
    const infoDiv = document.getElementById('categoryInfo');
    const contentDiv = document.getElementById('categoryInfoContent');

    if (category) {
        let content = '';
        switch(category) {
            case 'primary':
                content = `
                    <h6><i class="fas fa-school me-2"></i>Primary School Category</h6>
                    <ul class="mb-0">
                        <li><strong>Age Range:</strong> 5-13 years</li>
                        <li><strong>Form Filled By:</strong> Parent/Guardian</li>
                        <li><strong>Required Fields:</strong> Student details, Parent/Guardian info, Headmaster details, School account number</li>
                        <li><strong>Documents:</strong> School ID, Birth certificate, Academic records</li>
                    </ul>
                `;
                break;
            case 'secondary':
                content = `
                    <h6><i class="fas fa-graduation-cap me-2"></i>Secondary School Category</h6>
                    <ul class="mb-0">
                        <li><strong>Age Range:</strong> 12-20 years</li>
                        <li><strong>Form Filled By:</strong> Student</li>
                        <li><strong>Required Fields:</strong> Student details, Principal/Financial Officer info, School account number</li>
                        <li><strong>Documents:</strong> School ID, Academic transcripts, Recommendation letter</li>
                    </ul>
                `;
                break;
            case 'university':
                content = `
                    <h6><i class="fas fa-university me-2"></i>University Category</h6>
                    <ul class="mb-0">
                        <li><strong>Age Range:</strong> 16-35 years</li>
                        <li><strong>Form Filled By:</strong> Student</li>
                        <li><strong>Required Fields:</strong> Student details, Matriculation number, Student email</li>
                        <li><strong>Documents:</strong> Student ID, Transcripts, Admission letter</li>
                        <li><strong>Note:</strong> No school account number required</li>
                    </ul>
                `;
                break;
        }
        contentDiv.innerHTML = content;
        infoDiv.classList.remove('d-none');
    } else {
        infoDiv.classList.add('d-none');
    }
}

function addCustomField() {
    customFieldCounter++;
    const container = document.getElementById('customFieldsContainer');

    // Remove the "no fields" message if it exists
    if (container.querySelector('p.text-muted')) {
        container.innerHTML = '';
    }

    const fieldHtml = `
        <div class="custom-field-item border rounded p-3 mb-3 bg-white" data-field-id="${customFieldCounter}">
            <div class="d-flex justify-content-between align-items-center mb-2">
                <h6 class="mb-0">Custom Field #${customFieldCounter}</h6>
                <div>
                    <button type="button" class="btn btn-sm btn-outline-secondary me-1" onclick="moveFieldUp(${customFieldCounter})">
                        <i class="fas fa-arrow-up"></i>
                    </button>
                    <button type="button" class="btn btn-sm btn-outline-secondary me-1" onclick="moveFieldDown(${customFieldCounter})">
                        <i class="fas fa-arrow-down"></i>
                    </button>
                    <button type="button" class="btn btn-sm btn-outline-danger" onclick="removeCustomField(${customFieldCounter})">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </div>
            <div class="row">
                <div class="col-md-6">
                    <label class="form-label">Field Name</label>
                    <input type="text" class="form-control" name="custom_fields[${customFieldCounter}][field_name]" placeholder="e.g., gpa_score">
                </div>
                <div class="col-md-6">
                    <label class="form-label">Display Label</label>
                    <input type="text" class="form-control" name="custom_fields[${customFieldCounter}][field_label]" placeholder="e.g., GPA Score">
                </div>
            </div>
            <div class="row mt-2">
                <div class="col-md-4">
                    <label class="form-label">Field Type</label>
                    <select class="form-select" name="custom_fields[${customFieldCounter}][field_type]">
                        <option value="text">Text</option>
                        <option value="textarea">Textarea</option>
                        <option value="email">Email</option>
                        <option value="tel">Phone</option>
                        <option value="number">Number</option>
                        <option value="date">Date</option>
                        <option value="file">File Upload</option>
                        <option value="select">Dropdown</option>
                    </select>
                </div>
                <div class="col-md-4">
                    <label class="form-label">Validation</label>
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" name="custom_fields[${customFieldCounter}][is_required]" value="1">
                        <label class="form-check-label">Required</label>
                    </div>
                </div>
                <div class="col-md-4">
                    <label class="form-label">Display Order</label>
                    <input type="number" class="form-control" name="custom_fields[${customFieldCounter}][display_order]" value="${customFieldCounter}">
                </div>
            </div>
            <div class="mt-2">
                <label class="form-label">Help Text</label>
                <input type="text" class="form-control" name="custom_fields[${customFieldCounter}][help_text]" placeholder="Optional help text for applicants">
            </div>
        </div>
    `;

    container.insertAdjacentHTML('beforeend', fieldHtml);
}

function removeCustomField(fieldId) {
    if (confirm('Are you sure you want to remove this field?')) {
        const fieldElement = document.querySelector(`[data-field-id="${fieldId}"]`);
        fieldElement.remove();

        // Check if no fields remain
        const container = document.getElementById('customFieldsContainer');
        if (container.children.length === 0) {
            container.innerHTML = '<p class="text-muted mb-0">No custom fields added yet. Click "Add Field" to create application-specific fields.</p>';
        }
    }
}

function moveFieldUp(fieldId) {
    const fieldElement = document.querySelector(`[data-field-id="${fieldId}"]`);
    const previousElement = fieldElement.previousElementSibling;
    if (previousElement && previousElement.classList.contains('custom-field-item')) {
        fieldElement.parentNode.insertBefore(fieldElement, previousElement);
    }
}

function moveFieldDown(fieldId) {
    const fieldElement = document.querySelector(`[data-field-id="${fieldId}"]`);
    const nextElement = fieldElement.nextElementSibling;
    if (nextElement && nextElement.classList.contains('custom-field-item')) {
        fieldElement.parentNode.insertBefore(nextElement, fieldElement);
    }
}

function duplicateScholarship() {
    if (confirm('This will create a copy of this scholarship. Continue?')) {
        fetch(`/api/v1/admin/scholarships/<?php echo e($scholarship->id); ?>/duplicate`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                window.location.href = `/admin/scholarships/${data.data.id}/edit`;
            } else {
                alert('Error duplicating scholarship: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error duplicating scholarship');
        });
    }
}

function deleteScholarship() {
    if (confirm('Are you sure you want to delete this scholarship? This action cannot be undone.')) {
        fetch(`/api/v1/admin/scholarships/<?php echo e($scholarship->id); ?>`, {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                window.location.href = '/admin/scholarships';
            } else {
                alert('Error deleting scholarship: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error deleting scholarship');
        });
    }
}

function saveAsTemplate() {
    const templateName = document.getElementById('template_name').value;
    if (!templateName) {
        alert('Please enter a template name');
        return;
    }

    // Collect form data for template
    const formData = new FormData(document.querySelector('form'));
    formData.append('template_name', templateName);

    fetch('/api/v1/admin/scholarship-templates', {
        method: 'POST',
        body: formData,
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('Template saved successfully!');
            document.getElementById('template_name').value = '';
        } else {
            alert('Error saving template: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Error saving template');
    });
}
</script>
<?php $__env->stopSection(); ?> 
<?php echo $__env->make('layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\laragon\www\laravel-api-ngo\resources\views/admin/scholarships/edit.blade.php ENDPATH**/ ?>