"use strict";exports.id=961,exports.ids=[961],exports.modules={37961:(e,s,l)=>{l.r(s),l.d(s,{default:()=>w});var a=l(60687),t=l(43210);l(59556);var i=l(55192),r=l(24934),n=l(59821),c=l(86287),d=l(85910),x=l(5336),m=l(93613),h=l(48730),o=l(67760),j=l(96474),p=l(28947),u=l(41312),N=l(86561),g=l(25541),v=l(97992),f=l(82080),y=l(85814),b=l.n(y);function w(){let[e,s]=(0,t.useState)(null),[l,y]=(0,t.useState)(null),[w,A]=(0,t.useState)(null),[Z,_]=(0,t.useState)([]),[B,R]=(0,t.useState)([]),[W,k]=(0,t.useState)(!0);if(W)return(0,a.jsx)("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-green-600 mx-auto"}),(0,a.jsx)("p",{className:"mt-4 text-gray-600",children:"Loading dashboard..."})]})});if(!e)return(0,a.jsx)("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:(0,a.jsx)("div",{className:"text-center",children:(0,a.jsx)("p",{className:"text-gray-600",children:"Unable to load user data"})})});let S=e.preferences?.volunteer_data||{},E=l?l.hours_logged/100*100:0;return(0,a.jsx)("div",{className:"min-h-screen bg-gray-50",children:(0,a.jsxs)("div",{className:"max-w-7xl mx-auto",children:[(0,a.jsx)("div",{className:"bg-white rounded-none sm:rounded-lg shadow-sm p-4 sm:p-6 mx-0 sm:mx-6 mt-0 sm:mt-6",children:(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3 sm:space-x-4",children:[(0,a.jsx)("div",{className:"h-12 w-12 sm:h-16 sm:w-16 bg-green-600 rounded-full flex items-center justify-center",children:(0,a.jsx)(o.A,{className:"h-6 w-6 sm:h-8 sm:w-8 text-white"})}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("h1",{className:"text-xl sm:text-2xl font-bold text-gray-900",children:["Welcome back, ",e.first_name,"!"]}),(0,a.jsx)("p",{className:"text-sm sm:text-base text-gray-600",children:"Volunteer Dashboard"}),l&&(0,a.jsxs)("div",{className:"flex items-center mt-2",children:[(e=>{switch(e.toLowerCase()){case"approved":return(0,a.jsx)(x.A,{className:"h-4 w-4"});case"rejected":return(0,a.jsx)(m.A,{className:"h-4 w-4"});default:return(0,a.jsx)(h.A,{className:"h-4 w-4"})}})(l.application_status),(0,a.jsxs)("span",{className:"ml-2 text-xs sm:text-sm font-medium",children:["Status: ",l.application_status.replace("_"," ")]})]})]})]}),(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-3",children:[(0,a.jsx)(r.$,{variant:"outline",size:"sm",asChild:!0,className:"w-full sm:w-auto",children:(0,a.jsxs)(b(),{href:"/volunteer/opportunities",children:[(0,a.jsx)(j.A,{className:"h-4 w-4 mr-2"}),"Find Opportunities"]})}),!l&&(0,a.jsx)(r.$,{size:"sm",asChild:!0,className:"w-full sm:w-auto",children:(0,a.jsx)(b(),{href:"/volunteer/apply",children:"Apply as Volunteer"})})]})]})}),(0,a.jsxs)("div",{className:"grid grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-6 px-4 sm:px-6 mt-4 sm:mt-6",children:[(0,a.jsxs)(i.Zp,{className:"p-3 sm:p-4",children:[(0,a.jsxs)(i.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2 px-0",children:[(0,a.jsx)(i.ZB,{className:"text-xs sm:text-sm font-medium",children:"Hours Logged"}),(0,a.jsx)(h.A,{className:"h-3 w-3 sm:h-4 sm:w-4 text-muted-foreground"})]}),(0,a.jsxs)(i.Wu,{className:"px-0",children:[(0,a.jsxs)("div",{className:"text-lg sm:text-2xl font-bold",children:[l?.hours_logged||0,"h"]}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:"Total volunteer hours"})]})]}),(0,a.jsxs)(i.Zp,{className:"p-3 sm:p-4",children:[(0,a.jsxs)(i.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2 px-0",children:[(0,a.jsx)(i.ZB,{className:"text-xs sm:text-sm font-medium",children:"Progress"}),(0,a.jsx)(p.A,{className:"h-3 w-3 sm:h-4 sm:w-4 text-muted-foreground"})]}),(0,a.jsxs)(i.Wu,{className:"px-0",children:[(0,a.jsxs)("div",{className:"text-lg sm:text-2xl font-bold",children:[Math.round(E),"%"]}),(0,a.jsxs)("p",{className:"text-xs text-muted-foreground",children:["Of ",100,"h goal"]})]})]}),(0,a.jsxs)(i.Zp,{className:"p-3 sm:p-4",children:[(0,a.jsxs)(i.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2 px-0",children:[(0,a.jsx)(i.ZB,{className:"text-xs sm:text-sm font-medium",children:"Opportunities"}),(0,a.jsx)(u.A,{className:"h-3 w-3 sm:h-4 sm:w-4 text-muted-foreground"})]}),(0,a.jsxs)(i.Wu,{className:"px-0",children:[(0,a.jsx)("div",{className:"text-lg sm:text-2xl font-bold",children:Z.length}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:"Available to you"})]})]}),(0,a.jsxs)(i.Zp,{className:"p-3 sm:p-4",children:[(0,a.jsxs)(i.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2 px-0",children:[(0,a.jsx)(i.ZB,{className:"text-xs sm:text-sm font-medium",children:"Badges"}),(0,a.jsx)(N.A,{className:"h-3 w-3 sm:h-4 sm:w-4 text-muted-foreground"})]}),(0,a.jsxs)(i.Wu,{className:"px-0",children:[(0,a.jsx)("div",{className:"text-lg sm:text-2xl font-bold",children:w?.badges_earned?.length||0}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:"Earned badges"})]})]})]}),(0,a.jsxs)(d.tU,{defaultValue:"overview",className:"space-y-6",children:[(0,a.jsxs)(d.j7,{className:"grid w-full grid-cols-5",children:[(0,a.jsx)(d.Xi,{value:"overview",children:"Overview"}),(0,a.jsx)(d.Xi,{value:"hours",children:"Hours"}),(0,a.jsx)(d.Xi,{value:"opportunities",children:"Opportunities"}),(0,a.jsx)(d.Xi,{value:"events",children:"Events"}),(0,a.jsx)(d.Xi,{value:"profile",children:"Profile"})]}),(0,a.jsxs)(d.av,{value:"overview",className:"space-y-6",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,a.jsxs)(i.Zp,{children:[(0,a.jsx)(i.aR,{children:(0,a.jsxs)(i.ZB,{className:"flex items-center",children:[(0,a.jsx)(g.A,{className:"h-5 w-5 mr-2"}),"Volunteer Progress"]})}),(0,a.jsxs)(i.Wu,{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"flex justify-between text-sm mb-2",children:[(0,a.jsx)("span",{children:"Hours Goal Progress"}),(0,a.jsxs)("span",{children:[l?.hours_logged||0,"h / ",100,"h"]})]}),(0,a.jsx)(c.k,{value:E,className:"h-2"})]}),l?.approved_at&&(0,a.jsx)("div",{className:"bg-green-50 p-3 rounded-lg",children:(0,a.jsxs)("p",{className:"text-sm text-green-700",children:[(0,a.jsx)(x.A,{className:"h-4 w-4 inline mr-2"}),"Volunteer since ",new Date(l.approved_at).toLocaleDateString()]})}),l?.application_status==="pending"&&(0,a.jsx)("div",{className:"bg-yellow-50 p-3 rounded-lg",children:(0,a.jsxs)("p",{className:"text-sm text-yellow-700",children:[(0,a.jsx)(h.A,{className:"h-4 w-4 inline mr-2"}),"Your application is being reviewed. We'll notify you once it's processed."]})})]})]}),(0,a.jsxs)(i.Zp,{children:[(0,a.jsx)(i.aR,{children:(0,a.jsxs)(i.ZB,{className:"flex items-center",children:[(0,a.jsx)(u.A,{className:"h-5 w-5 mr-2"}),"Available Opportunities"]})}),(0,a.jsxs)(i.Wu,{className:"space-y-4",children:[Z.slice(0,3).map(e=>(0,a.jsxs)("div",{className:"p-3 border rounded-lg",children:[(0,a.jsx)("h4",{className:"font-medium",children:e.title}),(0,a.jsx)("p",{className:"text-sm text-gray-600 mt-1",children:e.description}),(0,a.jsxs)("div",{className:"flex items-center gap-4 mt-2 text-xs text-gray-500",children:[(0,a.jsxs)("span",{children:[(0,a.jsx)(v.A,{className:"h-3 w-3 inline mr-1"}),e.location]}),(0,a.jsxs)("span",{children:[(0,a.jsx)(h.A,{className:"h-3 w-3 inline mr-1"}),e.time_commitment]})]})]},e.id)),0===Z.length&&(0,a.jsx)("p",{className:"text-gray-500 text-center py-4",children:"No opportunities available"}),(0,a.jsx)(r.$,{variant:"outline",className:"w-full",asChild:!0,children:(0,a.jsx)(b(),{href:"/volunteer/opportunities",children:"View All Opportunities"})})]})]})]}),l&&(0,a.jsxs)(i.Zp,{children:[(0,a.jsx)(i.aR,{children:(0,a.jsxs)(i.ZB,{className:"flex items-center",children:[(0,a.jsx)(f.A,{className:"h-5 w-5 mr-2"}),"Your Skills & Interests"]})}),(0,a.jsx)(i.Wu,{children:(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-medium mb-3",children:"Skills"}),(0,a.jsxs)("div",{className:"flex flex-wrap gap-2",children:[l.skills?.map((e,s)=>a.jsx(n.E,{variant:"secondary",children:e},s)),(!l.skills||0===l.skills.length)&&(0,a.jsx)("p",{className:"text-gray-500",children:"No skills listed"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-medium mb-3",children:"Interests"}),(0,a.jsxs)("div",{className:"flex flex-wrap gap-2",children:[l.interests?.map((e,s)=>a.jsx(n.E,{variant:"outline",children:e},s)),(!l.interests||0===l.interests.length)&&(0,a.jsx)("p",{className:"text-gray-500",children:"No interests listed"})]})]})]})})]})]}),(0,a.jsx)(d.av,{value:"hours",className:"space-y-6",children:(0,a.jsxs)(i.Zp,{children:[(0,a.jsxs)(i.aR,{children:[(0,a.jsx)(i.ZB,{children:"Volunteer Hours"}),(0,a.jsx)(i.BT,{children:"Track and log your volunteer hours"})]}),(0,a.jsx)(i.Wu,{children:(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,a.jsxs)("div",{className:"text-center p-4 bg-green-50 rounded-lg",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-green-600",children:l?.hours_logged||0}),(0,a.jsx)("p",{className:"text-sm text-green-700",children:"Total Hours"})]}),(0,a.jsxs)("div",{className:"text-center p-4 bg-blue-50 rounded-lg",children:[(0,a.jsxs)("div",{className:"text-2xl font-bold text-blue-600",children:[Math.round(E),"%"]}),(0,a.jsx)("p",{className:"text-sm text-blue-700",children:"Goal Progress"})]}),(0,a.jsxs)("div",{className:"text-center p-4 bg-purple-50 rounded-lg",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-purple-600",children:w?.badges_earned?.length||0}),(0,a.jsx)("p",{className:"text-sm text-purple-700",children:"Badges Earned"})]})]}),(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsx)("h4",{className:"font-medium",children:"Recent Hours"}),(0,a.jsx)(r.$,{size:"sm",children:"Log New Hours"})]}),(0,a.jsxs)("div",{className:"text-center py-8 text-gray-500",children:[(0,a.jsx)(h.A,{className:"h-12 w-12 mx-auto mb-4 opacity-50"}),(0,a.jsx)("p",{children:"No logged hours yet"}),(0,a.jsx)(r.$,{className:"mt-4",children:"Log Your First Hours"})]})]})})]})}),(0,a.jsx)(d.av,{value:"opportunities",className:"space-y-6",children:(0,a.jsxs)(i.Zp,{children:[(0,a.jsxs)(i.aR,{children:[(0,a.jsx)(i.ZB,{children:"Volunteer Opportunities"}),(0,a.jsx)(i.BT,{children:"Find opportunities that match your skills and interests"})]}),(0,a.jsx)(i.Wu,{children:(0,a.jsxs)("div",{className:"space-y-4",children:[Z.map(e=>(0,a.jsxs)("div",{className:"border rounded-lg p-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,a.jsx)("h4",{className:"font-semibold",children:e.title}),(0,a.jsx)(r.$,{variant:"outline",size:"sm",children:"Learn More"})]}),(0,a.jsx)("p",{className:"text-sm text-gray-600 mb-3",children:e.description}),(0,a.jsxs)("div",{className:"flex items-center gap-4 text-sm text-gray-500",children:[(0,a.jsxs)("span",{children:[(0,a.jsx)(v.A,{className:"h-4 w-4 inline mr-1"}),e.location]}),(0,a.jsxs)("span",{children:[(0,a.jsx)(h.A,{className:"h-4 w-4 inline mr-1"}),e.time_commitment]})]}),(0,a.jsx)("div",{className:"flex flex-wrap gap-2 mt-3",children:e.skills_required?.map((e,s)=>a.jsx(n.E,{variant:"secondary",className:"text-xs",children:e},s))})]},e.id)),0===Z.length&&(0,a.jsxs)("div",{className:"text-center py-8",children:[(0,a.jsx)(u.A,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-gray-500 mb-4",children:"No opportunities available right now"}),(0,a.jsx)(r.$,{asChild:!0,children:(0,a.jsx)(b(),{href:"/volunteer/opportunities",children:"Browse All Opportunities"})})]})]})})]})}),(0,a.jsx)(d.av,{value:"events",className:"space-y-6",children:(0,a.jsxs)(i.Zp,{children:[(0,a.jsxs)(i.aR,{children:[(0,a.jsx)(i.ZB,{children:"Volunteer Events"}),(0,a.jsx)(i.BT,{children:"Join volunteer events and training sessions"})]}),(0,a.jsx)(i.Wu,{children:(0,a.jsxs)("div",{className:"space-y-4",children:[B.map(e=>(0,a.jsxs)("div",{className:"border rounded-lg p-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,a.jsx)("h4",{className:"font-semibold",children:e.title}),(0,a.jsx)(r.$,{variant:"outline",size:"sm",children:"Register"})]}),(0,a.jsxs)("div",{className:"text-sm text-gray-600",children:[(0,a.jsxs)("p",{children:["Date: ",new Date(e.start_datetime).toLocaleDateString()]}),(0,a.jsxs)("p",{children:["Type: ",e.event_type]})]})]},e.id)),0===B.length&&(0,a.jsx)("p",{className:"text-gray-500 text-center py-4",children:"No upcoming events"})]})})]})}),(0,a.jsx)(d.av,{value:"profile",className:"space-y-6",children:(0,a.jsxs)(i.Zp,{children:[(0,a.jsxs)(i.aR,{children:[(0,a.jsx)(i.ZB,{children:"Volunteer Profile"}),(0,a.jsx)(i.BT,{children:"Manage your volunteer information and preferences"})]}),(0,a.jsxs)(i.Wu,{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("label",{className:"text-sm font-medium",children:"Full Name"}),(0,a.jsxs)("p",{className:"text-sm text-gray-600",children:[e.first_name," ",e.last_name]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("label",{className:"text-sm font-medium",children:"Email"}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:e.email})]}),S.availability&&(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("label",{className:"text-sm font-medium",children:"Availability"}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:S.availability})]}),S.experience&&(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("label",{className:"text-sm font-medium",children:"Experience"}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:S.experience})]})]}),l?.motivation&&(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("label",{className:"text-sm font-medium",children:"Motivation"}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:l.motivation})]}),(0,a.jsx)(r.$,{variant:"outline",children:"Edit Profile"})]})]})})]})]})})}}};