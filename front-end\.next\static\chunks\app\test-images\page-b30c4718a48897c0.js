(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6384],{27507:(e,s,l)=>{"use strict";l.r(s),l.d(s,{default:()=>c});var o=l(95155),a=l(12115),t=l(31886),r=l(44531);function c(){let[e,s]=(0,a.useState)(null),[l,c]=(0,a.useState)(!0),{settings:n,loading:i}=(0,r.t)();return((0,a.useEffect)(()=>{(async()=>{try{let e=await t.uE.getProfile();e.success&&s(e.user)}catch(e){console.error("Failed to fetch user:",e)}finally{c(!1)}})()},[]),l||i)?(0,o.jsx)("div",{className:"p-8",children:"Loading..."}):(0,o.jsxs)("div",{className:"p-8 max-w-4xl mx-auto",children:[(0,o.jsx)("h1",{className:"text-2xl font-bold mb-8",children:"Image Loading Test"}),(0,o.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-8",children:[(0,o.jsxs)("div",{className:"border p-6 rounded-lg",children:[(0,o.jsx)("h2",{className:"text-xl font-semibold mb-4",children:"Settings API Response"}),(0,o.jsx)("pre",{className:"text-sm bg-gray-100 p-4 rounded overflow-auto",children:JSON.stringify(n,null,2)}),(0,o.jsxs)("div",{className:"mt-4",children:[(0,o.jsx)("h3",{className:"font-semibold mb-2",children:"Logo Display:"}),(null==n?void 0:n.app_logo)?(0,o.jsxs)("div",{children:[(0,o.jsxs)("p",{className:"text-sm text-gray-600 mb-2",children:["URL: ",n.app_logo]}),(0,o.jsx)("img",{src:n.app_logo,alt:"Logo",className:"w-16 h-16 object-cover border",onLoad:()=>console.log("Logo loaded successfully"),onError:e=>{console.error("Logo failed to load:",n.app_logo),console.log("Error event:",e)}})]}):(0,o.jsx)("p",{className:"text-red-500",children:"No logo URL found"})]})]}),(0,o.jsxs)("div",{className:"border p-6 rounded-lg",children:[(0,o.jsx)("h2",{className:"text-xl font-semibold mb-4",children:"User API Response"}),(0,o.jsx)("pre",{className:"text-sm bg-gray-100 p-4 rounded overflow-auto",children:JSON.stringify(e,null,2)}),(0,o.jsxs)("div",{className:"mt-4",children:[(0,o.jsx)("h3",{className:"font-semibold mb-2",children:"Avatar Display:"}),(null==e?void 0:e.profile_picture_url)?(0,o.jsxs)("div",{children:[(0,o.jsxs)("p",{className:"text-sm text-gray-600 mb-2",children:["URL: ",e.profile_picture_url]}),(0,o.jsx)("img",{src:e.profile_picture_url,alt:"Avatar",className:"w-16 h-16 object-cover border rounded-full",onLoad:()=>console.log("Avatar loaded successfully"),onError:s=>{console.error("Avatar failed to load:",e.profile_picture_url),console.log("Error event:",s)}})]}):(0,o.jsx)("p",{className:"text-red-500",children:"No avatar URL found"})]})]})]}),(0,o.jsxs)("div",{className:"border p-6 rounded-lg mt-8",children:[(0,o.jsx)("h2",{className:"text-xl font-semibold mb-4",children:"Direct API Tests"}),(0,o.jsxs)("div",{className:"space-y-4",children:[(0,o.jsxs)("div",{children:[(0,o.jsx)("h3",{className:"font-semibold mb-2",children:"Default Avatar (SVG):"}),(0,o.jsx)("p",{className:"text-sm text-gray-600 mb-2",children:"URL: http://localhost:8000/api/v1/images/default-avatar.svg"}),(0,o.jsx)("img",{src:"http://localhost:8000/api/v1/images/default-avatar.svg",alt:"Default Avatar",className:"w-16 h-16 object-cover border rounded-full",onLoad:()=>console.log("Default avatar loaded successfully"),onError:e=>{console.error("Default avatar failed to load"),console.log("Error event:",e)}})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("h3",{className:"font-semibold mb-2",children:"Test CORS:"}),(0,o.jsx)("button",{onClick:async()=>{try{let e=await fetch("http://localhost:8000/api/v1/settings"),s=await e.json();console.log("CORS test successful:",s),alert("CORS test successful - check console")}catch(e){console.error("CORS test failed:",e),alert("CORS test failed - check console")}},className:"px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600",children:"Test CORS API Call"})]})]})]})]})}},44531:(e,s,l)=>{"use strict";l.d(s,{t:()=>t});var o=l(12115),a=l(31886);let t=()=>{let[e,s]=(0,o.useState)(null),[l,t]=(0,o.useState)(!0),[r,c]=(0,o.useState)(null);return(0,o.useEffect)(()=>{(async()=>{try{let e=await a.uE.getSettings();if(e.success)console.log("Settings loaded:",e.data),s(e.data);else throw Error("Invalid settings response")}catch(e){console.error("Error fetching settings:",e),c(e instanceof Error?e.message:"Unknown error"),s({app_name:"HLTKKQ Foundation",site_description:"Transforming Lives, Building Communities",contact_email:"<EMAIL>",contact_phone:"+234 ************"})}finally{t(!1)}})()},[]),{settings:e,loading:l,error:r}}},69675:(e,s,l)=>{Promise.resolve().then(l.bind(l,27507))}},e=>{var s=s=>e(e.s=s);e.O(0,[1886,8441,1684,7358],()=>s(69675)),_N_E=e.O()}]);