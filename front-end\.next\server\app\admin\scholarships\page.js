(()=>{var e={};e.id=458,e.ids=[458],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},13861:(e,a,t)=>{"use strict";t.d(a,{A:()=>s});let s=(0,t(62688).A)("Eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},15616:(e,a,t)=>{"use strict";t.d(a,{T:()=>l});var s=t(60687),r=t(43210),i=t(96241);let l=r.forwardRef(({className:e,...a},t)=>(0,s.jsx)("textarea",{className:(0,i.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",e),ref:t,...a}));l.displayName="Textarea"},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},23928:(e,a,t)=>{"use strict";t.d(a,{A:()=>s});let s=(0,t(62688).A)("DollarSign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]])},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},36081:(e,a,t)=>{"use strict";t.r(a),t.d(a,{default:()=>s});let s=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\admin\\\\scholarships\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\laragon\\www\\laravel-api-ngo\\front-end\\app\\admin\\scholarships\\page.tsx","default")},37826:(e,a,t)=>{"use strict";t.d(a,{Cf:()=>u,L3:()=>x,c7:()=>m,lG:()=>d,rr:()=>h,zM:()=>o});var s=t(60687),r=t(43210),i=t(26134),l=t(11860),n=t(96241);let d=i.bL,o=i.l9,c=i.ZL;i.bm;let p=r.forwardRef(({className:e,...a},t)=>(0,s.jsx)(i.hJ,{ref:t,className:(0,n.cn)("fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",e),...a}));p.displayName=i.hJ.displayName;let u=r.forwardRef(({className:e,children:a,...t},r)=>(0,s.jsxs)(c,{children:[(0,s.jsx)(p,{}),(0,s.jsxs)(i.UC,{ref:r,className:(0,n.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",e),...t,children:[a,(0,s.jsxs)(i.bm,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",children:[(0,s.jsx)(l.A,{className:"h-4 w-4"}),(0,s.jsx)("span",{className:"sr-only",children:"Close"})]})]})]}));u.displayName=i.UC.displayName;let m=({className:e,...a})=>(0,s.jsx)("div",{className:(0,n.cn)("flex flex-col space-y-1.5 text-center sm:text-left",e),...a});m.displayName="DialogHeader";let x=r.forwardRef(({className:e,...a},t)=>(0,s.jsx)(i.hE,{ref:t,className:(0,n.cn)("text-lg font-semibold leading-none tracking-tight",e),...a}));x.displayName=i.hE.displayName;let h=r.forwardRef(({className:e,...a},t)=>(0,s.jsx)(i.VY,{ref:t,className:(0,n.cn)("text-sm text-muted-foreground",e),...a}));h.displayName=i.VY.displayName},39286:(e,a,t)=>{Promise.resolve().then(t.bind(t,36081))},39390:(e,a,t)=>{"use strict";t.d(a,{J:()=>o});var s=t(60687),r=t(43210),i=t(78148),l=t(24224),n=t(96241);let d=(0,l.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),o=r.forwardRef(({className:e,...a},t)=>(0,s.jsx)(i.b,{ref:t,className:(0,n.cn)(d(),e),...a}));o.displayName=i.b.displayName},40228:(e,a,t)=>{"use strict";t.d(a,{A:()=>s});let s=(0,t(62688).A)("Calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},55146:(e,a,t)=>{"use strict";t.d(a,{B8:()=>T,UC:()=>M,bL:()=>k,l9:()=>R});var s=t(43210),r=t(70569),i=t(11273),l=t(72942),n=t(46059),d=t(14163),o=t(43),c=t(65551),p=t(96963),u=t(60687),m="Tabs",[x,h]=(0,i.A)(m,[l.RG]),f=(0,l.RG)(),[g,y]=x(m),v=s.forwardRef((e,a)=>{let{__scopeTabs:t,value:s,onValueChange:r,defaultValue:i,orientation:l="horizontal",dir:n,activationMode:m="automatic",...x}=e,h=(0,o.jH)(n),[f,y]=(0,c.i)({prop:s,onChange:r,defaultProp:i});return(0,u.jsx)(g,{scope:t,baseId:(0,p.B)(),value:f,onValueChange:y,orientation:l,dir:h,activationMode:m,children:(0,u.jsx)(d.sG.div,{dir:h,"data-orientation":l,...x,ref:a})})});v.displayName=m;var j="TabsList",b=s.forwardRef((e,a)=>{let{__scopeTabs:t,loop:s=!0,...r}=e,i=y(j,t),n=f(t);return(0,u.jsx)(l.bL,{asChild:!0,...n,orientation:i.orientation,dir:i.dir,loop:s,children:(0,u.jsx)(d.sG.div,{role:"tablist","aria-orientation":i.orientation,...r,ref:a})})});b.displayName=j;var N="TabsTrigger",w=s.forwardRef((e,a)=>{let{__scopeTabs:t,value:s,disabled:i=!1,...n}=e,o=y(N,t),c=f(t),p=S(o.baseId,s),m=_(o.baseId,s),x=s===o.value;return(0,u.jsx)(l.q7,{asChild:!0,...c,focusable:!i,active:x,children:(0,u.jsx)(d.sG.button,{type:"button",role:"tab","aria-selected":x,"aria-controls":m,"data-state":x?"active":"inactive","data-disabled":i?"":void 0,disabled:i,id:p,...n,ref:a,onMouseDown:(0,r.m)(e.onMouseDown,e=>{i||0!==e.button||!1!==e.ctrlKey?e.preventDefault():o.onValueChange(s)}),onKeyDown:(0,r.m)(e.onKeyDown,e=>{[" ","Enter"].includes(e.key)&&o.onValueChange(s)}),onFocus:(0,r.m)(e.onFocus,()=>{let e="manual"!==o.activationMode;x||i||!e||o.onValueChange(s)})})})});w.displayName=N;var C="TabsContent",A=s.forwardRef((e,a)=>{let{__scopeTabs:t,value:r,forceMount:i,children:l,...o}=e,c=y(C,t),p=S(c.baseId,r),m=_(c.baseId,r),x=r===c.value,h=s.useRef(x);return s.useEffect(()=>{let e=requestAnimationFrame(()=>h.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,u.jsx)(n.C,{present:i||x,children:({present:t})=>(0,u.jsx)(d.sG.div,{"data-state":x?"active":"inactive","data-orientation":c.orientation,role:"tabpanel","aria-labelledby":p,hidden:!t,id:m,tabIndex:0,...o,ref:a,style:{...e.style,animationDuration:h.current?"0s":void 0},children:t&&l})})});function S(e,a){return`${e}-trigger-${a}`}function _(e,a){return`${e}-content-${a}`}A.displayName=C;var k=v,T=b,R=w,M=A},55192:(e,a,t)=>{"use strict";t.d(a,{BT:()=>o,Wu:()=>c,ZB:()=>d,Zp:()=>l,aR:()=>n,wL:()=>p});var s=t(60687),r=t(43210),i=t(96241);let l=r.forwardRef(({className:e,...a},t)=>(0,s.jsx)("div",{ref:t,className:(0,i.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",e),...a}));l.displayName="Card";let n=r.forwardRef(({className:e,...a},t)=>(0,s.jsx)("div",{ref:t,className:(0,i.cn)("flex flex-col space-y-1.5 p-6",e),...a}));n.displayName="CardHeader";let d=r.forwardRef(({className:e,...a},t)=>(0,s.jsx)("div",{ref:t,className:(0,i.cn)("text-2xl font-semibold leading-none tracking-tight",e),...a}));d.displayName="CardTitle";let o=r.forwardRef(({className:e,...a},t)=>(0,s.jsx)("div",{ref:t,className:(0,i.cn)("text-sm text-muted-foreground",e),...a}));o.displayName="CardDescription";let c=r.forwardRef(({className:e,...a},t)=>(0,s.jsx)("div",{ref:t,className:(0,i.cn)("p-6 pt-0",e),...a}));c.displayName="CardContent";let p=r.forwardRef(({className:e,...a},t)=>(0,s.jsx)("div",{ref:t,className:(0,i.cn)("flex items-center p-6 pt-0",e),...a}));p.displayName="CardFooter"},59821:(e,a,t)=>{"use strict";t.d(a,{E:()=>n});var s=t(60687);t(43210);var r=t(24224),i=t(96241);let l=(0,r.F)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function n({className:e,variant:a,...t}){return(0,s.jsx)("div",{className:(0,i.cn)(l({variant:a}),e),...t})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63143:(e,a,t)=>{"use strict";t.d(a,{A:()=>s});let s=(0,t(62688).A)("SquarePen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},63974:(e,a,t)=>{"use strict";t.d(a,{bq:()=>u,eb:()=>f,gC:()=>h,l6:()=>c,yv:()=>p});var s=t(60687),r=t(43210),i=t(97822),l=t(78272),n=t(3589),d=t(13964),o=t(96241);let c=i.bL;i.YJ;let p=i.WT,u=r.forwardRef(({className:e,children:a,...t},r)=>(0,s.jsxs)(i.l9,{ref:r,className:(0,o.cn)("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",e),...t,children:[a,(0,s.jsx)(i.In,{asChild:!0,children:(0,s.jsx)(l.A,{className:"h-4 w-4 opacity-50"})})]}));u.displayName=i.l9.displayName;let m=r.forwardRef(({className:e,...a},t)=>(0,s.jsx)(i.PP,{ref:t,className:(0,o.cn)("flex cursor-default items-center justify-center py-1",e),...a,children:(0,s.jsx)(n.A,{className:"h-4 w-4"})}));m.displayName=i.PP.displayName;let x=r.forwardRef(({className:e,...a},t)=>(0,s.jsx)(i.wn,{ref:t,className:(0,o.cn)("flex cursor-default items-center justify-center py-1",e),...a,children:(0,s.jsx)(l.A,{className:"h-4 w-4"})}));x.displayName=i.wn.displayName;let h=r.forwardRef(({className:e,children:a,position:t="popper",...r},l)=>(0,s.jsx)(i.ZL,{children:(0,s.jsxs)(i.UC,{ref:l,className:(0,o.cn)("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===t&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:t,...r,children:[(0,s.jsx)(m,{}),(0,s.jsx)(i.LM,{className:(0,o.cn)("p-1","popper"===t&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:a}),(0,s.jsx)(x,{})]})}));h.displayName=i.UC.displayName,r.forwardRef(({className:e,...a},t)=>(0,s.jsx)(i.JU,{ref:t,className:(0,o.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",e),...a})).displayName=i.JU.displayName;let f=r.forwardRef(({className:e,children:a,...t},r)=>(0,s.jsxs)(i.q7,{ref:r,className:(0,o.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),...t,children:[(0,s.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,s.jsx)(i.VF,{children:(0,s.jsx)(d.A,{className:"h-4 w-4"})})}),(0,s.jsx)(i.p4,{children:a})]}));f.displayName=i.q7.displayName,r.forwardRef(({className:e,...a},t)=>(0,s.jsx)(i.wv,{ref:t,className:(0,o.cn)("-mx-1 my-1 h-px bg-muted",e),...a})).displayName=i.wv.displayName},71702:(e,a,t)=>{"use strict";t.d(a,{dj:()=>u});var s=t(43210);let r=0,i=new Map,l=e=>{if(i.has(e))return;let a=setTimeout(()=>{i.delete(e),c({type:"REMOVE_TOAST",toastId:e})},1e6);i.set(e,a)},n=(e,a)=>{switch(a.type){case"ADD_TOAST":return{...e,toasts:[a.toast,...e.toasts].slice(0,1)};case"UPDATE_TOAST":return{...e,toasts:e.toasts.map(e=>e.id===a.toast.id?{...e,...a.toast}:e)};case"DISMISS_TOAST":{let{toastId:t}=a;return t?l(t):e.toasts.forEach(e=>{l(e.id)}),{...e,toasts:e.toasts.map(e=>e.id===t||void 0===t?{...e,open:!1}:e)}}case"REMOVE_TOAST":if(void 0===a.toastId)return{...e,toasts:[]};return{...e,toasts:e.toasts.filter(e=>e.id!==a.toastId)}}},d=[],o={toasts:[]};function c(e){o=n(o,e),d.forEach(e=>{e(o)})}function p({...e}){let a=(r=(r+1)%Number.MAX_SAFE_INTEGER).toString(),t=()=>c({type:"DISMISS_TOAST",toastId:a});return c({type:"ADD_TOAST",toast:{...e,id:a,open:!0,onOpenChange:e=>{e||t()}}}),{id:a,dismiss:t,update:e=>c({type:"UPDATE_TOAST",toast:{...e,id:a}})}}function u(){let[e,a]=s.useState(o);return s.useEffect(()=>(d.push(a),()=>{let e=d.indexOf(a);e>-1&&d.splice(e,1)}),[e]),{...e,toast:p,dismiss:e=>c({type:"DISMISS_TOAST",toastId:e})}}},78148:(e,a,t)=>{"use strict";t.d(a,{b:()=>n});var s=t(43210),r=t(14163),i=t(60687),l=s.forwardRef((e,a)=>(0,i.jsx)(r.sG.label,{...e,ref:a,onMouseDown:a=>{a.target.closest("button, input, select, textarea")||(e.onMouseDown?.(a),!a.defaultPrevented&&a.detail>1&&a.preventDefault())}}));l.displayName="Label";var n=l},85910:(e,a,t)=>{"use strict";t.d(a,{Xi:()=>o,av:()=>c,j7:()=>d,tU:()=>n});var s=t(60687),r=t(43210),i=t(55146),l=t(96241);let n=i.bL,d=r.forwardRef(({className:e,...a},t)=>(0,s.jsx)(i.B8,{ref:t,className:(0,l.cn)("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",e),...a}));d.displayName=i.B8.displayName;let o=r.forwardRef(({className:e,...a},t)=>(0,s.jsx)(i.l9,{ref:t,className:(0,l.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",e),...a}));o.displayName=i.l9.displayName;let c=r.forwardRef(({className:e,...a},t)=>(0,s.jsx)(i.UC,{ref:t,className:(0,l.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",e),...a}));c.displayName=i.UC.displayName},86142:(e,a,t)=>{Promise.resolve().then(t.bind(t,99991))},90381:(e,a,t)=>{"use strict";t.r(a),t.d(a,{GlobalError:()=>l.a,__next_app__:()=>p,pages:()=>c,routeModule:()=>u,tree:()=>o});var s=t(65239),r=t(48088),i=t(88170),l=t.n(i),n=t(30893),d={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>n[e]);t.d(a,d);let o={children:["",{children:["admin",{children:["scholarships",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,36081)),"C:\\laragon\\www\\laravel-api-ngo\\front-end\\app\\admin\\scholarships\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,58014)),"C:\\laragon\\www\\laravel-api-ngo\\front-end\\app\\layout.tsx"],error:[()=>Promise.resolve().then(t.bind(t,52608)),"C:\\laragon\\www\\laravel-api-ngo\\front-end\\app\\error.tsx"],loading:[()=>Promise.resolve().then(t.bind(t,99766)),"C:\\laragon\\www\\laravel-api-ngo\\front-end\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(t.bind(t,82366)),"C:\\laragon\\www\\laravel-api-ngo\\front-end\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["C:\\laragon\\www\\laravel-api-ngo\\front-end\\app\\admin\\scholarships\\page.tsx"],p={require:t,loadChunk:()=>Promise.resolve()},u=new s.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/admin/scholarships/page",pathname:"/admin/scholarships",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},96474:(e,a,t)=>{"use strict";t.d(a,{A:()=>s});let s=(0,t(62688).A)("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},99991:(e,a,t)=>{"use strict";t.r(a),t.d(a,{default:()=>R});var s=t(60687),r=t(43210),i=t.n(r),l=t(55192),n=t(24934),d=t(59821),o=t(85910),c=t(68988),p=t(39390),u=t(15616),m=t(63974),x=t(82080),h=t(28947),f=t(27351),g=t(62688);let y=(0,g.A)("Power",[["path",{d:"M12 2v10",key:"mnfbl"}],["path",{d:"M18.4 6.6a9 9 0 1 1-12.77.04",key:"obofu9"}]]),v=(0,g.A)("PowerOff",[["path",{d:"M18.36 6.64A9 9 0 0 1 20.77 15",key:"dxknvb"}],["path",{d:"M6.16 6.16a9 9 0 1 0 12.68 12.68",key:"1x7qb5"}],["path",{d:"M12 2v4",key:"3427ic"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]]);var j=t(41312),b=t(96474),N=t(23928),w=t(40228),C=t(13861),A=t(63143),S=t(71702),_=t(59556),k=t(37826);let T={primary:{icon:x.A,title:"Primary School",color:"bg-blue-500"},secondary:{icon:h.A,title:"Secondary School",color:"bg-green-500"},university:{icon:f.A,title:"University",color:"bg-purple-500"}};function R(){let[e,a]=(0,r.useState)([]),[t,h]=(0,r.useState)(!0),[f,g]=(0,r.useState)("all"),[R,M]=(0,r.useState)(!1),[E,P]=(0,r.useState)(!1),[D,q]=(0,r.useState)(null),[O,F]=(0,r.useState)({title:"",category:"primary",description:"",eligibility_criteria:"",amount:"",application_deadline:"",max_applicants:""}),{toast:I}=(0,S.dj)(),z=async()=>{try{let e=await _.uE.request("/admin/scholarships");e.success&&e.data&&a(e.data)}catch(e){console.error("Error fetching scholarships:",e),I({title:"Error",description:"Failed to load scholarships",variant:"destructive"})}finally{h(!1)}},U=async e=>{e.preventDefault();try{(await _.uE.request("/admin/scholarships",{method:"POST",body:JSON.stringify(O)})).success&&(I({title:"Success",description:"Scholarship created successfully"}),M(!1),G(),z())}catch(e){I({title:"Error",description:"Failed to create scholarship",variant:"destructive"})}},L=async(e,a)=>{try{let t="open"===a?"close":"open";(await _.uE.request(`/admin/scholarships/${e}/${t}`,{method:"POST"})).success&&(I({title:"Success",description:`Scholarship ${t}ed successfully`}),z())}catch(e){I({title:"Error",description:"Failed to update scholarship status",variant:"destructive"})}},G=()=>{F({title:"",category:"primary",description:"",eligibility_criteria:"",amount:"",application_deadline:"",max_applicants:""})},J="all"===f?e:e.filter(e=>e.category===f),$={total:e.length,open:e.filter(e=>"open"===e.status).length,closed:e.filter(e=>"closed"===e.status).length,applications:e.reduce((e,a)=>e+a.current_applicants,0)};return(0,s.jsx)("div",{className:"min-h-screen bg-gray-50 p-6",children:(0,s.jsxs)("div",{className:"max-w-7xl mx-auto",children:[(0,s.jsxs)("div",{className:"mb-8",children:[(0,s.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:"Scholarship Management"}),(0,s.jsx)("p",{className:"text-gray-600",children:"Manage scholarships, applications, and dynamic form fields"})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6 mb-8",children:[(0,s.jsx)(l.Zp,{children:(0,s.jsx)(l.Wu,{className:"p-6",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm text-gray-600",children:"Total Scholarships"}),(0,s.jsx)("p",{className:"text-2xl font-bold",children:$.total})]}),(0,s.jsx)(x.A,{className:"h-8 w-8 text-blue-500"})]})})}),(0,s.jsx)(l.Zp,{children:(0,s.jsx)(l.Wu,{className:"p-6",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm text-gray-600",children:"Open"}),(0,s.jsx)("p",{className:"text-2xl font-bold text-green-600",children:$.open})]}),(0,s.jsx)(y,{className:"h-8 w-8 text-green-500"})]})})}),(0,s.jsx)(l.Zp,{children:(0,s.jsx)(l.Wu,{className:"p-6",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm text-gray-600",children:"Closed"}),(0,s.jsx)("p",{className:"text-2xl font-bold text-red-600",children:$.closed})]}),(0,s.jsx)(v,{className:"h-8 w-8 text-red-500"})]})})}),(0,s.jsx)(l.Zp,{children:(0,s.jsx)(l.Wu,{className:"p-6",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm text-gray-600",children:"Total Applications"}),(0,s.jsx)("p",{className:"text-2xl font-bold text-purple-600",children:$.applications})]}),(0,s.jsx)(j.A,{className:"h-8 w-8 text-purple-500"})]})})})]}),(0,s.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,s.jsx)(o.tU,{value:f,onValueChange:g,children:(0,s.jsxs)(o.j7,{children:[(0,s.jsx)(o.Xi,{value:"all",children:"All"}),(0,s.jsx)(o.Xi,{value:"primary",children:"Primary"}),(0,s.jsx)(o.Xi,{value:"secondary",children:"Secondary"}),(0,s.jsx)(o.Xi,{value:"university",children:"University"})]})}),(0,s.jsxs)(k.lG,{open:R,onOpenChange:M,children:[(0,s.jsx)(k.zM,{asChild:!0,children:(0,s.jsxs)(n.$,{children:[(0,s.jsx)(b.A,{className:"h-4 w-4 mr-2"}),"Create Scholarship"]})}),(0,s.jsxs)(k.Cf,{className:"max-w-2xl",children:[(0,s.jsxs)(k.c7,{children:[(0,s.jsx)(k.L3,{children:"Create New Scholarship"}),(0,s.jsx)(k.rr,{children:"Create a new scholarship with custom form fields for applications."})]}),(0,s.jsxs)("form",{onSubmit:U,className:"space-y-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)(p.J,{htmlFor:"title",children:"Title"}),(0,s.jsx)(c.p,{id:"title",value:O.title,onChange:e=>F(a=>({...a,title:e.target.value})),required:!0})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)(p.J,{htmlFor:"category",children:"Category"}),(0,s.jsxs)(m.l6,{value:O.category,onValueChange:e=>F(a=>({...a,category:e})),children:[(0,s.jsx)(m.bq,{children:(0,s.jsx)(m.yv,{})}),(0,s.jsxs)(m.gC,{children:[(0,s.jsx)(m.eb,{value:"primary",children:"Primary School"}),(0,s.jsx)(m.eb,{value:"secondary",children:"Secondary School"}),(0,s.jsx)(m.eb,{value:"university",children:"University"})]})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)(p.J,{htmlFor:"description",children:"Description"}),(0,s.jsx)(u.T,{id:"description",value:O.description,onChange:e=>F(a=>({...a,description:e.target.value})),required:!0})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)(p.J,{htmlFor:"eligibility",children:"Eligibility Criteria"}),(0,s.jsx)(u.T,{id:"eligibility",value:O.eligibility_criteria,onChange:e=>F(a=>({...a,eligibility_criteria:e.target.value})),required:!0})]}),(0,s.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)(p.J,{htmlFor:"amount",children:"Amount (₦)"}),(0,s.jsx)(c.p,{id:"amount",type:"number",value:O.amount,onChange:e=>F(a=>({...a,amount:e.target.value})),required:!0})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)(p.J,{htmlFor:"deadline",children:"Application Deadline"}),(0,s.jsx)(c.p,{id:"deadline",type:"date",value:O.application_deadline,onChange:e=>F(a=>({...a,application_deadline:e.target.value})),required:!0})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)(p.J,{htmlFor:"max_applicants",children:"Maximum Applicants"}),(0,s.jsx)(c.p,{id:"max_applicants",type:"number",value:O.max_applicants,onChange:e=>F(a=>({...a,max_applicants:e.target.value}))})]}),(0,s.jsx)(n.$,{type:"submit",className:"w-full",children:"Create Scholarship"})]})]})]})]}),(0,s.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:J.map(e=>(0,s.jsxs)(l.Zp,{className:"hover:shadow-lg transition-shadow",children:[(0,s.jsxs)(l.aR,{children:[(0,s.jsxs)("div",{className:"flex items-start justify-between",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[i().createElement(T[e.category].icon,{className:"h-5 w-5"}),(0,s.jsx)(l.ZB,{className:"text-lg leading-tight",children:e.title})]}),(0,s.jsx)(d.E,{variant:"open"===e.status?"default":"secondary",className:"open"===e.status?"bg-green-500":"bg-red-500",children:e.status})]}),(0,s.jsx)(l.BT,{className:"line-clamp-2",children:e.description})]}),(0,s.jsxs)(l.Wu,{className:"space-y-3",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2 text-sm text-gray-600",children:[(0,s.jsx)(N.A,{className:"h-4 w-4"}),(0,s.jsxs)("span",{children:["₦",e.amount.toLocaleString()]})]}),(0,s.jsxs)("div",{className:"flex items-center gap-2 text-sm text-gray-600",children:[(0,s.jsx)(w.A,{className:"h-4 w-4"}),(0,s.jsxs)("span",{children:["Due: ",new Date(e.application_deadline).toLocaleDateString()]})]}),(0,s.jsxs)("div",{className:"flex items-center gap-2 text-sm text-gray-600",children:[(0,s.jsx)(j.A,{className:"h-4 w-4"}),(0,s.jsxs)("span",{children:[e.current_applicants,"/",e.max_applicants||"∞"," applicants"]})]}),(0,s.jsxs)("div",{className:"flex gap-2 pt-4",children:[(0,s.jsxs)(n.$,{variant:"outline",size:"sm",className:"flex-1",children:[(0,s.jsx)(C.A,{className:"h-4 w-4 mr-1"}),"View"]}),(0,s.jsxs)(n.$,{variant:"outline",size:"sm",className:"flex-1",children:[(0,s.jsx)(A.A,{className:"h-4 w-4 mr-1"}),"Edit"]}),(0,s.jsx)(n.$,{variant:"open"===e.status?"destructive":"default",size:"sm",className:"flex-1",onClick:()=>L(e.id,e.status),children:"open"===e.status?"Close":"Open"})]})]})]},e.id))}),0===J.length&&!t&&(0,s.jsxs)("div",{className:"text-center py-12",children:[(0,s.jsx)(x.A,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),(0,s.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"No scholarships found"}),(0,s.jsx)("p",{className:"text-gray-500 mb-4",children:"all"===f?"Create your first scholarship to get started.":`No scholarships in the ${f} category.`}),(0,s.jsxs)(n.$,{onClick:()=>M(!0),children:[(0,s.jsx)(b.A,{className:"h-4 w-4 mr-2"}),"Create Scholarship"]})]})]})})}}};var a=require("../../../webpack-runtime.js");a.C(e);var t=e=>a(a.s=e),s=a.X(0,[555,394,702],()=>t(90381));module.exports=s})();