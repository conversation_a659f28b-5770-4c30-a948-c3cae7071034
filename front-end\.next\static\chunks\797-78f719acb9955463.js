"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[797],{15452:(e,t,r)=>{r.d(t,{G$:()=>V,Hs:()=>x,UC:()=>et,VY:()=>en,ZL:()=>Q,bL:()=>Y,bm:()=>eo,hE:()=>er,hJ:()=>ee,l9:()=>$});var n=r(12115),o=r(85185),a=r(6101),l=r(46081),i=r(61285),s=r(5845),u=r(19178),c=r(25519),d=r(34378),f=r(28905),p=r(63655),v=r(92293),g=r(93795),m=r(38168),h=r(99708),y=r(95155),b="Dialog",[w,x]=(0,l.A)(b),[D,R]=w(b),j=e=>{let{__scopeDialog:t,children:r,open:o,defaultOpen:a,onOpenChange:l,modal:u=!0}=e,c=n.useRef(null),d=n.useRef(null),[f=!1,p]=(0,s.i)({prop:o,defaultProp:a,onChange:l});return(0,y.jsx)(D,{scope:t,triggerRef:c,contentRef:d,contentId:(0,i.B)(),titleId:(0,i.B)(),descriptionId:(0,i.B)(),open:f,onOpenChange:p,onOpenToggle:n.useCallback(()=>p(e=>!e),[p]),modal:u,children:r})};j.displayName=b;var I="DialogTrigger",C=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,l=R(I,r),i=(0,a.s)(t,l.triggerRef);return(0,y.jsx)(p.sG.button,{type:"button","aria-haspopup":"dialog","aria-expanded":l.open,"aria-controls":l.contentId,"data-state":W(l.open),...n,ref:i,onClick:(0,o.m)(e.onClick,l.onOpenToggle)})});C.displayName=I;var A="DialogPortal",[F,k]=w(A,{forceMount:void 0}),E=e=>{let{__scopeDialog:t,forceMount:r,children:o,container:a}=e,l=R(A,t);return(0,y.jsx)(F,{scope:t,forceMount:r,children:n.Children.map(o,e=>(0,y.jsx)(f.C,{present:r||l.open,children:(0,y.jsx)(d.Z,{asChild:!0,container:a,children:e})}))})};E.displayName=A;var _="DialogOverlay",N=n.forwardRef((e,t)=>{let r=k(_,e.__scopeDialog),{forceMount:n=r.forceMount,...o}=e,a=R(_,e.__scopeDialog);return a.modal?(0,y.jsx)(f.C,{present:n||a.open,children:(0,y.jsx)(O,{...o,ref:t})}):null});N.displayName=_;var O=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,o=R(_,r);return(0,y.jsx)(g.A,{as:h.DX,allowPinchZoom:!0,shards:[o.contentRef],children:(0,y.jsx)(p.sG.div,{"data-state":W(o.open),...n,ref:t,style:{pointerEvents:"auto",...n.style}})})}),T="DialogContent",G=n.forwardRef((e,t)=>{let r=k(T,e.__scopeDialog),{forceMount:n=r.forceMount,...o}=e,a=R(T,e.__scopeDialog);return(0,y.jsx)(f.C,{present:n||a.open,children:a.modal?(0,y.jsx)(P,{...o,ref:t}):(0,y.jsx)(M,{...o,ref:t})})});G.displayName=T;var P=n.forwardRef((e,t)=>{let r=R(T,e.__scopeDialog),l=n.useRef(null),i=(0,a.s)(t,r.contentRef,l);return n.useEffect(()=>{let e=l.current;if(e)return(0,m.Eq)(e)},[]),(0,y.jsx)(S,{...e,ref:i,trapFocus:r.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,o.m)(e.onCloseAutoFocus,e=>{var t;e.preventDefault(),null===(t=r.triggerRef.current)||void 0===t||t.focus()}),onPointerDownOutside:(0,o.m)(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,r=0===t.button&&!0===t.ctrlKey;(2===t.button||r)&&e.preventDefault()}),onFocusOutside:(0,o.m)(e.onFocusOutside,e=>e.preventDefault())})}),M=n.forwardRef((e,t)=>{let r=R(T,e.__scopeDialog),o=n.useRef(!1),a=n.useRef(!1);return(0,y.jsx)(S,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{var n,l;null===(n=e.onCloseAutoFocus)||void 0===n||n.call(e,t),t.defaultPrevented||(o.current||null===(l=r.triggerRef.current)||void 0===l||l.focus(),t.preventDefault()),o.current=!1,a.current=!1},onInteractOutside:t=>{var n,l;null===(n=e.onInteractOutside)||void 0===n||n.call(e,t),t.defaultPrevented||(o.current=!0,"pointerdown"!==t.detail.originalEvent.type||(a.current=!0));let i=t.target;(null===(l=r.triggerRef.current)||void 0===l?void 0:l.contains(i))&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&a.current&&t.preventDefault()}})}),S=n.forwardRef((e,t)=>{let{__scopeDialog:r,trapFocus:o,onOpenAutoFocus:l,onCloseAutoFocus:i,...s}=e,d=R(T,r),f=n.useRef(null),p=(0,a.s)(t,f);return(0,v.Oh)(),(0,y.jsxs)(y.Fragment,{children:[(0,y.jsx)(c.n,{asChild:!0,loop:!0,trapped:o,onMountAutoFocus:l,onUnmountAutoFocus:i,children:(0,y.jsx)(u.qW,{role:"dialog",id:d.contentId,"aria-describedby":d.descriptionId,"aria-labelledby":d.titleId,"data-state":W(d.open),...s,ref:p,onDismiss:()=>d.onOpenChange(!1)})}),(0,y.jsxs)(y.Fragment,{children:[(0,y.jsx)(z,{titleId:d.titleId}),(0,y.jsx)(J,{contentRef:f,descriptionId:d.descriptionId})]})]})}),B="DialogTitle",L=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,o=R(B,r);return(0,y.jsx)(p.sG.h2,{id:o.titleId,...n,ref:t})});L.displayName=B;var K="DialogDescription",q=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,o=R(K,r);return(0,y.jsx)(p.sG.p,{id:o.descriptionId,...n,ref:t})});q.displayName=K;var U="DialogClose",H=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,a=R(U,r);return(0,y.jsx)(p.sG.button,{type:"button",...n,ref:t,onClick:(0,o.m)(e.onClick,()=>a.onOpenChange(!1))})});function W(e){return e?"open":"closed"}H.displayName=U;var Z="DialogTitleWarning",[V,X]=(0,l.q)(Z,{contentName:T,titleName:B,docsSlug:"dialog"}),z=e=>{let{titleId:t}=e,r=X(Z),o="`".concat(r.contentName,"` requires a `").concat(r.titleName,"` for the component to be accessible for screen reader users.\n\nIf you want to hide the `").concat(r.titleName,"`, you can wrap it with our VisuallyHidden component.\n\nFor more information, see https://radix-ui.com/primitives/docs/components/").concat(r.docsSlug);return n.useEffect(()=>{t&&!document.getElementById(t)&&console.error(o)},[o,t]),null},J=e=>{let{contentRef:t,descriptionId:r}=e,o=X("DialogDescriptionWarning"),a="Warning: Missing `Description` or `aria-describedby={undefined}` for {".concat(o.contentName,"}.");return n.useEffect(()=>{var e;let n=null===(e=t.current)||void 0===e?void 0:e.getAttribute("aria-describedby");r&&n&&!document.getElementById(r)&&console.warn(a)},[a,t,r]),null},Y=j,$=C,Q=E,ee=N,et=G,er=L,en=q,eo=H},16785:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("Target",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"12",r:"6",key:"1vlfrh"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}]])},17580:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]])},54416:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},89196:(e,t,r)=>{r.d(t,{RG:()=>x,bL:()=>E,q7:()=>_});var n=r(12115),o=r(85185),a=r(82284),l=r(6101),i=r(46081),s=r(61285),u=r(63655),c=r(39033),d=r(5845),f=r(94315),p=r(95155),v="rovingFocusGroup.onEntryFocus",g={bubbles:!1,cancelable:!0},m="RovingFocusGroup",[h,y,b]=(0,a.N)(m),[w,x]=(0,i.A)(m,[b]),[D,R]=w(m),j=n.forwardRef((e,t)=>(0,p.jsx)(h.Provider,{scope:e.__scopeRovingFocusGroup,children:(0,p.jsx)(h.Slot,{scope:e.__scopeRovingFocusGroup,children:(0,p.jsx)(I,{...e,ref:t})})}));j.displayName=m;var I=n.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:r,orientation:a,loop:i=!1,dir:s,currentTabStopId:m,defaultCurrentTabStopId:h,onCurrentTabStopIdChange:b,onEntryFocus:w,preventScrollOnEntryFocus:x=!1,...R}=e,j=n.useRef(null),I=(0,l.s)(t,j),C=(0,f.jH)(s),[A=null,F]=(0,d.i)({prop:m,defaultProp:h,onChange:b}),[E,_]=n.useState(!1),N=(0,c.c)(w),O=y(r),T=n.useRef(!1),[G,P]=n.useState(0);return n.useEffect(()=>{let e=j.current;if(e)return e.addEventListener(v,N),()=>e.removeEventListener(v,N)},[N]),(0,p.jsx)(D,{scope:r,orientation:a,dir:C,loop:i,currentTabStopId:A,onItemFocus:n.useCallback(e=>F(e),[F]),onItemShiftTab:n.useCallback(()=>_(!0),[]),onFocusableItemAdd:n.useCallback(()=>P(e=>e+1),[]),onFocusableItemRemove:n.useCallback(()=>P(e=>e-1),[]),children:(0,p.jsx)(u.sG.div,{tabIndex:E||0===G?-1:0,"data-orientation":a,...R,ref:I,style:{outline:"none",...e.style},onMouseDown:(0,o.m)(e.onMouseDown,()=>{T.current=!0}),onFocus:(0,o.m)(e.onFocus,e=>{let t=!T.current;if(e.target===e.currentTarget&&t&&!E){let t=new CustomEvent(v,g);if(e.currentTarget.dispatchEvent(t),!t.defaultPrevented){let e=O().filter(e=>e.focusable);k([e.find(e=>e.active),e.find(e=>e.id===A),...e].filter(Boolean).map(e=>e.ref.current),x)}}T.current=!1}),onBlur:(0,o.m)(e.onBlur,()=>_(!1))})})}),C="RovingFocusGroupItem",A=n.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:r,focusable:a=!0,active:l=!1,tabStopId:i,...c}=e,d=(0,s.B)(),f=i||d,v=R(C,r),g=v.currentTabStopId===f,m=y(r),{onFocusableItemAdd:b,onFocusableItemRemove:w}=v;return n.useEffect(()=>{if(a)return b(),()=>w()},[a,b,w]),(0,p.jsx)(h.ItemSlot,{scope:r,id:f,focusable:a,active:l,children:(0,p.jsx)(u.sG.span,{tabIndex:g?0:-1,"data-orientation":v.orientation,...c,ref:t,onMouseDown:(0,o.m)(e.onMouseDown,e=>{a?v.onItemFocus(f):e.preventDefault()}),onFocus:(0,o.m)(e.onFocus,()=>v.onItemFocus(f)),onKeyDown:(0,o.m)(e.onKeyDown,e=>{if("Tab"===e.key&&e.shiftKey){v.onItemShiftTab();return}if(e.target!==e.currentTarget)return;let t=function(e,t,r){var n;let o=(n=e.key,"rtl"!==r?n:"ArrowLeft"===n?"ArrowRight":"ArrowRight"===n?"ArrowLeft":n);if(!("vertical"===t&&["ArrowLeft","ArrowRight"].includes(o))&&!("horizontal"===t&&["ArrowUp","ArrowDown"].includes(o)))return F[o]}(e,v.orientation,v.dir);if(void 0!==t){if(e.metaKey||e.ctrlKey||e.altKey||e.shiftKey)return;e.preventDefault();let r=m().filter(e=>e.focusable).map(e=>e.ref.current);if("last"===t)r.reverse();else if("prev"===t||"next"===t){"prev"===t&&r.reverse();let n=r.indexOf(e.currentTarget);r=v.loop?function(e,t){return e.map((r,n)=>e[(t+n)%e.length])}(r,n+1):r.slice(n+1)}setTimeout(()=>k(r))}})})})});A.displayName=C;var F={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function k(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],r=document.activeElement;for(let n of e)if(n===r||(n.focus({preventScroll:t}),document.activeElement!==r))return}var E=j,_=A}}]);