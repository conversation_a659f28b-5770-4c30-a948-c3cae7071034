"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6558],{3096:(e,t,r)=>{r.d(t,{Wx:()=>u});var n=r(12115),a=Object.defineProperty,o=(e,t,r)=>t in e?a(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,i=new Map,l=new WeakMap,s=0,c=void 0;function u(){var e;let{threshold:t,delay:r,trackVisibility:a,rootMargin:o,root:u,triggerOnce:d,skip:f,initialInView:v,fallbackInView:p,onChange:h}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},[y,m]=n.useState(null),b=n.useRef(h),[g,w]=n.useState({inView:!!v,entry:void 0});b.current=h,n.useEffect(()=>{let e;if(!f&&y)return e=function(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:c;if(void 0===window.IntersectionObserver&&void 0!==n){let a=e.getBoundingClientRect();return t(n,{isIntersecting:n,target:e,intersectionRatio:"number"==typeof r.threshold?r.threshold:0,time:0,boundingClientRect:a,intersectionRect:a,rootBounds:a}),()=>{}}let{id:a,observer:o,elements:u}=function(e){let t=Object.keys(e).sort().filter(t=>void 0!==e[t]).map(t=>{var r;return"".concat(t,"_").concat("root"===t?(r=e.root)?(l.has(r)||(s+=1,l.set(r,s.toString())),l.get(r)):"0":e[t])}).toString(),r=i.get(t);if(!r){let n;let a=new Map,o=new IntersectionObserver(t=>{t.forEach(t=>{var r;let o=t.isIntersecting&&n.some(e=>t.intersectionRatio>=e);e.trackVisibility&&void 0===t.isVisible&&(t.isVisible=o),null==(r=a.get(t.target))||r.forEach(e=>{e(o,t)})})},e);n=o.thresholds||(Array.isArray(e.threshold)?e.threshold:[e.threshold||0]),r={id:t,observer:o,elements:a},i.set(t,r)}return r}(r),d=u.get(e)||[];return u.has(e)||u.set(e,d),d.push(t),o.observe(e),function(){d.splice(d.indexOf(t),1),0===d.length&&(u.delete(e),o.unobserve(e)),0===u.size&&(o.disconnect(),i.delete(a))}}(y,(t,r)=>{w({inView:t,entry:r}),b.current&&b.current(t,r),r.isIntersecting&&d&&e&&(e(),e=void 0)},{root:u,rootMargin:o,threshold:t,trackVisibility:a,delay:r},p),()=>{e&&e()}},[Array.isArray(t)?t.toString():t,y,u,o,d,f,a,p,r]);let k=null==(e=g.entry)?void 0:e.target,A=n.useRef(void 0);y||!k||d||f||A.current===k||(A.current=k,w({inView:!!v,entry:void 0}));let x=[m,g.inView,g.entry];return x.ref=x[0],x.inView=x[1],x.entry=x[2],x}n.Component},4516:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("MapPin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])},5040:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("BookOpen",[["path",{d:"M12 7v14",key:"1akyts"}],["path",{d:"M3 18a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1h5a4 4 0 0 1 4 4 4 4 0 0 1 4-4h5a1 1 0 0 1 1 1v13a1 1 0 0 1-1 1h-6a3 3 0 0 0-3 3 3 3 0 0 0-3-3z",key:"ruj8y"}]])},16785:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("Target",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"12",r:"6",key:"1vlfrh"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}]])},17580:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]])},33109:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("TrendingUp",[["polyline",{points:"22 7 13.5 15.5 8.5 10.5 2 17",key:"126l90"}],["polyline",{points:"16 7 22 7 22 13",key:"kwv8wd"}]])},40646:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("CircleCheckBig",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},51976:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("Heart",[["path",{d:"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z",key:"c3ymky"}]])},55863:(e,t,r)=>{r.d(t,{C1:()=>k,bL:()=>w});var n=r(12115),a=r(46081),o=r(63655),i=r(95155),l="Progress",[s,c]=(0,a.A)(l),[u,d]=s(l),f=n.forwardRef((e,t)=>{var r,n,a,l;let{__scopeProgress:s,value:c=null,max:d,getValueLabel:f=h,...v}=e;(d||0===d)&&!b(d)&&console.error((r="".concat(d),n="Progress","Invalid prop `max` of value `".concat(r,"` supplied to `").concat(n,"`. Only numbers greater than 0 are valid max values. Defaulting to `").concat(100,"`.")));let p=b(d)?d:100;null===c||g(c,p)||console.error((a="".concat(c),l="Progress","Invalid prop `value` of value `".concat(a,"` supplied to `").concat(l,"`. The `value` prop must be:\n  - a positive number\n  - less than the value passed to `max` (or ").concat(100," if no `max` prop is set)\n  - `null` or `undefined` if the progress is indeterminate.\n\nDefaulting to `null`.")));let w=g(c,p)?c:null,k=m(w)?f(w,p):void 0;return(0,i.jsx)(u,{scope:s,value:w,max:p,children:(0,i.jsx)(o.sG.div,{"aria-valuemax":p,"aria-valuemin":0,"aria-valuenow":m(w)?w:void 0,"aria-valuetext":k,role:"progressbar","data-state":y(w,p),"data-value":null!=w?w:void 0,"data-max":p,...v,ref:t})})});f.displayName=l;var v="ProgressIndicator",p=n.forwardRef((e,t)=>{var r;let{__scopeProgress:n,...a}=e,l=d(v,n);return(0,i.jsx)(o.sG.div,{"data-state":y(l.value,l.max),"data-value":null!==(r=l.value)&&void 0!==r?r:void 0,"data-max":l.max,...a,ref:t})});function h(e,t){return"".concat(Math.round(e/t*100),"%")}function y(e,t){return null==e?"indeterminate":e===t?"complete":"loading"}function m(e){return"number"==typeof e}function b(e){return m(e)&&!isNaN(e)&&e>0}function g(e,t){return m(e)&&!isNaN(e)&&e<=t&&e>=0}p.displayName=v;var w=f,k=p},60704:(e,t,r)=>{r.d(t,{B8:()=>D,UC:()=>T,bL:()=>j,l9:()=>F});var n=r(12115),a=r(85185),o=r(46081),i=r(89196),l=r(28905),s=r(63655),c=r(94315),u=r(5845),d=r(61285),f=r(95155),v="Tabs",[p,h]=(0,o.A)(v,[i.RG]),y=(0,i.RG)(),[m,b]=p(v),g=n.forwardRef((e,t)=>{let{__scopeTabs:r,value:n,onValueChange:a,defaultValue:o,orientation:i="horizontal",dir:l,activationMode:v="automatic",...p}=e,h=(0,c.jH)(l),[y,b]=(0,u.i)({prop:n,onChange:a,defaultProp:o});return(0,f.jsx)(m,{scope:r,baseId:(0,d.B)(),value:y,onValueChange:b,orientation:i,dir:h,activationMode:v,children:(0,f.jsx)(s.sG.div,{dir:h,"data-orientation":i,...p,ref:t})})});g.displayName=v;var w="TabsList",k=n.forwardRef((e,t)=>{let{__scopeTabs:r,loop:n=!0,...a}=e,o=b(w,r),l=y(r);return(0,f.jsx)(i.bL,{asChild:!0,...l,orientation:o.orientation,dir:o.dir,loop:n,children:(0,f.jsx)(s.sG.div,{role:"tablist","aria-orientation":o.orientation,...a,ref:t})})});k.displayName=w;var A="TabsTrigger",x=n.forwardRef((e,t)=>{let{__scopeTabs:r,value:n,disabled:o=!1,...l}=e,c=b(A,r),u=y(r),d=I(c.baseId,n),v=M(c.baseId,n),p=n===c.value;return(0,f.jsx)(i.q7,{asChild:!0,...u,focusable:!o,active:p,children:(0,f.jsx)(s.sG.button,{type:"button",role:"tab","aria-selected":p,"aria-controls":v,"data-state":p?"active":"inactive","data-disabled":o?"":void 0,disabled:o,id:d,...l,ref:t,onMouseDown:(0,a.m)(e.onMouseDown,e=>{o||0!==e.button||!1!==e.ctrlKey?e.preventDefault():c.onValueChange(n)}),onKeyDown:(0,a.m)(e.onKeyDown,e=>{[" ","Enter"].includes(e.key)&&c.onValueChange(n)}),onFocus:(0,a.m)(e.onFocus,()=>{let e="manual"!==c.activationMode;p||o||!e||c.onValueChange(n)})})})});x.displayName=A;var R="TabsContent",C=n.forwardRef((e,t)=>{let{__scopeTabs:r,value:a,forceMount:o,children:i,...c}=e,u=b(R,r),d=I(u.baseId,a),v=M(u.baseId,a),p=a===u.value,h=n.useRef(p);return n.useEffect(()=>{let e=requestAnimationFrame(()=>h.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,f.jsx)(l.C,{present:o||p,children:r=>{let{present:n}=r;return(0,f.jsx)(s.sG.div,{"data-state":p?"active":"inactive","data-orientation":u.orientation,role:"tabpanel","aria-labelledby":d,hidden:!n,id:v,tabIndex:0,...c,ref:t,style:{...e.style,animationDuration:h.current?"0s":void 0},children:n&&i})}})});function I(e,t){return"".concat(e,"-trigger-").concat(t)}function M(e,t){return"".concat(e,"-content-").concat(t)}C.displayName=R;var j=g,D=k,F=x,T=C},69037:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("Award",[["path",{d:"m15.477 12.89 1.515 8.526a.5.5 0 0 1-.81.47l-3.58-2.687a1 1 0 0 0-1.197 0l-3.586 2.686a.5.5 0 0 1-.81-.469l1.514-8.526",key:"1yiouv"}],["circle",{cx:"12",cy:"8",r:"6",key:"1vp47v"}]])},72713:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("ChartColumn",[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]])},87949:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("GraduationCap",[["path",{d:"M21.42 10.922a1 1 0 0 0-.019-1.838L12.83 5.18a2 2 0 0 0-1.66 0L2.6 9.08a1 1 0 0 0 0 1.832l8.57 3.908a2 2 0 0 0 1.66 0z",key:"j76jl0"}],["path",{d:"M22 10v6",key:"1lu8f3"}],["path",{d:"M6 12.5V16a6 3 0 0 0 12 0v-3.5",key:"1r8lef"}]])},89196:(e,t,r)=>{r.d(t,{RG:()=>k,bL:()=>F,q7:()=>T});var n=r(12115),a=r(85185),o=r(82284),i=r(6101),l=r(46081),s=r(61285),c=r(63655),u=r(39033),d=r(5845),f=r(94315),v=r(95155),p="rovingFocusGroup.onEntryFocus",h={bubbles:!1,cancelable:!0},y="RovingFocusGroup",[m,b,g]=(0,o.N)(y),[w,k]=(0,l.A)(y,[g]),[A,x]=w(y),R=n.forwardRef((e,t)=>(0,v.jsx)(m.Provider,{scope:e.__scopeRovingFocusGroup,children:(0,v.jsx)(m.Slot,{scope:e.__scopeRovingFocusGroup,children:(0,v.jsx)(C,{...e,ref:t})})}));R.displayName=y;var C=n.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:r,orientation:o,loop:l=!1,dir:s,currentTabStopId:y,defaultCurrentTabStopId:m,onCurrentTabStopIdChange:g,onEntryFocus:w,preventScrollOnEntryFocus:k=!1,...x}=e,R=n.useRef(null),C=(0,i.s)(t,R),I=(0,f.jH)(s),[M=null,j]=(0,d.i)({prop:y,defaultProp:m,onChange:g}),[F,T]=n.useState(!1),E=(0,u.c)(w),G=b(r),N=n.useRef(!1),[V,L]=n.useState(0);return n.useEffect(()=>{let e=R.current;if(e)return e.addEventListener(p,E),()=>e.removeEventListener(p,E)},[E]),(0,v.jsx)(A,{scope:r,orientation:o,dir:I,loop:l,currentTabStopId:M,onItemFocus:n.useCallback(e=>j(e),[j]),onItemShiftTab:n.useCallback(()=>T(!0),[]),onFocusableItemAdd:n.useCallback(()=>L(e=>e+1),[]),onFocusableItemRemove:n.useCallback(()=>L(e=>e-1),[]),children:(0,v.jsx)(c.sG.div,{tabIndex:F||0===V?-1:0,"data-orientation":o,...x,ref:C,style:{outline:"none",...e.style},onMouseDown:(0,a.m)(e.onMouseDown,()=>{N.current=!0}),onFocus:(0,a.m)(e.onFocus,e=>{let t=!N.current;if(e.target===e.currentTarget&&t&&!F){let t=new CustomEvent(p,h);if(e.currentTarget.dispatchEvent(t),!t.defaultPrevented){let e=G().filter(e=>e.focusable);D([e.find(e=>e.active),e.find(e=>e.id===M),...e].filter(Boolean).map(e=>e.ref.current),k)}}N.current=!1}),onBlur:(0,a.m)(e.onBlur,()=>T(!1))})})}),I="RovingFocusGroupItem",M=n.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:r,focusable:o=!0,active:i=!1,tabStopId:l,...u}=e,d=(0,s.B)(),f=l||d,p=x(I,r),h=p.currentTabStopId===f,y=b(r),{onFocusableItemAdd:g,onFocusableItemRemove:w}=p;return n.useEffect(()=>{if(o)return g(),()=>w()},[o,g,w]),(0,v.jsx)(m.ItemSlot,{scope:r,id:f,focusable:o,active:i,children:(0,v.jsx)(c.sG.span,{tabIndex:h?0:-1,"data-orientation":p.orientation,...u,ref:t,onMouseDown:(0,a.m)(e.onMouseDown,e=>{o?p.onItemFocus(f):e.preventDefault()}),onFocus:(0,a.m)(e.onFocus,()=>p.onItemFocus(f)),onKeyDown:(0,a.m)(e.onKeyDown,e=>{if("Tab"===e.key&&e.shiftKey){p.onItemShiftTab();return}if(e.target!==e.currentTarget)return;let t=function(e,t,r){var n;let a=(n=e.key,"rtl"!==r?n:"ArrowLeft"===n?"ArrowRight":"ArrowRight"===n?"ArrowLeft":n);if(!("vertical"===t&&["ArrowLeft","ArrowRight"].includes(a))&&!("horizontal"===t&&["ArrowUp","ArrowDown"].includes(a)))return j[a]}(e,p.orientation,p.dir);if(void 0!==t){if(e.metaKey||e.ctrlKey||e.altKey||e.shiftKey)return;e.preventDefault();let r=y().filter(e=>e.focusable).map(e=>e.ref.current);if("last"===t)r.reverse();else if("prev"===t||"next"===t){"prev"===t&&r.reverse();let n=r.indexOf(e.currentTarget);r=p.loop?function(e,t){return e.map((r,n)=>e[(t+n)%e.length])}(r,n+1):r.slice(n+1)}setTimeout(()=>D(r))}})})})});M.displayName=I;var j={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function D(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],r=document.activeElement;for(let n of e)if(n===r||(n.focus({preventScroll:t}),document.activeElement!==r))return}var F=R,T=M}}]);