{"c": ["app/layout", "app/scholarship-application/page", "webpack"], "r": [], "m": ["(app-pages-browser)/./components/scholarship/ScholarshipApplicationPage.tsx", "(app-pages-browser)/./components/scholarship/SuccessMessage.tsx", "(app-pages-browser)/./components/ui/label.tsx", "(app-pages-browser)/./components/ui/tabs.tsx", "(app-pages-browser)/./components/ui/textarea.tsx", "(app-pages-browser)/./hooks/use-toast.ts", "(app-pages-browser)/./node_modules/@radix-ui/react-label/dist/index.mjs", "(app-pages-browser)/./node_modules/@radix-ui/react-tabs/dist/index.mjs", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/building.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/credit-card.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-up.js", "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5Claravel-api-ngo%5C%5Cfront-end%5C%5Ccomponents%5C%5Cscholarship%5C%5CScholarshipApplicationPage.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=false!"]}