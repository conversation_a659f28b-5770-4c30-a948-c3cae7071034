'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { BookOpen, School, GraduationCap, Calendar, Users, DollarSign, ArrowRight, Filter, Search, AlertCircle } from 'lucide-react'
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { apiClient } from '@/lib/api'

interface Scholarship {
  id: number
  title: string
  slug: string
  category: 'primary' | 'secondary' | 'university'
  description: string
  eligibility_criteria?: string
  requirements?: string
  amount: number
  application_deadline: string
  is_open?: boolean
  status: string
  current_applicants: number
  max_applicants?: number
  is_featured?: boolean
  category_instructions?: {
    title: string
    age_range: string
    filled_by: string
    instruction: string
    required_info: string[]
  }
  custom_fields?: Array<{
    id: number
    field_name: string
    field_type: string
    is_required: boolean
    field_options?: string[]
    validation_rules?: any
  }>
  form_metadata?: {
    validation_rules: any
    field_constraints: any
    has_file_uploads: boolean
  }
}

const categoryConfig = {
  primary: {
    icon: BookOpen,
    title: "Primary School",
    color: "bg-blue-500",
    bgColor: "bg-blue-50",
    textColor: "text-blue-700",
    borderColor: "border-blue-200"
  },
  secondary: {
    icon: School,
    title: "Secondary School", 
    color: "bg-green-500",
    bgColor: "bg-green-50",
    textColor: "text-green-700",
    borderColor: "border-green-200"
  },
  university: {
    icon: GraduationCap,
    title: "University",
    color: "bg-purple-500",
    bgColor: "bg-purple-50",
    textColor: "text-purple-700",
    borderColor: "border-purple-200"
  }
}

export default function ScholarshipsPage() {
  const [scholarships, setScholarships] = useState<Scholarship[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedCategory, setSelectedCategory] = useState<string>('all')
  const [selectedStatus, setSelectedStatus] = useState<string>('open')

  useEffect(() => {
    fetchScholarships()
  }, [selectedCategory, searchTerm])

  const fetchScholarships = async () => {
    try {
      setLoading(true)
      setError(null)

      const params: any = {}
      if (selectedCategory !== 'all') {
        params.category = selectedCategory
      }
      if (searchTerm.trim()) {
        params.search = searchTerm.trim()
      }

      const response = await apiClient.getPublicScholarships(params)

      if (response.success && response.data) {
        // Handle both paginated and direct array responses
        const scholarshipsData = Array.isArray(response.data)
          ? response.data
          : response.data.data || []
        setScholarships(scholarshipsData)
      } else {
        throw new Error(response.message || 'Failed to fetch scholarships')
      }
    } catch (error) {
      console.error('Error fetching scholarships:', error)
      setError('Failed to load scholarships. Please try again later.')
      setScholarships([])
    } finally {
      setLoading(false)
    }
  }

  const filteredScholarships = scholarships.filter(scholarship => {
    const matchesStatus = selectedStatus === 'all' ||
                         (selectedStatus === 'open' && (scholarship.status === 'active' || scholarship.status === 'open')) ||
                         (selectedStatus === 'closed' && (scholarship.status === 'closed' || scholarship.status === 'inactive'))

    return matchesStatus
  })

  const formatDeadline = (deadline: string) => {
    return new Date(deadline).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  }

  const getStatusBadge = (scholarship: Scholarship) => {
    if (scholarship.status === 'closed' || scholarship.status === 'inactive') {
      return <Badge variant="secondary">Closed</Badge>
    }

    if (scholarship.max_applicants) {
      const spotsLeft = scholarship.max_applicants - scholarship.current_applicants
      if (spotsLeft <= 5) {
        return <Badge variant="destructive">Few Spots Left</Badge>
      }
    }

    if (scholarship.is_featured) {
      return <Badge variant="default" className="bg-amber-500">Featured</Badge>
    }

    return <Badge variant="default" className="bg-green-500">Open</Badge>
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 py-8">
        <div className="container mx-auto px-4">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-600 mx-auto"></div>
            <p className="mt-4 text-gray-600">Loading scholarships...</p>
          </div>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 py-8">
        <div className="container mx-auto px-4">
          <div className="text-center">
            <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
            <h3 className="text-xl font-semibold text-gray-900 mb-2">Error Loading Scholarships</h3>
            <p className="text-gray-600 mb-6">{error}</p>
            <Button onClick={fetchScholarships} variant="outline">
              Try Again
            </Button>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* Hero Section */}
      <section className="relative py-20 bg-gradient-to-br from-green-600 to-green-800 text-white">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="max-w-4xl mx-auto text-center space-y-6">
            <Badge className="bg-green-700 hover:bg-green-600 text-white rounded-full px-4 py-2">Scholarships</Badge>
            <h1 className="text-4xl font-bold tracking-tight sm:text-5xl md:text-6xl">
              Educational Scholarships
            </h1>
            <p className="text-xl text-green-100 max-w-3xl mx-auto">
              Discover scholarship opportunities designed to support students at every educational level - from primary school through university.
            </p>
            <Link href="/scholarship-application">
              <Button size="lg" className="bg-amber-500 hover:bg-amber-600 text-green-950 font-semibold">
                Apply Now
                <ArrowRight className="ml-2 h-5 w-5" />
              </Button>
            </Link>
          </div>
        </div>
      </section>

      {/* Filters Section */}
      <section className="py-12 bg-white dark:bg-gray-900">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex flex-col md:flex-row gap-4 items-center justify-between mb-8">
            <div>
              <h2 className="text-3xl font-bold tracking-tight mb-2">Available Scholarships</h2>
              <p className="text-gray-600 dark:text-gray-400">
                Find the perfect scholarship opportunity for your educational journey
              </p>
            </div>
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="Search scholarships..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 w-64"
                />
              </div>
              <Select value={selectedCategory} onValueChange={setSelectedCategory}>
                <SelectTrigger className="w-48">
                  <SelectValue placeholder="All Categories" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Categories</SelectItem>
                  <SelectItem value="primary">Primary School</SelectItem>
                  <SelectItem value="secondary">Secondary School</SelectItem>
                  <SelectItem value="university">University</SelectItem>
                </SelectContent>
              </Select>
              <Select value={selectedStatus} onValueChange={setSelectedStatus}>
                <SelectTrigger className="w-32">
                  <SelectValue placeholder="Status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Status</SelectItem>
                  <SelectItem value="open">Open</SelectItem>
                  <SelectItem value="closed">Closed</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Scholarships Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredScholarships.map((scholarship) => {
              const config = categoryConfig[scholarship.category]
              const IconComponent = config.icon
              const spotsLeft = scholarship.max_applicants ? scholarship.max_applicants - scholarship.current_applicants : null
              const isOpen = scholarship.status === 'active' || scholarship.status === 'open'

              return (
                <Card key={scholarship.id} className="overflow-hidden hover:shadow-lg transition-shadow duration-300">
                  <CardHeader className="pb-4">
                    <div className="flex items-start justify-between">
                      {getStatusBadge(scholarship)}
                      <Badge variant="outline" className="rounded-full">
                        {config.title}
                      </Badge>
                    </div>
                    <CardTitle className="text-xl text-green-800 dark:text-green-200">{scholarship.title}</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <p className="text-gray-600 dark:text-gray-400 text-sm line-clamp-3">{scholarship.description}</p>

                    <div className="space-y-2">
                      <div className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400">
                        <DollarSign className="h-4 w-4 text-green-600" />
                        <span className="font-semibold text-green-600">
                          ₦{scholarship.amount.toLocaleString()}
                        </span>
                      </div>
                      <div className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400">
                        <Calendar className="h-4 w-4 text-green-600" />
                        Deadline: {formatDeadline(scholarship.application_deadline)}
                      </div>
                      {spotsLeft !== null && (
                        <div className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400">
                          <Users className="h-4 w-4 text-green-600" />
                          {scholarship.current_applicants}/{scholarship.max_applicants} applicants
                        </div>
                      )}
                      {scholarship.category_instructions && (
                        <div className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400">
                          <IconComponent className="h-4 w-4 text-green-600" />
                          <span className="text-xs">
                            {scholarship.category_instructions.instruction}
                          </span>
                        </div>
                      )}
                      {(scholarship.eligibility_criteria || scholarship.requirements) && (
                        <div className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400">
                          <IconComponent className="h-4 w-4 text-green-600" />
                          {scholarship.eligibility_criteria || scholarship.requirements}
                        </div>
                      )}
                    </div>

                    <Link href={`/scholarships/${scholarship.id}`} className="block">
                      <Button
                        className="w-full bg-green-600 hover:bg-green-700 text-white"
                        disabled={!isOpen || (spotsLeft !== null && spotsLeft <= 0)}
                      >
                        {!isOpen ? 'Closed' : (spotsLeft !== null && spotsLeft <= 0) ? 'Full' : 'View Details'}
                        {isOpen && (spotsLeft === null || spotsLeft > 0) && <ArrowRight className="ml-2 h-4 w-4" />}
                      </Button>
                    </Link>
                  </CardContent>
                </Card>
              )
            })}
          </div>
        </div>
      </section>

      {/* No Results */}
      {filteredScholarships.length === 0 && (
        <section className="py-20 bg-white dark:bg-gray-900">
          <div className="container mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center">
              <BookOpen className="h-16 w-16 text-gray-400 mx-auto mb-4" />
              <h3 className="text-xl font-semibold text-gray-900 mb-2">No scholarships found</h3>
              <p className="text-gray-600 mb-6">
                {searchTerm || selectedCategory !== 'all' || selectedStatus !== 'open'
                  ? 'Try adjusting your search criteria or filters.'
                  : 'No scholarships are currently available.'}
              </p>
              {(searchTerm || selectedCategory !== 'all' || selectedStatus !== 'open') && (
                <Button
                  variant="outline"
                  onClick={() => {
                    setSearchTerm('')
                    setSelectedCategory('all')
                    setSelectedStatus('open')
                  }}
                >
                  Clear Filters
                </Button>
              )}
            </div>
          </div>
        </section>
      )}

      {/* Call to Action */}
      <section className="py-20 bg-green-900 text-white">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="max-w-4xl mx-auto text-center space-y-8">
            <h2 className="text-3xl font-bold tracking-tight sm:text-4xl">Ready to Apply?</h2>
            <p className="text-green-100 text-lg">
              Take the first step towards your educational goals. Our scholarship application process is simple and straightforward.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link href="/scholarship-application">
                <Button size="lg" className="bg-amber-500 hover:bg-amber-600 text-green-950 font-semibold">
                  Start Application
                  <ArrowRight className="ml-2 h-5 w-5" />
                </Button>
              </Link>
              <Link href="/contact">
                <Button size="lg" variant="outline" className="border-2 border-green-400 text-green-400 hover:bg-green-400 hover:text-white font-semibold">
                  Need Help?
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </section>
    </div>
  )
}  