'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { BookOpen, School, GraduationCap, Calendar, Users, DollarSign, ArrowRight, Filter, Search } from 'lucide-react'
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"

interface Scholarship {
  id: number
  title: string
  category: 'primary' | 'secondary' | 'university'
  description: string
  eligibility_criteria: string
  amount: number
  application_deadline: string
  is_open: boolean
  status: string
  current_applicants: number
  max_applicants: number
}

const categoryConfig = {
  primary: {
    icon: BookOpen,
    title: "Primary School",
    color: "bg-blue-500",
    bgColor: "bg-blue-50",
    textColor: "text-blue-700",
    borderColor: "border-blue-200"
  },
  secondary: {
    icon: School,
    title: "Secondary School", 
    color: "bg-green-500",
    bgColor: "bg-green-50",
    textColor: "text-green-700",
    borderColor: "border-green-200"
  },
  university: {
    icon: GraduationCap,
    title: "University",
    color: "bg-purple-500",
    bgColor: "bg-purple-50",
    textColor: "text-purple-700",
    borderColor: "border-purple-200"
  }
}

export default function ScholarshipsPage() {
  const [scholarships, setScholarships] = useState<Scholarship[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedCategory, setSelectedCategory] = useState<string>('all')
  const [selectedStatus, setSelectedStatus] = useState<string>('open')

  useEffect(() => {
    fetchScholarships()
  }, [])

  const fetchScholarships = async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/scholarships/public')
      if (response.ok) {
        const data = await response.json()
        setScholarships(data.data || [])
      }
    } catch (error) {
      console.error('Error fetching scholarships:', error)
      // For demo purposes, use mock data
      setScholarships([
        {
          id: 1,
          title: "Excellence in Primary Education",
          category: "primary",
          description: "Supporting outstanding primary school students with financial assistance for their educational journey.",
          eligibility_criteria: "Students in Primary 1-6 with good academic performance and financial need",
          amount: 100000,
          application_deadline: "2024-12-31",
          is_open: true,
          status: "open",
          current_applicants: 45,
          max_applicants: 100
        },
        {
          id: 2,
          title: "Secondary School Achievement Award",
          category: "secondary",
          description: "Empowering secondary school students to achieve their academic goals through financial support.",
          eligibility_criteria: "Secondary school students with minimum 70% average and demonstrated need",
          amount: 200000,
          application_deadline: "2024-11-30",
          is_open: true,
          status: "open",
          current_applicants: 32,
          max_applicants: 50
        },
        {
          id: 3,
          title: "University Excellence Scholarship",
          category: "university",
          description: "Supporting university students in their pursuit of higher education and career development.",
          eligibility_criteria: "University students with valid matriculation number and good academic standing",
          amount: 350000,
          application_deadline: "2024-10-15",
          is_open: true,
          status: "open",
          current_applicants: 28,
          max_applicants: 30
        }
      ])
    } finally {
      setLoading(false)
    }
  }

  const filteredScholarships = scholarships.filter(scholarship => {
    const matchesSearch = scholarship.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         scholarship.description.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesCategory = selectedCategory === 'all' || scholarship.category === selectedCategory
    const matchesStatus = selectedStatus === 'all' || 
                         (selectedStatus === 'open' && scholarship.is_open) ||
                         (selectedStatus === 'closed' && !scholarship.is_open)
    
    return matchesSearch && matchesCategory && matchesStatus
  })

  const formatDeadline = (deadline: string) => {
    return new Date(deadline).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  }

  const getStatusBadge = (scholarship: Scholarship) => {
    if (!scholarship.is_open) {
      return <Badge variant="secondary">Closed</Badge>
    }
    
    const spotsLeft = scholarship.max_applicants - scholarship.current_applicants
    if (spotsLeft <= 5) {
      return <Badge variant="destructive">Few Spots Left</Badge>
    }
    
    return <Badge variant="default" className="bg-green-500">Open</Badge>
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 py-8">
        <div className="container mx-auto px-4">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
            <p className="mt-4 text-gray-600">Loading scholarships...</p>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50">
      {/* Hero Section */}
      <div className="bg-gradient-to-r from-blue-600 via-indigo-600 to-purple-600 text-white py-16">
        <div className="container mx-auto px-4 text-center">
          <h1 className="text-4xl md:text-6xl font-bold mb-6">
            Available Scholarships
          </h1>
          <p className="text-xl md:text-2xl mb-8 text-blue-100">
            Discover scholarship opportunities that match your educational level
          </p>
          <Link href="/scholarship-application">
            <Button size="lg" className="bg-white text-blue-600 hover:bg-blue-50 font-semibold px-8 py-4 text-lg">
              Apply Now
              <ArrowRight className="ml-2 h-5 w-5" />
            </Button>
          </Link>
        </div>
      </div>

      {/* Filters Section */}
      <div className="container mx-auto px-4 py-8">
        <div className="bg-white rounded-lg shadow-sm p-6 mb-8">
          <div className="flex flex-col md:flex-row gap-4 items-center">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="Search scholarships..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <div className="flex gap-4">
              <Select value={selectedCategory} onValueChange={setSelectedCategory}>
                <SelectTrigger className="w-48">
                  <SelectValue placeholder="All Categories" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Categories</SelectItem>
                  <SelectItem value="primary">Primary School</SelectItem>
                  <SelectItem value="secondary">Secondary School</SelectItem>
                  <SelectItem value="university">University</SelectItem>
                </SelectContent>
              </Select>
              <Select value={selectedStatus} onValueChange={setSelectedStatus}>
                <SelectTrigger className="w-32">
                  <SelectValue placeholder="Status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Status</SelectItem>
                  <SelectItem value="open">Open</SelectItem>
                  <SelectItem value="closed">Closed</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </div>

        {/* Scholarships Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredScholarships.map((scholarship) => {
            const config = categoryConfig[scholarship.category]
            const IconComponent = config.icon
            const spotsLeft = scholarship.max_applicants - scholarship.current_applicants

            return (
              <Card key={scholarship.id} className={`border-2 ${config.borderColor} hover:shadow-xl transition-all duration-300 hover:scale-105`}>
                <CardHeader className={`${config.bgColor}`}>
                  <div className="flex items-center justify-between mb-2">
                    <div className={`inline-flex p-2 rounded-full ${config.color} text-white`}>
                      <IconComponent className="h-5 w-5" />
                    </div>
                    {getStatusBadge(scholarship)}
                  </div>
                  <CardTitle className="text-lg font-bold text-gray-900">
                    {scholarship.title}
                  </CardTitle>
                  <CardDescription className="text-gray-600">
                    {config.title}
                  </CardDescription>
                </CardHeader>
                <CardContent className="p-6">
                  <p className="text-gray-700 mb-4 text-sm leading-relaxed">
                    {scholarship.description}
                  </p>

                  <div className="space-y-3 mb-6">
                    <div className="flex items-center text-sm text-gray-600">
                      <DollarSign className="h-4 w-4 mr-2 text-green-600" />
                      <span className="font-semibold text-green-600">
                        ₦{scholarship.amount.toLocaleString()}
                      </span>
                    </div>

                    <div className="flex items-center text-sm text-gray-600">
                      <Calendar className="h-4 w-4 mr-2 text-orange-600" />
                      <span>Deadline: {formatDeadline(scholarship.application_deadline)}</span>
                    </div>

                    <div className="flex items-center text-sm text-gray-600">
                      <Users className="h-4 w-4 mr-2 text-blue-600" />
                      <span>{scholarship.current_applicants}/{scholarship.max_applicants} applicants</span>
                    </div>
                  </div>

                  <div className="mb-4">
                    <h4 className="font-semibold text-sm text-gray-900 mb-2">Eligibility:</h4>
                    <p className="text-xs text-gray-600 leading-relaxed">
                      {scholarship.eligibility_criteria}
                    </p>
                  </div>

                  <div className="flex gap-2">
                    <Link href="/scholarship-application" className="flex-1">
                      <Button
                        className="w-full"
                        disabled={!scholarship.is_open || spotsLeft <= 0}
                      >
                        {!scholarship.is_open ? 'Closed' : spotsLeft <= 0 ? 'Full' : 'Apply Now'}
                        {scholarship.is_open && spotsLeft > 0 && <ArrowRight className="ml-2 h-4 w-4" />}
                      </Button>
                    </Link>
                  </div>
                </CardContent>
              </Card>
            )
          })}
        </div>

        {/* No Results */}
        {filteredScholarships.length === 0 && (
          <div className="text-center py-12">
            <BookOpen className="h-16 w-16 text-gray-400 mx-auto mb-4" />
            <h3 className="text-xl font-semibold text-gray-900 mb-2">No scholarships found</h3>
            <p className="text-gray-600 mb-6">
              {searchTerm || selectedCategory !== 'all' || selectedStatus !== 'open'
                ? 'Try adjusting your search criteria or filters.'
                : 'No scholarships are currently available.'}
            </p>
            {(searchTerm || selectedCategory !== 'all' || selectedStatus !== 'open') && (
              <Button
                variant="outline"
                onClick={() => {
                  setSearchTerm('')
                  setSelectedCategory('all')
                  setSelectedStatus('open')
                }}
              >
                Clear Filters
              </Button>
            )}
          </div>
        )}

        {/* Call to Action */}
        <div className="mt-16 text-center bg-white rounded-lg shadow-sm p-8">
          <h2 className="text-2xl font-bold text-gray-900 mb-4">Ready to Apply?</h2>
          <p className="text-gray-600 mb-6 max-w-2xl mx-auto">
            Take the first step towards your educational goals. Our scholarship application process is simple and straightforward.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link href="/scholarship-application">
              <Button size="lg" className="bg-blue-600 hover:bg-blue-700">
                Start Application
                <ArrowRight className="ml-2 h-5 w-5" />
              </Button>
            </Link>
            <Link href="/contact">
              <Button size="lg" variant="outline">
                Need Help?
              </Button>
            </Link>
          </div>
        </div>
      </div>
    </div>
  )
}
