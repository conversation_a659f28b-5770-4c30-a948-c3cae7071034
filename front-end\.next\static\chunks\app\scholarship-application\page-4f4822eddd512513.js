(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3094],{34964:(e,r,s)=>{"use strict";s.d(r,{Xi:()=>o,av:()=>c,j7:()=>i,tU:()=>d});var a=s(95155),t=s(12115),l=s(60704),n=s(53999);let d=l.bL,i=t.forwardRef((e,r)=>{let{className:s,...t}=e;return(0,a.jsx)(l.B8,{ref:r,className:(0,n.cn)("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",s),...t})});i.displayName=l.B8.displayName;let o=t.forwardRef((e,r)=>{let{className:s,...t}=e;return(0,a.jsx)(l.l9,{ref:r,className:(0,n.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",s),...t})});o.displayName=l.l9.displayName;let c=t.forwardRef((e,r)=>{let{className:s,...t}=e;return(0,a.jsx)(l.UC,{ref:r,className:(0,n.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",s),...t})});c.displayName=l.UC.displayName},48315:(e,r,s)=>{"use strict";s.d(r,{default:()=>P});var a=s(95155),t=s(12115),l=s(35695),n=s(97168),d=s(89852),i=s(82714),o=s(99474),c=s(95784),m=s(34964),u=s(5040),x=s(53896),h=s(87949),p=s(1243),g=s(71007),f=s(19420),b=s(4516),v=s(48136),j=s(81586),_=s(51154),N=s(40646),y=s(83744),w=s(28883),k=s(81497),S=s(53580),A=s(31886),C=s(88482),F=s(57434),J=s(57340);function E(e){let{onBackToForm:r,onGoHome:s}=e;return(0,a.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-green-50 via-blue-50 to-indigo-50 py-8 flex items-center justify-center",children:(0,a.jsx)("div",{className:"container mx-auto px-4 max-w-2xl",children:(0,a.jsxs)(C.Zp,{className:"shadow-xl border-0 overflow-hidden",children:[(0,a.jsxs)(C.aR,{className:"bg-gradient-to-r from-green-500 to-blue-500 text-white text-center py-12",children:[(0,a.jsx)("div",{className:"flex justify-center mb-4",children:(0,a.jsx)("div",{className:"p-4 bg-white bg-opacity-20 rounded-full",children:(0,a.jsx)(N.A,{className:"h-16 w-16 text-white"})})}),(0,a.jsx)(C.ZB,{className:"text-3xl font-bold mb-2",children:"Application Submitted Successfully!"}),(0,a.jsx)(C.BT,{className:"text-green-100 text-lg",children:"Your scholarship application has been received and is being processed"})]}),(0,a.jsx)(C.Wu,{className:"p-8 text-center",children:(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"bg-blue-50 p-6 rounded-lg border-l-4 border-blue-500",children:[(0,a.jsx)("h3",{className:"font-semibold text-blue-900 mb-2",children:"What happens next?"}),(0,a.jsxs)("ul",{className:"text-blue-800 text-left space-y-2",children:[(0,a.jsxs)("li",{className:"flex items-start",children:[(0,a.jsx)("span",{className:"inline-block w-2 h-2 bg-blue-500 rounded-full mt-2 mr-3 flex-shrink-0"}),"Your application will be reviewed by our scholarship committee"]}),(0,a.jsxs)("li",{className:"flex items-start",children:[(0,a.jsx)("span",{className:"inline-block w-2 h-2 bg-blue-500 rounded-full mt-2 mr-3 flex-shrink-0"}),"You will receive an email confirmation within 24 hours"]}),(0,a.jsxs)("li",{className:"flex items-start",children:[(0,a.jsx)("span",{className:"inline-block w-2 h-2 bg-blue-500 rounded-full mt-2 mr-3 flex-shrink-0"}),"The review process typically takes 5-10 business days"]}),(0,a.jsxs)("li",{className:"flex items-start",children:[(0,a.jsx)("span",{className:"inline-block w-2 h-2 bg-blue-500 rounded-full mt-2 mr-3 flex-shrink-0"}),"You will be notified of the decision via email and SMS"]})]})]}),(0,a.jsx)("div",{className:"bg-yellow-50 p-4 rounded-lg border-l-4 border-yellow-500",children:(0,a.jsxs)("p",{className:"text-yellow-800 font-medium",children:[(0,a.jsx)("strong",{children:"Important:"})," Please keep your phone and email accessible for any follow-up communications."]})}),(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center pt-4",children:[(0,a.jsxs)(n.$,{onClick:r,variant:"outline",className:"flex items-center justify-center px-6 py-3",children:[(0,a.jsx)(F.A,{className:"mr-2 h-5 w-5"}),"Submit Another Application"]}),(0,a.jsxs)(n.$,{onClick:s,className:"bg-blue-600 hover:bg-blue-700 text-white flex items-center justify-center px-6 py-3",children:[(0,a.jsx)(J.A,{className:"mr-2 h-5 w-5"}),"Go to Dashboard"]})]})]})})]})})})}function P(){let e=(0,l.useSearchParams)(),[r,s]=(0,t.useState)("primary"),[C,F]=(0,t.useState)(!1),[J,P]=(0,t.useState)(!1),[q,T]=(0,t.useState)(null),{toast:R}=(0,S.dj)();(0,t.useEffect)(()=>{let r=e.get("category"),a=e.get("scholarship_id");r&&["primary","secondary","university"].includes(r)&&s(r),a&&T(a)},[e]);let[I,D]=(0,t.useState)({student_full_name:"",age:"",current_class:"",father_name:"",mother_name:"",parent_phone:"",home_address:"",school_name:"",headmaster_name:"",school_account_number:"",reason_for_scholarship:"",current_school_fee:"",supporting_information:""}),[O,U]=(0,t.useState)({student_full_name:"",age:"",class:"",parent_phone:"",address:"",school_name:"",principal_name:"",principal_account_number:"",reason_for_scholarship:"",school_fee_amount:""}),[L,M]=(0,t.useState)({full_name:"",age:"",course_of_study:"",current_level:"",phone_number:"",email_address:"",matriculation_number:"",reason_for_scholarship:""}),[z,V]=(0,t.useState)({student_picture:null}),[B,$]=(0,t.useState)({student_picture:null}),[G,Y]=(0,t.useState)({student_id_card:null,payment_evidence:null,supporting_documents:[]}),H={primary:{icon:u.A,title:"Primary School Scholarship",description:"For Primary 1-6 students (Filled by Parent/Guardian)",color:"bg-blue-500",bgColor:"bg-blue-50",textColor:"text-blue-700",borderColor:"border-blue-200"},secondary:{icon:x.A,title:"Secondary School Scholarship",description:"For Secondary school students (Filled by Student)",color:"bg-green-500",bgColor:"bg-green-50",textColor:"text-green-700",borderColor:"border-green-200"},university:{icon:h.A,title:"University Scholarship",description:"For University students (Filled by Student)",color:"bg-purple-500",bgColor:"bg-purple-50",textColor:"text-purple-700",borderColor:"border-purple-200"}},W=(e,r,s)=>{"primary"===e?V(e=>({...e,[r]:s})):"secondary"===e?$(e=>({...e,[r]:s})):"university"===e&&("supporting_documents"===r&&s?Y(e=>({...e,supporting_documents:[...e.supporting_documents,s]})):Y(e=>({...e,[r]:s})))},X=e=>{if(e&&"university"===r){let r=Array.from(e);Y(e=>({...e,supporting_documents:[...e.supporting_documents,...r]}))}},Z=e=>{Y(r=>({...r,supporting_documents:r.supporting_documents.filter((r,s)=>s!==e)}))},Q=()=>"primary"===r?["student_full_name","age","current_class","father_name","mother_name","parent_phone","home_address","school_name","headmaster_name","school_account_number","reason_for_scholarship","current_school_fee"].every(e=>""!==I[e].trim())&&z.student_picture:"secondary"===r?["student_full_name","age","class","parent_phone","address","school_name","principal_name","principal_account_number","reason_for_scholarship","school_fee_amount"].every(e=>""!==O[e].trim())&&B.student_picture:"university"===r&&["full_name","age","course_of_study","current_level","phone_number","email_address","matriculation_number","reason_for_scholarship"].every(e=>""!==L[e].trim())&&G.student_id_card&&G.payment_evidence,K=async e=>{if(e.preventDefault(),!Q()){R({title:"Validation Error",description:"Please fill in all required fields and upload required documents",variant:"destructive"});return}try{F(!0);let e=new FormData;if(e.append("category",r),"primary"===r?(e.append("form_data",JSON.stringify(I)),z.student_picture&&e.append("student_picture",z.student_picture)):"secondary"===r?(e.append("form_data",JSON.stringify(O)),B.student_picture&&e.append("student_picture",B.student_picture)):"university"===r&&(e.append("form_data",JSON.stringify(L)),G.student_id_card&&e.append("student_id_card",G.student_id_card),G.payment_evidence&&e.append("payment_evidence",G.payment_evidence),G.supporting_documents.forEach((r,s)=>{e.append("supporting_documents[".concat(s,"]"),r)})),!q){R({title:"Error",description:"Scholarship ID is required for application submission.",variant:"destructive"});return}let s=await A.uE.submitScholarshipApplication(q,e);s.success?(P(!0),"primary"===r?(D({student_full_name:"",age:"",current_class:"",father_name:"",mother_name:"",parent_phone:"",home_address:"",school_name:"",headmaster_name:"",school_account_number:"",reason_for_scholarship:"",current_school_fee:"",supporting_information:""}),V({student_picture:null})):"secondary"===r?(U({student_full_name:"",age:"",class:"",parent_phone:"",address:"",school_name:"",principal_name:"",principal_account_number:"",reason_for_scholarship:"",school_fee_amount:""}),$({student_picture:null})):"university"===r&&(M({full_name:"",age:"",course_of_study:"",current_level:"",phone_number:"",email_address:"",matriculation_number:"",reason_for_scholarship:""}),Y({student_id_card:null,payment_evidence:null,supporting_documents:[]}))):R({title:"Error",description:s.message||"Failed to submit application",variant:"destructive"})}catch(e){var s,a;console.error("Error submitting application:",e),R({title:"Error",description:(null===(a=e.response)||void 0===a?void 0:null===(s=a.data)||void 0===s?void 0:s.message)||"Failed to submit application",variant:"destructive"})}finally{F(!1)}};return J?(0,a.jsx)(E,{onBackToForm:()=>{P(!1)},onGoHome:()=>{window.location.href="/dashboard"}}):(0,a.jsxs)("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900",children:[(0,a.jsx)("section",{className:"relative py-12 sm:py-16 md:py-20 bg-gradient-to-br from-green-600 to-green-800 text-white",children:(0,a.jsx)("div",{className:"container mx-auto px-3 sm:px-4 md:px-6 lg:px-8",children:(0,a.jsxs)("div",{className:"max-w-4xl mx-auto text-center space-y-4 sm:space-y-6",children:[(0,a.jsx)("h1",{className:"text-2xl sm:text-3xl md:text-4xl lg:text-5xl xl:text-6xl font-bold tracking-tight leading-tight",children:"Scholarship Application"}),(0,a.jsx)("p",{className:"text-base sm:text-lg md:text-xl text-green-100 max-w-3xl mx-auto px-2",children:"Choose your scholarship category and complete the application form"})]})})}),(0,a.jsx)("section",{className:"py-6 sm:py-8 md:py-12 bg-white dark:bg-gray-900",children:(0,a.jsx)("div",{className:"container mx-auto px-3 sm:px-4 md:px-6 lg:px-8",children:(0,a.jsxs)("div",{className:"max-w-4xl mx-auto",children:[(0,a.jsxs)("div",{className:"mb-4 sm:mb-6 md:mb-8",children:[(0,a.jsx)("h2",{className:"text-xl sm:text-2xl md:text-3xl font-bold tracking-tight mb-2 text-center sm:text-left",children:"Apply for Scholarship"}),(0,a.jsx)("p",{className:"text-sm sm:text-base text-gray-600 dark:text-gray-400 text-center sm:text-left",children:"Select your education level and fill out the appropriate form"})]}),(0,a.jsxs)(m.tU,{value:r,onValueChange:e=>s(e),className:"w-full",children:[(0,a.jsx)(m.j7,{className:"grid w-full grid-cols-1 sm:grid-cols-3 mb-4 sm:mb-6 h-auto p-1 gap-1 sm:gap-2 bg-gray-100 rounded-lg",children:Object.entries(H).map(e=>{let[r,s]=e,t=s.icon;return(0,a.jsxs)(m.Xi,{value:r,className:"flex flex-col sm:flex-col items-center p-2 sm:p-3 space-y-1 sm:space-y-2 rounded-md transition-all duration-300 data-[state=active]:bg-white data-[state=active]:shadow-md min-h-[60px] sm:min-h-[80px]",children:[(0,a.jsx)("div",{className:"p-1.5 sm:p-2 rounded-full bg-green-600 text-white shadow-sm",children:(0,a.jsx)(t,{className:"h-3 w-3 sm:h-4 sm:w-4"})}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"font-medium text-xs sm:text-xs text-gray-900 leading-tight",children:s.title}),(0,a.jsx)("div",{className:"text-xs sm:text-xs text-gray-600 mt-0.5 hidden sm:block",children:s.description})]})]},r)})}),(0,a.jsx)(m.av,{value:"primary",className:"space-y-4 sm:space-y-6 md:space-y-8",children:(0,a.jsxs)("form",{onSubmit:K,className:"space-y-4 sm:space-y-6 md:space-y-8",children:[(0,a.jsx)("div",{className:"bg-green-50 p-3 sm:p-4 md:p-6 rounded-lg border border-green-200",children:(0,a.jsxs)("div",{className:"flex items-start sm:items-center",children:[(0,a.jsx)(p.A,{className:"h-4 w-4 sm:h-5 sm:w-5 text-green-600 mr-2 sm:mr-3 mt-0.5 sm:mt-0 flex-shrink-0"}),(0,a.jsx)("p",{className:"text-green-800 font-medium text-sm sm:text-base",children:"This form should be filled by a Parent or Guardian on behalf of the student"})]})}),(0,a.jsxs)("div",{className:"space-y-4 sm:space-y-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)("h3",{className:"text-base sm:text-lg font-semibold text-green-800 dark:text-green-200 mb-3 sm:mb-4 flex items-center",children:[(0,a.jsx)(g.A,{className:"h-4 w-4 sm:h-5 sm:w-5 mr-2 text-green-600"}),"Student Information"]}),(0,a.jsxs)("div",{className:"space-y-3 sm:space-y-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(i.J,{htmlFor:"student_full_name",className:"text-green-800 dark:text-green-200 text-sm sm:text-base",children:"Student Full Name *"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(g.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-green-500"}),(0,a.jsx)(d.p,{id:"student_full_name",value:I.student_full_name,onChange:e=>D(r=>({...r,student_full_name:e.target.value})),placeholder:"Enter student's full name",className:"pl-10 rounded-xl border-green-200 dark:border-green-700 focus:border-green-500 text-sm sm:text-base",required:!0})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(i.J,{htmlFor:"age",className:"text-green-800 dark:text-green-200 text-sm sm:text-base",children:"Age *"}),(0,a.jsx)(d.p,{id:"age",type:"number",value:I.age,onChange:e=>D(r=>({...r,age:e.target.value})),placeholder:"Age",className:"rounded-xl border-green-200 dark:border-green-700 focus:border-green-500 text-sm sm:text-base",required:!0})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(i.J,{htmlFor:"current_class",className:"text-green-800 dark:text-green-200 text-sm sm:text-base",children:"Current Class *"}),(0,a.jsxs)(c.l6,{value:I.current_class,onValueChange:e=>D(r=>({...r,current_class:e})),children:[(0,a.jsx)(c.bq,{className:"rounded-xl border-green-200 dark:border-green-700 focus:border-green-500 text-sm sm:text-base",children:(0,a.jsx)(c.yv,{placeholder:"Select class"})}),(0,a.jsxs)(c.gC,{children:[(0,a.jsx)(c.eb,{value:"Primary 1",children:"Primary 1"}),(0,a.jsx)(c.eb,{value:"Primary 2",children:"Primary 2"}),(0,a.jsx)(c.eb,{value:"Primary 3",children:"Primary 3"}),(0,a.jsx)(c.eb,{value:"Primary 4",children:"Primary 4"}),(0,a.jsx)(c.eb,{value:"Primary 5",children:"Primary 5"}),(0,a.jsx)(c.eb,{value:"Primary 6",children:"Primary 6"})]})]})]})]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("h3",{className:"text-lg font-semibold text-green-800 dark:text-green-200 mb-4 flex items-center",children:[(0,a.jsx)(g.A,{className:"h-5 w-5 mr-2 text-green-600"}),"Parent/Guardian Information"]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(i.J,{htmlFor:"father_name",className:"text-green-800 dark:text-green-200",children:"Father's Name *"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(g.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-green-500"}),(0,a.jsx)(d.p,{id:"father_name",value:I.father_name,onChange:e=>D(r=>({...r,father_name:e.target.value})),placeholder:"Enter father's name",className:"pl-10 rounded-xl border-green-200 dark:border-green-700 focus:border-green-500",required:!0})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(i.J,{htmlFor:"mother_name",className:"text-green-800 dark:text-green-200",children:"Mother's Name *"}),(0,a.jsx)(d.p,{id:"mother_name",value:I.mother_name,onChange:e=>D(r=>({...r,mother_name:e.target.value})),placeholder:"Enter mother's name",className:"rounded-xl border-green-200 dark:border-green-700 focus:border-green-500",required:!0})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(i.J,{htmlFor:"parent_phone",className:"text-green-800 dark:text-green-200",children:"Father's or Mother's Phone Number *"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(f.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-green-500"}),(0,a.jsx)(d.p,{id:"parent_phone",type:"tel",value:I.parent_phone,onChange:e=>D(r=>({...r,parent_phone:e.target.value})),placeholder:"Enter phone number",className:"pl-10 rounded-xl border-green-200 dark:border-green-700 focus:border-green-500",required:!0})]})]})]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("h3",{className:"text-lg font-semibold text-green-800 dark:text-green-200 mb-4 flex items-center",children:[(0,a.jsx)(b.A,{className:"h-5 w-5 mr-2 text-green-600"}),"Address Information"]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(i.J,{htmlFor:"home_address",className:"text-green-800 dark:text-green-200",children:"Home Address *"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(b.A,{className:"absolute left-3 top-3 h-4 w-4 text-green-500"}),(0,a.jsx)(o.T,{id:"home_address",value:I.home_address,onChange:e=>D(r=>({...r,home_address:e.target.value})),placeholder:"Enter complete home address",className:"pl-10 rounded-xl border-green-200 dark:border-green-700 focus:border-green-500",rows:3,required:!0})]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("h3",{className:"text-lg font-semibold text-green-800 dark:text-green-200 mb-4 flex items-center",children:[(0,a.jsx)(v.A,{className:"h-5 w-5 mr-2 text-green-600"}),"School Information"]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(i.J,{htmlFor:"school_name",className:"text-green-800 dark:text-green-200",children:"Name of the School *"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(v.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-green-500"}),(0,a.jsx)(d.p,{id:"school_name",value:I.school_name,onChange:e=>D(r=>({...r,school_name:e.target.value})),placeholder:"Enter school name",className:"pl-10 rounded-xl border-green-200 dark:border-green-700 focus:border-green-500",required:!0})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(i.J,{htmlFor:"headmaster_name",className:"text-green-800 dark:text-green-200",children:"Name of Headmaster/Director *"}),(0,a.jsx)(d.p,{id:"headmaster_name",value:I.headmaster_name,onChange:e=>D(r=>({...r,headmaster_name:e.target.value})),placeholder:"Enter headmaster's name",className:"rounded-xl border-green-200 dark:border-green-700 focus:border-green-500",required:!0})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(i.J,{htmlFor:"school_account_number",className:"text-green-800 dark:text-green-200",children:"School Account Number *"}),(0,a.jsx)(d.p,{id:"school_account_number",value:I.school_account_number,onChange:e=>D(r=>({...r,school_account_number:e.target.value})),placeholder:"Enter account number for scholarship payment",className:"rounded-xl border-green-200 dark:border-green-700 focus:border-green-500",required:!0})]})]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("h3",{className:"text-lg font-semibold text-green-800 dark:text-green-200 mb-4 flex items-center",children:[(0,a.jsx)(j.A,{className:"h-5 w-5 mr-2 text-green-600"}),"Financial & Additional Information"]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(i.J,{htmlFor:"current_school_fee",className:"text-green-800 dark:text-green-200",children:"Current School Fee Amount *"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(j.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-green-500"}),(0,a.jsx)(d.p,{id:"current_school_fee",type:"number",value:I.current_school_fee,onChange:e=>D(r=>({...r,current_school_fee:e.target.value})),placeholder:"Enter amount in Naira",className:"pl-10 rounded-xl border-green-200 dark:border-green-700 focus:border-green-500",required:!0})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(i.J,{htmlFor:"student_picture",className:"text-green-800 dark:text-green-200",children:"Upload Student Picture *"}),(0,a.jsx)(d.p,{id:"student_picture",type:"file",accept:"image/*",onChange:e=>{var r;return W("primary","student_picture",(null===(r=e.target.files)||void 0===r?void 0:r[0])||null)},className:"rounded-xl border-green-200 dark:border-green-700 focus:border-green-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-green-50 file:text-green-700 hover:file:bg-green-100",required:!0})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(i.J,{htmlFor:"reason_for_scholarship",className:"text-green-800 dark:text-green-200",children:"Reason for the Scholarship *"}),(0,a.jsx)(o.T,{id:"reason_for_scholarship",value:I.reason_for_scholarship,onChange:e=>D(r=>({...r,reason_for_scholarship:e.target.value})),placeholder:"Please explain why your child needs this scholarship",className:"rounded-xl border-green-200 dark:border-green-700 focus:border-green-500",rows:4,required:!0})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(i.J,{htmlFor:"supporting_information",className:"text-green-800 dark:text-green-200",children:"Any Other Supporting Information"}),(0,a.jsx)(o.T,{id:"supporting_information",value:I.supporting_information,onChange:e=>D(r=>({...r,supporting_information:e.target.value})),placeholder:"Any additional information that supports your application",className:"rounded-xl border-green-200 dark:border-green-700 focus:border-green-500",rows:3})]})]})]}),(0,a.jsx)("div",{className:"flex justify-center pt-6 sm:pt-8",children:(0,a.jsx)(n.$,{type:"submit",disabled:C||!Q(),size:"lg",className:"w-full sm:w-auto bg-green-600 hover:bg-green-700 text-white px-6 sm:px-12 py-3 sm:py-4 text-base sm:text-lg font-semibold shadow-lg hover:shadow-xl transition-all duration-300",children:C?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(_.A,{className:"mr-2 sm:mr-3 h-5 w-5 sm:h-6 sm:w-6 animate-spin"}),(0,a.jsx)("span",{className:"text-sm sm:text-base",children:"Submitting..."})]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(N.A,{className:"mr-2 sm:mr-3 h-5 w-5 sm:h-6 sm:w-6"}),(0,a.jsx)("span",{className:"text-sm sm:text-base",children:"Submit Primary School Application"})]})})})]})}),(0,a.jsx)(m.av,{value:"secondary",className:"space-y-4 sm:space-y-6 md:space-y-8",children:(0,a.jsxs)("form",{onSubmit:K,className:"space-y-4 sm:space-y-6 md:space-y-8",children:[(0,a.jsx)("div",{className:"bg-green-50 p-3 sm:p-4 md:p-6 rounded-lg border border-green-200",children:(0,a.jsxs)("div",{className:"flex items-start sm:items-center",children:[(0,a.jsx)(p.A,{className:"h-4 w-4 sm:h-5 sm:w-5 text-green-600 mr-2 sm:mr-3 mt-0.5 sm:mt-0 flex-shrink-0"}),(0,a.jsx)("p",{className:"text-green-800 font-medium text-sm sm:text-base",children:"This form should be filled by the Student directly"})]})}),(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)("h3",{className:"text-lg font-semibold text-green-800 dark:text-green-200 mb-4 flex items-center",children:[(0,a.jsx)(g.A,{className:"h-5 w-5 mr-2 text-green-600"}),"Student Information"]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(i.J,{htmlFor:"sec_student_full_name",className:"text-green-800 dark:text-green-200",children:"Student Full Name *"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(g.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-green-500"}),(0,a.jsx)(d.p,{id:"sec_student_full_name",value:O.student_full_name,onChange:e=>U(r=>({...r,student_full_name:e.target.value})),placeholder:"Enter your full name",className:"pl-10 rounded-xl border-green-200 dark:border-green-700 focus:border-green-500",required:!0})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 sm:gap-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(i.J,{htmlFor:"sec_age",className:"text-green-800 dark:text-green-200",children:"Age *"}),(0,a.jsx)(d.p,{id:"sec_age",type:"number",value:O.age,onChange:e=>U(r=>({...r,age:e.target.value})),placeholder:"Your age",className:"rounded-xl border-green-200 dark:border-green-700 focus:border-green-500",required:!0})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(i.J,{htmlFor:"sec_class",className:"text-green-800 dark:text-green-200",children:"Class *"}),(0,a.jsxs)(c.l6,{value:O.class,onValueChange:e=>U(r=>({...r,class:e})),children:[(0,a.jsx)(c.bq,{className:"rounded-xl border-green-200 dark:border-green-700 focus:border-green-500",children:(0,a.jsx)(c.yv,{placeholder:"Select class"})}),(0,a.jsxs)(c.gC,{children:[(0,a.jsx)(c.eb,{value:"JSS 1",children:"JSS 1"}),(0,a.jsx)(c.eb,{value:"JSS 2",children:"JSS 2"}),(0,a.jsx)(c.eb,{value:"JSS 3",children:"JSS 3"}),(0,a.jsx)(c.eb,{value:"SS 1",children:"SS 1"}),(0,a.jsx)(c.eb,{value:"SS 2",children:"SS 2"}),(0,a.jsx)(c.eb,{value:"SS 3",children:"SS 3"})]})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(i.J,{htmlFor:"sec_parent_phone",className:"text-green-800 dark:text-green-200",children:"Parent's Phone Number *"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(f.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-green-500"}),(0,a.jsx)(d.p,{id:"sec_parent_phone",type:"tel",value:O.parent_phone,onChange:e=>U(r=>({...r,parent_phone:e.target.value})),placeholder:"Enter parent's phone number",className:"pl-10 rounded-xl border-green-200 dark:border-green-700 focus:border-green-500",required:!0})]})]})]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("h3",{className:"text-lg font-semibold text-green-800 dark:text-green-200 mb-4 flex items-center",children:[(0,a.jsx)(b.A,{className:"h-5 w-5 mr-2 text-green-600"}),"Address & School Information"]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(i.J,{htmlFor:"sec_address",className:"text-green-800 dark:text-green-200",children:"Address *"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(b.A,{className:"absolute left-3 top-3 h-4 w-4 text-green-500"}),(0,a.jsx)(o.T,{id:"sec_address",value:O.address,onChange:e=>U(r=>({...r,address:e.target.value})),placeholder:"Enter your complete address",className:"pl-10 rounded-xl border-green-200 dark:border-green-700 focus:border-green-500",rows:3,required:!0})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(i.J,{htmlFor:"sec_school_name",className:"text-green-800 dark:text-green-200",children:"School Name *"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(v.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-green-500"}),(0,a.jsx)(d.p,{id:"sec_school_name",value:O.school_name,onChange:e=>U(r=>({...r,school_name:e.target.value})),placeholder:"Enter your school name",className:"pl-10 rounded-xl border-green-200 dark:border-green-700 focus:border-green-500",required:!0})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(i.J,{htmlFor:"sec_principal_name",className:"text-green-800 dark:text-green-200",children:"School Principal or Director Name *"}),(0,a.jsx)(d.p,{id:"sec_principal_name",value:O.principal_name,onChange:e=>U(r=>({...r,principal_name:e.target.value})),placeholder:"Enter principal's name",className:"rounded-xl border-green-200 dark:border-green-700 focus:border-green-500",required:!0})]})]})]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("h3",{className:"text-lg font-semibold text-green-800 dark:text-green-200 mb-4 flex items-center",children:[(0,a.jsx)(j.A,{className:"h-5 w-5 mr-2 text-green-600"}),"Financial Information"]}),(0,a.jsx)("div",{className:"space-y-4",children:(0,a.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(i.J,{htmlFor:"sec_principal_account_number",className:"text-green-800 dark:text-green-200",children:"Principal or Financial Officer Account Number *"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(j.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-green-500"}),(0,a.jsx)(d.p,{id:"sec_principal_account_number",value:O.principal_account_number,onChange:e=>U(r=>({...r,principal_account_number:e.target.value})),placeholder:"Enter account number for scholarship payment",className:"pl-10 rounded-xl border-green-200 dark:border-green-700 focus:border-green-500",required:!0})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(i.J,{htmlFor:"sec_school_fee_amount",className:"text-green-800 dark:text-green-200",children:"School Fee Amount *"}),(0,a.jsx)(d.p,{id:"sec_school_fee_amount",type:"number",value:O.school_fee_amount,onChange:e=>U(r=>({...r,school_fee_amount:e.target.value})),placeholder:"Enter amount in Naira",className:"rounded-xl border-green-200 dark:border-green-700 focus:border-green-500",required:!0})]})]})})]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("h3",{className:"text-lg font-semibold text-green-800 dark:text-green-200 mb-4 flex items-center",children:[(0,a.jsx)(y.A,{className:"h-5 w-5 mr-2 text-green-600"}),"Picture & Application Reason"]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(i.J,{htmlFor:"sec_student_picture",className:"text-green-800 dark:text-green-200",children:"Upload Student Picture *"}),(0,a.jsx)(d.p,{id:"sec_student_picture",type:"file",accept:"image/*",onChange:e=>{var r;return W("secondary","student_picture",(null===(r=e.target.files)||void 0===r?void 0:r[0])||null)},className:"rounded-xl border-green-200 dark:border-green-700 focus:border-green-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-green-50 file:text-green-700 hover:file:bg-green-100",required:!0})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(i.J,{htmlFor:"sec_reason_for_scholarship",className:"text-green-800 dark:text-green-200",children:"Reason for the Scholarship *"}),(0,a.jsx)(o.T,{id:"sec_reason_for_scholarship",value:O.reason_for_scholarship,onChange:e=>U(r=>({...r,reason_for_scholarship:e.target.value})),placeholder:"Please explain why you need this scholarship",className:"rounded-xl border-green-200 dark:border-green-700 focus:border-green-500",rows:4,required:!0})]})]})]}),(0,a.jsx)("div",{className:"flex justify-center pt-6 sm:pt-8",children:(0,a.jsx)(n.$,{type:"submit",disabled:C||!Q(),size:"lg",className:"w-full sm:w-auto bg-gradient-to-r from-green-600 via-emerald-600 to-teal-600 hover:from-green-700 hover:via-emerald-700 hover:to-teal-700 text-white px-6 sm:px-12 py-3 sm:py-4 text-base sm:text-lg font-bold shadow-lg hover:shadow-xl transition-all duration-300",children:C?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(_.A,{className:"mr-2 sm:mr-3 h-5 w-5 sm:h-6 sm:w-6 animate-spin"}),(0,a.jsx)("span",{className:"text-sm sm:text-base",children:"Submitting..."})]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(N.A,{className:"mr-2 sm:mr-3 h-5 w-5 sm:h-6 sm:w-6"}),(0,a.jsx)("span",{className:"text-sm sm:text-base",children:"Submit Secondary School Application"})]})})})]})}),(0,a.jsx)(m.av,{value:"university",className:"space-y-4 sm:space-y-6 md:space-y-8",children:(0,a.jsxs)("form",{onSubmit:K,className:"space-y-4 sm:space-y-6 md:space-y-8",children:[(0,a.jsx)("div",{className:"bg-green-50 p-3 sm:p-4 md:p-6 rounded-lg border border-green-200",children:(0,a.jsxs)("div",{className:"flex items-start sm:items-center",children:[(0,a.jsx)(p.A,{className:"h-4 w-4 sm:h-5 sm:w-5 text-green-600 mr-2 sm:mr-3 mt-0.5 sm:mt-0 flex-shrink-0"}),(0,a.jsx)("p",{className:"text-green-800 font-medium text-sm sm:text-base",children:"This form should be filled by the University Student directly"})]})}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("h3",{className:"text-lg font-semibold text-green-800 dark:text-green-200 mb-4 flex items-center",children:[(0,a.jsx)(g.A,{className:"h-5 w-5 mr-2 text-green-600"}),"Personal Information"]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(i.J,{htmlFor:"uni_full_name",className:"text-green-800 dark:text-green-200",children:"Full Name *"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(g.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-green-500"}),(0,a.jsx)(d.p,{id:"uni_full_name",value:L.full_name,onChange:e=>M(r=>({...r,full_name:e.target.value})),placeholder:"Enter your full name",className:"pl-10 rounded-xl border-green-200 dark:border-green-700 focus:border-green-500",required:!0})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(i.J,{htmlFor:"uni_age",className:"text-green-800 dark:text-green-200",children:"Age *"}),(0,a.jsx)(d.p,{id:"uni_age",type:"number",value:L.age,onChange:e=>M(r=>({...r,age:e.target.value})),placeholder:"Your age",className:"rounded-xl border-green-200 dark:border-green-700 focus:border-green-500",required:!0})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(i.J,{htmlFor:"uni_phone_number",className:"text-green-800 dark:text-green-200",children:"Phone Number *"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(f.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-green-500"}),(0,a.jsx)(d.p,{id:"uni_phone_number",type:"tel",value:L.phone_number,onChange:e=>M(r=>({...r,phone_number:e.target.value})),placeholder:"Enter your phone number",className:"pl-10 rounded-xl border-green-200 dark:border-green-700 focus:border-green-500",required:!0})]})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(i.J,{htmlFor:"uni_email_address",className:"text-green-800 dark:text-green-200",children:"Email Address *"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(w.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-green-500"}),(0,a.jsx)(d.p,{id:"uni_email_address",type:"email",value:L.email_address,onChange:e=>M(r=>({...r,email_address:e.target.value})),placeholder:"Enter your email address",className:"pl-10 rounded-xl border-green-200 dark:border-green-700 focus:border-green-500",required:!0})]})]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("h3",{className:"text-lg font-semibold text-green-800 dark:text-green-200 mb-4 flex items-center",children:[(0,a.jsx)(h.A,{className:"h-5 w-5 mr-2 text-green-600"}),"Academic Information"]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(i.J,{htmlFor:"uni_course_of_study",className:"text-green-800 dark:text-green-200",children:"Course of Study *"}),(0,a.jsx)(d.p,{id:"uni_course_of_study",value:L.course_of_study,onChange:e=>M(r=>({...r,course_of_study:e.target.value})),placeholder:"Enter your course of study",className:"rounded-xl border-green-200 dark:border-green-700 focus:border-green-500",required:!0})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(i.J,{htmlFor:"uni_current_level",className:"text-green-800 dark:text-green-200",children:"Current Level *"}),(0,a.jsxs)(c.l6,{value:L.current_level,onValueChange:e=>M(r=>({...r,current_level:e})),children:[(0,a.jsx)(c.bq,{className:"rounded-xl border-green-200 dark:border-green-700 focus:border-green-500",children:(0,a.jsx)(c.yv,{placeholder:"Select your current level"})}),(0,a.jsxs)(c.gC,{children:[(0,a.jsx)(c.eb,{value:"100L",children:"100 Level"}),(0,a.jsx)(c.eb,{value:"200L",children:"200 Level"}),(0,a.jsx)(c.eb,{value:"300L",children:"300 Level"}),(0,a.jsx)(c.eb,{value:"400L",children:"400 Level"}),(0,a.jsx)(c.eb,{value:"500L",children:"500 Level"}),(0,a.jsx)(c.eb,{value:"600L",children:"600 Level"})]})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(i.J,{htmlFor:"uni_matriculation_number",className:"text-green-800 dark:text-green-200",children:"Matriculation Number *"}),(0,a.jsx)(d.p,{id:"uni_matriculation_number",value:L.matriculation_number,onChange:e=>M(r=>({...r,matriculation_number:e.target.value})),placeholder:"Enter your matriculation number",className:"rounded-xl border-green-200 dark:border-green-700 focus:border-green-500",required:!0})]})]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("h3",{className:"text-lg font-semibold text-green-800 dark:text-green-200 mb-4 flex items-center",children:[(0,a.jsx)(y.A,{className:"h-5 w-5 mr-2 text-green-600"}),"Required Documents"]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(i.J,{htmlFor:"uni_student_id_card",className:"text-green-800 dark:text-green-200",children:"Upload Student ID Card *"}),(0,a.jsx)(d.p,{id:"uni_student_id_card",type:"file",accept:"image/*,.pdf",onChange:e=>{var r;return W("university","student_id_card",(null===(r=e.target.files)||void 0===r?void 0:r[0])||null)},className:"rounded-xl border-green-200 dark:border-green-700 focus:border-green-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-green-50 file:text-green-700 hover:file:bg-green-100",required:!0})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(i.J,{htmlFor:"uni_payment_evidence",className:"text-green-800 dark:text-green-200",children:"Upload Payment Evidence (Remita/Screenshot) *"}),(0,a.jsx)(d.p,{id:"uni_payment_evidence",type:"file",accept:"image/*,.pdf",onChange:e=>{var r;return W("university","payment_evidence",(null===(r=e.target.files)||void 0===r?void 0:r[0])||null)},className:"rounded-xl border-green-200 dark:border-green-700 focus:border-green-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-green-50 file:text-green-700 hover:file:bg-green-100",required:!0})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(i.J,{htmlFor:"uni_supporting_documents",className:"text-green-800 dark:text-green-200",children:"Upload Supporting Documents (PDF, DOCX, JPG)"}),(0,a.jsx)(d.p,{id:"uni_supporting_documents",type:"file",accept:".pdf,.docx,.doc,.jpg,.jpeg,.png",multiple:!0,onChange:e=>X(e.target.files),className:"rounded-xl border-green-200 dark:border-green-700 focus:border-green-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-green-50 file:text-green-700 hover:file:bg-green-100"}),G.supporting_documents.length>0&&(0,a.jsxs)("div",{className:"mt-2 space-y-2",children:[(0,a.jsx)("p",{className:"text-sm text-green-600",children:"Selected files:"}),G.supporting_documents.map((e,r)=>(0,a.jsxs)("div",{className:"flex items-center justify-between bg-green-50 p-2 rounded-xl border border-green-200",children:[(0,a.jsx)("span",{className:"text-sm text-green-700",children:e.name}),(0,a.jsx)(n.$,{type:"button",variant:"ghost",size:"sm",onClick:()=>Z(r),className:"text-red-600 hover:text-red-800",children:"Remove"})]},r))]})]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("h3",{className:"text-lg font-semibold text-green-800 dark:text-green-200 mb-4 flex items-center",children:[(0,a.jsx)(k.A,{className:"h-5 w-5 mr-2 text-green-600"}),"Application Reason"]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(i.J,{htmlFor:"uni_reason_for_scholarship",className:"text-green-800 dark:text-green-200",children:"Reason for the Scholarship *"}),(0,a.jsx)(o.T,{id:"uni_reason_for_scholarship",value:L.reason_for_scholarship,onChange:e=>M(r=>({...r,reason_for_scholarship:e.target.value})),placeholder:"Please explain why you need this scholarship",className:"rounded-xl border-green-200 dark:border-green-700 focus:border-green-500",rows:4,required:!0})]})]}),(0,a.jsx)("div",{className:"flex justify-center pt-6 sm:pt-8",children:(0,a.jsx)(n.$,{type:"submit",disabled:C||!Q(),size:"lg",className:"w-full sm:w-auto bg-gradient-to-r from-green-600 via-emerald-600 to-teal-600 hover:from-green-700 hover:via-emerald-700 hover:to-teal-700 text-white px-6 sm:px-12 py-3 sm:py-4 text-base sm:text-lg font-bold shadow-lg hover:shadow-xl transition-all duration-300",children:C?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(_.A,{className:"mr-2 sm:mr-3 h-5 w-5 sm:h-6 sm:w-6 animate-spin"}),(0,a.jsx)("span",{className:"text-sm sm:text-base",children:"Submitting..."})]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(N.A,{className:"mr-2 sm:mr-3 h-5 w-5 sm:h-6 sm:w-6"}),(0,a.jsx)("span",{className:"text-sm sm:text-base",children:"Submit University Application"})]})})})]})})]})]})})})]})}},53580:(e,r,s)=>{"use strict";s.d(r,{dj:()=>u});var a=s(12115);let t=0,l=new Map,n=e=>{if(l.has(e))return;let r=setTimeout(()=>{l.delete(e),c({type:"REMOVE_TOAST",toastId:e})},1e6);l.set(e,r)},d=(e,r)=>{switch(r.type){case"ADD_TOAST":return{...e,toasts:[r.toast,...e.toasts].slice(0,1)};case"UPDATE_TOAST":return{...e,toasts:e.toasts.map(e=>e.id===r.toast.id?{...e,...r.toast}:e)};case"DISMISS_TOAST":{let{toastId:s}=r;return s?n(s):e.toasts.forEach(e=>{n(e.id)}),{...e,toasts:e.toasts.map(e=>e.id===s||void 0===s?{...e,open:!1}:e)}}case"REMOVE_TOAST":if(void 0===r.toastId)return{...e,toasts:[]};return{...e,toasts:e.toasts.filter(e=>e.id!==r.toastId)}}},i=[],o={toasts:[]};function c(e){o=d(o,e),i.forEach(e=>{e(o)})}function m(e){let{...r}=e,s=(t=(t+1)%Number.MAX_SAFE_INTEGER).toString(),a=()=>c({type:"DISMISS_TOAST",toastId:s});return c({type:"ADD_TOAST",toast:{...r,id:s,open:!0,onOpenChange:e=>{e||a()}}}),{id:s,dismiss:a,update:e=>c({type:"UPDATE_TOAST",toast:{...e,id:s}})}}function u(){let[e,r]=a.useState(o);return a.useEffect(()=>(i.push(r),()=>{let e=i.indexOf(r);e>-1&&i.splice(e,1)}),[e]),{...e,toast:m,dismiss:e=>c({type:"DISMISS_TOAST",toastId:e})}}},53999:(e,r,s)=>{"use strict";s.d(r,{cn:()=>l});var a=s(52596),t=s(39688);function l(){for(var e=arguments.length,r=Array(e),s=0;s<e;s++)r[s]=arguments[s];return(0,t.QP)((0,a.$)(r))}},82714:(e,r,s)=>{"use strict";s.d(r,{J:()=>o});var a=s(95155),t=s(12115),l=s(40968),n=s(74466),d=s(53999);let i=(0,n.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),o=t.forwardRef((e,r)=>{let{className:s,...t}=e;return(0,a.jsx)(l.b,{ref:r,className:(0,d.cn)(i(),s),...t})});o.displayName=l.b.displayName},88482:(e,r,s)=>{"use strict";s.d(r,{BT:()=>o,Wu:()=>c,ZB:()=>i,Zp:()=>n,aR:()=>d,wL:()=>m});var a=s(95155),t=s(12115),l=s(53999);let n=t.forwardRef((e,r)=>{let{className:s,...t}=e;return(0,a.jsx)("div",{ref:r,className:(0,l.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",s),...t})});n.displayName="Card";let d=t.forwardRef((e,r)=>{let{className:s,...t}=e;return(0,a.jsx)("div",{ref:r,className:(0,l.cn)("flex flex-col space-y-1.5 p-6",s),...t})});d.displayName="CardHeader";let i=t.forwardRef((e,r)=>{let{className:s,...t}=e;return(0,a.jsx)("div",{ref:r,className:(0,l.cn)("text-2xl font-semibold leading-none tracking-tight",s),...t})});i.displayName="CardTitle";let o=t.forwardRef((e,r)=>{let{className:s,...t}=e;return(0,a.jsx)("div",{ref:r,className:(0,l.cn)("text-sm text-muted-foreground",s),...t})});o.displayName="CardDescription";let c=t.forwardRef((e,r)=>{let{className:s,...t}=e;return(0,a.jsx)("div",{ref:r,className:(0,l.cn)("p-6 pt-0",s),...t})});c.displayName="CardContent";let m=t.forwardRef((e,r)=>{let{className:s,...t}=e;return(0,a.jsx)("div",{ref:r,className:(0,l.cn)("flex items-center p-6 pt-0",s),...t})});m.displayName="CardFooter"},89852:(e,r,s)=>{"use strict";s.d(r,{p:()=>n});var a=s(95155),t=s(12115),l=s(53999);let n=t.forwardRef((e,r)=>{let{className:s,type:t,...n}=e;return(0,a.jsx)("input",{type:t,className:(0,l.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",s),ref:r,suppressHydrationWarning:!0,...n})});n.displayName="Input"},91284:(e,r,s)=>{Promise.resolve().then(s.bind(s,48315))},95784:(e,r,s)=>{"use strict";s.d(r,{bq:()=>u,eb:()=>g,gC:()=>p,l6:()=>c,yv:()=>m});var a=s(95155),t=s(12115),l=s(38715),n=s(66474),d=s(47863),i=s(5196),o=s(53999);let c=l.bL;l.YJ;let m=l.WT,u=t.forwardRef((e,r)=>{let{className:s,children:t,...d}=e;return(0,a.jsxs)(l.l9,{ref:r,className:(0,o.cn)("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",s),...d,children:[t,(0,a.jsx)(l.In,{asChild:!0,children:(0,a.jsx)(n.A,{className:"h-4 w-4 opacity-50"})})]})});u.displayName=l.l9.displayName;let x=t.forwardRef((e,r)=>{let{className:s,...t}=e;return(0,a.jsx)(l.PP,{ref:r,className:(0,o.cn)("flex cursor-default items-center justify-center py-1",s),...t,children:(0,a.jsx)(d.A,{className:"h-4 w-4"})})});x.displayName=l.PP.displayName;let h=t.forwardRef((e,r)=>{let{className:s,...t}=e;return(0,a.jsx)(l.wn,{ref:r,className:(0,o.cn)("flex cursor-default items-center justify-center py-1",s),...t,children:(0,a.jsx)(n.A,{className:"h-4 w-4"})})});h.displayName=l.wn.displayName;let p=t.forwardRef((e,r)=>{let{className:s,children:t,position:n="popper",...d}=e;return(0,a.jsx)(l.ZL,{children:(0,a.jsxs)(l.UC,{ref:r,className:(0,o.cn)("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===n&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",s),position:n,...d,children:[(0,a.jsx)(x,{}),(0,a.jsx)(l.LM,{className:(0,o.cn)("p-1","popper"===n&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:t}),(0,a.jsx)(h,{})]})})});p.displayName=l.UC.displayName,t.forwardRef((e,r)=>{let{className:s,...t}=e;return(0,a.jsx)(l.JU,{ref:r,className:(0,o.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",s),...t})}).displayName=l.JU.displayName;let g=t.forwardRef((e,r)=>{let{className:s,children:t,...n}=e;return(0,a.jsxs)(l.q7,{ref:r,className:(0,o.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",s),...n,children:[(0,a.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,a.jsx)(l.VF,{children:(0,a.jsx)(i.A,{className:"h-4 w-4"})})}),(0,a.jsx)(l.p4,{children:t})]})});g.displayName=l.q7.displayName,t.forwardRef((e,r)=>{let{className:s,...t}=e;return(0,a.jsx)(l.wv,{ref:r,className:(0,o.cn)("-mx-1 my-1 h-px bg-muted",s),...t})}).displayName=l.wv.displayName},97168:(e,r,s)=>{"use strict";s.d(r,{$:()=>o,r:()=>i});var a=s(95155),t=s(12115),l=s(99708),n=s(74466),d=s(53999);let i=(0,n.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-all duration-200 ease-in-out focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 transform hover:scale-105",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90 hover:brightness-110",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90 hover:brightness-110",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground hover:brightness-105",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80 hover:brightness-105",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),o=t.forwardRef((e,r)=>{let{className:s,variant:t,size:n,asChild:o=!1,...c}=e,m=o?l.DX:"button";return(0,a.jsx)(m,{className:(0,d.cn)(i({variant:t,size:n,className:s})),ref:r,suppressHydrationWarning:!0,...c})});o.displayName="Button"},99474:(e,r,s)=>{"use strict";s.d(r,{T:()=>n});var a=s(95155),t=s(12115),l=s(53999);let n=t.forwardRef((e,r)=>{let{className:s,...t}=e;return(0,a.jsx)("textarea",{className:(0,l.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",s),ref:r,...t})});n.displayName="Textarea"}},e=>{var r=r=>e(e.s=r);e.O(0,[1778,598,4057,461,6172,1886,8441,1684,7358],()=>r(91284)),_N_E=e.O()}]);