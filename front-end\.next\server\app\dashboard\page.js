(()=>{var e={};e.id=105,e.ids=[105],e.modules={1130:(e,a,t)=>{Promise.resolve().then(t.bind(t,64118))},2002:(e,a,t)=>{"use strict";t.r(a),t.d(a,{default:()=>er});var r=t(60687),s=t(43210),n=t(16189),l=t(59556),i=t(41862),o=t(67760),d=t(62688);let c=(0,d.A)("ExternalLink",[["path",{d:"M15 3h6v6",key:"1q9fwt"}],["path",{d:"M10 14 21 3",key:"gplh6r"}],["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}]]);var m=t(84027);let u=(0,d.A)("LogOut",[["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}],["polyline",{points:"16 17 21 12 16 7",key:"1gabdz"}],["line",{x1:"21",x2:"9",y1:"12",y2:"12",key:"1uyos4"}]]);var p=t(24934),x=t(55629),f=t(11273),h=t(98599),g=t(26134),j=t(70569),v=t(8730),y="AlertDialog",[w,b]=(0,f.A)(y,[g.Hs]),N=(0,g.Hs)(),A=e=>{let{__scopeAlertDialog:a,...t}=e,s=N(a);return(0,r.jsx)(g.bL,{...s,...t,modal:!0})};A.displayName=y;var _=s.forwardRef((e,a)=>{let{__scopeAlertDialog:t,...s}=e,n=N(t);return(0,r.jsx)(g.l9,{...n,...s,ref:a})});_.displayName="AlertDialogTrigger";var C=e=>{let{__scopeAlertDialog:a,...t}=e,s=N(a);return(0,r.jsx)(g.ZL,{...s,...t})};C.displayName="AlertDialogPortal";var k=s.forwardRef((e,a)=>{let{__scopeAlertDialog:t,...s}=e,n=N(t);return(0,r.jsx)(g.hJ,{...n,...s,ref:a})});k.displayName="AlertDialogOverlay";var P="AlertDialogContent",[R,D]=w(P),q=s.forwardRef((e,a)=>{let{__scopeAlertDialog:t,children:n,...l}=e,i=N(t),o=s.useRef(null),d=(0,h.s)(a,o),c=s.useRef(null);return(0,r.jsx)(g.G$,{contentName:P,titleName:L,docsSlug:"alert-dialog",children:(0,r.jsx)(R,{scope:t,cancelRef:c,children:(0,r.jsxs)(g.UC,{role:"alertdialog",...i,...l,ref:d,onOpenAutoFocus:(0,j.m)(l.onOpenAutoFocus,e=>{e.preventDefault(),c.current?.focus({preventScroll:!0})}),onPointerDownOutside:e=>e.preventDefault(),onInteractOutside:e=>e.preventDefault(),children:[(0,r.jsx)(v.xV,{children:n}),(0,r.jsx)(M,{contentRef:o})]})})})});q.displayName=P;var L="AlertDialogTitle",S=s.forwardRef((e,a)=>{let{__scopeAlertDialog:t,...s}=e,n=N(t);return(0,r.jsx)(g.hE,{...n,...s,ref:a})});S.displayName=L;var z="AlertDialogDescription",E=s.forwardRef((e,a)=>{let{__scopeAlertDialog:t,...s}=e,n=N(t);return(0,r.jsx)(g.VY,{...n,...s,ref:a})});E.displayName=z;var O=s.forwardRef((e,a)=>{let{__scopeAlertDialog:t,...s}=e,n=N(t);return(0,r.jsx)(g.bm,{...n,...s,ref:a})});O.displayName="AlertDialogAction";var $="AlertDialogCancel",I=s.forwardRef((e,a)=>{let{__scopeAlertDialog:t,...s}=e,{cancelRef:n}=D($,t),l=N(t),i=(0,h.s)(a,n);return(0,r.jsx)(g.bm,{...l,...s,ref:i})});I.displayName=$;var M=({contentRef:e})=>{let a=`\`${P}\` requires a description for the component to be accessible for screen reader users.

You can add a description to the \`${P}\` by passing a \`${z}\` component as a child, which also benefits sighted users by adding visible context to the dialog.

Alternatively, you can use your own component as a description by assigning it an \`id\` and passing the same value to the \`aria-describedby\` prop in \`${P}\`. If the description is confusing or duplicative for sighted users, you can use the \`@radix-ui/react-visually-hidden\` primitive as a wrapper around your description component.

For more information, see https://radix-ui.com/primitives/docs/components/alert-dialog`;return s.useEffect(()=>{document.getElementById(e.current?.getAttribute("aria-describedby"))||console.warn(a)},[a,e]),null},G=t(96241);let F=s.forwardRef(({className:e,...a},t)=>(0,r.jsx)(k,{className:(0,G.cn)("fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",e),...a,ref:t}));F.displayName=k.displayName;let H=s.forwardRef(({className:e,...a},t)=>(0,r.jsxs)(C,{children:[(0,r.jsx)(F,{}),(0,r.jsx)(q,{ref:t,className:(0,G.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",e),...a})]}));H.displayName=q.displayName;let V=({className:e,...a})=>(0,r.jsx)("div",{className:(0,G.cn)("flex flex-col space-y-2 text-center sm:text-left",e),...a});V.displayName="AlertDialogHeader";let T=({className:e,...a})=>(0,r.jsx)("div",{className:(0,G.cn)("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",e),...a});T.displayName="AlertDialogFooter";let B=s.forwardRef(({className:e,...a},t)=>(0,r.jsx)(S,{ref:t,className:(0,G.cn)("text-lg font-semibold",e),...a}));B.displayName=S.displayName;let W=s.forwardRef(({className:e,...a},t)=>(0,r.jsx)(E,{ref:t,className:(0,G.cn)("text-sm text-muted-foreground",e),...a}));W.displayName=E.displayName;let Y=s.forwardRef(({className:e,...a},t)=>(0,r.jsx)(O,{ref:t,className:(0,G.cn)((0,p.r)(),e),...a}));Y.displayName=O.displayName;let U=s.forwardRef(({className:e,...a},t)=>(0,r.jsx)(I,{ref:t,className:(0,G.cn)((0,p.r)({variant:"outline"}),"mt-2 sm:mt-0",e),...a}));U.displayName=I.displayName;var J=t(85814),K=t.n(J),Q=t(40093),X=t(77348),Z=t(37961),ee=t(23226),ea=t(91666),et=t(10452);function er(){let[e,a]=(0,s.useState)(null),[t,d]=(0,s.useState)(!0),[f,h]=(0,s.useState)(!1),g=(0,n.useRouter)(),{settings:j}=(0,Q.t)(),v=j?.app_name||"Laravel NGO",y=j?.app_logo,w=async()=>{try{h(!0),await (0,l.ri)(),g.push("/auth/login")}catch(e){console.error("Logout failed:",e),localStorage.removeItem("authToken"),localStorage.removeItem("user"),g.push("/auth/login")}finally{h(!1)}};if(t)return(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)(i.A,{className:"h-8 w-8 animate-spin mx-auto mb-4 text-green-600"}),(0,r.jsx)("p",{className:"text-gray-600",children:"Loading your dashboard..."})]})});if(!e)return(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50",children:(0,r.jsx)("div",{className:"text-center",children:(0,r.jsx)("p",{className:"text-red-600",children:"Unable to load user data. Please try logging in again."})})});let b=e.preferences?.user_type?e.preferences.user_type:"volunteer"===e.role?"volunteer":"admin"===e.role?"admin":"user";return(0,r.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,r.jsx)(()=>(0,r.jsx)("div",{className:"bg-white border-b border-gray-200 px-4 py-3 sm:px-6",children:(0,r.jsxs)("div",{className:"flex justify-between items-center max-w-7xl mx-auto",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[y?(0,r.jsx)("img",{src:y,alt:`${v} Logo`,className:"h-8 w-auto object-contain"}):(0,r.jsx)("div",{className:"relative h-8 w-8 overflow-hidden rounded-full bg-gradient-to-br from-green-600 to-green-700 shadow-lg",children:(0,r.jsx)(o.A,{className:"absolute inset-0 m-auto h-5 w-5 text-white"})}),(0,r.jsxs)("h1",{className:"text-xl sm:text-2xl font-bold text-gray-900",children:[v," Dashboard"]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsxs)("div",{className:"hidden sm:block text-sm text-gray-600",children:["Welcome, ",e.first_name]}),(0,r.jsx)(K(),{href:"/",children:(0,r.jsxs)(p.$,{variant:"outline",size:"sm",className:"flex items-center space-x-2",children:[(0,r.jsx)(c,{className:"h-4 w-4"}),(0,r.jsx)("span",{className:"hidden sm:inline",children:"Visit Website"}),(0,r.jsx)("span",{className:"sm:hidden",children:"Website"})]})}),(0,r.jsxs)(x.rI,{children:[(0,r.jsx)(x.ty,{asChild:!0,children:(0,r.jsxs)(p.$,{variant:"ghost",size:"sm",className:"flex items-center space-x-2",children:[(0,r.jsxs)("div",{className:"w-8 h-8 bg-green-600 rounded-full flex items-center justify-center text-white text-sm font-medium",children:[e.first_name.charAt(0),e.last_name.charAt(0)]}),(0,r.jsx)("span",{className:"hidden sm:inline",children:e.first_name})]})}),(0,r.jsxs)(x.SQ,{align:"end",className:"w-56",children:[(0,r.jsxs)("div",{className:"px-2 py-1.5 text-sm font-medium",children:[e.first_name," ",e.last_name]}),(0,r.jsx)("div",{className:"px-2 py-1.5 text-xs text-gray-500",children:e.email}),(0,r.jsx)(x.mB,{}),(0,r.jsxs)(x._2,{onClick:()=>{let e=document.querySelector('[data-value="profile"]');e&&e.click()},children:[(0,r.jsx)(m.A,{className:"mr-2 h-4 w-4"}),"Profile Settings"]}),(0,r.jsx)(x.mB,{}),(0,r.jsxs)(A,{children:[(0,r.jsx)(_,{asChild:!0,children:(0,r.jsxs)(x._2,{onSelect:e=>e.preventDefault(),children:[(0,r.jsx)(u,{className:"mr-2 h-4 w-4"}),"Logout"]})}),(0,r.jsxs)(H,{children:[(0,r.jsxs)(V,{children:[(0,r.jsx)(B,{children:"Are you sure you want to logout?"}),(0,r.jsx)(W,{children:"You will need to login again to access your dashboard."})]}),(0,r.jsxs)(T,{children:[(0,r.jsx)(U,{children:"Cancel"}),(0,r.jsx)(Y,{onClick:w,disabled:f,className:"bg-red-600 hover:bg-red-700",children:f?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(i.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Logging out..."]}):"Logout"})]})]})]})]})]})]})]})}),{}),(()=>{let a={user:e};switch(b){case"student":return(0,r.jsx)(X.default,{...a});case"volunteer":return(0,r.jsx)(Z.default,{...a});case"partner":return(0,r.jsx)(ee.default,{...a});case"admin":return(0,r.jsx)(et.default,{...a});default:return(0,r.jsx)(ea.default,{...a})}})()]})}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},8086:e=>{"use strict";e.exports=require("module")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34271:(e,a,t)=>{"use strict";t.r(a),t.d(a,{GlobalError:()=>l.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>u,tree:()=>d});var r=t(65239),s=t(48088),n=t(88170),l=t.n(n),i=t(30893),o={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>i[e]);t.d(a,o);let d={children:["",{children:["dashboard",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,64118)),"C:\\laragon\\www\\laravel-api-ngo\\front-end\\app\\dashboard\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,58014)),"C:\\laragon\\www\\laravel-api-ngo\\front-end\\app\\layout.tsx"],error:[()=>Promise.resolve().then(t.bind(t,52608)),"C:\\laragon\\www\\laravel-api-ngo\\front-end\\app\\error.tsx"],loading:[()=>Promise.resolve().then(t.bind(t,99766)),"C:\\laragon\\www\\laravel-api-ngo\\front-end\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(t.bind(t,82366)),"C:\\laragon\\www\\laravel-api-ngo\\front-end\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["C:\\laragon\\www\\laravel-api-ngo\\front-end\\app\\dashboard\\page.tsx"],m={require:t,loadChunk:()=>Promise.resolve()},u=new r.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/dashboard/page",pathname:"/dashboard",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64118:(e,a,t)=>{"use strict";t.r(a),t.d(a,{default:()=>r});let r=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\laragon\\www\\laravel-api-ngo\\front-end\\app\\dashboard\\page.tsx","default")},64178:(e,a,t)=>{Promise.resolve().then(t.bind(t,2002))}};var a=require("../../webpack-runtime.js");a.C(e);var t=e=>a(a.s=e),r=a.X(0,[555,394,616,202,880,702,452,666,226,961,348],()=>t(34271));module.exports=r})();