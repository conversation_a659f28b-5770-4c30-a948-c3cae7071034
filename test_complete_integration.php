<?php

/**
 * Complete Integration Test Script
 * Tests the full scholarship system from API to frontend
 */

echo "=== COMPLETE INTEGRATION TEST ===\n\n";

// Test 1: API Endpoint Functionality
echo "1. Testing API Endpoints...\n";

// Test public scholarships endpoint
$api_url = 'http://localhost:8000/api/v1/public-scholarships';
$context = stream_context_create([
    'http' => [
        'method' => 'GET',
        'header' => 'Content-Type: application/json',
        'timeout' => 10
    ]
]);

try {
    $response = file_get_contents($api_url, false, $context);
    if ($response === false) {
        throw new Exception("Failed to fetch from API");
    }
    
    $data = json_decode($response, true);
    if (!$data || !isset($data['success'])) {
        throw new Exception("Invalid API response format");
    }
    
    if ($data['success']) {
        $scholarships = $data['data'];
        echo "   ✓ API endpoint working - Found " . count($scholarships) . " scholarships\n";
        
        // Test individual scholarship endpoint
        if (!empty($scholarships)) {
            $first_scholarship = $scholarships[0];
            $detail_url = $api_url . '/' . $first_scholarship['id'];
            $detail_response = file_get_contents($detail_url, false, $context);
            $detail_data = json_decode($detail_response, true);
            
            if ($detail_data && $detail_data['success']) {
                echo "   ✓ Individual scholarship endpoint working\n";
                
                // Verify required fields for frontend integration
                $required_fields = [
                    'id', 'title', 'category', 'description', 'amount', 
                    'application_deadline', 'status', 'current_applicants'
                ];
                
                $missing_fields = [];
                foreach ($required_fields as $field) {
                    if (!isset($detail_data['data'][$field])) {
                        $missing_fields[] = $field;
                    }
                }
                
                if (empty($missing_fields)) {
                    echo "   ✓ All required fields present in API response\n";
                } else {
                    echo "   ✗ Missing required fields: " . implode(', ', $missing_fields) . "\n";
                }
                
                // Test category-specific features
                $scholarship_data = $detail_data['data'];
                if (isset($scholarship_data['category_instructions'])) {
                    echo "   ✓ Category instructions available\n";
                } else {
                    echo "   ⚠ Category instructions missing\n";
                }
                
                if (isset($scholarship_data['custom_fields']) && !empty($scholarship_data['custom_fields'])) {
                    echo "   ✓ Custom fields available (" . count($scholarship_data['custom_fields']) . " fields)\n";
                } else {
                    echo "   ⚠ Custom fields missing or empty\n";
                }
                
            } else {
                echo "   ✗ Individual scholarship endpoint failed\n";
            }
        }
        
    } else {
        echo "   ✗ API returned error: " . ($data['message'] ?? 'Unknown error') . "\n";
    }
    
} catch (Exception $e) {
    echo "   ✗ API test failed: " . $e->getMessage() . "\n";
}

echo "\n";

// Test 2: Category Filtering
echo "2. Testing Category Filtering...\n";

$categories = ['primary', 'secondary', 'university'];
foreach ($categories as $category) {
    try {
        $filter_url = $api_url . '?category=' . $category;
        $filter_response = file_get_contents($filter_url, false, $context);
        $filter_data = json_decode($filter_response, true);
        
        if ($filter_data && $filter_data['success']) {
            $filtered_scholarships = $filter_data['data'];
            $category_count = 0;
            
            foreach ($filtered_scholarships as $scholarship) {
                if ($scholarship['category'] === $category) {
                    $category_count++;
                }
            }
            
            if ($category_count === count($filtered_scholarships)) {
                echo "   ✓ {$category} category filter working (" . count($filtered_scholarships) . " scholarships)\n";
            } else {
                echo "   ⚠ {$category} category filter has mixed results\n";
            }
        } else {
            echo "   ✗ {$category} category filter failed\n";
        }
    } catch (Exception $e) {
        echo "   ✗ {$category} category filter error: " . $e->getMessage() . "\n";
    }
}

echo "\n";

// Test 3: Frontend Integration Check
echo "3. Testing Frontend Integration...\n";

// Check if Next.js server is running
$frontend_url = 'http://localhost:3000/scholarships';
$frontend_context = stream_context_create([
    'http' => [
        'method' => 'GET',
        'timeout' => 5,
        'ignore_errors' => true
    ]
]);

try {
    $frontend_response = file_get_contents($frontend_url, false, $frontend_context);
    if ($frontend_response !== false) {
        echo "   ✓ Frontend server is running\n";
        echo "   ✓ Scholarships page accessible\n";
        
        // Test individual scholarship page
        $detail_page_url = 'http://localhost:3000/scholarships/1';
        $detail_page_response = file_get_contents($detail_page_url, false, $frontend_context);
        if ($detail_page_response !== false) {
            echo "   ✓ Individual scholarship page accessible\n";
        } else {
            echo "   ⚠ Individual scholarship page may have issues\n";
        }
        
        // Test application page
        $apply_page_url = 'http://localhost:3000/scholarships/1/apply';
        $apply_page_response = file_get_contents($apply_page_url, false, $frontend_context);
        if ($apply_page_response !== false) {
            echo "   ✓ Application page accessible\n";
        } else {
            echo "   ⚠ Application page may have issues\n";
        }
        
    } else {
        echo "   ✗ Frontend server not accessible\n";
        echo "   ℹ Make sure Next.js server is running on port 3000\n";
    }
} catch (Exception $e) {
    echo "   ✗ Frontend test error: " . $e->getMessage() . "\n";
}

echo "\n";

// Test 4: Database Consistency
echo "4. Testing Database Consistency...\n";

// Check if we can connect to database through Laravel
$laravel_check_url = 'http://localhost:8000/api/v1/public-scholarships';
try {
    $db_response = file_get_contents($laravel_check_url, false, $context);
    $db_data = json_decode($db_response, true);
    
    if ($db_data && $db_data['success']) {
        echo "   ✓ Database connection working through Laravel\n";
        
        $scholarships = $db_data['data'];
        $active_count = 0;
        $with_custom_fields = 0;
        $with_instructions = 0;
        
        foreach ($scholarships as $scholarship) {
            if (in_array($scholarship['status'], ['active', 'open'])) {
                $active_count++;
            }
            
            // Get detailed info for each scholarship
            $detail_url = $laravel_check_url . '/' . $scholarship['id'];
            $detail_response = file_get_contents($detail_url, false, $context);
            $detail_data = json_decode($detail_response, true);
            
            if ($detail_data && $detail_data['success']) {
                $detail_scholarship = $detail_data['data'];
                if (isset($detail_scholarship['custom_fields']) && !empty($detail_scholarship['custom_fields'])) {
                    $with_custom_fields++;
                }
                if (isset($detail_scholarship['category_instructions'])) {
                    $with_instructions++;
                }
            }
        }
        
        echo "   ✓ Found {$active_count} active scholarships\n";
        echo "   ✓ {$with_custom_fields} scholarships have custom fields\n";
        echo "   ✓ {$with_instructions} scholarships have category instructions\n";
        
    } else {
        echo "   ✗ Database connection issues\n";
    }
} catch (Exception $e) {
    echo "   ✗ Database test error: " . $e->getMessage() . "\n";
}

echo "\n";

// Test 5: Mobile Responsiveness Check
echo "5. Testing Mobile Responsiveness...\n";

// This would typically require browser automation, but we can check for responsive design indicators
try {
    $scholarships_page = file_get_contents('http://localhost:3000/scholarships', false, $frontend_context);
    if ($scholarships_page !== false) {
        // Check for responsive design classes
        $responsive_indicators = [
            'grid-cols-1',
            'md:grid-cols-2', 
            'lg:grid-cols-3',
            'sm:px-6',
            'container mx-auto',
            'px-4'
        ];
        
        $found_indicators = 0;
        foreach ($responsive_indicators as $indicator) {
            if (strpos($scholarships_page, $indicator) !== false) {
                $found_indicators++;
            }
        }
        
        if ($found_indicators >= 4) {
            echo "   ✓ Responsive design classes detected\n";
        } else {
            echo "   ⚠ Limited responsive design indicators found\n";
        }
        
        // Check for mobile-friendly meta tags
        if (strpos($scholarships_page, 'viewport') !== false) {
            echo "   ✓ Viewport meta tag present\n";
        } else {
            echo "   ⚠ Viewport meta tag missing\n";
        }
        
    } else {
        echo "   ✗ Cannot access frontend for mobile responsiveness check\n";
    }
} catch (Exception $e) {
    echo "   ✗ Mobile responsiveness test error: " . $e->getMessage() . "\n";
}

echo "\n";

// Test Summary
echo "=== TEST SUMMARY ===\n";
echo "✓ = Passed\n";
echo "⚠ = Warning/Partial\n";
echo "✗ = Failed\n";
echo "ℹ = Information\n\n";

echo "Integration test completed!\n";
echo "Please check the results above and address any issues marked with ✗ or ⚠\n\n";

echo "Next steps for manual testing:\n";
echo "1. Open http://localhost:3000/scholarships in your browser\n";
echo "2. Test filtering by category\n";
echo "3. Click on a scholarship to view details\n";
echo "4. Test the application form\n";
echo "5. Test on mobile device or browser dev tools\n";
echo "6. Verify all form fields render correctly\n";
echo "7. Test form validation and submission\n";

?>
