"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2612],{14186:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(19946).A)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},16785:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(19946).A)("Target",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"12",r:"6",key:"1vlfrh"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}]])},57434:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(19946).A)("FileText",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]])},60704:(e,t,r)=>{r.d(t,{B8:()=>I,UC:()=>T,bL:()=>j,l9:()=>D});var a=r(12115),n=r(85185),o=r(46081),i=r(89196),l=r(28905),s=r(63655),c=r(94315),u=r(5845),d=r(61285),f=r(95155),p="Tabs",[v,y]=(0,o.A)(p,[i.RG]),h=(0,i.RG)(),[m,b]=v(p),w=a.forwardRef((e,t)=>{let{__scopeTabs:r,value:a,onValueChange:n,defaultValue:o,orientation:i="horizontal",dir:l,activationMode:p="automatic",...v}=e,y=(0,c.jH)(l),[h,b]=(0,u.i)({prop:a,onChange:n,defaultProp:o});return(0,f.jsx)(m,{scope:r,baseId:(0,d.B)(),value:h,onValueChange:b,orientation:i,dir:y,activationMode:p,children:(0,f.jsx)(s.sG.div,{dir:y,"data-orientation":i,...v,ref:t})})});w.displayName=p;var g="TabsList",k=a.forwardRef((e,t)=>{let{__scopeTabs:r,loop:a=!0,...n}=e,o=b(g,r),l=h(r);return(0,f.jsx)(i.bL,{asChild:!0,...l,orientation:o.orientation,dir:o.dir,loop:a,children:(0,f.jsx)(s.sG.div,{role:"tablist","aria-orientation":o.orientation,...n,ref:t})})});k.displayName=g;var A="TabsTrigger",x=a.forwardRef((e,t)=>{let{__scopeTabs:r,value:a,disabled:o=!1,...l}=e,c=b(A,r),u=h(r),d=F(c.baseId,a),p=M(c.baseId,a),v=a===c.value;return(0,f.jsx)(i.q7,{asChild:!0,...u,focusable:!o,active:v,children:(0,f.jsx)(s.sG.button,{type:"button",role:"tab","aria-selected":v,"aria-controls":p,"data-state":v?"active":"inactive","data-disabled":o?"":void 0,disabled:o,id:d,...l,ref:t,onMouseDown:(0,n.m)(e.onMouseDown,e=>{o||0!==e.button||!1!==e.ctrlKey?e.preventDefault():c.onValueChange(a)}),onKeyDown:(0,n.m)(e.onKeyDown,e=>{[" ","Enter"].includes(e.key)&&c.onValueChange(a)}),onFocus:(0,n.m)(e.onFocus,()=>{let e="manual"!==c.activationMode;v||o||!e||c.onValueChange(a)})})})});x.displayName=A;var R="TabsContent",C=a.forwardRef((e,t)=>{let{__scopeTabs:r,value:n,forceMount:o,children:i,...c}=e,u=b(R,r),d=F(u.baseId,n),p=M(u.baseId,n),v=n===u.value,y=a.useRef(v);return a.useEffect(()=>{let e=requestAnimationFrame(()=>y.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,f.jsx)(l.C,{present:o||v,children:r=>{let{present:a}=r;return(0,f.jsx)(s.sG.div,{"data-state":v?"active":"inactive","data-orientation":u.orientation,role:"tabpanel","aria-labelledby":d,hidden:!a,id:p,tabIndex:0,...c,ref:t,style:{...e.style,animationDuration:y.current?"0s":void 0},children:a&&i})}})});function F(e,t){return"".concat(e,"-trigger-").concat(t)}function M(e,t){return"".concat(e,"-content-").concat(t)}C.displayName=R;var j=w,I=k,D=x,T=C},69037:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(19946).A)("Award",[["path",{d:"m15.477 12.89 1.515 8.526a.5.5 0 0 1-.81.47l-3.58-2.687a1 1 0 0 0-1.197 0l-3.586 2.686a.5.5 0 0 1-.81-.469l1.514-8.526",key:"1yiouv"}],["circle",{cx:"12",cy:"8",r:"6",key:"1vp47v"}]])},69074:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(19946).A)("Calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},84616:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(19946).A)("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},87949:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(19946).A)("GraduationCap",[["path",{d:"M21.42 10.922a1 1 0 0 0-.019-1.838L12.83 5.18a2 2 0 0 0-1.66 0L2.6 9.08a1 1 0 0 0 0 1.832l8.57 3.908a2 2 0 0 0 1.66 0z",key:"j76jl0"}],["path",{d:"M22 10v6",key:"1lu8f3"}],["path",{d:"M6 12.5V16a6 3 0 0 0 12 0v-3.5",key:"1r8lef"}]])},89196:(e,t,r)=>{r.d(t,{RG:()=>k,bL:()=>D,q7:()=>T});var a=r(12115),n=r(85185),o=r(82284),i=r(6101),l=r(46081),s=r(61285),c=r(63655),u=r(39033),d=r(5845),f=r(94315),p=r(95155),v="rovingFocusGroup.onEntryFocus",y={bubbles:!1,cancelable:!0},h="RovingFocusGroup",[m,b,w]=(0,o.N)(h),[g,k]=(0,l.A)(h,[w]),[A,x]=g(h),R=a.forwardRef((e,t)=>(0,p.jsx)(m.Provider,{scope:e.__scopeRovingFocusGroup,children:(0,p.jsx)(m.Slot,{scope:e.__scopeRovingFocusGroup,children:(0,p.jsx)(C,{...e,ref:t})})}));R.displayName=h;var C=a.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:r,orientation:o,loop:l=!1,dir:s,currentTabStopId:h,defaultCurrentTabStopId:m,onCurrentTabStopIdChange:w,onEntryFocus:g,preventScrollOnEntryFocus:k=!1,...x}=e,R=a.useRef(null),C=(0,i.s)(t,R),F=(0,f.jH)(s),[M=null,j]=(0,d.i)({prop:h,defaultProp:m,onChange:w}),[D,T]=a.useState(!1),G=(0,u.c)(g),E=b(r),L=a.useRef(!1),[K,N]=a.useState(0);return a.useEffect(()=>{let e=R.current;if(e)return e.addEventListener(v,G),()=>e.removeEventListener(v,G)},[G]),(0,p.jsx)(A,{scope:r,orientation:o,dir:F,loop:l,currentTabStopId:M,onItemFocus:a.useCallback(e=>j(e),[j]),onItemShiftTab:a.useCallback(()=>T(!0),[]),onFocusableItemAdd:a.useCallback(()=>N(e=>e+1),[]),onFocusableItemRemove:a.useCallback(()=>N(e=>e-1),[]),children:(0,p.jsx)(c.sG.div,{tabIndex:D||0===K?-1:0,"data-orientation":o,...x,ref:C,style:{outline:"none",...e.style},onMouseDown:(0,n.m)(e.onMouseDown,()=>{L.current=!0}),onFocus:(0,n.m)(e.onFocus,e=>{let t=!L.current;if(e.target===e.currentTarget&&t&&!D){let t=new CustomEvent(v,y);if(e.currentTarget.dispatchEvent(t),!t.defaultPrevented){let e=E().filter(e=>e.focusable);I([e.find(e=>e.active),e.find(e=>e.id===M),...e].filter(Boolean).map(e=>e.ref.current),k)}}L.current=!1}),onBlur:(0,n.m)(e.onBlur,()=>T(!1))})})}),F="RovingFocusGroupItem",M=a.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:r,focusable:o=!0,active:i=!1,tabStopId:l,...u}=e,d=(0,s.B)(),f=l||d,v=x(F,r),y=v.currentTabStopId===f,h=b(r),{onFocusableItemAdd:w,onFocusableItemRemove:g}=v;return a.useEffect(()=>{if(o)return w(),()=>g()},[o,w,g]),(0,p.jsx)(m.ItemSlot,{scope:r,id:f,focusable:o,active:i,children:(0,p.jsx)(c.sG.span,{tabIndex:y?0:-1,"data-orientation":v.orientation,...u,ref:t,onMouseDown:(0,n.m)(e.onMouseDown,e=>{o?v.onItemFocus(f):e.preventDefault()}),onFocus:(0,n.m)(e.onFocus,()=>v.onItemFocus(f)),onKeyDown:(0,n.m)(e.onKeyDown,e=>{if("Tab"===e.key&&e.shiftKey){v.onItemShiftTab();return}if(e.target!==e.currentTarget)return;let t=function(e,t,r){var a;let n=(a=e.key,"rtl"!==r?a:"ArrowLeft"===a?"ArrowRight":"ArrowRight"===a?"ArrowLeft":a);if(!("vertical"===t&&["ArrowLeft","ArrowRight"].includes(n))&&!("horizontal"===t&&["ArrowUp","ArrowDown"].includes(n)))return j[n]}(e,v.orientation,v.dir);if(void 0!==t){if(e.metaKey||e.ctrlKey||e.altKey||e.shiftKey)return;e.preventDefault();let r=h().filter(e=>e.focusable).map(e=>e.ref.current);if("last"===t)r.reverse();else if("prev"===t||"next"===t){"prev"===t&&r.reverse();let a=r.indexOf(e.currentTarget);r=v.loop?function(e,t){return e.map((r,a)=>e[(t+a)%e.length])}(r,a+1):r.slice(a+1)}setTimeout(()=>I(r))}})})})});M.displayName=F;var j={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function I(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],r=document.activeElement;for(let a of e)if(a===r||(a.focus({preventScroll:t}),document.activeElement!==r))return}var D=R,T=M}}]);