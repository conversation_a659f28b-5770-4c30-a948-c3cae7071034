(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[983],{4516:(e,r,a)=>{"use strict";a.d(r,{A:()=>s});let s=(0,a(19946).A)("MapPin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])},17580:(e,r,a)=>{"use strict";a.d(r,{A:()=>s});let s=(0,a(19946).A)("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]])},19420:(e,r,a)=>{"use strict";a.d(r,{A:()=>s});let s=(0,a(19946).A)("Phone",[["path",{d:"M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z",key:"foiqr5"}]])},28883:(e,r,a)=>{"use strict";a.d(r,{A:()=>s});let s=(0,a(19946).A)("Mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]])},32919:(e,r,a)=>{"use strict";a.d(r,{A:()=>s});let s=(0,a(19946).A)("Lock",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]])},35169:(e,r,a)=>{"use strict";a.d(r,{A:()=>s});let s=(0,a(19946).A)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},35695:(e,r,a)=>{"use strict";var s=a(18999);a.o(s,"notFound")&&a.d(r,{notFound:function(){return s.notFound}}),a.o(s,"useParams")&&a.d(r,{useParams:function(){return s.useParams}}),a.o(s,"usePathname")&&a.d(r,{usePathname:function(){return s.usePathname}}),a.o(s,"useRouter")&&a.d(r,{useRouter:function(){return s.useRouter}}),a.o(s,"useSearchParams")&&a.d(r,{useSearchParams:function(){return s.useSearchParams}})},37841:(e,r,a)=>{Promise.resolve().then(a.bind(a,58247))},40968:(e,r,a)=>{"use strict";a.d(r,{b:()=>d});var s=a(12115),t=a(63655),n=a(95155),l=s.forwardRef((e,r)=>(0,n.jsx)(t.sG.label,{...e,ref:r,onMouseDown:r=>{var a;r.target.closest("button, input, select, textarea")||(null===(a=e.onMouseDown)||void 0===a||a.call(e,r),!r.defaultPrevented&&r.detail>1&&r.preventDefault())}}));l.displayName="Label";var d=l},44531:(e,r,a)=>{"use strict";a.d(r,{t:()=>n});var s=a(12115),t=a(31886);let n=()=>{let[e,r]=(0,s.useState)(null),[a,n]=(0,s.useState)(!0),[l,d]=(0,s.useState)(null);return(0,s.useEffect)(()=>{(async()=>{try{let e=await t.uE.getSettings();if(e.success)console.log("Settings loaded:",e.data),r(e.data);else throw Error("Invalid settings response")}catch(e){console.error("Error fetching settings:",e),d(e instanceof Error?e.message:"Unknown error"),r({app_name:"HLTKKQ Foundation",site_description:"Transforming Lives, Building Communities",contact_email:"<EMAIL>",contact_phone:"+234 ************"})}finally{n(!1)}})()},[]),{settings:e,loading:a,error:l}}},51976:(e,r,a)=>{"use strict";a.d(r,{A:()=>s});let s=(0,a(19946).A)("Heart",[["path",{d:"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z",key:"c3ymky"}]])},53999:(e,r,a)=>{"use strict";a.d(r,{cn:()=>n});var s=a(52596),t=a(39688);function n(){for(var e=arguments.length,r=Array(e),a=0;a<e;a++)r[a]=arguments[a];return(0,t.QP)((0,s.$)(r))}},58247:(e,r,a)=>{"use strict";a.r(r),a.d(r,{default:()=>F});var s=a(95155),t=a(12115),n=a(6874),l=a.n(n),d=a(35695),o=a(97168),i=a(89852),c=a(82714),u=a(95139),m=a(88482),x=a(95784),g=a(99474),h=a(87949);let p=(0,a(19946).A)("Briefcase",[["path",{d:"M16 20V4a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16",key:"jecpp"}],["rect",{width:"20",height:"14",x:"2",y:"6",rx:"2",key:"i6l2r4"}]]);var f=a(17580),b=a(71007),v=a(28883),j=a(19420),y=a(4516),N=a(32919),k=a(78749),w=a(92657),C=a(51976),A=a(35169),S=a(31886),P=a(44531);function F(){let e=(0,d.useRouter)(),{settings:r}=(0,P.t)(),[a,n]=(0,t.useState)(!1),[F,R]=(0,t.useState)(!1),[_,z]=(0,t.useState)(!1),[J,L]=(0,t.useState)(null),[O,q]=(0,t.useState)({firstName:"",lastName:"",email:"",phone:"",password:"",confirmPassword:"",address:"",city:"",state:"",country:"",institution:"",course:"",yearOfStudy:"",studentId:"",organizationName:"",organizationType:"",position:"",website:"",skills:"",availability:"",experience:"",motivation:""}),[E,T]=(0,t.useState)(""),M=e=>{let{name:r,value:a}=e.target;q(e=>({...e,[r]:a}))},I=(e,r)=>{q(a=>({...a,[e]:r}))},D=()=>{switch(J){case"student":return{institution:O.institution,course:O.course,year_of_study:O.yearOfStudy,student_id:O.studentId};case"partner":return{organization_name:O.organizationName,organization_type:O.organizationType,position:O.position,website:O.website};case"volunteer":return{skills:O.skills,availability:O.availability,experience:O.experience,motivation:O.motivation};default:return null}},G=async a=>{if(a.preventDefault(),n(!0),O.password!==O.confirmPassword){alert("Passwords do not match"),n(!1);return}if(!J){alert("Please select a user type"),n(!1);return}try{let a=await fetch("".concat("http://localhost:8000","/api/v1/check-email"),{method:"POST",headers:{"Content-Type":"application/json",Accept:"application/json"},body:JSON.stringify({email:O.email})});if(a.ok&&(await a.json()).exists){alert("An account with this email already exists. Please use a different email or login to your existing account."),n(!1);return}let l={first_name:O.firstName,last_name:O.lastName,email:O.email,password:O.password,password_confirmation:O.confirmPassword,phone_number:O.phone,address:O.address,city:O.city,state:O.state,country:O.country,user_type:J,additional_data:J?D():null},d=await S.uE.register(l);if(d.success){var s,t;T(d.message||"Registration successful!"),alert(d.message||"Registration successful! Welcome to ".concat((null==r?void 0:r.app_name)||"our platform","!")),(null===(s=d.data)||void 0===s?void 0:s.token)&&localStorage.setItem("authToken",d.data.token),(null===(t=d.data)||void 0===t?void 0:t.user)&&localStorage.setItem("user",JSON.stringify(d.data.user)),e.push("/dashboard")}else alert(d.message||"Registration failed. Please try again."),d.errors&&console.error("Validation errors:",d.errors)}catch(e){console.error("Registration error:",e),alert("An unexpected error occurred. Please try again.")}finally{n(!1)}};return(0,s.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-green-50 to-green-100 dark:from-green-950 dark:to-green-900",children:[(0,s.jsx)("header",{className:"border-b border-green-200 dark:border-green-800 bg-white/80 dark:bg-gray-900/80 backdrop-blur-sm sticky top-0 z-40",children:(0,s.jsx)("div",{className:"container mx-auto px-4 py-4",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)(l(),{href:"/",className:"flex items-center gap-3 hover:opacity-80 transition-opacity",children:[(null==r?void 0:r.app_logo)?(0,s.jsx)("div",{className:"relative h-10 w-10 overflow-hidden rounded-full",children:(0,s.jsx)("img",{src:r.app_logo,alt:"".concat((null==r?void 0:r.app_name)||"Laravel NGO Foundation"," Logo"),className:"w-full h-full object-cover"})}):(0,s.jsx)("div",{className:"h-10 w-10 bg-gradient-to-br from-green-600 to-green-700 rounded-full flex items-center justify-center shadow-lg",children:(0,s.jsx)(C.A,{className:"h-6 w-6 text-white"})}),(0,s.jsx)("span",{className:"text-xl font-bold text-green-800 dark:text-green-200",children:(null==r?void 0:r.app_name)||"Laravel NGO Foundation"})]}),(0,s.jsx)(l(),{href:"/auth/login",children:(0,s.jsxs)(o.$,{variant:"outline",className:"border-green-300 text-green-600 hover:bg-green-50 dark:border-green-700 dark:text-green-400 dark:hover:bg-green-900/50",children:[(0,s.jsx)(A.A,{className:"h-4 w-4 mr-2"}),"Back to Login"]})})]})})}),(0,s.jsx)("main",{className:"container mx-auto px-4 py-8",children:(0,s.jsx)("div",{className:"max-w-2xl mx-auto",children:(0,s.jsxs)(m.Zp,{className:"shadow-2xl border-green-200 dark:border-green-800 rounded-3xl overflow-hidden backdrop-blur-sm bg-white/95 dark:bg-gray-900/95",children:[(0,s.jsxs)(m.aR,{className:"text-center pb-6 bg-gradient-to-r from-green-50 to-green-100 dark:from-green-900/50 dark:to-green-800/50",children:[(0,s.jsx)("div",{className:"flex justify-center mb-4",children:(null==r?void 0:r.app_logo)?(0,s.jsx)("div",{className:"relative h-16 w-16 overflow-hidden rounded-full shadow-lg animate-float",children:(0,s.jsx)("img",{src:r.app_logo,alt:"".concat((null==r?void 0:r.app_name)||"Laravel NGO Foundation"," Logo"),className:"w-full h-full object-cover"})}):(0,s.jsx)("div",{className:"relative h-16 w-16 overflow-hidden rounded-full bg-gradient-to-br from-green-500 to-green-700 shadow-lg animate-float",children:(0,s.jsx)(C.A,{className:"absolute inset-0 m-auto h-8 w-8 text-white drop-shadow-md"})})}),(0,s.jsxs)(m.ZB,{className:"text-3xl font-bold text-green-800 dark:text-green-200",children:["Join ",(null==r?void 0:r.app_name)||"Laravel NGO Foundation"]}),(0,s.jsx)(m.BT,{className:"text-green-600 dark:text-green-400",children:"Start your journey towards making a positive impact in communities"})]}),(0,s.jsx)(m.Wu,{children:J?(0,s.jsxs)("form",{onSubmit:G,className:"space-y-6",children:[(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(c.J,{htmlFor:"firstName",className:"text-green-800 dark:text-green-200",children:"First Name *"}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(b.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-green-500"}),(0,s.jsx)(i.p,{id:"firstName",name:"firstName",value:O.firstName,onChange:M,placeholder:"John",className:"pl-10 rounded-xl border-green-200 dark:border-green-700 focus:border-green-500",required:!0})]})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(c.J,{htmlFor:"lastName",className:"text-green-800 dark:text-green-200",children:"Last Name *"}),(0,s.jsx)(i.p,{id:"lastName",name:"lastName",value:O.lastName,onChange:M,placeholder:"Doe",className:"rounded-xl border-green-200 dark:border-green-700 focus:border-green-500",required:!0})]})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(c.J,{htmlFor:"email",className:"text-green-800 dark:text-green-200",children:"Email Address *"}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(v.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-green-500"}),(0,s.jsx)(i.p,{id:"email",name:"email",type:"email",value:O.email,onChange:M,placeholder:"<EMAIL>",className:"pl-10 rounded-xl border-green-200 dark:border-green-700 focus:border-green-500",required:!0})]})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(c.J,{htmlFor:"phone",className:"text-green-800 dark:text-green-200",children:"Phone Number *"}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(j.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-green-500"}),(0,s.jsx)(i.p,{id:"phone",name:"phone",type:"tel",value:O.phone,onChange:M,placeholder:"+234 ************",className:"pl-10 rounded-xl border-green-200 dark:border-green-700 focus:border-green-500",required:!0})]})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(c.J,{htmlFor:"address",className:"text-green-800 dark:text-green-200",children:"Address"}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(y.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-green-500"}),(0,s.jsx)(i.p,{id:"address",name:"address",value:O.address,onChange:M,placeholder:"Street address",className:"pl-10 rounded-xl border-green-200 dark:border-green-700 focus:border-green-500"})]})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(c.J,{htmlFor:"city",className:"text-green-800 dark:text-green-200",children:"City"}),(0,s.jsx)(i.p,{id:"city",name:"city",value:O.city,onChange:M,placeholder:"Lagos",className:"rounded-xl border-green-200 dark:border-green-700 focus:border-green-500"})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(c.J,{htmlFor:"state",className:"text-green-800 dark:text-green-200",children:"State"}),(0,s.jsxs)(x.l6,{onValueChange:e=>I("state",e),children:[(0,s.jsx)(x.bq,{className:"rounded-xl border-green-200 dark:border-green-700 focus:border-green-500",children:(0,s.jsx)(x.yv,{placeholder:"Select state"})}),(0,s.jsxs)(x.gC,{children:[(0,s.jsx)(x.eb,{value:"Lagos",children:"Lagos"}),(0,s.jsx)(x.eb,{value:"Abuja",children:"Abuja (FCT)"}),(0,s.jsx)(x.eb,{value:"Kano",children:"Kano"}),(0,s.jsx)(x.eb,{value:"Rivers",children:"Rivers"}),(0,s.jsx)(x.eb,{value:"Ogun",children:"Ogun"}),(0,s.jsx)(x.eb,{value:"Kaduna",children:"Kaduna"}),(0,s.jsx)(x.eb,{value:"Oyo",children:"Oyo"}),(0,s.jsx)(x.eb,{value:"Delta",children:"Delta"}),(0,s.jsx)(x.eb,{value:"Imo",children:"Imo"}),(0,s.jsx)(x.eb,{value:"Anambra",children:"Anambra"})]})]})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(c.J,{htmlFor:"country",className:"text-green-800 dark:text-green-200",children:"Country"}),(0,s.jsxs)(x.l6,{onValueChange:e=>I("country",e),children:[(0,s.jsx)(x.bq,{className:"rounded-xl border-green-200 dark:border-green-700 focus:border-green-500",children:(0,s.jsx)(x.yv,{placeholder:"Select country"})}),(0,s.jsxs)(x.gC,{children:[(0,s.jsx)(x.eb,{value:"Nigeria",children:"Nigeria"}),(0,s.jsx)(x.eb,{value:"Ghana",children:"Ghana"}),(0,s.jsx)(x.eb,{value:"Kenya",children:"Kenya"}),(0,s.jsx)(x.eb,{value:"South Africa",children:"South Africa"}),(0,s.jsx)(x.eb,{value:"Other",children:"Other"})]})]})]})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(c.J,{htmlFor:"password",className:"text-green-800 dark:text-green-200",children:"Password *"}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(N.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-green-500"}),(0,s.jsx)(i.p,{id:"password",name:"password",type:F?"text":"password",value:O.password,onChange:M,placeholder:"••••••••",className:"pl-10 pr-10 rounded-xl border-green-200 dark:border-green-700 focus:border-green-500",required:!0}),(0,s.jsx)("button",{type:"button",onClick:()=>R(!F),className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-green-500 hover:text-green-600",children:F?(0,s.jsx)(k.A,{className:"h-4 w-4"}):(0,s.jsx)(w.A,{className:"h-4 w-4"})})]})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(c.J,{htmlFor:"confirmPassword",className:"text-green-800 dark:text-green-200",children:"Confirm Password *"}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(N.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-green-500"}),(0,s.jsx)(i.p,{id:"confirmPassword",name:"confirmPassword",type:_?"text":"password",value:O.confirmPassword,onChange:M,placeholder:"••••••••",className:"pl-10 pr-10 rounded-xl border-green-200 dark:border-green-700 focus:border-green-500",required:!0}),(0,s.jsx)("button",{type:"button",onClick:()=>z(!_),className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-green-500 hover:text-green-600",children:_?(0,s.jsx)(k.A,{className:"h-4 w-4"}):(0,s.jsx)(w.A,{className:"h-4 w-4"})})]})]})]})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(c.J,{htmlFor:"address",className:"text-green-800 dark:text-green-200",children:"Address *"}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(y.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-green-500"}),(0,s.jsx)(g.T,{id:"address",name:"address",value:O.address,onChange:M,placeholder:"Your full address",className:"pl-10 rounded-xl border-green-200 dark:border-green-700 focus:border-green-500",rows:2,required:!0})]})]}),"student"===J&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(c.J,{htmlFor:"institution",className:"text-green-800 dark:text-green-200",children:"Educational Institution *"}),(0,s.jsx)(i.p,{id:"institution",name:"institution",value:O.institution,onChange:M,placeholder:"University of Lagos",className:"rounded-xl border-green-200 dark:border-green-700 focus:border-green-500",required:!0})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(c.J,{htmlFor:"course",className:"text-green-800 dark:text-green-200",children:"Course of Study *"}),(0,s.jsx)(i.p,{id:"course",name:"course",value:O.course,onChange:M,placeholder:"Computer Science",className:"rounded-xl border-green-200 dark:border-green-700 focus:border-green-500",required:!0})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(c.J,{htmlFor:"yearOfStudy",className:"text-green-800 dark:text-green-200",children:"Year of Study *"}),(0,s.jsxs)(x.l6,{onValueChange:e=>I("yearOfStudy",e),children:[(0,s.jsx)(x.bq,{className:"rounded-xl border-green-200 dark:border-green-700 focus:border-green-500",children:(0,s.jsx)(x.yv,{placeholder:"Select year"})}),(0,s.jsxs)(x.gC,{children:[(0,s.jsx)(x.eb,{value:"1",children:"1st Year"}),(0,s.jsx)(x.eb,{value:"2",children:"2nd Year"}),(0,s.jsx)(x.eb,{value:"3",children:"3rd Year"}),(0,s.jsx)(x.eb,{value:"4",children:"4th Year"}),(0,s.jsx)(x.eb,{value:"5",children:"5th Year"}),(0,s.jsx)(x.eb,{value:"graduate",children:"Graduate"})]})]})]})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(c.J,{htmlFor:"studentId",className:"text-green-800 dark:text-green-200",children:"Student ID Number"}),(0,s.jsx)(i.p,{id:"studentId",name:"studentId",value:O.studentId,onChange:M,placeholder:"STU/2024/001",className:"rounded-xl border-green-200 dark:border-green-700 focus:border-green-500"})]})]}),"partner"===J&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(c.J,{htmlFor:"organizationName",className:"text-green-800 dark:text-green-200",children:"Organization Name *"}),(0,s.jsx)(i.p,{id:"organizationName",name:"organizationName",value:O.organizationName,onChange:M,placeholder:"ABC Foundation",className:"rounded-xl border-green-200 dark:border-green-700 focus:border-green-500",required:!0})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(c.J,{htmlFor:"organizationType",className:"text-green-800 dark:text-green-200",children:"Organization Type *"}),(0,s.jsxs)(x.l6,{onValueChange:e=>I("organizationType",e),children:[(0,s.jsx)(x.bq,{className:"rounded-xl border-green-200 dark:border-green-700 focus:border-green-500",children:(0,s.jsx)(x.yv,{placeholder:"Select type"})}),(0,s.jsxs)(x.gC,{children:[(0,s.jsx)(x.eb,{value:"ngo",children:"NGO/Non-Profit"}),(0,s.jsx)(x.eb,{value:"corporate",children:"Corporate"}),(0,s.jsx)(x.eb,{value:"government",children:"Government Agency"}),(0,s.jsx)(x.eb,{value:"educational",children:"Educational Institution"}),(0,s.jsx)(x.eb,{value:"international",children:"International Organization"})]})]})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(c.J,{htmlFor:"position",className:"text-green-800 dark:text-green-200",children:"Your Position *"}),(0,s.jsx)(i.p,{id:"position",name:"position",value:O.position,onChange:M,placeholder:"Program Director",className:"rounded-xl border-green-200 dark:border-green-700 focus:border-green-500",required:!0})]})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(c.J,{htmlFor:"website",className:"text-green-800 dark:text-green-200",children:"Organization Website"}),(0,s.jsx)(i.p,{id:"website",name:"website",type:"url",value:O.website,onChange:M,placeholder:"https://www.organization.com",className:"rounded-xl border-green-200 dark:border-green-700 focus:border-green-500"})]})]}),"volunteer"===J&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(c.J,{htmlFor:"skills",className:"text-green-800 dark:text-green-200",children:"Skills & Expertise *"}),(0,s.jsx)(g.T,{id:"skills",name:"skills",value:O.skills,onChange:M,placeholder:"Teaching, IT support, project management, etc.",className:"rounded-xl border-green-200 dark:border-green-700 focus:border-green-500",rows:3,required:!0})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(c.J,{htmlFor:"availability",className:"text-green-800 dark:text-green-200",children:"Availability *"}),(0,s.jsxs)(x.l6,{onValueChange:e=>I("availability",e),children:[(0,s.jsx)(x.bq,{className:"rounded-xl border-green-200 dark:border-green-700 focus:border-green-500",children:(0,s.jsx)(x.yv,{placeholder:"Select availability"})}),(0,s.jsxs)(x.gC,{children:[(0,s.jsx)(x.eb,{value:"weekends",children:"Weekends only"}),(0,s.jsx)(x.eb,{value:"evenings",children:"Evenings (weekdays)"}),(0,s.jsx)(x.eb,{value:"flexible",children:"Flexible schedule"}),(0,s.jsx)(x.eb,{value:"full-time",children:"Full-time availability"}),(0,s.jsx)(x.eb,{value:"seasonal",children:"Seasonal/Project-based"})]})]})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(c.J,{htmlFor:"experience",className:"text-green-800 dark:text-green-200",children:"Previous Volunteer Experience"}),(0,s.jsx)(g.T,{id:"experience",name:"experience",value:O.experience,onChange:M,placeholder:"Describe any previous volunteer work or community involvement...",className:"rounded-xl border-green-200 dark:border-green-700 focus:border-green-500",rows:3})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(c.J,{htmlFor:"motivation",className:"text-green-800 dark:text-green-200",children:"Why do you want to volunteer with us? *"}),(0,s.jsx)(g.T,{id:"motivation",name:"motivation",value:O.motivation,onChange:M,placeholder:"Share your motivation for joining our mission...",className:"rounded-xl border-green-200 dark:border-green-700 focus:border-green-500",rows:4,required:!0})]})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)(u.S,{id:"terms",required:!0,className:"border-green-300 data-[state=checked]:bg-green-600 data-[state=checked]:border-green-600"}),(0,s.jsxs)(c.J,{htmlFor:"terms",className:"text-sm font-normal text-green-700 dark:text-green-300",children:["I agree to the"," ",(0,s.jsx)(l(),{href:"/terms",className:"text-green-600 hover:text-green-700 dark:text-green-400 dark:hover:text-green-300 underline",children:"Terms of Service"})," ","and"," ",(0,s.jsx)(l(),{href:"/privacy",className:"text-green-600 hover:text-green-700 dark:text-green-400 dark:hover:text-green-300 underline",children:"Privacy Policy"})]})]}),(0,s.jsxs)("div",{className:"flex gap-3",children:[(0,s.jsx)(o.$,{type:"button",variant:"outline",onClick:()=>L(null),className:"flex-1 border-green-600 text-green-600 hover:bg-green-50 dark:border-green-400 dark:text-green-400",children:"Back"}),(0,s.jsx)(o.$,{type:"submit",disabled:a,className:"flex-1 bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 text-white py-3 font-semibold",children:a?(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)("div",{className:"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"}),"Creating Account..."]}):"Create Account"})]})]}):(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("h3",{className:"text-xl font-semibold text-green-800 dark:text-green-200 mb-2",children:"How would you like to join us?"}),(0,s.jsx)("p",{className:"text-green-600 dark:text-green-400 text-sm",children:"Select your role to customize your registration experience"})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 gap-4",children:[(0,s.jsx)(m.Zp,{className:"cursor-pointer transition-all duration-200 hover:shadow-lg ".concat("student"===J?"border-green-500 bg-green-50 dark:bg-green-900/20":"border-green-200 dark:border-green-700 hover:border-green-300"),onClick:()=>L("student"),children:(0,s.jsxs)(m.Wu,{className:"flex items-center gap-4 p-4",children:[(0,s.jsx)("div",{className:"h-12 w-12 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center",children:(0,s.jsx)(h.A,{className:"h-6 w-6 text-green-600 dark:text-green-400"})}),(0,s.jsxs)("div",{className:"flex-1",children:[(0,s.jsx)("h4",{className:"font-semibold text-green-800 dark:text-green-200",children:"Student"}),(0,s.jsx)("p",{className:"text-sm text-green-600 dark:text-green-400",children:"Apply for scholarships and access educational resources"})]})]})}),(0,s.jsx)(m.Zp,{className:"cursor-pointer transition-all duration-200 hover:shadow-lg ".concat("partner"===J?"border-green-500 bg-green-50 dark:bg-green-900/20":"border-green-200 dark:border-green-700 hover:border-green-300"),onClick:()=>L("partner"),children:(0,s.jsxs)(m.Wu,{className:"flex items-center gap-4 p-4",children:[(0,s.jsx)("div",{className:"h-12 w-12 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center",children:(0,s.jsx)(p,{className:"h-6 w-6 text-green-600 dark:text-green-400"})}),(0,s.jsxs)("div",{className:"flex-1",children:[(0,s.jsx)("h4",{className:"font-semibold text-green-800 dark:text-green-200",children:"Partner Organization"}),(0,s.jsx)("p",{className:"text-sm text-green-600 dark:text-green-400",children:"Collaborate with us to expand educational opportunities"})]})]})}),(0,s.jsx)(m.Zp,{className:"cursor-pointer transition-all duration-200 hover:shadow-lg ".concat("volunteer"===J?"border-green-500 bg-green-50 dark:bg-green-900/20":"border-green-200 dark:border-green-700 hover:border-green-300"),onClick:()=>L("volunteer"),children:(0,s.jsxs)(m.Wu,{className:"flex items-center gap-4 p-4",children:[(0,s.jsx)("div",{className:"h-12 w-12 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center",children:(0,s.jsx)(f.A,{className:"h-6 w-6 text-green-600 dark:text-green-400"})}),(0,s.jsxs)("div",{className:"flex-1",children:[(0,s.jsx)("h4",{className:"font-semibold text-green-800 dark:text-green-200",children:"Volunteer"}),(0,s.jsx)("p",{className:"text-sm text-green-600 dark:text-green-400",children:"Contribute your time and skills to support our mission"})]})]})})]}),J&&(0,s.jsxs)(o.$,{onClick:()=>{},className:"w-full bg-green-600 hover:bg-green-700 text-white",children:["Continue as ",J.charAt(0).toUpperCase()+J.slice(1)]})]})}),(0,s.jsxs)("div",{className:"px-6 pb-6",children:[(0,s.jsx)("div",{className:"text-center",children:(0,s.jsxs)("p",{className:"text-sm text-green-600 dark:text-green-400",children:["Already have an account?"," ",(0,s.jsx)(l(),{href:"/auth/login",className:"text-green-700 hover:text-green-800 dark:text-green-300 dark:hover:text-green-200 font-medium underline",children:"Sign in here"})]})}),(0,s.jsx)("div",{className:"flex justify-center mt-4",children:(0,s.jsxs)(l(),{href:"/",className:"flex items-center text-sm text-green-600 hover:text-green-700 dark:text-green-400 dark:hover:text-green-300 transition-colors duration-200 group",children:[(0,s.jsx)(A.A,{className:"mr-2 h-4 w-4 group-hover:-translate-x-1 transition-transform duration-200"}),"Back to home"]})})]})]})})})]})}},71007:(e,r,a)=>{"use strict";a.d(r,{A:()=>s});let s=(0,a(19946).A)("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},76981:(e,r,a)=>{"use strict";a.d(r,{C1:()=>C,bL:()=>w});var s=a(12115),t=a(6101),n=a(46081),l=a(85185),d=a(5845),o=a(67884),i=a(11275),c=a(28905),u=a(63655),m=a(95155),x="Checkbox",[g,h]=(0,n.A)(x),[p,f]=g(x),b=s.forwardRef((e,r)=>{let{__scopeCheckbox:a,name:n,checked:o,defaultChecked:i,required:c,disabled:x,value:g="on",onCheckedChange:h,form:f,...b}=e,[v,j]=s.useState(null),w=(0,t.s)(r,e=>j(e)),C=s.useRef(!1),A=!v||f||!!v.closest("form"),[S=!1,P]=(0,d.i)({prop:o,defaultProp:i,onChange:h}),F=s.useRef(S);return s.useEffect(()=>{let e=null==v?void 0:v.form;if(e){let r=()=>P(F.current);return e.addEventListener("reset",r),()=>e.removeEventListener("reset",r)}},[v,P]),(0,m.jsxs)(p,{scope:a,state:S,disabled:x,children:[(0,m.jsx)(u.sG.button,{type:"button",role:"checkbox","aria-checked":N(S)?"mixed":S,"aria-required":c,"data-state":k(S),"data-disabled":x?"":void 0,disabled:x,value:g,...b,ref:w,onKeyDown:(0,l.m)(e.onKeyDown,e=>{"Enter"===e.key&&e.preventDefault()}),onClick:(0,l.m)(e.onClick,e=>{P(e=>!!N(e)||!e),A&&(C.current=e.isPropagationStopped(),C.current||e.stopPropagation())})}),A&&(0,m.jsx)(y,{control:v,bubbles:!C.current,name:n,value:g,checked:S,required:c,disabled:x,form:f,style:{transform:"translateX(-100%)"},defaultChecked:!N(i)&&i})]})});b.displayName=x;var v="CheckboxIndicator",j=s.forwardRef((e,r)=>{let{__scopeCheckbox:a,forceMount:s,...t}=e,n=f(v,a);return(0,m.jsx)(c.C,{present:s||N(n.state)||!0===n.state,children:(0,m.jsx)(u.sG.span,{"data-state":k(n.state),"data-disabled":n.disabled?"":void 0,...t,ref:r,style:{pointerEvents:"none",...e.style}})})});j.displayName=v;var y=e=>{let{control:r,checked:a,bubbles:t=!0,defaultChecked:n,...l}=e,d=s.useRef(null),c=(0,o.Z)(a),u=(0,i.X)(r);s.useEffect(()=>{let e=d.current,r=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(c!==a&&r){let s=new Event("click",{bubbles:t});e.indeterminate=N(a),r.call(e,!N(a)&&a),e.dispatchEvent(s)}},[c,a,t]);let x=s.useRef(!N(a)&&a);return(0,m.jsx)("input",{type:"checkbox","aria-hidden":!0,defaultChecked:null!=n?n:x.current,...l,tabIndex:-1,ref:d,style:{...e.style,...u,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})};function N(e){return"indeterminate"===e}function k(e){return N(e)?"indeterminate":e?"checked":"unchecked"}var w=b,C=j},78749:(e,r,a)=>{"use strict";a.d(r,{A:()=>s});let s=(0,a(19946).A)("EyeOff",[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]])},82714:(e,r,a)=>{"use strict";a.d(r,{J:()=>i});var s=a(95155),t=a(12115),n=a(40968),l=a(74466),d=a(53999);let o=(0,l.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),i=t.forwardRef((e,r)=>{let{className:a,...t}=e;return(0,s.jsx)(n.b,{ref:r,className:(0,d.cn)(o(),a),...t})});i.displayName=n.b.displayName},87949:(e,r,a)=>{"use strict";a.d(r,{A:()=>s});let s=(0,a(19946).A)("GraduationCap",[["path",{d:"M21.42 10.922a1 1 0 0 0-.019-1.838L12.83 5.18a2 2 0 0 0-1.66 0L2.6 9.08a1 1 0 0 0 0 1.832l8.57 3.908a2 2 0 0 0 1.66 0z",key:"j76jl0"}],["path",{d:"M22 10v6",key:"1lu8f3"}],["path",{d:"M6 12.5V16a6 3 0 0 0 12 0v-3.5",key:"1r8lef"}]])},88482:(e,r,a)=>{"use strict";a.d(r,{BT:()=>i,Wu:()=>c,ZB:()=>o,Zp:()=>l,aR:()=>d,wL:()=>u});var s=a(95155),t=a(12115),n=a(53999);let l=t.forwardRef((e,r)=>{let{className:a,...t}=e;return(0,s.jsx)("div",{ref:r,className:(0,n.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",a),...t})});l.displayName="Card";let d=t.forwardRef((e,r)=>{let{className:a,...t}=e;return(0,s.jsx)("div",{ref:r,className:(0,n.cn)("flex flex-col space-y-1.5 p-6",a),...t})});d.displayName="CardHeader";let o=t.forwardRef((e,r)=>{let{className:a,...t}=e;return(0,s.jsx)("div",{ref:r,className:(0,n.cn)("text-2xl font-semibold leading-none tracking-tight",a),...t})});o.displayName="CardTitle";let i=t.forwardRef((e,r)=>{let{className:a,...t}=e;return(0,s.jsx)("div",{ref:r,className:(0,n.cn)("text-sm text-muted-foreground",a),...t})});i.displayName="CardDescription";let c=t.forwardRef((e,r)=>{let{className:a,...t}=e;return(0,s.jsx)("div",{ref:r,className:(0,n.cn)("p-6 pt-0",a),...t})});c.displayName="CardContent";let u=t.forwardRef((e,r)=>{let{className:a,...t}=e;return(0,s.jsx)("div",{ref:r,className:(0,n.cn)("flex items-center p-6 pt-0",a),...t})});u.displayName="CardFooter"},89852:(e,r,a)=>{"use strict";a.d(r,{p:()=>l});var s=a(95155),t=a(12115),n=a(53999);let l=t.forwardRef((e,r)=>{let{className:a,type:t,...l}=e;return(0,s.jsx)("input",{type:t,className:(0,n.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",a),ref:r,suppressHydrationWarning:!0,...l})});l.displayName="Input"},92657:(e,r,a)=>{"use strict";a.d(r,{A:()=>s});let s=(0,a(19946).A)("Eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},95139:(e,r,a)=>{"use strict";a.d(r,{S:()=>o});var s=a(95155),t=a(12115),n=a(76981),l=a(5196),d=a(53999);let o=t.forwardRef((e,r)=>{let{className:a,...t}=e;return(0,s.jsx)(n.bL,{ref:r,className:(0,d.cn)("peer h-4 w-4 shrink-0 rounded-sm border border-primary ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground",a),suppressHydrationWarning:!0,...t,children:(0,s.jsx)(n.C1,{className:(0,d.cn)("flex items-center justify-center text-current"),children:(0,s.jsx)(l.A,{className:"h-4 w-4"})})})});o.displayName=n.bL.displayName},95784:(e,r,a)=>{"use strict";a.d(r,{bq:()=>m,eb:()=>p,gC:()=>h,l6:()=>c,yv:()=>u});var s=a(95155),t=a(12115),n=a(38715),l=a(66474),d=a(47863),o=a(5196),i=a(53999);let c=n.bL;n.YJ;let u=n.WT,m=t.forwardRef((e,r)=>{let{className:a,children:t,...d}=e;return(0,s.jsxs)(n.l9,{ref:r,className:(0,i.cn)("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",a),...d,children:[t,(0,s.jsx)(n.In,{asChild:!0,children:(0,s.jsx)(l.A,{className:"h-4 w-4 opacity-50"})})]})});m.displayName=n.l9.displayName;let x=t.forwardRef((e,r)=>{let{className:a,...t}=e;return(0,s.jsx)(n.PP,{ref:r,className:(0,i.cn)("flex cursor-default items-center justify-center py-1",a),...t,children:(0,s.jsx)(d.A,{className:"h-4 w-4"})})});x.displayName=n.PP.displayName;let g=t.forwardRef((e,r)=>{let{className:a,...t}=e;return(0,s.jsx)(n.wn,{ref:r,className:(0,i.cn)("flex cursor-default items-center justify-center py-1",a),...t,children:(0,s.jsx)(l.A,{className:"h-4 w-4"})})});g.displayName=n.wn.displayName;let h=t.forwardRef((e,r)=>{let{className:a,children:t,position:l="popper",...d}=e;return(0,s.jsx)(n.ZL,{children:(0,s.jsxs)(n.UC,{ref:r,className:(0,i.cn)("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===l&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",a),position:l,...d,children:[(0,s.jsx)(x,{}),(0,s.jsx)(n.LM,{className:(0,i.cn)("p-1","popper"===l&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:t}),(0,s.jsx)(g,{})]})})});h.displayName=n.UC.displayName,t.forwardRef((e,r)=>{let{className:a,...t}=e;return(0,s.jsx)(n.JU,{ref:r,className:(0,i.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",a),...t})}).displayName=n.JU.displayName;let p=t.forwardRef((e,r)=>{let{className:a,children:t,...l}=e;return(0,s.jsxs)(n.q7,{ref:r,className:(0,i.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",a),...l,children:[(0,s.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,s.jsx)(n.VF,{children:(0,s.jsx)(o.A,{className:"h-4 w-4"})})}),(0,s.jsx)(n.p4,{children:t})]})});p.displayName=n.q7.displayName,t.forwardRef((e,r)=>{let{className:a,...t}=e;return(0,s.jsx)(n.wv,{ref:r,className:(0,i.cn)("-mx-1 my-1 h-px bg-muted",a),...t})}).displayName=n.wv.displayName},97168:(e,r,a)=>{"use strict";a.d(r,{$:()=>i,r:()=>o});var s=a(95155),t=a(12115),n=a(99708),l=a(74466),d=a(53999);let o=(0,l.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-all duration-200 ease-in-out focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 transform hover:scale-105",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90 hover:brightness-110",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90 hover:brightness-110",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground hover:brightness-105",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80 hover:brightness-105",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),i=t.forwardRef((e,r)=>{let{className:a,variant:t,size:l,asChild:i=!1,...c}=e,u=i?n.DX:"button";return(0,s.jsx)(u,{className:(0,d.cn)(o({variant:t,size:l,className:a})),ref:r,suppressHydrationWarning:!0,...c})});i.displayName="Button"},99474:(e,r,a)=>{"use strict";a.d(r,{T:()=>l});var s=a(95155),t=a(12115),n=a(53999);let l=t.forwardRef((e,r)=>{let{className:a,...t}=e;return(0,s.jsx)("textarea",{className:(0,n.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",a),ref:r,...t})});l.displayName="Textarea"}},e=>{var r=r=>e(e.s=r);e.O(0,[1778,6874,598,4057,461,1886,8441,1684,7358],()=>r(37841)),_N_E=e.O()}]);