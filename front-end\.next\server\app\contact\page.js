(()=>{var e={};e.id=977,e.ids=[977],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5336:(e,r,a)=>{"use strict";a.d(r,{A:()=>s});let s=(0,a(62688).A)("CircleCheckBig",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},7710:(e,r,a)=>{"use strict";a.r(r),a.d(r,{default:()=>s});let s=(0,a(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\contact\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\laragon\\www\\laravel-api-ngo\\front-end\\app\\contact\\page.tsx","default")},7884:(e,r,a)=>{"use strict";a.r(r),a.d(r,{default:()=>y});var s=a(60687),t=a(43210),n=a(24934),l=a(68988),d=a(39390),o=a(15616),i=a(55192),c=a(59821),x=a(58887),m=a(5336),g=a(27900),u=a(97992),h=a(48340),p=a(41550),b=a(48730),f=a(19526),j=a(72575),v=a(66232),N=a(98876),w=a(67760),k=a(40093);function y(){let{settings:e,loading:r}=(0,k.t)(),[a,y]=(0,t.useState)({name:"",email:"",subject:"",message:""}),[A,C]=(0,t.useState)(!1),[_,P]=(0,t.useState)(!1),M=e?.app_name||"Laravel NGO Foundation",R=e?.contact_email||"<EMAIL>",S=e?.contact_phone||"+234 ************",F=e?.organization_address,q=e?.organization_city,z=e?.organization_state,Z=[F,q,z,e?.organization_country,e?.organization_postal_code].filter(Boolean).join(", "),E=e=>{let{name:r,value:a}=e.target;y(e=>({...e,[r]:a}))},T=async e=>{e.preventDefault(),C(!0),setTimeout(()=>{C(!1),P(!0),y({name:"",email:"",subject:"",message:""})},2e3)};return(0,s.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-green-50 to-green-100 dark:from-green-950 dark:to-green-900",children:(0,s.jsxs)("main",{className:"pt-20",children:[(0,s.jsx)("section",{className:"py-16 px-4",children:(0,s.jsxs)("div",{className:"max-w-4xl mx-auto text-center",children:[(0,s.jsx)("div",{className:"flex justify-center mb-6",children:e?.app_logo?(0,s.jsx)("div",{className:"relative h-20 w-20 overflow-hidden rounded-full shadow-lg",children:(0,s.jsx)("img",{src:e.app_logo,alt:`${M} Logo`,className:"w-full h-full object-cover"})}):(0,s.jsx)("div",{className:"relative h-20 w-20 overflow-hidden rounded-full bg-gradient-to-br from-green-500 to-green-700 shadow-lg",children:(0,s.jsx)(x.A,{className:"absolute inset-0 m-auto h-10 w-10 text-white"})})}),(0,s.jsx)("h1",{className:"text-4xl md:text-5xl font-bold text-green-800 dark:text-green-200 mb-6",children:"Get in Touch"}),(0,s.jsx)("p",{className:"text-xl text-green-700 dark:text-green-300 max-w-2xl mx-auto",children:"We'd love to hear from you. Send us a message and we'll respond as soon as possible."})]})}),(0,s.jsx)("div",{className:"max-w-7xl mx-auto px-4 pb-20",children:(0,s.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[(0,s.jsx)("div",{className:"lg:col-span-2",children:(0,s.jsxs)(i.Zp,{className:"shadow-xl border-green-200 dark:border-green-800",children:[(0,s.jsxs)(i.aR,{children:[(0,s.jsx)(i.ZB,{className:"text-2xl text-green-800 dark:text-green-200",children:"Send us a Message"}),(0,s.jsx)(i.BT,{className:"text-green-600 dark:text-green-400",children:"Fill out the form below and we'll get back to you within 24 hours."})]}),(0,s.jsx)(i.Wu,{children:_?(0,s.jsxs)("div",{className:"text-center py-8",children:[(0,s.jsx)(m.A,{className:"h-16 w-16 text-green-600 mx-auto mb-4"}),(0,s.jsx)("h3",{className:"text-xl font-semibold text-green-800 dark:text-green-200 mb-2",children:"Message Sent Successfully!"}),(0,s.jsx)("p",{className:"text-green-600 dark:text-green-400",children:"Thank you for reaching out. We'll get back to you soon."}),(0,s.jsx)(n.$,{onClick:()=>P(!1),className:"mt-4 bg-green-600 hover:bg-green-700",children:"Send Another Message"})]}):(0,s.jsxs)("form",{onSubmit:T,className:"space-y-6",children:[(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(d.J,{htmlFor:"name",className:"text-green-800 dark:text-green-200",children:"Full Name *"}),(0,s.jsx)(l.p,{id:"name",name:"name",value:a.name,onChange:E,placeholder:"Your full name",className:"border-green-200 dark:border-green-700 focus:border-green-500",required:!0})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(d.J,{htmlFor:"email",className:"text-green-800 dark:text-green-200",children:"Email Address *"}),(0,s.jsx)(l.p,{id:"email",name:"email",type:"email",value:a.email,onChange:E,placeholder:"<EMAIL>",className:"border-green-200 dark:border-green-700 focus:border-green-500",required:!0})]})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(d.J,{htmlFor:"subject",className:"text-green-800 dark:text-green-200",children:"Subject *"}),(0,s.jsx)(l.p,{id:"subject",name:"subject",value:a.subject,onChange:E,placeholder:"What is this regarding?",className:"border-green-200 dark:border-green-700 focus:border-green-500",required:!0})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(d.J,{htmlFor:"message",className:"text-green-800 dark:text-green-200",children:"Message *"}),(0,s.jsx)(o.T,{id:"message",name:"message",value:a.message,onChange:E,placeholder:"Tell us more about your inquiry...",rows:6,className:"border-green-200 dark:border-green-700 focus:border-green-500",required:!0})]}),(0,s.jsx)(n.$,{type:"submit",disabled:A,className:"w-full bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 text-white py-3",children:A?(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)("div",{className:"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"}),"Sending Message..."]}):(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(g.A,{className:"h-4 w-4"}),"Send Message"]})})]})})]})}),(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)(i.Zp,{className:"shadow-xl border-green-200 dark:border-green-800",children:[(0,s.jsx)(i.aR,{children:(0,s.jsx)(i.ZB,{className:"text-xl text-green-800 dark:text-green-200",children:"Office Information"})}),(0,s.jsxs)(i.Wu,{className:"space-y-4",children:[Z&&(0,s.jsxs)("div",{className:"flex items-start gap-3",children:[(0,s.jsx)(u.A,{className:"h-5 w-5 text-green-600 mt-1 flex-shrink-0"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"font-medium text-green-800 dark:text-green-200",children:"Address"}),(0,s.jsx)("p",{className:"text-green-600 dark:text-green-400 text-sm",children:Z})]})]}),(0,s.jsxs)("div",{className:"flex items-start gap-3",children:[(0,s.jsx)(h.A,{className:"h-5 w-5 text-green-600 mt-1 flex-shrink-0"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"font-medium text-green-800 dark:text-green-200",children:"Phone"}),(0,s.jsx)("a",{href:`tel:${S}`,className:"text-green-600 dark:text-green-400 text-sm hover:text-green-700 transition-colors",children:S})]})]}),(0,s.jsxs)("div",{className:"flex items-start gap-3",children:[(0,s.jsx)(p.A,{className:"h-5 w-5 text-green-600 mt-1 flex-shrink-0"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"font-medium text-green-800 dark:text-green-200",children:"Email"}),(0,s.jsx)("a",{href:`mailto:${R}`,className:"text-green-600 dark:text-green-400 text-sm hover:text-green-700 transition-colors",children:R})]})]}),(0,s.jsxs)("div",{className:"flex items-start gap-3",children:[(0,s.jsx)(b.A,{className:"h-5 w-5 text-green-600 mt-1 flex-shrink-0"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"font-medium text-green-800 dark:text-green-200",children:"Office Hours"}),(0,s.jsxs)("p",{className:"text-green-600 dark:text-green-400 text-sm",children:["Monday - Friday: 9:00 AM - 5:00 PM",(0,s.jsx)("br",{}),"Saturday: 10:00 AM - 2:00 PM",(0,s.jsx)("br",{}),"Sunday: Closed"]})]})]})]})]}),(0,s.jsxs)(i.Zp,{className:"shadow-xl border-green-200 dark:border-green-800",children:[(0,s.jsx)(i.aR,{children:(0,s.jsx)(i.ZB,{className:"text-xl text-green-800 dark:text-green-200",children:"Quick Contact"})}),(0,s.jsx)(i.Wu,{className:"space-y-3",children:(0,s.jsxs)("div",{className:"grid grid-cols-2 gap-3",children:[(0,s.jsxs)(n.$,{variant:"outline",className:"border-green-200 hover:bg-green-50 dark:border-green-700 dark:hover:bg-green-900/20",children:[(0,s.jsx)(h.A,{className:"h-4 w-4 mr-2"}),"Call Us"]}),(0,s.jsxs)(n.$,{variant:"outline",className:"border-green-200 hover:bg-green-50 dark:border-green-700 dark:hover:bg-green-900/20",children:[(0,s.jsx)(p.A,{className:"h-4 w-4 mr-2"}),"Email Us"]})]})})]}),(0,s.jsxs)(i.Zp,{className:"shadow-xl border-green-200 dark:border-green-800",children:[(0,s.jsx)(i.aR,{children:(0,s.jsx)(i.ZB,{className:"text-xl text-green-800 dark:text-green-200",children:"Follow Us"})}),(0,s.jsx)(i.Wu,{children:(0,s.jsxs)("div",{className:"flex gap-3",children:[(0,s.jsx)(n.$,{size:"icon",variant:"outline",className:"border-green-200 hover:bg-green-50 dark:border-green-700 dark:hover:bg-green-900/20",children:(0,s.jsx)(f.A,{className:"h-4 w-4 text-blue-600"})}),(0,s.jsx)(n.$,{size:"icon",variant:"outline",className:"border-green-200 hover:bg-green-50 dark:border-green-700 dark:hover:bg-green-900/20",children:(0,s.jsx)(j.A,{className:"h-4 w-4 text-blue-400"})}),(0,s.jsx)(n.$,{size:"icon",variant:"outline",className:"border-green-200 hover:bg-green-50 dark:border-green-700 dark:hover:bg-green-900/20",children:(0,s.jsx)(v.A,{className:"h-4 w-4 text-pink-600"})}),(0,s.jsx)(n.$,{size:"icon",variant:"outline",className:"border-green-200 hover:bg-green-50 dark:border-green-700 dark:hover:bg-green-900/20",children:(0,s.jsx)(N.A,{className:"h-4 w-4 text-blue-700"})})]})})]}),(0,s.jsxs)(i.Zp,{className:"shadow-xl border-amber-200 dark:border-amber-800 bg-amber-50 dark:bg-amber-950/20",children:[(0,s.jsx)(i.aR,{children:(0,s.jsxs)(i.ZB,{className:"text-xl text-amber-800 dark:text-amber-200 flex items-center gap-2",children:[(0,s.jsx)(w.A,{className:"h-5 w-5"}),"Emergency Support"]})}),(0,s.jsxs)(i.Wu,{children:[(0,s.jsx)("p",{className:"text-amber-700 dark:text-amber-300 text-sm mb-3",children:"For urgent educational support or emergency assistance:"}),(0,s.jsx)(c.E,{variant:"secondary",className:"bg-amber-200 text-amber-800 dark:bg-amber-800 dark:text-amber-200",children:"24/7 Hotline: +234 800 KOFA (5632)"})]})]})]})]})})]})})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},15616:(e,r,a)=>{"use strict";a.d(r,{T:()=>l});var s=a(60687),t=a(43210),n=a(96241);let l=t.forwardRef(({className:e,...r},a)=>(0,s.jsx)("textarea",{className:(0,n.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",e),ref:a,...r}));l.displayName="Textarea"},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},27900:(e,r,a)=>{"use strict";a.d(r,{A:()=>s});let s=(0,a(62688).A)("Send",[["path",{d:"M14.536 21.686a.5.5 0 0 0 .937-.024l6.5-19a.496.496 0 0 0-.635-.635l-19 6.5a.5.5 0 0 0-.024.937l7.93 3.18a2 2 0 0 1 1.112 1.11z",key:"1ffxy3"}],["path",{d:"m21.854 2.147-10.94 10.939",key:"12cjpa"}]])},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33170:(e,r,a)=>{Promise.resolve().then(a.bind(a,7884))},33727:(e,r,a)=>{"use strict";a.r(r),a.d(r,{GlobalError:()=>l.a,__next_app__:()=>x,pages:()=>c,routeModule:()=>m,tree:()=>i});var s=a(65239),t=a(48088),n=a(88170),l=a.n(n),d=a(30893),o={};for(let e in d)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>d[e]);a.d(r,o);let i={children:["",{children:["contact",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,7710)),"C:\\laragon\\www\\laravel-api-ngo\\front-end\\app\\contact\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(a.bind(a,58014)),"C:\\laragon\\www\\laravel-api-ngo\\front-end\\app\\layout.tsx"],error:[()=>Promise.resolve().then(a.bind(a,52608)),"C:\\laragon\\www\\laravel-api-ngo\\front-end\\app\\error.tsx"],loading:[()=>Promise.resolve().then(a.bind(a,99766)),"C:\\laragon\\www\\laravel-api-ngo\\front-end\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(a.bind(a,82366)),"C:\\laragon\\www\\laravel-api-ngo\\front-end\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(a.t.bind(a,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(a.t.bind(a,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["C:\\laragon\\www\\laravel-api-ngo\\front-end\\app\\contact\\page.tsx"],x={require:a,loadChunk:()=>Promise.resolve()},m=new s.AppPageRouteModule({definition:{kind:t.RouteKind.APP_PAGE,page:"/contact/page",pathname:"/contact",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:i}})},33873:e=>{"use strict";e.exports=require("path")},39390:(e,r,a)=>{"use strict";a.d(r,{J:()=>i});var s=a(60687),t=a(43210),n=a(78148),l=a(24224),d=a(96241);let o=(0,l.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),i=t.forwardRef(({className:e,...r},a)=>(0,s.jsx)(n.b,{ref:a,className:(0,d.cn)(o(),e),...r}));i.displayName=n.b.displayName},48730:(e,r,a)=>{"use strict";a.d(r,{A:()=>s});let s=(0,a(62688).A)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},55192:(e,r,a)=>{"use strict";a.d(r,{BT:()=>i,Wu:()=>c,ZB:()=>o,Zp:()=>l,aR:()=>d,wL:()=>x});var s=a(60687),t=a(43210),n=a(96241);let l=t.forwardRef(({className:e,...r},a)=>(0,s.jsx)("div",{ref:a,className:(0,n.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",e),...r}));l.displayName="Card";let d=t.forwardRef(({className:e,...r},a)=>(0,s.jsx)("div",{ref:a,className:(0,n.cn)("flex flex-col space-y-1.5 p-6",e),...r}));d.displayName="CardHeader";let o=t.forwardRef(({className:e,...r},a)=>(0,s.jsx)("div",{ref:a,className:(0,n.cn)("text-2xl font-semibold leading-none tracking-tight",e),...r}));o.displayName="CardTitle";let i=t.forwardRef(({className:e,...r},a)=>(0,s.jsx)("div",{ref:a,className:(0,n.cn)("text-sm text-muted-foreground",e),...r}));i.displayName="CardDescription";let c=t.forwardRef(({className:e,...r},a)=>(0,s.jsx)("div",{ref:a,className:(0,n.cn)("p-6 pt-0",e),...r}));c.displayName="CardContent";let x=t.forwardRef(({className:e,...r},a)=>(0,s.jsx)("div",{ref:a,className:(0,n.cn)("flex items-center p-6 pt-0",e),...r}));x.displayName="CardFooter"},59821:(e,r,a)=>{"use strict";a.d(r,{E:()=>d});var s=a(60687);a(43210);var t=a(24224),n=a(96241);let l=(0,t.F)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function d({className:e,variant:r,...a}){return(0,s.jsx)("div",{className:(0,n.cn)(l({variant:r}),e),...a})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},70122:(e,r,a)=>{Promise.resolve().then(a.bind(a,7710))},78148:(e,r,a)=>{"use strict";a.d(r,{b:()=>d});var s=a(43210),t=a(14163),n=a(60687),l=s.forwardRef((e,r)=>(0,n.jsx)(t.sG.label,{...e,ref:r,onMouseDown:r=>{r.target.closest("button, input, select, textarea")||(e.onMouseDown?.(r),!r.defaultPrevented&&r.detail>1&&r.preventDefault())}}));l.displayName="Label";var d=l}};var r=require("../../webpack-runtime.js");r.C(e);var a=e=>r(r.s=e),s=r.X(0,[555,702],()=>a(33727));module.exports=s})();