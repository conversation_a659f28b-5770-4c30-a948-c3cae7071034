"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/contact/page",{

/***/ "(app-pages-browser)/./lib/api.ts":
/*!********************!*\
  !*** ./lib/api.ts ***!
  \********************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ApiClient: () => (/* binding */ ApiClient),\n/* harmony export */   apiClient: () => (/* binding */ apiClient),\n/* harmony export */   extractArrayData: () => (/* binding */ extractArrayData),\n/* harmony export */   getCurrentUser: () => (/* binding */ getCurrentUser),\n/* harmony export */   isAuthenticated: () => (/* binding */ isAuthenticated),\n/* harmony export */   logout: () => (/* binding */ logout)\n/* harmony export */ });\nconst API_BASE_URL = \"http://localhost:8000/api/v1\" || 0;\n// Utility function to extract array data from paginated or direct responses\nconst extractArrayData = (response)=>{\n    if (!response || !response.data) return [];\n    // If data is already an array, return it\n    if (Array.isArray(response.data)) {\n        return response.data;\n    }\n    // If data has a data property (paginated response), return that array\n    if (response.data.data && Array.isArray(response.data.data)) {\n        return response.data.data;\n    }\n    // Default to empty array\n    return [];\n};\nclass ApiClient {\n    async request(endpoint) {\n        let options = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};\n        // Use the base URL directly if it already contains /api/v1, otherwise add it\n        const url = this.baseURL.includes('/api/v1') ? \"\".concat(this.baseURL).concat(endpoint) : \"\".concat(this.baseURL, \"/api/v1\").concat(endpoint);\n        const config = {\n            headers: {\n                'Content-Type': 'application/json',\n                'Accept': 'application/json',\n                ...options.headers\n            },\n            ...options\n        };\n        // Add authentication header if token exists\n        const token =  true ? localStorage.getItem('authToken') : 0;\n        if (token) {\n            config.headers = {\n                ...config.headers,\n                'Authorization': \"Bearer \".concat(token)\n            };\n        }\n        try {\n            console.log(\"API Request: \".concat(options.method || 'GET', \" \").concat(url)) // Debug logging\n            ;\n            const response = await fetch(url, config);\n            const data = await response.json();\n            console.log(\"API Response for \".concat(endpoint, \":\"), data) // Debug logging\n            ;\n            // Handle 401 Unauthorized - redirect to login\n            if (response.status === 401) {\n                if (true) {\n                    localStorage.removeItem('authToken');\n                    localStorage.removeItem('user');\n                    window.location.href = '/auth/login';\n                }\n            }\n            return data;\n        } catch (error) {\n            console.error('API request failed:', error);\n            throw error;\n        }\n    }\n    // Auth endpoints\n    async login(email, password) {\n        var _response_data;\n        const response = await this.request('/login', {\n            method: 'POST',\n            body: JSON.stringify({\n                email,\n                password\n            })\n        });\n        // Store token and user data if login successful\n        if (response.success && ((_response_data = response.data) === null || _response_data === void 0 ? void 0 : _response_data.access_token)) {\n            if (true) {\n                localStorage.setItem('authToken', response.data.access_token);\n                localStorage.setItem('user', JSON.stringify(response.data.user));\n            }\n        }\n        return response;\n    }\n    async register(userData) {\n        var _response_data;\n        // First, register the basic user\n        const response = await this.request('/register', {\n            method: 'POST',\n            body: JSON.stringify({\n                first_name: userData.first_name,\n                last_name: userData.last_name,\n                email: userData.email,\n                password: userData.password,\n                password_confirmation: userData.password_confirmation,\n                phone_number: userData.phone_number,\n                address: userData.address,\n                date_of_birth: userData.date_of_birth,\n                city: userData.city,\n                state: userData.state,\n                country: userData.country\n            })\n        });\n        // Store token and user data if registration successful\n        if (response.success && ((_response_data = response.data) === null || _response_data === void 0 ? void 0 : _response_data.token)) {\n            if (true) {\n                localStorage.setItem('authToken', response.data.token);\n                localStorage.setItem('user', JSON.stringify(response.data.user));\n            }\n        }\n        // If registration is successful, handle user type specific data\n        if (response.success && userData.user_type && userData.additional_data) {\n            try {\n                if (userData.user_type === 'volunteer') {\n                    // Submit volunteer application\n                    await this.applyAsVolunteer(userData.additional_data);\n                } else if (userData.user_type === 'student' || userData.user_type === 'partner') {\n                    // Store additional data in user preferences\n                    const preferences = {\n                        user_type: userData.user_type,\n                        profile_data: userData.additional_data,\n                        profile_completed: true\n                    };\n                    await this.updateUserPreferences(preferences);\n                }\n            } catch (error) {\n                console.error('Additional data submission failed:', error);\n                // Return the user registration success but note additional data failed\n                return {\n                    ...response,\n                    message: response.message + ' However, additional profile information could not be saved. You can complete your profile later.'\n                };\n            }\n        }\n        return response;\n    }\n    async logout() {\n        const response = await this.request('/logout', {\n            method: 'POST'\n        });\n        // Clear stored token and user data on logout\n        if (true) {\n            localStorage.removeItem('authToken');\n            localStorage.removeItem('user');\n        }\n        return response;\n    }\n    async getUser() {\n        return this.request('/user');\n    }\n    async forgotPassword(email) {\n        return this.request('/forgot-password', {\n            method: 'POST',\n            body: JSON.stringify({\n                email\n            })\n        });\n    }\n    // Profile endpoints\n    async getProfile() {\n        return this.request('/profile');\n    }\n    async updateProfile(profileData) {\n        return this.request('/profile', {\n            method: 'PUT',\n            body: JSON.stringify(profileData)\n        });\n    }\n    async updateUserPreferences(preferences) {\n        return this.request('/profile', {\n            method: 'PUT',\n            body: JSON.stringify({\n                preferences\n            })\n        });\n    }\n    async uploadAvatar(file) {\n        const formData = new FormData();\n        formData.append('avatar', file);\n        // Use the base URL directly if it already contains /api/v1, otherwise add it\n        const url = this.baseURL.includes('/api/v1') ? \"\".concat(this.baseURL, \"/profile/avatar\") : \"\".concat(this.baseURL, \"/api/v1/profile/avatar\");\n        const token =  true ? localStorage.getItem('authToken') : 0;\n        try {\n            const response = await fetch(url, {\n                method: 'POST',\n                headers: {\n                    'Authorization': token ? \"Bearer \".concat(token) : '',\n                    'Accept': 'application/json'\n                },\n                body: formData\n            });\n            const data = await response.json();\n            if (response.status === 401) {\n                if (true) {\n                    localStorage.removeItem('authToken');\n                    localStorage.removeItem('user');\n                    window.location.href = '/auth/login';\n                }\n            }\n            return data;\n        } catch (error) {\n            console.error('Avatar upload failed:', error);\n            throw error;\n        }\n    }\n    async changePassword(passwordData) {\n        return this.request('/profile/password', {\n            method: 'PUT',\n            body: JSON.stringify(passwordData)\n        });\n    }\n    async generateQrCode() {\n        return this.request('/profile/generate-qr', {\n            method: 'POST'\n        });\n    }\n    async getIdCard() {\n        return this.request('/profile/id-card');\n    }\n    // Dashboard endpoints\n    async getDashboardSummary() {\n        return this.request('/dashboard/summary');\n    }\n    // Volunteer endpoints\n    async getVolunteerApplication() {\n        return this.request('/volunteer/application');\n    }\n    async getVolunteerHours() {\n        return this.request('/volunteer/hours');\n    }\n    async getVolunteerOpportunities() {\n        return this.request('/volunteer/opportunities');\n    }\n    async logVolunteerHours(hoursData) {\n        return this.request('/volunteer/hours', {\n            method: 'POST',\n            body: JSON.stringify(hoursData)\n        });\n    }\n    // Scholarship endpoints\n    async getMyScholarshipApplications() {\n        return this.request('/scholarships/my-applications');\n    }\n    async getScholarships() {\n        return this.request('/scholarships');\n    }\n    async applyForScholarship(scholarshipId, applicationData) {\n        return this.request(\"/scholarships/\".concat(scholarshipId, \"/apply\"), {\n            method: 'POST',\n            body: JSON.stringify(applicationData)\n        });\n    }\n    // Event endpoints\n    async getMyEventRegistrations() {\n        return this.request('/events/my-registrations');\n    }\n    async getUpcomingEvents() {\n        return this.request('/events/upcoming');\n    }\n    // Helper method for building query strings\n    buildQueryString(params) {\n        const searchParams = new URLSearchParams();\n        Object.entries(params).forEach((param)=>{\n            let [key, value] = param;\n            if (value !== undefined && value !== null && value !== '') {\n                if (Array.isArray(value)) {\n                    value.forEach((item)=>searchParams.append(\"\".concat(key, \"[]\"), item));\n                } else {\n                    searchParams.append(key, value.toString());\n                }\n            }\n        });\n        return searchParams.toString();\n    }\n    // Admin Dashboard endpoints\n    async getAdminDashboard() {\n        return this.request('/admin/dashboard');\n    }\n    async getAdminAnalytics(params) {\n        const queryString = params ? \"?\".concat(this.buildQueryString(params)) : '';\n        return this.request(\"/admin/dashboard/analytics\".concat(queryString));\n    }\n    async getAdminStats(params) {\n        const queryString = params ? \"?\".concat(this.buildQueryString(params)) : '';\n        return this.request(\"/admin/dashboard/stats\".concat(queryString));\n    }\n    // Admin User Management\n    async getAdminUsers(params) {\n        const queryString = params ? \"?\".concat(this.buildQueryString(params)) : '';\n        return this.request(\"/admin/users\".concat(queryString));\n    }\n    async createAdminUser(userData) {\n        return this.request('/admin/users', {\n            method: 'POST',\n            body: JSON.stringify(userData)\n        });\n    }\n    async updateAdminUser(userId, userData) {\n        return this.request(\"/admin/users/\".concat(userId), {\n            method: 'PUT',\n            body: JSON.stringify(userData)\n        });\n    }\n    async deleteAdminUser(userId) {\n        return this.request(\"/admin/users/\".concat(userId), {\n            method: 'DELETE'\n        });\n    }\n    async bulkActionUsers(data) {\n        return this.request('/admin/users/bulk-action', {\n            method: 'POST',\n            body: JSON.stringify(data)\n        });\n    }\n    async exportUsers(params) {\n        const queryString = params ? \"?\".concat(this.buildQueryString(params)) : '';\n        return this.request(\"/admin/users/export\".concat(queryString));\n    }\n    // Admin Scholarship Management\n    async getScholarshipApplications(params) {\n        const queryString = params ? \"?\".concat(this.buildQueryString(params)) : '';\n        return this.request(\"/admin/scholarship-applications\".concat(queryString));\n    }\n    async reviewScholarshipApplication(applicationId, reviewData) {\n        return this.request(\"/admin/scholarship-applications/\".concat(applicationId, \"/review\"), {\n            method: 'PUT',\n            body: JSON.stringify(reviewData)\n        });\n    }\n    async bulkActionScholarshipApplications(data) {\n        return this.request('/admin/scholarship-applications/bulk-action', {\n            method: 'POST',\n            body: JSON.stringify(data)\n        });\n    }\n    async exportScholarshipApplications(params) {\n        const queryString = params ? \"?\".concat(this.buildQueryString(params)) : '';\n        return this.request(\"/admin/scholarship-applications/export\".concat(queryString));\n    }\n    async getScholarshipStatistics() {\n        return this.request('/admin/scholarships/statistics');\n    }\n    // Admin Event Management\n    async getAdminEvents(params) {\n        const queryString = params ? \"?\".concat(this.buildQueryString(params)) : '';\n        return this.request(\"/admin/events\".concat(queryString));\n    }\n    async createAdminEvent(eventData) {\n        return this.request('/admin/events', {\n            method: 'POST',\n            body: JSON.stringify(eventData)\n        });\n    }\n    async updateAdminEvent(eventId, eventData) {\n        return this.request(\"/admin/events/\".concat(eventId), {\n            method: 'PUT',\n            body: JSON.stringify(eventData)\n        });\n    }\n    async deleteAdminEvent(eventId) {\n        return this.request(\"/admin/events/\".concat(eventId), {\n            method: 'DELETE'\n        });\n    }\n    async bulkActionEvents(data) {\n        return this.request('/admin/events/bulk-action', {\n            method: 'POST',\n            body: JSON.stringify(data)\n        });\n    }\n    async exportEvents(params) {\n        const queryString = params ? \"?\".concat(this.buildQueryString(params)) : '';\n        return this.request(\"/admin/events/export\".concat(queryString));\n    }\n    async getEventStatistics() {\n        return this.request('/admin/events/statistics');\n    }\n    // Admin Program Management\n    async getAdminPrograms(params) {\n        const queryString = params ? \"?\".concat(this.buildQueryString(params)) : '';\n        return this.request(\"/admin/programs\".concat(queryString));\n    }\n    async createAdminProgram(programData) {\n        return this.request('/admin/programs', {\n            method: 'POST',\n            body: JSON.stringify(programData)\n        });\n    }\n    async updateAdminProgram(programId, programData) {\n        return this.request(\"/admin/programs/\".concat(programId), {\n            method: 'PUT',\n            body: JSON.stringify(programData)\n        });\n    }\n    async deleteAdminProgram(programId) {\n        return this.request(\"/admin/programs/\".concat(programId), {\n            method: 'DELETE'\n        });\n    }\n    // Admin Blog Management\n    async getAdminBlogPosts(params) {\n        const queryString = params ? \"?\".concat(this.buildQueryString(params)) : '';\n        return this.request(\"/admin/blog/posts\".concat(queryString));\n    }\n    async createAdminBlogPost(postData) {\n        return this.request('/admin/blog/posts', {\n            method: 'POST',\n            body: JSON.stringify(postData)\n        });\n    }\n    async updateAdminBlogPost(postId, postData) {\n        return this.request(\"/admin/blog/posts/\".concat(postId), {\n            method: 'PUT',\n            body: JSON.stringify(postData)\n        });\n    }\n    async deleteAdminBlogPost(postId) {\n        return this.request(\"/admin/blog/posts/\".concat(postId), {\n            method: 'DELETE'\n        });\n    }\n    async exportBlogPosts(params) {\n        const queryString = params ? \"?\".concat(this.buildQueryString(params)) : '';\n        return this.request(\"/admin/blog/posts/export\".concat(queryString));\n    }\n    // Donation endpoints\n    async getMyDonations() {\n        return this.request('/donations/my-donations');\n    }\n    async getDonationCampaigns() {\n        return this.request('/donations/campaigns');\n    }\n    // Blog endpoints\n    async getBlogPosts() {\n        let page = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 1;\n        return this.request(\"/blog/posts?page=\".concat(page));\n    }\n    async getBlogPost(slug) {\n        return this.request(\"/blog/posts/\".concat(slug));\n    }\n    // Events endpoints\n    async getEvents() {\n        let page = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 1;\n        return this.request(\"/events?page=\".concat(page));\n    }\n    async getEvent(id) {\n        return this.request(\"/events/\".concat(id));\n    }\n    async registerForEvent(eventId, additionalInfo) {\n        return this.request(\"/events/\".concat(eventId, \"/register\"), {\n            method: 'POST',\n            body: JSON.stringify({\n                additional_info: additionalInfo\n            })\n        });\n    }\n    // Programs endpoints\n    async getPrograms() {\n        let page = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 1;\n        return this.request(\"/programs?page=\".concat(page));\n    }\n    async getProgram(slug) {\n        return this.request(\"/programs/\".concat(slug));\n    }\n    // Additional scholarship endpoint\n    async getScholarship(id) {\n        return this.request(\"/scholarships/\".concat(id));\n    }\n    // Public scholarships endpoints (no authentication required)\n    async getPublicScholarships(params) {\n        const queryString = params ? \"?\".concat(this.buildQueryString(params)) : '';\n        return this.request(\"/public-scholarships\".concat(queryString));\n    }\n    async getPublicScholarship(id) {\n        return this.request(\"/public-scholarships/\".concat(id));\n    }\n    async submitScholarshipApplication(scholarshipId, formData) {\n        const token = this.getToken();\n        const url = \"\".concat(this.baseURL, \"/api/v1/apply-scholarship/\").concat(scholarshipId);\n        try {\n            const response = await fetch(url, {\n                method: 'POST',\n                headers: {\n                    'Authorization': token ? \"Bearer \".concat(token) : '',\n                    'Accept': 'application/json'\n                },\n                body: formData\n            });\n            const data = await response.json();\n            console.log(\"API Response for scholarship application:\", data);\n            if (!response.ok) {\n                throw new Error(data.message || \"HTTP error! status: \".concat(response.status));\n            }\n            return data;\n        } catch (error) {\n            console.error('Error submitting scholarship application:', error);\n            throw error;\n        }\n    }\n    async applyAsVolunteer(applicationData) {\n        return this.request('/volunteer/apply', {\n            method: 'POST',\n            body: JSON.stringify(applicationData)\n        });\n    }\n    // Contact endpoints\n    async submitContactForm(contactData) {\n        return this.request('/contact', {\n            method: 'POST',\n            body: JSON.stringify(contactData)\n        });\n    }\n    // Newsletter endpoints\n    async subscribeToNewsletter(email) {\n        return this.request('/newsletter/subscribe', {\n            method: 'POST',\n            body: JSON.stringify({\n                email\n            })\n        });\n    }\n    // Settings endpoint\n    async getSettings() {\n        return this.request('/settings');\n    }\n    constructor(baseURL = API_BASE_URL !== null && API_BASE_URL !== void 0 ? API_BASE_URL : \"\"){\n        this.baseURL = baseURL;\n    }\n}\n// Create and export a default instance\nconst apiClient = new ApiClient();\n// Export the class for custom instances\n\n// Export the extractArrayData utility function\n\n// Helper function to check if user is authenticated\nconst isAuthenticated = ()=>{\n    if (false) {}\n    return !!localStorage.getItem('authToken');\n};\n// Helper function to get current user\nconst getCurrentUser = ()=>{\n    if (false) {}\n    const userStr = localStorage.getItem('user');\n    return userStr ? JSON.parse(userStr) : null;\n};\n// Helper function to logout\nconst logout = async ()=>{\n    try {\n        await apiClient.logout();\n    } catch (error) {\n        console.error('Logout error:', error);\n    } finally{\n        if (true) {\n            localStorage.removeItem('authToken');\n            localStorage.removeItem('user');\n            window.location.href = '/auth/login';\n        }\n    }\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2xpYi9hcGkudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBQUEsTUFBTUEsZUFBZUMsOEJBQStCLElBQUlBLENBQW9DO0FBUzVGLDRFQUE0RTtBQUM1RSxNQUFNSSxtQkFBbUIsQ0FBQ0M7SUFDeEIsSUFBSSxDQUFDQSxZQUFZLENBQUNBLFNBQVNDLElBQUksRUFBRSxPQUFPLEVBQUU7SUFFMUMseUNBQXlDO0lBQ3pDLElBQUlDLE1BQU1DLE9BQU8sQ0FBQ0gsU0FBU0MsSUFBSSxHQUFHO1FBQ2hDLE9BQU9ELFNBQVNDLElBQUk7SUFDdEI7SUFFQSxzRUFBc0U7SUFDdEUsSUFBSUQsU0FBU0MsSUFBSSxDQUFDQSxJQUFJLElBQUlDLE1BQU1DLE9BQU8sQ0FBQ0gsU0FBU0MsSUFBSSxDQUFDQSxJQUFJLEdBQUc7UUFDM0QsT0FBT0QsU0FBU0MsSUFBSSxDQUFDQSxJQUFJO0lBQzNCO0lBRUEseUJBQXlCO0lBQ3pCLE9BQU8sRUFBRTtBQUNYO0FBRUEsTUFBTUc7SUFPSixNQUFNQyxRQUNKQyxRQUFnQixFQUVTO1lBRHpCQyxVQUFBQSxpRUFBdUIsQ0FBQztRQUV4Qiw2RUFBNkU7UUFDN0UsTUFBTUMsTUFBTSxJQUFJLENBQUNDLE9BQU8sQ0FBQ0MsUUFBUSxDQUFDLGFBQzlCLEdBQWtCSixPQUFmLElBQUksQ0FBQ0csT0FBTyxFQUFZLE9BQVRILFlBQ2xCLEdBQXlCQSxPQUF0QixJQUFJLENBQUNHLE9BQU8sRUFBQyxXQUFrQixPQUFUSDtRQUU3QixNQUFNSyxTQUFzQjtZQUMxQkMsU0FBUztnQkFDUCxnQkFBZ0I7Z0JBQ2hCLFVBQVU7Z0JBQ1YsR0FBR0wsUUFBUUssT0FBTztZQUNwQjtZQUNBLEdBQUdMLE9BQU87UUFDWjtRQUVBLDRDQUE0QztRQUM1QyxNQUFNTSxRQUFRLEtBQTZCLEdBQUdDLGFBQWFDLE9BQU8sQ0FBQyxlQUFlLENBQUk7UUFDdEYsSUFBSUYsT0FBTztZQUNURixPQUFPQyxPQUFPLEdBQUc7Z0JBQ2YsR0FBR0QsT0FBT0MsT0FBTztnQkFDakIsaUJBQWlCLFVBQWdCLE9BQU5DO1lBQzdCO1FBQ0Y7UUFFQSxJQUFJO1lBQ0ZHLFFBQVFDLEdBQUcsQ0FBQyxnQkFBMkNULE9BQTNCRCxRQUFRVyxNQUFNLElBQUksT0FBTSxLQUFPLE9BQUpWLE1BQU8sZ0JBQWdCOztZQUM5RSxNQUFNUixXQUFXLE1BQU1tQixNQUFNWCxLQUFLRztZQUNsQyxNQUFNVixPQUFPLE1BQU1ELFNBQVNvQixJQUFJO1lBQ2hDSixRQUFRQyxHQUFHLENBQUMsb0JBQTZCLE9BQVRYLFVBQVMsTUFBSUwsTUFBTSxnQkFBZ0I7O1lBRW5FLDhDQUE4QztZQUM5QyxJQUFJRCxTQUFTcUIsTUFBTSxLQUFLLEtBQUs7Z0JBQzNCLElBQUksSUFBNkIsRUFBRTtvQkFDakNQLGFBQWFRLFVBQVUsQ0FBQztvQkFDeEJSLGFBQWFRLFVBQVUsQ0FBQztvQkFDeEJDLE9BQU9DLFFBQVEsQ0FBQ0MsSUFBSSxHQUFHO2dCQUN6QjtZQUNGO1lBRUEsT0FBT3hCO1FBQ1QsRUFBRSxPQUFPeUIsT0FBTztZQUNkVixRQUFRVSxLQUFLLENBQUMsdUJBQXVCQTtZQUNyQyxNQUFNQTtRQUNSO0lBQ0Y7SUFFQSxpQkFBaUI7SUFDakIsTUFBTUMsTUFBTUMsS0FBYSxFQUFFQyxRQUFnQixFQUFFO1lBT25CN0I7UUFOeEIsTUFBTUEsV0FBVyxNQUFNLElBQUksQ0FBQ0ssT0FBTyxDQUFDLFVBQVU7WUFDNUNhLFFBQVE7WUFDUlksTUFBTUMsS0FBS0MsU0FBUyxDQUFDO2dCQUFFSjtnQkFBT0M7WUFBUztRQUN6QztRQUVBLGdEQUFnRDtRQUNoRCxJQUFJN0IsU0FBU2lDLE9BQU8sTUFBSWpDLGlCQUFBQSxTQUFTQyxJQUFJLGNBQWJELHFDQUFBQSxlQUFla0MsWUFBWSxHQUFFO1lBQ25ELElBQUksSUFBNkIsRUFBRTtnQkFDakNwQixhQUFhcUIsT0FBTyxDQUFDLGFBQWFuQyxTQUFTQyxJQUFJLENBQUNpQyxZQUFZO2dCQUM1RHBCLGFBQWFxQixPQUFPLENBQUMsUUFBUUosS0FBS0MsU0FBUyxDQUFDaEMsU0FBU0MsSUFBSSxDQUFDbUMsSUFBSTtZQUNoRTtRQUNGO1FBRUEsT0FBT3BDO0lBQ1Q7SUFFQSxNQUFNcUMsU0FBU0MsUUFlZCxFQUFFO1lBb0J1QnRDO1FBbkJ4QixpQ0FBaUM7UUFDakMsTUFBTUEsV0FBVyxNQUFNLElBQUksQ0FBQ0ssT0FBTyxDQUFDLGFBQWE7WUFDL0NhLFFBQVE7WUFDUlksTUFBTUMsS0FBS0MsU0FBUyxDQUFDO2dCQUNuQk8sWUFBWUQsU0FBU0MsVUFBVTtnQkFDL0JDLFdBQVdGLFNBQVNFLFNBQVM7Z0JBQzdCWixPQUFPVSxTQUFTVixLQUFLO2dCQUNyQkMsVUFBVVMsU0FBU1QsUUFBUTtnQkFDM0JZLHVCQUF1QkgsU0FBU0cscUJBQXFCO2dCQUNyREMsY0FBY0osU0FBU0ksWUFBWTtnQkFDbkNDLFNBQVNMLFNBQVNLLE9BQU87Z0JBQ3pCQyxlQUFlTixTQUFTTSxhQUFhO2dCQUNyQ0MsTUFBTVAsU0FBU08sSUFBSTtnQkFDbkJDLE9BQU9SLFNBQVNRLEtBQUs7Z0JBQ3JCQyxTQUFTVCxTQUFTUyxPQUFPO1lBQzNCO1FBQ0Y7UUFFQSx1REFBdUQ7UUFDdkQsSUFBSS9DLFNBQVNpQyxPQUFPLE1BQUlqQyxpQkFBQUEsU0FBU0MsSUFBSSxjQUFiRCxxQ0FBQUEsZUFBZWEsS0FBSyxHQUFFO1lBQzVDLElBQUksSUFBNkIsRUFBRTtnQkFDakNDLGFBQWFxQixPQUFPLENBQUMsYUFBYW5DLFNBQVNDLElBQUksQ0FBQ1ksS0FBSztnQkFDckRDLGFBQWFxQixPQUFPLENBQUMsUUFBUUosS0FBS0MsU0FBUyxDQUFDaEMsU0FBU0MsSUFBSSxDQUFDbUMsSUFBSTtZQUNoRTtRQUNGO1FBRUEsZ0VBQWdFO1FBQ2hFLElBQUlwQyxTQUFTaUMsT0FBTyxJQUFJSyxTQUFTVSxTQUFTLElBQUlWLFNBQVNXLGVBQWUsRUFBRTtZQUN0RSxJQUFJO2dCQUNGLElBQUlYLFNBQVNVLFNBQVMsS0FBSyxhQUFhO29CQUN0QywrQkFBK0I7b0JBQy9CLE1BQU0sSUFBSSxDQUFDRSxnQkFBZ0IsQ0FBQ1osU0FBU1csZUFBZTtnQkFDdEQsT0FBTyxJQUFJWCxTQUFTVSxTQUFTLEtBQUssYUFBYVYsU0FBU1UsU0FBUyxLQUFLLFdBQVc7b0JBQy9FLDRDQUE0QztvQkFDNUMsTUFBTUcsY0FBYzt3QkFDbEJILFdBQVdWLFNBQVNVLFNBQVM7d0JBQzdCSSxjQUFjZCxTQUFTVyxlQUFlO3dCQUN0Q0ksbUJBQW1CO29CQUNyQjtvQkFDQSxNQUFNLElBQUksQ0FBQ0MscUJBQXFCLENBQUNIO2dCQUNuQztZQUNGLEVBQUUsT0FBT3pCLE9BQU87Z0JBQ2RWLFFBQVFVLEtBQUssQ0FBQyxzQ0FBc0NBO2dCQUNwRCx1RUFBdUU7Z0JBQ3ZFLE9BQU87b0JBQ0wsR0FBRzFCLFFBQVE7b0JBQ1h1RCxTQUFTdkQsU0FBU3VELE9BQU8sR0FBRztnQkFDOUI7WUFDRjtRQUNGO1FBRUEsT0FBT3ZEO0lBQ1Q7SUFFQSxNQUFNd0QsU0FBUztRQUNiLE1BQU14RCxXQUFXLE1BQU0sSUFBSSxDQUFDSyxPQUFPLENBQUMsV0FBVztZQUM3Q2EsUUFBUTtRQUNWO1FBRUEsNkNBQTZDO1FBQzdDLElBQUksSUFBNkIsRUFBRTtZQUNqQ0osYUFBYVEsVUFBVSxDQUFDO1lBQ3hCUixhQUFhUSxVQUFVLENBQUM7UUFDMUI7UUFFQSxPQUFPdEI7SUFDVDtJQUVBLE1BQU15RCxVQUFVO1FBQ2QsT0FBTyxJQUFJLENBQUNwRCxPQUFPLENBQUM7SUFDdEI7SUFFQSxNQUFNcUQsZUFBZTlCLEtBQWEsRUFBRTtRQUNsQyxPQUFPLElBQUksQ0FBQ3ZCLE9BQU8sQ0FBQyxvQkFBb0I7WUFDdENhLFFBQVE7WUFDUlksTUFBTUMsS0FBS0MsU0FBUyxDQUFDO2dCQUFFSjtZQUFNO1FBQy9CO0lBQ0Y7SUFFQSxvQkFBb0I7SUFDcEIsTUFBTStCLGFBQWE7UUFDakIsT0FBTyxJQUFJLENBQUN0RCxPQUFPLENBQUM7SUFDdEI7SUFFQSxNQUFNdUQsY0FBY0MsV0FBZ0IsRUFBRTtRQUNwQyxPQUFPLElBQUksQ0FBQ3hELE9BQU8sQ0FBQyxZQUFZO1lBQzlCYSxRQUFRO1lBQ1JZLE1BQU1DLEtBQUtDLFNBQVMsQ0FBQzZCO1FBQ3ZCO0lBQ0Y7SUFFQSxNQUFNUCxzQkFBc0JILFdBQWdCLEVBQUU7UUFDNUMsT0FBTyxJQUFJLENBQUM5QyxPQUFPLENBQUMsWUFBWTtZQUM5QmEsUUFBUTtZQUNSWSxNQUFNQyxLQUFLQyxTQUFTLENBQUM7Z0JBQUVtQjtZQUFZO1FBQ3JDO0lBQ0Y7SUFFQSxNQUFNVyxhQUFhQyxJQUFVLEVBQUU7UUFDN0IsTUFBTUMsV0FBVyxJQUFJQztRQUNyQkQsU0FBU0UsTUFBTSxDQUFDLFVBQVVIO1FBRTFCLDZFQUE2RTtRQUM3RSxNQUFNdkQsTUFBTSxJQUFJLENBQUNDLE9BQU8sQ0FBQ0MsUUFBUSxDQUFDLGFBQzlCLEdBQWdCLE9BQWIsSUFBSSxDQUFDRCxPQUFPLEVBQUMscUJBQ2hCLEdBQWdCLE9BQWIsSUFBSSxDQUFDQSxPQUFPLEVBQUM7UUFFcEIsTUFBTUksUUFBUSxLQUE2QixHQUFHQyxhQUFhQyxPQUFPLENBQUMsZUFBZSxDQUFJO1FBRXRGLElBQUk7WUFDRixNQUFNZixXQUFXLE1BQU1tQixNQUFNWCxLQUFLO2dCQUNoQ1UsUUFBUTtnQkFDUk4sU0FBUztvQkFDUCxpQkFBaUJDLFFBQVEsVUFBZ0IsT0FBTkEsU0FBVTtvQkFDN0MsVUFBVTtnQkFDWjtnQkFDQWlCLE1BQU1rQztZQUNSO1lBRUEsTUFBTS9ELE9BQU8sTUFBTUQsU0FBU29CLElBQUk7WUFFaEMsSUFBSXBCLFNBQVNxQixNQUFNLEtBQUssS0FBSztnQkFDM0IsSUFBSSxJQUE2QixFQUFFO29CQUNqQ1AsYUFBYVEsVUFBVSxDQUFDO29CQUN4QlIsYUFBYVEsVUFBVSxDQUFDO29CQUN4QkMsT0FBT0MsUUFBUSxDQUFDQyxJQUFJLEdBQUc7Z0JBQ3pCO1lBQ0Y7WUFFQSxPQUFPeEI7UUFDVCxFQUFFLE9BQU95QixPQUFPO1lBQ2RWLFFBQVFVLEtBQUssQ0FBQyx5QkFBeUJBO1lBQ3ZDLE1BQU1BO1FBQ1I7SUFDRjtJQUVBLE1BQU15QyxlQUFlQyxZQUlwQixFQUFFO1FBQ0QsT0FBTyxJQUFJLENBQUMvRCxPQUFPLENBQUMscUJBQXFCO1lBQ3ZDYSxRQUFRO1lBQ1JZLE1BQU1DLEtBQUtDLFNBQVMsQ0FBQ29DO1FBQ3ZCO0lBQ0Y7SUFFQSxNQUFNQyxpQkFBaUI7UUFDckIsT0FBTyxJQUFJLENBQUNoRSxPQUFPLENBQUMsd0JBQXdCO1lBQzFDYSxRQUFRO1FBQ1Y7SUFDRjtJQUVBLE1BQU1vRCxZQUFZO1FBQ2hCLE9BQU8sSUFBSSxDQUFDakUsT0FBTyxDQUFDO0lBQ3RCO0lBRUEsc0JBQXNCO0lBQ3RCLE1BQU1rRSxzQkFBc0I7UUFDMUIsT0FBTyxJQUFJLENBQUNsRSxPQUFPLENBQUM7SUFDdEI7SUFFQSxzQkFBc0I7SUFDdEIsTUFBTW1FLDBCQUEwQjtRQUM5QixPQUFPLElBQUksQ0FBQ25FLE9BQU8sQ0FBQztJQUN0QjtJQUVBLE1BQU1vRSxvQkFBb0I7UUFDeEIsT0FBTyxJQUFJLENBQUNwRSxPQUFPLENBQUM7SUFDdEI7SUFFQSxNQUFNcUUsNEJBQTRCO1FBQ2hDLE9BQU8sSUFBSSxDQUFDckUsT0FBTyxDQUFDO0lBQ3RCO0lBRUEsTUFBTXNFLGtCQUFrQkMsU0FBYyxFQUFFO1FBQ3RDLE9BQU8sSUFBSSxDQUFDdkUsT0FBTyxDQUFDLG9CQUFvQjtZQUN0Q2EsUUFBUTtZQUNSWSxNQUFNQyxLQUFLQyxTQUFTLENBQUM0QztRQUN2QjtJQUNGO0lBRUEsd0JBQXdCO0lBQ3hCLE1BQU1DLCtCQUErQjtRQUNuQyxPQUFPLElBQUksQ0FBQ3hFLE9BQU8sQ0FBQztJQUN0QjtJQUVBLE1BQU15RSxrQkFBa0I7UUFDdEIsT0FBTyxJQUFJLENBQUN6RSxPQUFPLENBQUM7SUFDdEI7SUFFQSxNQUFNMEUsb0JBQW9CQyxhQUFxQixFQUFFQyxlQUFvQixFQUFFO1FBQ3JFLE9BQU8sSUFBSSxDQUFDNUUsT0FBTyxDQUFDLGlCQUErQixPQUFkMkUsZUFBYyxXQUFTO1lBQzFEOUQsUUFBUTtZQUNSWSxNQUFNQyxLQUFLQyxTQUFTLENBQUNpRDtRQUN2QjtJQUNGO0lBRUEsa0JBQWtCO0lBQ2xCLE1BQU1DLDBCQUEwQjtRQUM5QixPQUFPLElBQUksQ0FBQzdFLE9BQU8sQ0FBQztJQUN0QjtJQUVBLE1BQU04RSxvQkFBb0I7UUFDeEIsT0FBTyxJQUFJLENBQUM5RSxPQUFPLENBQUM7SUFDdEI7SUFFQSwyQ0FBMkM7SUFDbkMrRSxpQkFBaUJDLE1BQTJCLEVBQVU7UUFDNUQsTUFBTUMsZUFBZSxJQUFJQztRQUV6QkMsT0FBT0MsT0FBTyxDQUFDSixRQUFRSyxPQUFPLENBQUM7Z0JBQUMsQ0FBQ0MsS0FBS0MsTUFBTTtZQUMxQyxJQUFJQSxVQUFVQyxhQUFhRCxVQUFVLFFBQVFBLFVBQVUsSUFBSTtnQkFDekQsSUFBSTFGLE1BQU1DLE9BQU8sQ0FBQ3lGLFFBQVE7b0JBQ3hCQSxNQUFNRixPQUFPLENBQUNJLENBQUFBLE9BQVFSLGFBQWFwQixNQUFNLENBQUMsR0FBTyxPQUFKeUIsS0FBSSxPQUFLRztnQkFDeEQsT0FBTztvQkFDTFIsYUFBYXBCLE1BQU0sQ0FBQ3lCLEtBQUtDLE1BQU1HLFFBQVE7Z0JBQ3pDO1lBQ0Y7UUFDRjtRQUVBLE9BQU9ULGFBQWFTLFFBQVE7SUFDOUI7SUFFQSw0QkFBNEI7SUFDNUIsTUFBTUMsb0JBQW9CO1FBQ3hCLE9BQU8sSUFBSSxDQUFDM0YsT0FBTyxDQUFDO0lBQ3RCO0lBRUEsTUFBTTRGLGtCQUFrQlosTUFBMEIsRUFBRTtRQUNsRCxNQUFNYSxjQUFjYixTQUFTLElBQWtDLE9BQTlCLElBQUksQ0FBQ0QsZ0JBQWdCLENBQUNDLFdBQVk7UUFDbkUsT0FBTyxJQUFJLENBQUNoRixPQUFPLENBQUMsNkJBQXlDLE9BQVo2RjtJQUNuRDtJQUVBLE1BQU1DLGNBQWNkLE1BQTRCLEVBQUU7UUFDaEQsTUFBTWEsY0FBY2IsU0FBUyxJQUFrQyxPQUE5QixJQUFJLENBQUNELGdCQUFnQixDQUFDQyxXQUFZO1FBQ25FLE9BQU8sSUFBSSxDQUFDaEYsT0FBTyxDQUFDLHlCQUFxQyxPQUFaNkY7SUFDL0M7SUFFQSx3QkFBd0I7SUFDeEIsTUFBTUUsY0FBY2YsTUFTbkIsRUFBRTtRQUNELE1BQU1hLGNBQWNiLFNBQVMsSUFBa0MsT0FBOUIsSUFBSSxDQUFDRCxnQkFBZ0IsQ0FBQ0MsV0FBWTtRQUNuRSxPQUFPLElBQUksQ0FBQ2hGLE9BQU8sQ0FBQyxlQUEyQixPQUFaNkY7SUFDckM7SUFFQSxNQUFNRyxnQkFBZ0IvRCxRQUFhLEVBQUU7UUFDbkMsT0FBTyxJQUFJLENBQUNqQyxPQUFPLENBQUMsZ0JBQWdCO1lBQ2xDYSxRQUFRO1lBQ1JZLE1BQU1DLEtBQUtDLFNBQVMsQ0FBQ007UUFDdkI7SUFDRjtJQUVBLE1BQU1nRSxnQkFBZ0JDLE1BQWMsRUFBRWpFLFFBQWEsRUFBRTtRQUNuRCxPQUFPLElBQUksQ0FBQ2pDLE9BQU8sQ0FBQyxnQkFBdUIsT0FBUGtHLFNBQVU7WUFDNUNyRixRQUFRO1lBQ1JZLE1BQU1DLEtBQUtDLFNBQVMsQ0FBQ007UUFDdkI7SUFDRjtJQUVBLE1BQU1rRSxnQkFBZ0JELE1BQWMsRUFBRTtRQUNwQyxPQUFPLElBQUksQ0FBQ2xHLE9BQU8sQ0FBQyxnQkFBdUIsT0FBUGtHLFNBQVU7WUFDNUNyRixRQUFRO1FBQ1Y7SUFDRjtJQUVBLE1BQU11RixnQkFBZ0J4RyxJQUE0QyxFQUFFO1FBQ2xFLE9BQU8sSUFBSSxDQUFDSSxPQUFPLENBQUMsNEJBQTRCO1lBQzlDYSxRQUFRO1lBQ1JZLE1BQU1DLEtBQUtDLFNBQVMsQ0FBQy9CO1FBQ3ZCO0lBQ0Y7SUFFQSxNQUFNeUcsWUFBWXJCLE1BQTRELEVBQUU7UUFDOUUsTUFBTWEsY0FBY2IsU0FBUyxJQUFrQyxPQUE5QixJQUFJLENBQUNELGdCQUFnQixDQUFDQyxXQUFZO1FBQ25FLE9BQU8sSUFBSSxDQUFDaEYsT0FBTyxDQUFDLHNCQUFrQyxPQUFaNkY7SUFDNUM7SUFFQSwrQkFBK0I7SUFDL0IsTUFBTVMsMkJBQTJCdEIsTUFTaEMsRUFBRTtRQUNELE1BQU1hLGNBQWNiLFNBQVMsSUFBa0MsT0FBOUIsSUFBSSxDQUFDRCxnQkFBZ0IsQ0FBQ0MsV0FBWTtRQUNuRSxPQUFPLElBQUksQ0FBQ2hGLE9BQU8sQ0FBQyxrQ0FBOEMsT0FBWjZGO0lBQ3hEO0lBRUEsTUFBTVUsNkJBQTZCQyxhQUFxQixFQUFFQyxVQUFlLEVBQUU7UUFDekUsT0FBTyxJQUFJLENBQUN6RyxPQUFPLENBQUMsbUNBQWlELE9BQWR3RyxlQUFjLFlBQVU7WUFDN0UzRixRQUFRO1lBQ1JZLE1BQU1DLEtBQUtDLFNBQVMsQ0FBQzhFO1FBQ3ZCO0lBQ0Y7SUFFQSxNQUFNQyxrQ0FBa0M5RyxJQUt2QyxFQUFFO1FBQ0QsT0FBTyxJQUFJLENBQUNJLE9BQU8sQ0FBQywrQ0FBK0M7WUFDakVhLFFBQVE7WUFDUlksTUFBTUMsS0FBS0MsU0FBUyxDQUFDL0I7UUFDdkI7SUFDRjtJQUVBLE1BQU0rRyw4QkFBOEIzQixNQUluQyxFQUFFO1FBQ0QsTUFBTWEsY0FBY2IsU0FBUyxJQUFrQyxPQUE5QixJQUFJLENBQUNELGdCQUFnQixDQUFDQyxXQUFZO1FBQ25FLE9BQU8sSUFBSSxDQUFDaEYsT0FBTyxDQUFDLHlDQUFxRCxPQUFaNkY7SUFDL0Q7SUFFQSxNQUFNZSwyQkFBMkI7UUFDL0IsT0FBTyxJQUFJLENBQUM1RyxPQUFPLENBQUM7SUFDdEI7SUFFQSx5QkFBeUI7SUFDekIsTUFBTTZHLGVBQWU3QixNQVFwQixFQUFFO1FBQ0QsTUFBTWEsY0FBY2IsU0FBUyxJQUFrQyxPQUE5QixJQUFJLENBQUNELGdCQUFnQixDQUFDQyxXQUFZO1FBQ25FLE9BQU8sSUFBSSxDQUFDaEYsT0FBTyxDQUFDLGdCQUE0QixPQUFaNkY7SUFDdEM7SUFFQSxNQUFNaUIsaUJBQWlCQyxTQUFjLEVBQUU7UUFDckMsT0FBTyxJQUFJLENBQUMvRyxPQUFPLENBQUMsaUJBQWlCO1lBQ25DYSxRQUFRO1lBQ1JZLE1BQU1DLEtBQUtDLFNBQVMsQ0FBQ29GO1FBQ3ZCO0lBQ0Y7SUFFQSxNQUFNQyxpQkFBaUJDLE9BQWUsRUFBRUYsU0FBYyxFQUFFO1FBQ3RELE9BQU8sSUFBSSxDQUFDL0csT0FBTyxDQUFDLGlCQUF5QixPQUFSaUgsVUFBVztZQUM5Q3BHLFFBQVE7WUFDUlksTUFBTUMsS0FBS0MsU0FBUyxDQUFDb0Y7UUFDdkI7SUFDRjtJQUVBLE1BQU1HLGlCQUFpQkQsT0FBZSxFQUFFO1FBQ3RDLE9BQU8sSUFBSSxDQUFDakgsT0FBTyxDQUFDLGlCQUF5QixPQUFSaUgsVUFBVztZQUM5Q3BHLFFBQVE7UUFDVjtJQUNGO0lBRUEsTUFBTXNHLGlCQUFpQnZILElBQTZDLEVBQUU7UUFDcEUsT0FBTyxJQUFJLENBQUNJLE9BQU8sQ0FBQyw2QkFBNkI7WUFDL0NhLFFBQVE7WUFDUlksTUFBTUMsS0FBS0MsU0FBUyxDQUFDL0I7UUFDdkI7SUFDRjtJQUVBLE1BQU13SCxhQUFhcEMsTUFBNkMsRUFBRTtRQUNoRSxNQUFNYSxjQUFjYixTQUFTLElBQWtDLE9BQTlCLElBQUksQ0FBQ0QsZ0JBQWdCLENBQUNDLFdBQVk7UUFDbkUsT0FBTyxJQUFJLENBQUNoRixPQUFPLENBQUMsdUJBQW1DLE9BQVo2RjtJQUM3QztJQUVBLE1BQU13QixxQkFBcUI7UUFDekIsT0FBTyxJQUFJLENBQUNySCxPQUFPLENBQUM7SUFDdEI7SUFFQSwyQkFBMkI7SUFDM0IsTUFBTXNILGlCQUFpQnRDLE1BUXRCLEVBQUU7UUFDRCxNQUFNYSxjQUFjYixTQUFTLElBQWtDLE9BQTlCLElBQUksQ0FBQ0QsZ0JBQWdCLENBQUNDLFdBQVk7UUFDbkUsT0FBTyxJQUFJLENBQUNoRixPQUFPLENBQUMsa0JBQThCLE9BQVo2RjtJQUN4QztJQUVBLE1BQU0wQixtQkFBbUJDLFdBQWdCLEVBQUU7UUFDekMsT0FBTyxJQUFJLENBQUN4SCxPQUFPLENBQUMsbUJBQW1CO1lBQ3JDYSxRQUFRO1lBQ1JZLE1BQU1DLEtBQUtDLFNBQVMsQ0FBQzZGO1FBQ3ZCO0lBQ0Y7SUFFQSxNQUFNQyxtQkFBbUJDLFNBQWlCLEVBQUVGLFdBQWdCLEVBQUU7UUFDNUQsT0FBTyxJQUFJLENBQUN4SCxPQUFPLENBQUMsbUJBQTZCLE9BQVYwSCxZQUFhO1lBQ2xEN0csUUFBUTtZQUNSWSxNQUFNQyxLQUFLQyxTQUFTLENBQUM2RjtRQUN2QjtJQUNGO0lBRUEsTUFBTUcsbUJBQW1CRCxTQUFpQixFQUFFO1FBQzFDLE9BQU8sSUFBSSxDQUFDMUgsT0FBTyxDQUFDLG1CQUE2QixPQUFWMEgsWUFBYTtZQUNsRDdHLFFBQVE7UUFDVjtJQUNGO0lBRUEsd0JBQXdCO0lBQ3hCLE1BQU0rRyxrQkFBa0I1QyxNQVF2QixFQUFFO1FBQ0QsTUFBTWEsY0FBY2IsU0FBUyxJQUFrQyxPQUE5QixJQUFJLENBQUNELGdCQUFnQixDQUFDQyxXQUFZO1FBQ25FLE9BQU8sSUFBSSxDQUFDaEYsT0FBTyxDQUFDLG9CQUFnQyxPQUFaNkY7SUFDMUM7SUFFQSxNQUFNZ0Msb0JBQW9CQyxRQUFhLEVBQUU7UUFDdkMsT0FBTyxJQUFJLENBQUM5SCxPQUFPLENBQUMscUJBQXFCO1lBQ3ZDYSxRQUFRO1lBQ1JZLE1BQU1DLEtBQUtDLFNBQVMsQ0FBQ21HO1FBQ3ZCO0lBQ0Y7SUFFQSxNQUFNQyxvQkFBb0JDLE1BQWMsRUFBRUYsUUFBYSxFQUFFO1FBQ3ZELE9BQU8sSUFBSSxDQUFDOUgsT0FBTyxDQUFDLHFCQUE0QixPQUFQZ0ksU0FBVTtZQUNqRG5ILFFBQVE7WUFDUlksTUFBTUMsS0FBS0MsU0FBUyxDQUFDbUc7UUFDdkI7SUFDRjtJQUVBLE1BQU1HLG9CQUFvQkQsTUFBYyxFQUFFO1FBQ3hDLE9BQU8sSUFBSSxDQUFDaEksT0FBTyxDQUFDLHFCQUE0QixPQUFQZ0ksU0FBVTtZQUNqRG5ILFFBQVE7UUFDVjtJQUNGO0lBRUEsTUFBTXFILGdCQUFnQmxELE1BQTZDLEVBQUU7UUFDbkUsTUFBTWEsY0FBY2IsU0FBUyxJQUFrQyxPQUE5QixJQUFJLENBQUNELGdCQUFnQixDQUFDQyxXQUFZO1FBQ25FLE9BQU8sSUFBSSxDQUFDaEYsT0FBTyxDQUFDLDJCQUF1QyxPQUFaNkY7SUFDakQ7SUFFQSxxQkFBcUI7SUFDckIsTUFBTXNDLGlCQUFpQjtRQUNyQixPQUFPLElBQUksQ0FBQ25JLE9BQU8sQ0FBQztJQUN0QjtJQUVBLE1BQU1vSSx1QkFBdUI7UUFDM0IsT0FBTyxJQUFJLENBQUNwSSxPQUFPLENBQUM7SUFDdEI7SUFFQSxpQkFBaUI7SUFDakIsTUFBTXFJLGVBQXVCO1lBQVZDLE9BQUFBLGlFQUFPO1FBQ3hCLE9BQU8sSUFBSSxDQUFDdEksT0FBTyxDQUFDLG9CQUF5QixPQUFMc0k7SUFDMUM7SUFFQSxNQUFNQyxZQUFZQyxJQUFZLEVBQUU7UUFDOUIsT0FBTyxJQUFJLENBQUN4SSxPQUFPLENBQUMsZUFBb0IsT0FBTHdJO0lBQ3JDO0lBRUEsbUJBQW1CO0lBQ25CLE1BQU1DLFlBQW9CO1lBQVZILE9BQUFBLGlFQUFPO1FBQ3JCLE9BQU8sSUFBSSxDQUFDdEksT0FBTyxDQUFDLGdCQUFxQixPQUFMc0k7SUFDdEM7SUFFQSxNQUFNSSxTQUFTQyxFQUFVLEVBQUU7UUFDekIsT0FBTyxJQUFJLENBQUMzSSxPQUFPLENBQUMsV0FBYyxPQUFIMkk7SUFDakM7SUFFQSxNQUFNQyxpQkFBaUIzQixPQUFlLEVBQUU0QixjQUFvQixFQUFFO1FBQzVELE9BQU8sSUFBSSxDQUFDN0ksT0FBTyxDQUFDLFdBQW1CLE9BQVJpSCxTQUFRLGNBQVk7WUFDakRwRyxRQUFRO1lBQ1JZLE1BQU1DLEtBQUtDLFNBQVMsQ0FBQztnQkFBRW1ILGlCQUFpQkQ7WUFBZTtRQUN6RDtJQUNGO0lBRUEscUJBQXFCO0lBQ3JCLE1BQU1FLGNBQXNCO1lBQVZULE9BQUFBLGlFQUFPO1FBQ3ZCLE9BQU8sSUFBSSxDQUFDdEksT0FBTyxDQUFDLGtCQUF1QixPQUFMc0k7SUFDeEM7SUFFQSxNQUFNVSxXQUFXUixJQUFZLEVBQUU7UUFDN0IsT0FBTyxJQUFJLENBQUN4SSxPQUFPLENBQUMsYUFBa0IsT0FBTHdJO0lBQ25DO0lBRUEsa0NBQWtDO0lBQ2xDLE1BQU1TLGVBQWVOLEVBQVUsRUFBRTtRQUMvQixPQUFPLElBQUksQ0FBQzNJLE9BQU8sQ0FBQyxpQkFBb0IsT0FBSDJJO0lBQ3ZDO0lBRUEsNkRBQTZEO0lBQzdELE1BQU1PLHNCQUFzQmxFLE1BTzNCLEVBQUU7UUFDRCxNQUFNYSxjQUFjYixTQUFTLElBQWtDLE9BQTlCLElBQUksQ0FBQ0QsZ0JBQWdCLENBQUNDLFdBQVk7UUFDbkUsT0FBTyxJQUFJLENBQUNoRixPQUFPLENBQUMsdUJBQW1DLE9BQVo2RjtJQUM3QztJQUVBLE1BQU1zRCxxQkFBcUJSLEVBQW1CLEVBQUU7UUFDOUMsT0FBTyxJQUFJLENBQUMzSSxPQUFPLENBQUMsd0JBQTJCLE9BQUgySTtJQUM5QztJQUVBLE1BQU1TLDZCQUE2QnpFLGFBQXFCLEVBQUVoQixRQUFrQixFQUFFO1FBQzVFLE1BQU1uRCxRQUFRLElBQUksQ0FBQzZJLFFBQVE7UUFDM0IsTUFBTWxKLE1BQU0sR0FBNEN3RSxPQUF6QyxJQUFJLENBQUN2RSxPQUFPLEVBQUMsOEJBQTBDLE9BQWR1RTtRQUV4RCxJQUFJO1lBQ0YsTUFBTWhGLFdBQVcsTUFBTW1CLE1BQU1YLEtBQUs7Z0JBQ2hDVSxRQUFRO2dCQUNSTixTQUFTO29CQUNQLGlCQUFpQkMsUUFBUSxVQUFnQixPQUFOQSxTQUFVO29CQUM3QyxVQUFVO2dCQUVaO2dCQUNBaUIsTUFBTWtDO1lBQ1I7WUFFQSxNQUFNL0QsT0FBTyxNQUFNRCxTQUFTb0IsSUFBSTtZQUNoQ0osUUFBUUMsR0FBRyxDQUFFLDZDQUE0Q2hCO1lBRXpELElBQUksQ0FBQ0QsU0FBUzJKLEVBQUUsRUFBRTtnQkFDaEIsTUFBTSxJQUFJQyxNQUFNM0osS0FBS3NELE9BQU8sSUFBSSx1QkFBdUMsT0FBaEJ2RCxTQUFTcUIsTUFBTTtZQUN4RTtZQUVBLE9BQU9wQjtRQUNULEVBQUUsT0FBT3lCLE9BQU87WUFDZFYsUUFBUVUsS0FBSyxDQUFDLDZDQUE2Q0E7WUFDM0QsTUFBTUE7UUFDUjtJQUNGO0lBRUEsTUFBTXdCLGlCQUFpQitCLGVBQW9CLEVBQUU7UUFDM0MsT0FBTyxJQUFJLENBQUM1RSxPQUFPLENBQUMsb0JBQW9CO1lBQ3RDYSxRQUFRO1lBQ1JZLE1BQU1DLEtBQUtDLFNBQVMsQ0FBQ2lEO1FBQ3ZCO0lBQ0Y7SUFFQSxvQkFBb0I7SUFDcEIsTUFBTTRFLGtCQUFrQkMsV0FBZ0IsRUFBRTtRQUN4QyxPQUFPLElBQUksQ0FBQ3pKLE9BQU8sQ0FBQyxZQUFZO1lBQzlCYSxRQUFRO1lBQ1JZLE1BQU1DLEtBQUtDLFNBQVMsQ0FBQzhIO1FBQ3ZCO0lBQ0Y7SUFFQSx1QkFBdUI7SUFDdkIsTUFBTUMsc0JBQXNCbkksS0FBYSxFQUFFO1FBQ3pDLE9BQU8sSUFBSSxDQUFDdkIsT0FBTyxDQUFDLHlCQUF5QjtZQUMzQ2EsUUFBUTtZQUNSWSxNQUFNQyxLQUFLQyxTQUFTLENBQUM7Z0JBQUVKO1lBQU07UUFDL0I7SUFDRjtJQUVBLG9CQUFvQjtJQUNwQixNQUFNb0ksY0FBYztRQUNsQixPQUFPLElBQUksQ0FBQzNKLE9BQU8sQ0FBQztJQUN0QjtJQXZwQkE0SixZQUFZeEosVUFBa0JmLHlCQUFBQSwwQkFBQUEsZUFBZ0IsRUFBRSxDQUFFO1FBQ2hELElBQUksQ0FBQ2UsT0FBTyxHQUFHQTtJQUNqQjtBQXNwQkY7QUFFQSx1Q0FBdUM7QUFDaEMsTUFBTXlKLFlBQVksSUFBSTlKLFlBQVc7QUFFeEMsd0NBQXdDO0FBQ3BCO0FBRXBCLCtDQUErQztBQUNwQjtBQUUzQixvREFBb0Q7QUFDN0MsTUFBTStKLGtCQUFrQjtJQUM3QixJQUFJLEtBQTZCLEVBQUUsRUFBTztJQUMxQyxPQUFPLENBQUMsQ0FBQ3JKLGFBQWFDLE9BQU8sQ0FBQztBQUNoQyxFQUFDO0FBRUQsc0NBQXNDO0FBQy9CLE1BQU1xSixpQkFBaUI7SUFDNUIsSUFBSSxLQUE2QixFQUFFLEVBQU87SUFDMUMsTUFBTUMsVUFBVXZKLGFBQWFDLE9BQU8sQ0FBQztJQUNyQyxPQUFPc0osVUFBVXRJLEtBQUt1SSxLQUFLLENBQUNELFdBQVc7QUFDekMsRUFBQztBQUVELDRCQUE0QjtBQUNyQixNQUFNN0csU0FBUztJQUNwQixJQUFJO1FBQ0YsTUFBTTBHLFVBQVUxRyxNQUFNO0lBQ3hCLEVBQUUsT0FBTzlCLE9BQU87UUFDZFYsUUFBUVUsS0FBSyxDQUFDLGlCQUFpQkE7SUFDakMsU0FBVTtRQUNSLElBQUksSUFBNkIsRUFBRTtZQUNqQ1osYUFBYVEsVUFBVSxDQUFDO1lBQ3hCUixhQUFhUSxVQUFVLENBQUM7WUFDeEJDLE9BQU9DLFFBQVEsQ0FBQ0MsSUFBSSxHQUFHO1FBQ3pCO0lBQ0Y7QUFDRixFQUFDIiwic291cmNlcyI6WyJDOlxcbGFyYWdvblxcd3d3XFxsYXJhdmVsLWFwaS1uZ29cXGZyb250LWVuZFxcbGliXFxhcGkudHMiXSwic291cmNlc0NvbnRlbnQiOlsiY29uc3QgQVBJX0JBU0VfVVJMID0gcHJvY2Vzcy5lbnYuTkVYVF9QVUJMSUNfQVBJX1VSTCB8fCBwcm9jZXNzLmVudi5ORVhUX1BVQkxJQ19BUElfQkFTRV9VUkxcclxuXHJcbmludGVyZmFjZSBBcGlSZXNwb25zZTxUID0gYW55PiB7XHJcbiAgc3VjY2VzczogYm9vbGVhblxyXG4gIG1lc3NhZ2U/OiBzdHJpbmdcclxuICBkYXRhPzogVFxyXG4gIGVycm9ycz86IFJlY29yZDxzdHJpbmcsIHN0cmluZ1tdPlxyXG59XHJcblxyXG4vLyBVdGlsaXR5IGZ1bmN0aW9uIHRvIGV4dHJhY3QgYXJyYXkgZGF0YSBmcm9tIHBhZ2luYXRlZCBvciBkaXJlY3QgcmVzcG9uc2VzXHJcbmNvbnN0IGV4dHJhY3RBcnJheURhdGEgPSAocmVzcG9uc2U6IGFueSk6IGFueVtdID0+IHtcclxuICBpZiAoIXJlc3BvbnNlIHx8ICFyZXNwb25zZS5kYXRhKSByZXR1cm4gW11cclxuICBcclxuICAvLyBJZiBkYXRhIGlzIGFscmVhZHkgYW4gYXJyYXksIHJldHVybiBpdFxyXG4gIGlmIChBcnJheS5pc0FycmF5KHJlc3BvbnNlLmRhdGEpKSB7XHJcbiAgICByZXR1cm4gcmVzcG9uc2UuZGF0YVxyXG4gIH1cclxuICBcclxuICAvLyBJZiBkYXRhIGhhcyBhIGRhdGEgcHJvcGVydHkgKHBhZ2luYXRlZCByZXNwb25zZSksIHJldHVybiB0aGF0IGFycmF5XHJcbiAgaWYgKHJlc3BvbnNlLmRhdGEuZGF0YSAmJiBBcnJheS5pc0FycmF5KHJlc3BvbnNlLmRhdGEuZGF0YSkpIHtcclxuICAgIHJldHVybiByZXNwb25zZS5kYXRhLmRhdGFcclxuICB9XHJcbiAgXHJcbiAgLy8gRGVmYXVsdCB0byBlbXB0eSBhcnJheVxyXG4gIHJldHVybiBbXVxyXG59XHJcblxyXG5jbGFzcyBBcGlDbGllbnQge1xyXG4gIHByaXZhdGUgYmFzZVVSTDogc3RyaW5nXHJcblxyXG4gIGNvbnN0cnVjdG9yKGJhc2VVUkw6IHN0cmluZyA9IEFQSV9CQVNFX1VSTCA/PyBcIlwiKSB7XHJcbiAgICB0aGlzLmJhc2VVUkwgPSBiYXNlVVJMXHJcbiAgfVxyXG5cclxuICBhc3luYyByZXF1ZXN0PFQgPSBhbnk+KFxyXG4gICAgZW5kcG9pbnQ6IHN0cmluZywgXHJcbiAgICBvcHRpb25zOiBSZXF1ZXN0SW5pdCA9IHt9XHJcbiAgKTogUHJvbWlzZTxBcGlSZXNwb25zZTxUPj4ge1xyXG4gICAgLy8gVXNlIHRoZSBiYXNlIFVSTCBkaXJlY3RseSBpZiBpdCBhbHJlYWR5IGNvbnRhaW5zIC9hcGkvdjEsIG90aGVyd2lzZSBhZGQgaXRcclxuICAgIGNvbnN0IHVybCA9IHRoaXMuYmFzZVVSTC5pbmNsdWRlcygnL2FwaS92MScpIFxyXG4gICAgICA/IGAke3RoaXMuYmFzZVVSTH0ke2VuZHBvaW50fWBcclxuICAgICAgOiBgJHt0aGlzLmJhc2VVUkx9L2FwaS92MSR7ZW5kcG9pbnR9YFxyXG4gICAgXHJcbiAgICBjb25zdCBjb25maWc6IFJlcXVlc3RJbml0ID0ge1xyXG4gICAgICBoZWFkZXJzOiB7XHJcbiAgICAgICAgJ0NvbnRlbnQtVHlwZSc6ICdhcHBsaWNhdGlvbi9qc29uJyxcclxuICAgICAgICAnQWNjZXB0JzogJ2FwcGxpY2F0aW9uL2pzb24nLFxyXG4gICAgICAgIC4uLm9wdGlvbnMuaGVhZGVycyxcclxuICAgICAgfSxcclxuICAgICAgLi4ub3B0aW9ucyxcclxuICAgIH1cclxuXHJcbiAgICAvLyBBZGQgYXV0aGVudGljYXRpb24gaGVhZGVyIGlmIHRva2VuIGV4aXN0c1xyXG4gICAgY29uc3QgdG9rZW4gPSB0eXBlb2Ygd2luZG93ICE9PSAndW5kZWZpbmVkJyA/IGxvY2FsU3RvcmFnZS5nZXRJdGVtKCdhdXRoVG9rZW4nKSA6IG51bGxcclxuICAgIGlmICh0b2tlbikge1xyXG4gICAgICBjb25maWcuaGVhZGVycyA9IHtcclxuICAgICAgICAuLi5jb25maWcuaGVhZGVycyxcclxuICAgICAgICAnQXV0aG9yaXphdGlvbic6IGBCZWFyZXIgJHt0b2tlbn1gLFxyXG4gICAgICB9XHJcbiAgICB9XHJcblxyXG4gICAgdHJ5IHtcclxuICAgICAgY29uc29sZS5sb2coYEFQSSBSZXF1ZXN0OiAke29wdGlvbnMubWV0aG9kIHx8ICdHRVQnfSAke3VybH1gKSAvLyBEZWJ1ZyBsb2dnaW5nXHJcbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZmV0Y2godXJsLCBjb25maWcpXHJcbiAgICAgIGNvbnN0IGRhdGEgPSBhd2FpdCByZXNwb25zZS5qc29uKClcclxuICAgICAgY29uc29sZS5sb2coYEFQSSBSZXNwb25zZSBmb3IgJHtlbmRwb2ludH06YCwgZGF0YSkgLy8gRGVidWcgbG9nZ2luZ1xyXG5cclxuICAgICAgLy8gSGFuZGxlIDQwMSBVbmF1dGhvcml6ZWQgLSByZWRpcmVjdCB0byBsb2dpblxyXG4gICAgICBpZiAocmVzcG9uc2Uuc3RhdHVzID09PSA0MDEpIHtcclxuICAgICAgICBpZiAodHlwZW9mIHdpbmRvdyAhPT0gJ3VuZGVmaW5lZCcpIHtcclxuICAgICAgICAgIGxvY2FsU3RvcmFnZS5yZW1vdmVJdGVtKCdhdXRoVG9rZW4nKVxyXG4gICAgICAgICAgbG9jYWxTdG9yYWdlLnJlbW92ZUl0ZW0oJ3VzZXInKVxyXG4gICAgICAgICAgd2luZG93LmxvY2F0aW9uLmhyZWYgPSAnL2F1dGgvbG9naW4nXHJcbiAgICAgICAgfVxyXG4gICAgICB9XHJcblxyXG4gICAgICByZXR1cm4gZGF0YVxyXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgICAgY29uc29sZS5lcnJvcignQVBJIHJlcXVlc3QgZmFpbGVkOicsIGVycm9yKVxyXG4gICAgICB0aHJvdyBlcnJvclxyXG4gICAgfVxyXG4gIH1cclxuXHJcbiAgLy8gQXV0aCBlbmRwb2ludHNcclxuICBhc3luYyBsb2dpbihlbWFpbDogc3RyaW5nLCBwYXNzd29yZDogc3RyaW5nKSB7XHJcbiAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IHRoaXMucmVxdWVzdCgnL2xvZ2luJywge1xyXG4gICAgICBtZXRob2Q6ICdQT1NUJyxcclxuICAgICAgYm9keTogSlNPTi5zdHJpbmdpZnkoeyBlbWFpbCwgcGFzc3dvcmQgfSksXHJcbiAgICB9KVxyXG5cclxuICAgIC8vIFN0b3JlIHRva2VuIGFuZCB1c2VyIGRhdGEgaWYgbG9naW4gc3VjY2Vzc2Z1bFxyXG4gICAgaWYgKHJlc3BvbnNlLnN1Y2Nlc3MgJiYgcmVzcG9uc2UuZGF0YT8uYWNjZXNzX3Rva2VuKSB7XHJcbiAgICAgIGlmICh0eXBlb2Ygd2luZG93ICE9PSAndW5kZWZpbmVkJykge1xyXG4gICAgICAgIGxvY2FsU3RvcmFnZS5zZXRJdGVtKCdhdXRoVG9rZW4nLCByZXNwb25zZS5kYXRhLmFjY2Vzc190b2tlbilcclxuICAgICAgICBsb2NhbFN0b3JhZ2Uuc2V0SXRlbSgndXNlcicsIEpTT04uc3RyaW5naWZ5KHJlc3BvbnNlLmRhdGEudXNlcikpXHJcbiAgICAgIH1cclxuICAgIH1cclxuXHJcbiAgICByZXR1cm4gcmVzcG9uc2VcclxuICB9XHJcblxyXG4gIGFzeW5jIHJlZ2lzdGVyKHVzZXJEYXRhOiB7XHJcbiAgICBmaXJzdF9uYW1lOiBzdHJpbmdcclxuICAgIGxhc3RfbmFtZTogc3RyaW5nXHJcbiAgICBlbWFpbDogc3RyaW5nXHJcbiAgICBwYXNzd29yZDogc3RyaW5nXHJcbiAgICBwYXNzd29yZF9jb25maXJtYXRpb246IHN0cmluZ1xyXG4gICAgcGhvbmVfbnVtYmVyPzogc3RyaW5nXHJcbiAgICBhZGRyZXNzPzogc3RyaW5nXHJcbiAgICBkYXRlX29mX2JpcnRoPzogc3RyaW5nXHJcbiAgICBjaXR5Pzogc3RyaW5nXHJcbiAgICBzdGF0ZT86IHN0cmluZ1xyXG4gICAgY291bnRyeT86IHN0cmluZ1xyXG4gICAgdXNlcl90eXBlPzogJ3N0dWRlbnQnIHwgJ3BhcnRuZXInIHwgJ3ZvbHVudGVlcidcclxuICAgIC8vIEFkZGl0aW9uYWwgZGF0YSBmb3Igc3BlY2lmaWMgdXNlciB0eXBlc1xyXG4gICAgYWRkaXRpb25hbF9kYXRhPzogYW55XHJcbiAgfSkge1xyXG4gICAgLy8gRmlyc3QsIHJlZ2lzdGVyIHRoZSBiYXNpYyB1c2VyXHJcbiAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IHRoaXMucmVxdWVzdCgnL3JlZ2lzdGVyJywge1xyXG4gICAgICBtZXRob2Q6ICdQT1NUJyxcclxuICAgICAgYm9keTogSlNPTi5zdHJpbmdpZnkoe1xyXG4gICAgICAgIGZpcnN0X25hbWU6IHVzZXJEYXRhLmZpcnN0X25hbWUsXHJcbiAgICAgICAgbGFzdF9uYW1lOiB1c2VyRGF0YS5sYXN0X25hbWUsXHJcbiAgICAgICAgZW1haWw6IHVzZXJEYXRhLmVtYWlsLFxyXG4gICAgICAgIHBhc3N3b3JkOiB1c2VyRGF0YS5wYXNzd29yZCxcclxuICAgICAgICBwYXNzd29yZF9jb25maXJtYXRpb246IHVzZXJEYXRhLnBhc3N3b3JkX2NvbmZpcm1hdGlvbixcclxuICAgICAgICBwaG9uZV9udW1iZXI6IHVzZXJEYXRhLnBob25lX251bWJlcixcclxuICAgICAgICBhZGRyZXNzOiB1c2VyRGF0YS5hZGRyZXNzLFxyXG4gICAgICAgIGRhdGVfb2ZfYmlydGg6IHVzZXJEYXRhLmRhdGVfb2ZfYmlydGgsXHJcbiAgICAgICAgY2l0eTogdXNlckRhdGEuY2l0eSxcclxuICAgICAgICBzdGF0ZTogdXNlckRhdGEuc3RhdGUsXHJcbiAgICAgICAgY291bnRyeTogdXNlckRhdGEuY291bnRyeSxcclxuICAgICAgfSksXHJcbiAgICB9KVxyXG5cclxuICAgIC8vIFN0b3JlIHRva2VuIGFuZCB1c2VyIGRhdGEgaWYgcmVnaXN0cmF0aW9uIHN1Y2Nlc3NmdWxcclxuICAgIGlmIChyZXNwb25zZS5zdWNjZXNzICYmIHJlc3BvbnNlLmRhdGE/LnRva2VuKSB7XHJcbiAgICAgIGlmICh0eXBlb2Ygd2luZG93ICE9PSAndW5kZWZpbmVkJykge1xyXG4gICAgICAgIGxvY2FsU3RvcmFnZS5zZXRJdGVtKCdhdXRoVG9rZW4nLCByZXNwb25zZS5kYXRhLnRva2VuKVxyXG4gICAgICAgIGxvY2FsU3RvcmFnZS5zZXRJdGVtKCd1c2VyJywgSlNPTi5zdHJpbmdpZnkocmVzcG9uc2UuZGF0YS51c2VyKSlcclxuICAgICAgfVxyXG4gICAgfVxyXG5cclxuICAgIC8vIElmIHJlZ2lzdHJhdGlvbiBpcyBzdWNjZXNzZnVsLCBoYW5kbGUgdXNlciB0eXBlIHNwZWNpZmljIGRhdGFcclxuICAgIGlmIChyZXNwb25zZS5zdWNjZXNzICYmIHVzZXJEYXRhLnVzZXJfdHlwZSAmJiB1c2VyRGF0YS5hZGRpdGlvbmFsX2RhdGEpIHtcclxuICAgICAgdHJ5IHtcclxuICAgICAgICBpZiAodXNlckRhdGEudXNlcl90eXBlID09PSAndm9sdW50ZWVyJykge1xyXG4gICAgICAgICAgLy8gU3VibWl0IHZvbHVudGVlciBhcHBsaWNhdGlvblxyXG4gICAgICAgICAgYXdhaXQgdGhpcy5hcHBseUFzVm9sdW50ZWVyKHVzZXJEYXRhLmFkZGl0aW9uYWxfZGF0YSlcclxuICAgICAgICB9IGVsc2UgaWYgKHVzZXJEYXRhLnVzZXJfdHlwZSA9PT0gJ3N0dWRlbnQnIHx8IHVzZXJEYXRhLnVzZXJfdHlwZSA9PT0gJ3BhcnRuZXInKSB7XHJcbiAgICAgICAgICAvLyBTdG9yZSBhZGRpdGlvbmFsIGRhdGEgaW4gdXNlciBwcmVmZXJlbmNlc1xyXG4gICAgICAgICAgY29uc3QgcHJlZmVyZW5jZXMgPSB7XHJcbiAgICAgICAgICAgIHVzZXJfdHlwZTogdXNlckRhdGEudXNlcl90eXBlLFxyXG4gICAgICAgICAgICBwcm9maWxlX2RhdGE6IHVzZXJEYXRhLmFkZGl0aW9uYWxfZGF0YSxcclxuICAgICAgICAgICAgcHJvZmlsZV9jb21wbGV0ZWQ6IHRydWVcclxuICAgICAgICAgIH1cclxuICAgICAgICAgIGF3YWl0IHRoaXMudXBkYXRlVXNlclByZWZlcmVuY2VzKHByZWZlcmVuY2VzKVxyXG4gICAgICAgIH1cclxuICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgICAgICBjb25zb2xlLmVycm9yKCdBZGRpdGlvbmFsIGRhdGEgc3VibWlzc2lvbiBmYWlsZWQ6JywgZXJyb3IpXHJcbiAgICAgICAgLy8gUmV0dXJuIHRoZSB1c2VyIHJlZ2lzdHJhdGlvbiBzdWNjZXNzIGJ1dCBub3RlIGFkZGl0aW9uYWwgZGF0YSBmYWlsZWRcclxuICAgICAgICByZXR1cm4ge1xyXG4gICAgICAgICAgLi4ucmVzcG9uc2UsXHJcbiAgICAgICAgICBtZXNzYWdlOiByZXNwb25zZS5tZXNzYWdlICsgJyBIb3dldmVyLCBhZGRpdGlvbmFsIHByb2ZpbGUgaW5mb3JtYXRpb24gY291bGQgbm90IGJlIHNhdmVkLiBZb3UgY2FuIGNvbXBsZXRlIHlvdXIgcHJvZmlsZSBsYXRlci4nXHJcbiAgICAgICAgfVxyXG4gICAgICB9XHJcbiAgICB9XHJcblxyXG4gICAgcmV0dXJuIHJlc3BvbnNlXHJcbiAgfVxyXG5cclxuICBhc3luYyBsb2dvdXQoKSB7XHJcbiAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IHRoaXMucmVxdWVzdCgnL2xvZ291dCcsIHtcclxuICAgICAgbWV0aG9kOiAnUE9TVCcsXHJcbiAgICB9KVxyXG5cclxuICAgIC8vIENsZWFyIHN0b3JlZCB0b2tlbiBhbmQgdXNlciBkYXRhIG9uIGxvZ291dFxyXG4gICAgaWYgKHR5cGVvZiB3aW5kb3cgIT09ICd1bmRlZmluZWQnKSB7XHJcbiAgICAgIGxvY2FsU3RvcmFnZS5yZW1vdmVJdGVtKCdhdXRoVG9rZW4nKVxyXG4gICAgICBsb2NhbFN0b3JhZ2UucmVtb3ZlSXRlbSgndXNlcicpXHJcbiAgICB9XHJcblxyXG4gICAgcmV0dXJuIHJlc3BvbnNlXHJcbiAgfVxyXG5cclxuICBhc3luYyBnZXRVc2VyKCkge1xyXG4gICAgcmV0dXJuIHRoaXMucmVxdWVzdCgnL3VzZXInKVxyXG4gIH1cclxuXHJcbiAgYXN5bmMgZm9yZ290UGFzc3dvcmQoZW1haWw6IHN0cmluZykge1xyXG4gICAgcmV0dXJuIHRoaXMucmVxdWVzdCgnL2ZvcmdvdC1wYXNzd29yZCcsIHtcclxuICAgICAgbWV0aG9kOiAnUE9TVCcsXHJcbiAgICAgIGJvZHk6IEpTT04uc3RyaW5naWZ5KHsgZW1haWwgfSksXHJcbiAgICB9KVxyXG4gIH1cclxuXHJcbiAgLy8gUHJvZmlsZSBlbmRwb2ludHNcclxuICBhc3luYyBnZXRQcm9maWxlKCkge1xyXG4gICAgcmV0dXJuIHRoaXMucmVxdWVzdCgnL3Byb2ZpbGUnKVxyXG4gIH1cclxuXHJcbiAgYXN5bmMgdXBkYXRlUHJvZmlsZShwcm9maWxlRGF0YTogYW55KSB7XHJcbiAgICByZXR1cm4gdGhpcy5yZXF1ZXN0KCcvcHJvZmlsZScsIHtcclxuICAgICAgbWV0aG9kOiAnUFVUJyxcclxuICAgICAgYm9keTogSlNPTi5zdHJpbmdpZnkocHJvZmlsZURhdGEpLFxyXG4gICAgfSlcclxuICB9XHJcblxyXG4gIGFzeW5jIHVwZGF0ZVVzZXJQcmVmZXJlbmNlcyhwcmVmZXJlbmNlczogYW55KSB7XHJcbiAgICByZXR1cm4gdGhpcy5yZXF1ZXN0KCcvcHJvZmlsZScsIHtcclxuICAgICAgbWV0aG9kOiAnUFVUJyxcclxuICAgICAgYm9keTogSlNPTi5zdHJpbmdpZnkoeyBwcmVmZXJlbmNlcyB9KSxcclxuICAgIH0pXHJcbiAgfVxyXG5cclxuICBhc3luYyB1cGxvYWRBdmF0YXIoZmlsZTogRmlsZSkge1xyXG4gICAgY29uc3QgZm9ybURhdGEgPSBuZXcgRm9ybURhdGEoKVxyXG4gICAgZm9ybURhdGEuYXBwZW5kKCdhdmF0YXInLCBmaWxlKVxyXG5cclxuICAgIC8vIFVzZSB0aGUgYmFzZSBVUkwgZGlyZWN0bHkgaWYgaXQgYWxyZWFkeSBjb250YWlucyAvYXBpL3YxLCBvdGhlcndpc2UgYWRkIGl0XHJcbiAgICBjb25zdCB1cmwgPSB0aGlzLmJhc2VVUkwuaW5jbHVkZXMoJy9hcGkvdjEnKSBcclxuICAgICAgPyBgJHt0aGlzLmJhc2VVUkx9L3Byb2ZpbGUvYXZhdGFyYFxyXG4gICAgICA6IGAke3RoaXMuYmFzZVVSTH0vYXBpL3YxL3Byb2ZpbGUvYXZhdGFyYFxyXG5cclxuICAgIGNvbnN0IHRva2VuID0gdHlwZW9mIHdpbmRvdyAhPT0gJ3VuZGVmaW5lZCcgPyBsb2NhbFN0b3JhZ2UuZ2V0SXRlbSgnYXV0aFRva2VuJykgOiBudWxsXHJcbiAgICBcclxuICAgIHRyeSB7XHJcbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZmV0Y2godXJsLCB7XHJcbiAgICAgICAgbWV0aG9kOiAnUE9TVCcsXHJcbiAgICAgICAgaGVhZGVyczoge1xyXG4gICAgICAgICAgJ0F1dGhvcml6YXRpb24nOiB0b2tlbiA/IGBCZWFyZXIgJHt0b2tlbn1gIDogJycsXHJcbiAgICAgICAgICAnQWNjZXB0JzogJ2FwcGxpY2F0aW9uL2pzb24nLFxyXG4gICAgICAgIH0sXHJcbiAgICAgICAgYm9keTogZm9ybURhdGEsXHJcbiAgICAgIH0pXHJcblxyXG4gICAgICBjb25zdCBkYXRhID0gYXdhaXQgcmVzcG9uc2UuanNvbigpXHJcbiAgICAgIFxyXG4gICAgICBpZiAocmVzcG9uc2Uuc3RhdHVzID09PSA0MDEpIHtcclxuICAgICAgICBpZiAodHlwZW9mIHdpbmRvdyAhPT0gJ3VuZGVmaW5lZCcpIHtcclxuICAgICAgICAgIGxvY2FsU3RvcmFnZS5yZW1vdmVJdGVtKCdhdXRoVG9rZW4nKVxyXG4gICAgICAgICAgbG9jYWxTdG9yYWdlLnJlbW92ZUl0ZW0oJ3VzZXInKVxyXG4gICAgICAgICAgd2luZG93LmxvY2F0aW9uLmhyZWYgPSAnL2F1dGgvbG9naW4nXHJcbiAgICAgICAgfVxyXG4gICAgICB9XHJcblxyXG4gICAgICByZXR1cm4gZGF0YVxyXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgICAgY29uc29sZS5lcnJvcignQXZhdGFyIHVwbG9hZCBmYWlsZWQ6JywgZXJyb3IpXHJcbiAgICAgIHRocm93IGVycm9yXHJcbiAgICB9XHJcbiAgfVxyXG5cclxuICBhc3luYyBjaGFuZ2VQYXNzd29yZChwYXNzd29yZERhdGE6IHtcclxuICAgIGN1cnJlbnRfcGFzc3dvcmQ6IHN0cmluZ1xyXG4gICAgbmV3X3Bhc3N3b3JkOiBzdHJpbmdcclxuICAgIG5ld19wYXNzd29yZF9jb25maXJtYXRpb246IHN0cmluZ1xyXG4gIH0pIHtcclxuICAgIHJldHVybiB0aGlzLnJlcXVlc3QoJy9wcm9maWxlL3Bhc3N3b3JkJywge1xyXG4gICAgICBtZXRob2Q6ICdQVVQnLFxyXG4gICAgICBib2R5OiBKU09OLnN0cmluZ2lmeShwYXNzd29yZERhdGEpLFxyXG4gICAgfSlcclxuICB9XHJcblxyXG4gIGFzeW5jIGdlbmVyYXRlUXJDb2RlKCkge1xyXG4gICAgcmV0dXJuIHRoaXMucmVxdWVzdCgnL3Byb2ZpbGUvZ2VuZXJhdGUtcXInLCB7XHJcbiAgICAgIG1ldGhvZDogJ1BPU1QnLFxyXG4gICAgfSlcclxuICB9XHJcblxyXG4gIGFzeW5jIGdldElkQ2FyZCgpIHtcclxuICAgIHJldHVybiB0aGlzLnJlcXVlc3QoJy9wcm9maWxlL2lkLWNhcmQnKVxyXG4gIH1cclxuXHJcbiAgLy8gRGFzaGJvYXJkIGVuZHBvaW50c1xyXG4gIGFzeW5jIGdldERhc2hib2FyZFN1bW1hcnkoKSB7XHJcbiAgICByZXR1cm4gdGhpcy5yZXF1ZXN0KCcvZGFzaGJvYXJkL3N1bW1hcnknKVxyXG4gIH1cclxuXHJcbiAgLy8gVm9sdW50ZWVyIGVuZHBvaW50c1xyXG4gIGFzeW5jIGdldFZvbHVudGVlckFwcGxpY2F0aW9uKCkge1xyXG4gICAgcmV0dXJuIHRoaXMucmVxdWVzdCgnL3ZvbHVudGVlci9hcHBsaWNhdGlvbicpXHJcbiAgfVxyXG5cclxuICBhc3luYyBnZXRWb2x1bnRlZXJIb3VycygpIHtcclxuICAgIHJldHVybiB0aGlzLnJlcXVlc3QoJy92b2x1bnRlZXIvaG91cnMnKVxyXG4gIH1cclxuXHJcbiAgYXN5bmMgZ2V0Vm9sdW50ZWVyT3Bwb3J0dW5pdGllcygpIHtcclxuICAgIHJldHVybiB0aGlzLnJlcXVlc3QoJy92b2x1bnRlZXIvb3Bwb3J0dW5pdGllcycpXHJcbiAgfVxyXG5cclxuICBhc3luYyBsb2dWb2x1bnRlZXJIb3Vycyhob3Vyc0RhdGE6IGFueSkge1xyXG4gICAgcmV0dXJuIHRoaXMucmVxdWVzdCgnL3ZvbHVudGVlci9ob3VycycsIHtcclxuICAgICAgbWV0aG9kOiAnUE9TVCcsXHJcbiAgICAgIGJvZHk6IEpTT04uc3RyaW5naWZ5KGhvdXJzRGF0YSksXHJcbiAgICB9KVxyXG4gIH1cclxuXHJcbiAgLy8gU2Nob2xhcnNoaXAgZW5kcG9pbnRzXHJcbiAgYXN5bmMgZ2V0TXlTY2hvbGFyc2hpcEFwcGxpY2F0aW9ucygpIHtcclxuICAgIHJldHVybiB0aGlzLnJlcXVlc3QoJy9zY2hvbGFyc2hpcHMvbXktYXBwbGljYXRpb25zJylcclxuICB9XHJcblxyXG4gIGFzeW5jIGdldFNjaG9sYXJzaGlwcygpIHtcclxuICAgIHJldHVybiB0aGlzLnJlcXVlc3QoJy9zY2hvbGFyc2hpcHMnKVxyXG4gIH1cclxuXHJcbiAgYXN5bmMgYXBwbHlGb3JTY2hvbGFyc2hpcChzY2hvbGFyc2hpcElkOiBudW1iZXIsIGFwcGxpY2F0aW9uRGF0YTogYW55KSB7XHJcbiAgICByZXR1cm4gdGhpcy5yZXF1ZXN0KGAvc2Nob2xhcnNoaXBzLyR7c2Nob2xhcnNoaXBJZH0vYXBwbHlgLCB7XHJcbiAgICAgIG1ldGhvZDogJ1BPU1QnLFxyXG4gICAgICBib2R5OiBKU09OLnN0cmluZ2lmeShhcHBsaWNhdGlvbkRhdGEpLFxyXG4gICAgfSlcclxuICB9XHJcblxyXG4gIC8vIEV2ZW50IGVuZHBvaW50c1xyXG4gIGFzeW5jIGdldE15RXZlbnRSZWdpc3RyYXRpb25zKCkge1xyXG4gICAgcmV0dXJuIHRoaXMucmVxdWVzdCgnL2V2ZW50cy9teS1yZWdpc3RyYXRpb25zJylcclxuICB9XHJcblxyXG4gIGFzeW5jIGdldFVwY29taW5nRXZlbnRzKCkge1xyXG4gICAgcmV0dXJuIHRoaXMucmVxdWVzdCgnL2V2ZW50cy91cGNvbWluZycpXHJcbiAgfVxyXG5cclxuICAvLyBIZWxwZXIgbWV0aG9kIGZvciBidWlsZGluZyBxdWVyeSBzdHJpbmdzXHJcbiAgcHJpdmF0ZSBidWlsZFF1ZXJ5U3RyaW5nKHBhcmFtczogUmVjb3JkPHN0cmluZywgYW55Pik6IHN0cmluZyB7XHJcbiAgICBjb25zdCBzZWFyY2hQYXJhbXMgPSBuZXcgVVJMU2VhcmNoUGFyYW1zKClcclxuXHJcbiAgICBPYmplY3QuZW50cmllcyhwYXJhbXMpLmZvckVhY2goKFtrZXksIHZhbHVlXSkgPT4ge1xyXG4gICAgICBpZiAodmFsdWUgIT09IHVuZGVmaW5lZCAmJiB2YWx1ZSAhPT0gbnVsbCAmJiB2YWx1ZSAhPT0gJycpIHtcclxuICAgICAgICBpZiAoQXJyYXkuaXNBcnJheSh2YWx1ZSkpIHtcclxuICAgICAgICAgIHZhbHVlLmZvckVhY2goaXRlbSA9PiBzZWFyY2hQYXJhbXMuYXBwZW5kKGAke2tleX1bXWAsIGl0ZW0pKVxyXG4gICAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgICBzZWFyY2hQYXJhbXMuYXBwZW5kKGtleSwgdmFsdWUudG9TdHJpbmcoKSlcclxuICAgICAgICB9XHJcbiAgICAgIH1cclxuICAgIH0pXHJcblxyXG4gICAgcmV0dXJuIHNlYXJjaFBhcmFtcy50b1N0cmluZygpXHJcbiAgfVxyXG5cclxuICAvLyBBZG1pbiBEYXNoYm9hcmQgZW5kcG9pbnRzXHJcbiAgYXN5bmMgZ2V0QWRtaW5EYXNoYm9hcmQoKSB7XHJcbiAgICByZXR1cm4gdGhpcy5yZXF1ZXN0KCcvYWRtaW4vZGFzaGJvYXJkJylcclxuICB9XHJcblxyXG4gIGFzeW5jIGdldEFkbWluQW5hbHl0aWNzKHBhcmFtcz86IHsgZGF5cz86IG51bWJlciB9KSB7XHJcbiAgICBjb25zdCBxdWVyeVN0cmluZyA9IHBhcmFtcyA/IGA/JHt0aGlzLmJ1aWxkUXVlcnlTdHJpbmcocGFyYW1zKX1gIDogJydcclxuICAgIHJldHVybiB0aGlzLnJlcXVlc3QoYC9hZG1pbi9kYXNoYm9hcmQvYW5hbHl0aWNzJHtxdWVyeVN0cmluZ31gKVxyXG4gIH1cclxuXHJcbiAgYXN5bmMgZ2V0QWRtaW5TdGF0cyhwYXJhbXM/OiB7IHBlcmlvZD86IHN0cmluZyB9KSB7XHJcbiAgICBjb25zdCBxdWVyeVN0cmluZyA9IHBhcmFtcyA/IGA/JHt0aGlzLmJ1aWxkUXVlcnlTdHJpbmcocGFyYW1zKX1gIDogJydcclxuICAgIHJldHVybiB0aGlzLnJlcXVlc3QoYC9hZG1pbi9kYXNoYm9hcmQvc3RhdHMke3F1ZXJ5U3RyaW5nfWApXHJcbiAgfVxyXG5cclxuICAvLyBBZG1pbiBVc2VyIE1hbmFnZW1lbnRcclxuICBhc3luYyBnZXRBZG1pblVzZXJzKHBhcmFtcz86IHtcclxuICAgIHBhZ2U/OiBudW1iZXJcclxuICAgIHBlcl9wYWdlPzogbnVtYmVyXHJcbiAgICBzZWFyY2g/OiBzdHJpbmdcclxuICAgIHJvbGU/OiBzdHJpbmdcclxuICAgIHN0YXR1cz86IHN0cmluZ1xyXG4gICAgZGF0ZV9yYW5nZT86IHN0cmluZ1xyXG4gICAgc29ydF9ieT86IHN0cmluZ1xyXG4gICAgc29ydF9vcmRlcj86IHN0cmluZ1xyXG4gIH0pIHtcclxuICAgIGNvbnN0IHF1ZXJ5U3RyaW5nID0gcGFyYW1zID8gYD8ke3RoaXMuYnVpbGRRdWVyeVN0cmluZyhwYXJhbXMpfWAgOiAnJ1xyXG4gICAgcmV0dXJuIHRoaXMucmVxdWVzdChgL2FkbWluL3VzZXJzJHtxdWVyeVN0cmluZ31gKVxyXG4gIH1cclxuXHJcbiAgYXN5bmMgY3JlYXRlQWRtaW5Vc2VyKHVzZXJEYXRhOiBhbnkpIHtcclxuICAgIHJldHVybiB0aGlzLnJlcXVlc3QoJy9hZG1pbi91c2VycycsIHtcclxuICAgICAgbWV0aG9kOiAnUE9TVCcsXHJcbiAgICAgIGJvZHk6IEpTT04uc3RyaW5naWZ5KHVzZXJEYXRhKSxcclxuICAgIH0pXHJcbiAgfVxyXG5cclxuICBhc3luYyB1cGRhdGVBZG1pblVzZXIodXNlcklkOiBudW1iZXIsIHVzZXJEYXRhOiBhbnkpIHtcclxuICAgIHJldHVybiB0aGlzLnJlcXVlc3QoYC9hZG1pbi91c2Vycy8ke3VzZXJJZH1gLCB7XHJcbiAgICAgIG1ldGhvZDogJ1BVVCcsXHJcbiAgICAgIGJvZHk6IEpTT04uc3RyaW5naWZ5KHVzZXJEYXRhKSxcclxuICAgIH0pXHJcbiAgfVxyXG5cclxuICBhc3luYyBkZWxldGVBZG1pblVzZXIodXNlcklkOiBudW1iZXIpIHtcclxuICAgIHJldHVybiB0aGlzLnJlcXVlc3QoYC9hZG1pbi91c2Vycy8ke3VzZXJJZH1gLCB7XHJcbiAgICAgIG1ldGhvZDogJ0RFTEVURScsXHJcbiAgICB9KVxyXG4gIH1cclxuXHJcbiAgYXN5bmMgYnVsa0FjdGlvblVzZXJzKGRhdGE6IHsgYWN0aW9uOiBzdHJpbmc7IHVzZXJfaWRzOiBudW1iZXJbXSB9KSB7XHJcbiAgICByZXR1cm4gdGhpcy5yZXF1ZXN0KCcvYWRtaW4vdXNlcnMvYnVsay1hY3Rpb24nLCB7XHJcbiAgICAgIG1ldGhvZDogJ1BPU1QnLFxyXG4gICAgICBib2R5OiBKU09OLnN0cmluZ2lmeShkYXRhKSxcclxuICAgIH0pXHJcbiAgfVxyXG5cclxuICBhc3luYyBleHBvcnRVc2VycyhwYXJhbXM/OiB7IGZvcm1hdD86IHN0cmluZzsgcm9sZT86IHN0cmluZzsgc3RhdHVzPzogc3RyaW5nIH0pIHtcclxuICAgIGNvbnN0IHF1ZXJ5U3RyaW5nID0gcGFyYW1zID8gYD8ke3RoaXMuYnVpbGRRdWVyeVN0cmluZyhwYXJhbXMpfWAgOiAnJ1xyXG4gICAgcmV0dXJuIHRoaXMucmVxdWVzdChgL2FkbWluL3VzZXJzL2V4cG9ydCR7cXVlcnlTdHJpbmd9YClcclxuICB9XHJcblxyXG4gIC8vIEFkbWluIFNjaG9sYXJzaGlwIE1hbmFnZW1lbnRcclxuICBhc3luYyBnZXRTY2hvbGFyc2hpcEFwcGxpY2F0aW9ucyhwYXJhbXM/OiB7XHJcbiAgICBwYWdlPzogbnVtYmVyXHJcbiAgICBwZXJfcGFnZT86IG51bWJlclxyXG4gICAgc3RhdHVzPzogc3RyaW5nXHJcbiAgICBzY2hvbGFyc2hpcF9pZD86IG51bWJlclxyXG4gICAgc2VhcmNoPzogc3RyaW5nXHJcbiAgICBkYXRlX3JhbmdlPzogc3RyaW5nXHJcbiAgICBzb3J0X2J5Pzogc3RyaW5nXHJcbiAgICBzb3J0X29yZGVyPzogc3RyaW5nXHJcbiAgfSkge1xyXG4gICAgY29uc3QgcXVlcnlTdHJpbmcgPSBwYXJhbXMgPyBgPyR7dGhpcy5idWlsZFF1ZXJ5U3RyaW5nKHBhcmFtcyl9YCA6ICcnXHJcbiAgICByZXR1cm4gdGhpcy5yZXF1ZXN0KGAvYWRtaW4vc2Nob2xhcnNoaXAtYXBwbGljYXRpb25zJHtxdWVyeVN0cmluZ31gKVxyXG4gIH1cclxuXHJcbiAgYXN5bmMgcmV2aWV3U2Nob2xhcnNoaXBBcHBsaWNhdGlvbihhcHBsaWNhdGlvbklkOiBudW1iZXIsIHJldmlld0RhdGE6IGFueSkge1xyXG4gICAgcmV0dXJuIHRoaXMucmVxdWVzdChgL2FkbWluL3NjaG9sYXJzaGlwLWFwcGxpY2F0aW9ucy8ke2FwcGxpY2F0aW9uSWR9L3Jldmlld2AsIHtcclxuICAgICAgbWV0aG9kOiAnUFVUJyxcclxuICAgICAgYm9keTogSlNPTi5zdHJpbmdpZnkocmV2aWV3RGF0YSksXHJcbiAgICB9KVxyXG4gIH1cclxuXHJcbiAgYXN5bmMgYnVsa0FjdGlvblNjaG9sYXJzaGlwQXBwbGljYXRpb25zKGRhdGE6IHtcclxuICAgIGFjdGlvbjogc3RyaW5nXHJcbiAgICBhcHBsaWNhdGlvbl9pZHM6IG51bWJlcltdXHJcbiAgICByZXZpZXdfbm90ZXM/OiBzdHJpbmdcclxuICAgIGF3YXJkX2Ftb3VudD86IG51bWJlclxyXG4gIH0pIHtcclxuICAgIHJldHVybiB0aGlzLnJlcXVlc3QoJy9hZG1pbi9zY2hvbGFyc2hpcC1hcHBsaWNhdGlvbnMvYnVsay1hY3Rpb24nLCB7XHJcbiAgICAgIG1ldGhvZDogJ1BPU1QnLFxyXG4gICAgICBib2R5OiBKU09OLnN0cmluZ2lmeShkYXRhKSxcclxuICAgIH0pXHJcbiAgfVxyXG5cclxuICBhc3luYyBleHBvcnRTY2hvbGFyc2hpcEFwcGxpY2F0aW9ucyhwYXJhbXM/OiB7XHJcbiAgICBmb3JtYXQ/OiBzdHJpbmdcclxuICAgIHN0YXR1cz86IHN0cmluZ1xyXG4gICAgc2Nob2xhcnNoaXBfaWQ/OiBudW1iZXJcclxuICB9KSB7XHJcbiAgICBjb25zdCBxdWVyeVN0cmluZyA9IHBhcmFtcyA/IGA/JHt0aGlzLmJ1aWxkUXVlcnlTdHJpbmcocGFyYW1zKX1gIDogJydcclxuICAgIHJldHVybiB0aGlzLnJlcXVlc3QoYC9hZG1pbi9zY2hvbGFyc2hpcC1hcHBsaWNhdGlvbnMvZXhwb3J0JHtxdWVyeVN0cmluZ31gKVxyXG4gIH1cclxuXHJcbiAgYXN5bmMgZ2V0U2Nob2xhcnNoaXBTdGF0aXN0aWNzKCkge1xyXG4gICAgcmV0dXJuIHRoaXMucmVxdWVzdCgnL2FkbWluL3NjaG9sYXJzaGlwcy9zdGF0aXN0aWNzJylcclxuICB9XHJcblxyXG4gIC8vIEFkbWluIEV2ZW50IE1hbmFnZW1lbnRcclxuICBhc3luYyBnZXRBZG1pbkV2ZW50cyhwYXJhbXM/OiB7XHJcbiAgICBwYWdlPzogbnVtYmVyXHJcbiAgICBwZXJfcGFnZT86IG51bWJlclxyXG4gICAgc2VhcmNoPzogc3RyaW5nXHJcbiAgICBzdGF0dXM/OiBzdHJpbmdcclxuICAgIGRhdGVfcmFuZ2U/OiBzdHJpbmdcclxuICAgIHNvcnRfYnk/OiBzdHJpbmdcclxuICAgIHNvcnRfb3JkZXI/OiBzdHJpbmdcclxuICB9KSB7XHJcbiAgICBjb25zdCBxdWVyeVN0cmluZyA9IHBhcmFtcyA/IGA/JHt0aGlzLmJ1aWxkUXVlcnlTdHJpbmcocGFyYW1zKX1gIDogJydcclxuICAgIHJldHVybiB0aGlzLnJlcXVlc3QoYC9hZG1pbi9ldmVudHMke3F1ZXJ5U3RyaW5nfWApXHJcbiAgfVxyXG5cclxuICBhc3luYyBjcmVhdGVBZG1pbkV2ZW50KGV2ZW50RGF0YTogYW55KSB7XHJcbiAgICByZXR1cm4gdGhpcy5yZXF1ZXN0KCcvYWRtaW4vZXZlbnRzJywge1xyXG4gICAgICBtZXRob2Q6ICdQT1NUJyxcclxuICAgICAgYm9keTogSlNPTi5zdHJpbmdpZnkoZXZlbnREYXRhKSxcclxuICAgIH0pXHJcbiAgfVxyXG5cclxuICBhc3luYyB1cGRhdGVBZG1pbkV2ZW50KGV2ZW50SWQ6IG51bWJlciwgZXZlbnREYXRhOiBhbnkpIHtcclxuICAgIHJldHVybiB0aGlzLnJlcXVlc3QoYC9hZG1pbi9ldmVudHMvJHtldmVudElkfWAsIHtcclxuICAgICAgbWV0aG9kOiAnUFVUJyxcclxuICAgICAgYm9keTogSlNPTi5zdHJpbmdpZnkoZXZlbnREYXRhKSxcclxuICAgIH0pXHJcbiAgfVxyXG5cclxuICBhc3luYyBkZWxldGVBZG1pbkV2ZW50KGV2ZW50SWQ6IG51bWJlcikge1xyXG4gICAgcmV0dXJuIHRoaXMucmVxdWVzdChgL2FkbWluL2V2ZW50cy8ke2V2ZW50SWR9YCwge1xyXG4gICAgICBtZXRob2Q6ICdERUxFVEUnLFxyXG4gICAgfSlcclxuICB9XHJcblxyXG4gIGFzeW5jIGJ1bGtBY3Rpb25FdmVudHMoZGF0YTogeyBhY3Rpb246IHN0cmluZzsgZXZlbnRfaWRzOiBudW1iZXJbXSB9KSB7XHJcbiAgICByZXR1cm4gdGhpcy5yZXF1ZXN0KCcvYWRtaW4vZXZlbnRzL2J1bGstYWN0aW9uJywge1xyXG4gICAgICBtZXRob2Q6ICdQT1NUJyxcclxuICAgICAgYm9keTogSlNPTi5zdHJpbmdpZnkoZGF0YSksXHJcbiAgICB9KVxyXG4gIH1cclxuXHJcbiAgYXN5bmMgZXhwb3J0RXZlbnRzKHBhcmFtcz86IHsgZm9ybWF0Pzogc3RyaW5nOyBzdGF0dXM/OiBzdHJpbmcgfSkge1xyXG4gICAgY29uc3QgcXVlcnlTdHJpbmcgPSBwYXJhbXMgPyBgPyR7dGhpcy5idWlsZFF1ZXJ5U3RyaW5nKHBhcmFtcyl9YCA6ICcnXHJcbiAgICByZXR1cm4gdGhpcy5yZXF1ZXN0KGAvYWRtaW4vZXZlbnRzL2V4cG9ydCR7cXVlcnlTdHJpbmd9YClcclxuICB9XHJcblxyXG4gIGFzeW5jIGdldEV2ZW50U3RhdGlzdGljcygpIHtcclxuICAgIHJldHVybiB0aGlzLnJlcXVlc3QoJy9hZG1pbi9ldmVudHMvc3RhdGlzdGljcycpXHJcbiAgfVxyXG5cclxuICAvLyBBZG1pbiBQcm9ncmFtIE1hbmFnZW1lbnRcclxuICBhc3luYyBnZXRBZG1pblByb2dyYW1zKHBhcmFtcz86IHtcclxuICAgIHBhZ2U/OiBudW1iZXJcclxuICAgIHBlcl9wYWdlPzogbnVtYmVyXHJcbiAgICBzZWFyY2g/OiBzdHJpbmdcclxuICAgIHN0YXR1cz86IHN0cmluZ1xyXG4gICAgZGF0ZV9yYW5nZT86IHN0cmluZ1xyXG4gICAgc29ydF9ieT86IHN0cmluZ1xyXG4gICAgc29ydF9vcmRlcj86IHN0cmluZ1xyXG4gIH0pIHtcclxuICAgIGNvbnN0IHF1ZXJ5U3RyaW5nID0gcGFyYW1zID8gYD8ke3RoaXMuYnVpbGRRdWVyeVN0cmluZyhwYXJhbXMpfWAgOiAnJ1xyXG4gICAgcmV0dXJuIHRoaXMucmVxdWVzdChgL2FkbWluL3Byb2dyYW1zJHtxdWVyeVN0cmluZ31gKVxyXG4gIH1cclxuXHJcbiAgYXN5bmMgY3JlYXRlQWRtaW5Qcm9ncmFtKHByb2dyYW1EYXRhOiBhbnkpIHtcclxuICAgIHJldHVybiB0aGlzLnJlcXVlc3QoJy9hZG1pbi9wcm9ncmFtcycsIHtcclxuICAgICAgbWV0aG9kOiAnUE9TVCcsXHJcbiAgICAgIGJvZHk6IEpTT04uc3RyaW5naWZ5KHByb2dyYW1EYXRhKSxcclxuICAgIH0pXHJcbiAgfVxyXG5cclxuICBhc3luYyB1cGRhdGVBZG1pblByb2dyYW0ocHJvZ3JhbUlkOiBudW1iZXIsIHByb2dyYW1EYXRhOiBhbnkpIHtcclxuICAgIHJldHVybiB0aGlzLnJlcXVlc3QoYC9hZG1pbi9wcm9ncmFtcy8ke3Byb2dyYW1JZH1gLCB7XHJcbiAgICAgIG1ldGhvZDogJ1BVVCcsXHJcbiAgICAgIGJvZHk6IEpTT04uc3RyaW5naWZ5KHByb2dyYW1EYXRhKSxcclxuICAgIH0pXHJcbiAgfVxyXG5cclxuICBhc3luYyBkZWxldGVBZG1pblByb2dyYW0ocHJvZ3JhbUlkOiBudW1iZXIpIHtcclxuICAgIHJldHVybiB0aGlzLnJlcXVlc3QoYC9hZG1pbi9wcm9ncmFtcy8ke3Byb2dyYW1JZH1gLCB7XHJcbiAgICAgIG1ldGhvZDogJ0RFTEVURScsXHJcbiAgICB9KVxyXG4gIH1cclxuXHJcbiAgLy8gQWRtaW4gQmxvZyBNYW5hZ2VtZW50XHJcbiAgYXN5bmMgZ2V0QWRtaW5CbG9nUG9zdHMocGFyYW1zPzoge1xyXG4gICAgcGFnZT86IG51bWJlclxyXG4gICAgcGVyX3BhZ2U/OiBudW1iZXJcclxuICAgIHNlYXJjaD86IHN0cmluZ1xyXG4gICAgc3RhdHVzPzogc3RyaW5nXHJcbiAgICBkYXRlX3JhbmdlPzogc3RyaW5nXHJcbiAgICBzb3J0X2J5Pzogc3RyaW5nXHJcbiAgICBzb3J0X29yZGVyPzogc3RyaW5nXHJcbiAgfSkge1xyXG4gICAgY29uc3QgcXVlcnlTdHJpbmcgPSBwYXJhbXMgPyBgPyR7dGhpcy5idWlsZFF1ZXJ5U3RyaW5nKHBhcmFtcyl9YCA6ICcnXHJcbiAgICByZXR1cm4gdGhpcy5yZXF1ZXN0KGAvYWRtaW4vYmxvZy9wb3N0cyR7cXVlcnlTdHJpbmd9YClcclxuICB9XHJcblxyXG4gIGFzeW5jIGNyZWF0ZUFkbWluQmxvZ1Bvc3QocG9zdERhdGE6IGFueSkge1xyXG4gICAgcmV0dXJuIHRoaXMucmVxdWVzdCgnL2FkbWluL2Jsb2cvcG9zdHMnLCB7XHJcbiAgICAgIG1ldGhvZDogJ1BPU1QnLFxyXG4gICAgICBib2R5OiBKU09OLnN0cmluZ2lmeShwb3N0RGF0YSksXHJcbiAgICB9KVxyXG4gIH1cclxuXHJcbiAgYXN5bmMgdXBkYXRlQWRtaW5CbG9nUG9zdChwb3N0SWQ6IG51bWJlciwgcG9zdERhdGE6IGFueSkge1xyXG4gICAgcmV0dXJuIHRoaXMucmVxdWVzdChgL2FkbWluL2Jsb2cvcG9zdHMvJHtwb3N0SWR9YCwge1xyXG4gICAgICBtZXRob2Q6ICdQVVQnLFxyXG4gICAgICBib2R5OiBKU09OLnN0cmluZ2lmeShwb3N0RGF0YSksXHJcbiAgICB9KVxyXG4gIH1cclxuXHJcbiAgYXN5bmMgZGVsZXRlQWRtaW5CbG9nUG9zdChwb3N0SWQ6IG51bWJlcikge1xyXG4gICAgcmV0dXJuIHRoaXMucmVxdWVzdChgL2FkbWluL2Jsb2cvcG9zdHMvJHtwb3N0SWR9YCwge1xyXG4gICAgICBtZXRob2Q6ICdERUxFVEUnLFxyXG4gICAgfSlcclxuICB9XHJcblxyXG4gIGFzeW5jIGV4cG9ydEJsb2dQb3N0cyhwYXJhbXM/OiB7IGZvcm1hdD86IHN0cmluZzsgc3RhdHVzPzogc3RyaW5nIH0pIHtcclxuICAgIGNvbnN0IHF1ZXJ5U3RyaW5nID0gcGFyYW1zID8gYD8ke3RoaXMuYnVpbGRRdWVyeVN0cmluZyhwYXJhbXMpfWAgOiAnJ1xyXG4gICAgcmV0dXJuIHRoaXMucmVxdWVzdChgL2FkbWluL2Jsb2cvcG9zdHMvZXhwb3J0JHtxdWVyeVN0cmluZ31gKVxyXG4gIH1cclxuXHJcbiAgLy8gRG9uYXRpb24gZW5kcG9pbnRzXHJcbiAgYXN5bmMgZ2V0TXlEb25hdGlvbnMoKSB7XHJcbiAgICByZXR1cm4gdGhpcy5yZXF1ZXN0KCcvZG9uYXRpb25zL215LWRvbmF0aW9ucycpXHJcbiAgfVxyXG5cclxuICBhc3luYyBnZXREb25hdGlvbkNhbXBhaWducygpIHtcclxuICAgIHJldHVybiB0aGlzLnJlcXVlc3QoJy9kb25hdGlvbnMvY2FtcGFpZ25zJylcclxuICB9XHJcblxyXG4gIC8vIEJsb2cgZW5kcG9pbnRzXHJcbiAgYXN5bmMgZ2V0QmxvZ1Bvc3RzKHBhZ2UgPSAxKSB7XHJcbiAgICByZXR1cm4gdGhpcy5yZXF1ZXN0KGAvYmxvZy9wb3N0cz9wYWdlPSR7cGFnZX1gKVxyXG4gIH1cclxuXHJcbiAgYXN5bmMgZ2V0QmxvZ1Bvc3Qoc2x1Zzogc3RyaW5nKSB7XHJcbiAgICByZXR1cm4gdGhpcy5yZXF1ZXN0KGAvYmxvZy9wb3N0cy8ke3NsdWd9YClcclxuICB9XHJcblxyXG4gIC8vIEV2ZW50cyBlbmRwb2ludHNcclxuICBhc3luYyBnZXRFdmVudHMocGFnZSA9IDEpIHtcclxuICAgIHJldHVybiB0aGlzLnJlcXVlc3QoYC9ldmVudHM/cGFnZT0ke3BhZ2V9YClcclxuICB9XHJcblxyXG4gIGFzeW5jIGdldEV2ZW50KGlkOiBzdHJpbmcpIHtcclxuICAgIHJldHVybiB0aGlzLnJlcXVlc3QoYC9ldmVudHMvJHtpZH1gKVxyXG4gIH1cclxuXHJcbiAgYXN5bmMgcmVnaXN0ZXJGb3JFdmVudChldmVudElkOiBzdHJpbmcsIGFkZGl0aW9uYWxJbmZvPzogYW55KSB7XHJcbiAgICByZXR1cm4gdGhpcy5yZXF1ZXN0KGAvZXZlbnRzLyR7ZXZlbnRJZH0vcmVnaXN0ZXJgLCB7XHJcbiAgICAgIG1ldGhvZDogJ1BPU1QnLFxyXG4gICAgICBib2R5OiBKU09OLnN0cmluZ2lmeSh7IGFkZGl0aW9uYWxfaW5mbzogYWRkaXRpb25hbEluZm8gfSksXHJcbiAgICB9KVxyXG4gIH1cclxuXHJcbiAgLy8gUHJvZ3JhbXMgZW5kcG9pbnRzXHJcbiAgYXN5bmMgZ2V0UHJvZ3JhbXMocGFnZSA9IDEpIHtcclxuICAgIHJldHVybiB0aGlzLnJlcXVlc3QoYC9wcm9ncmFtcz9wYWdlPSR7cGFnZX1gKVxyXG4gIH1cclxuXHJcbiAgYXN5bmMgZ2V0UHJvZ3JhbShzbHVnOiBzdHJpbmcpIHtcclxuICAgIHJldHVybiB0aGlzLnJlcXVlc3QoYC9wcm9ncmFtcy8ke3NsdWd9YClcclxuICB9XHJcblxyXG4gIC8vIEFkZGl0aW9uYWwgc2Nob2xhcnNoaXAgZW5kcG9pbnRcclxuICBhc3luYyBnZXRTY2hvbGFyc2hpcChpZDogc3RyaW5nKSB7XHJcbiAgICByZXR1cm4gdGhpcy5yZXF1ZXN0KGAvc2Nob2xhcnNoaXBzLyR7aWR9YClcclxuICB9XHJcblxyXG4gIC8vIFB1YmxpYyBzY2hvbGFyc2hpcHMgZW5kcG9pbnRzIChubyBhdXRoZW50aWNhdGlvbiByZXF1aXJlZClcclxuICBhc3luYyBnZXRQdWJsaWNTY2hvbGFyc2hpcHMocGFyYW1zPzoge1xyXG4gICAgY2F0ZWdvcnk/OiBzdHJpbmdcclxuICAgIHNlYXJjaD86IHN0cmluZ1xyXG4gICAgc29ydD86IHN0cmluZ1xyXG4gICAgZmVhdHVyZWQ/OiBib29sZWFuXHJcbiAgICBwYWdlPzogbnVtYmVyXHJcbiAgICBwZXJfcGFnZT86IG51bWJlclxyXG4gIH0pIHtcclxuICAgIGNvbnN0IHF1ZXJ5U3RyaW5nID0gcGFyYW1zID8gYD8ke3RoaXMuYnVpbGRRdWVyeVN0cmluZyhwYXJhbXMpfWAgOiAnJ1xyXG4gICAgcmV0dXJuIHRoaXMucmVxdWVzdChgL3B1YmxpYy1zY2hvbGFyc2hpcHMke3F1ZXJ5U3RyaW5nfWApXHJcbiAgfVxyXG5cclxuICBhc3luYyBnZXRQdWJsaWNTY2hvbGFyc2hpcChpZDogc3RyaW5nIHwgbnVtYmVyKSB7XHJcbiAgICByZXR1cm4gdGhpcy5yZXF1ZXN0KGAvcHVibGljLXNjaG9sYXJzaGlwcy8ke2lkfWApXHJcbiAgfVxyXG5cclxuICBhc3luYyBzdWJtaXRTY2hvbGFyc2hpcEFwcGxpY2F0aW9uKHNjaG9sYXJzaGlwSWQ6IHN0cmluZywgZm9ybURhdGE6IEZvcm1EYXRhKSB7XHJcbiAgICBjb25zdCB0b2tlbiA9IHRoaXMuZ2V0VG9rZW4oKVxyXG4gICAgY29uc3QgdXJsID0gYCR7dGhpcy5iYXNlVVJMfS9hcGkvdjEvYXBwbHktc2Nob2xhcnNoaXAvJHtzY2hvbGFyc2hpcElkfWBcclxuXHJcbiAgICB0cnkge1xyXG4gICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGZldGNoKHVybCwge1xyXG4gICAgICAgIG1ldGhvZDogJ1BPU1QnLFxyXG4gICAgICAgIGhlYWRlcnM6IHtcclxuICAgICAgICAgICdBdXRob3JpemF0aW9uJzogdG9rZW4gPyBgQmVhcmVyICR7dG9rZW59YCA6ICcnLFxyXG4gICAgICAgICAgJ0FjY2VwdCc6ICdhcHBsaWNhdGlvbi9qc29uJyxcclxuICAgICAgICAgIC8vIERvbid0IHNldCBDb250ZW50LVR5cGUgZm9yIEZvcm1EYXRhLCBsZXQgYnJvd3NlciBzZXQgaXQgd2l0aCBib3VuZGFyeVxyXG4gICAgICAgIH0sXHJcbiAgICAgICAgYm9keTogZm9ybURhdGFcclxuICAgICAgfSlcclxuXHJcbiAgICAgIGNvbnN0IGRhdGEgPSBhd2FpdCByZXNwb25zZS5qc29uKClcclxuICAgICAgY29uc29sZS5sb2coYEFQSSBSZXNwb25zZSBmb3Igc2Nob2xhcnNoaXAgYXBwbGljYXRpb246YCwgZGF0YSlcclxuXHJcbiAgICAgIGlmICghcmVzcG9uc2Uub2spIHtcclxuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoZGF0YS5tZXNzYWdlIHx8IGBIVFRQIGVycm9yISBzdGF0dXM6ICR7cmVzcG9uc2Uuc3RhdHVzfWApXHJcbiAgICAgIH1cclxuXHJcbiAgICAgIHJldHVybiBkYXRhXHJcbiAgICB9IGNhdGNoIChlcnJvcikge1xyXG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBzdWJtaXR0aW5nIHNjaG9sYXJzaGlwIGFwcGxpY2F0aW9uOicsIGVycm9yKVxyXG4gICAgICB0aHJvdyBlcnJvclxyXG4gICAgfVxyXG4gIH1cclxuXHJcbiAgYXN5bmMgYXBwbHlBc1ZvbHVudGVlcihhcHBsaWNhdGlvbkRhdGE6IGFueSkge1xyXG4gICAgcmV0dXJuIHRoaXMucmVxdWVzdCgnL3ZvbHVudGVlci9hcHBseScsIHtcclxuICAgICAgbWV0aG9kOiAnUE9TVCcsXHJcbiAgICAgIGJvZHk6IEpTT04uc3RyaW5naWZ5KGFwcGxpY2F0aW9uRGF0YSksXHJcbiAgICB9KVxyXG4gIH1cclxuXHJcbiAgLy8gQ29udGFjdCBlbmRwb2ludHNcclxuICBhc3luYyBzdWJtaXRDb250YWN0Rm9ybShjb250YWN0RGF0YTogYW55KSB7XHJcbiAgICByZXR1cm4gdGhpcy5yZXF1ZXN0KCcvY29udGFjdCcsIHtcclxuICAgICAgbWV0aG9kOiAnUE9TVCcsXHJcbiAgICAgIGJvZHk6IEpTT04uc3RyaW5naWZ5KGNvbnRhY3REYXRhKSxcclxuICAgIH0pXHJcbiAgfVxyXG5cclxuICAvLyBOZXdzbGV0dGVyIGVuZHBvaW50c1xyXG4gIGFzeW5jIHN1YnNjcmliZVRvTmV3c2xldHRlcihlbWFpbDogc3RyaW5nKSB7XHJcbiAgICByZXR1cm4gdGhpcy5yZXF1ZXN0KCcvbmV3c2xldHRlci9zdWJzY3JpYmUnLCB7XHJcbiAgICAgIG1ldGhvZDogJ1BPU1QnLFxyXG4gICAgICBib2R5OiBKU09OLnN0cmluZ2lmeSh7IGVtYWlsIH0pLFxyXG4gICAgfSlcclxuICB9XHJcblxyXG4gIC8vIFNldHRpbmdzIGVuZHBvaW50XHJcbiAgYXN5bmMgZ2V0U2V0dGluZ3MoKSB7XHJcbiAgICByZXR1cm4gdGhpcy5yZXF1ZXN0KCcvc2V0dGluZ3MnKVxyXG4gIH1cclxufVxyXG5cclxuLy8gQ3JlYXRlIGFuZCBleHBvcnQgYSBkZWZhdWx0IGluc3RhbmNlXHJcbmV4cG9ydCBjb25zdCBhcGlDbGllbnQgPSBuZXcgQXBpQ2xpZW50KClcclxuXHJcbi8vIEV4cG9ydCB0aGUgY2xhc3MgZm9yIGN1c3RvbSBpbnN0YW5jZXNcclxuZXhwb3J0IHsgQXBpQ2xpZW50IH1cclxuXHJcbi8vIEV4cG9ydCB0aGUgZXh0cmFjdEFycmF5RGF0YSB1dGlsaXR5IGZ1bmN0aW9uXHJcbmV4cG9ydCB7IGV4dHJhY3RBcnJheURhdGEgfVxyXG5cclxuLy8gSGVscGVyIGZ1bmN0aW9uIHRvIGNoZWNrIGlmIHVzZXIgaXMgYXV0aGVudGljYXRlZFxyXG5leHBvcnQgY29uc3QgaXNBdXRoZW50aWNhdGVkID0gKCk6IGJvb2xlYW4gPT4ge1xyXG4gIGlmICh0eXBlb2Ygd2luZG93ID09PSAndW5kZWZpbmVkJykgcmV0dXJuIGZhbHNlXHJcbiAgcmV0dXJuICEhbG9jYWxTdG9yYWdlLmdldEl0ZW0oJ2F1dGhUb2tlbicpXHJcbn1cclxuXHJcbi8vIEhlbHBlciBmdW5jdGlvbiB0byBnZXQgY3VycmVudCB1c2VyXHJcbmV4cG9ydCBjb25zdCBnZXRDdXJyZW50VXNlciA9ICgpID0+IHtcclxuICBpZiAodHlwZW9mIHdpbmRvdyA9PT0gJ3VuZGVmaW5lZCcpIHJldHVybiBudWxsXHJcbiAgY29uc3QgdXNlclN0ciA9IGxvY2FsU3RvcmFnZS5nZXRJdGVtKCd1c2VyJylcclxuICByZXR1cm4gdXNlclN0ciA/IEpTT04ucGFyc2UodXNlclN0cikgOiBudWxsXHJcbn1cclxuXHJcbi8vIEhlbHBlciBmdW5jdGlvbiB0byBsb2dvdXRcclxuZXhwb3J0IGNvbnN0IGxvZ291dCA9IGFzeW5jICgpID0+IHtcclxuICB0cnkge1xyXG4gICAgYXdhaXQgYXBpQ2xpZW50LmxvZ291dCgpXHJcbiAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgIGNvbnNvbGUuZXJyb3IoJ0xvZ291dCBlcnJvcjonLCBlcnJvcilcclxuICB9IGZpbmFsbHkge1xyXG4gICAgaWYgKHR5cGVvZiB3aW5kb3cgIT09ICd1bmRlZmluZWQnKSB7XHJcbiAgICAgIGxvY2FsU3RvcmFnZS5yZW1vdmVJdGVtKCdhdXRoVG9rZW4nKVxyXG4gICAgICBsb2NhbFN0b3JhZ2UucmVtb3ZlSXRlbSgndXNlcicpXHJcbiAgICAgIHdpbmRvdy5sb2NhdGlvbi5ocmVmID0gJy9hdXRoL2xvZ2luJ1xyXG4gICAgfVxyXG4gIH1cclxufSAiXSwibmFtZXMiOlsiQVBJX0JBU0VfVVJMIiwicHJvY2VzcyIsImVudiIsIk5FWFRfUFVCTElDX0FQSV9VUkwiLCJORVhUX1BVQkxJQ19BUElfQkFTRV9VUkwiLCJleHRyYWN0QXJyYXlEYXRhIiwicmVzcG9uc2UiLCJkYXRhIiwiQXJyYXkiLCJpc0FycmF5IiwiQXBpQ2xpZW50IiwicmVxdWVzdCIsImVuZHBvaW50Iiwib3B0aW9ucyIsInVybCIsImJhc2VVUkwiLCJpbmNsdWRlcyIsImNvbmZpZyIsImhlYWRlcnMiLCJ0b2tlbiIsImxvY2FsU3RvcmFnZSIsImdldEl0ZW0iLCJjb25zb2xlIiwibG9nIiwibWV0aG9kIiwiZmV0Y2giLCJqc29uIiwic3RhdHVzIiwicmVtb3ZlSXRlbSIsIndpbmRvdyIsImxvY2F0aW9uIiwiaHJlZiIsImVycm9yIiwibG9naW4iLCJlbWFpbCIsInBhc3N3b3JkIiwiYm9keSIsIkpTT04iLCJzdHJpbmdpZnkiLCJzdWNjZXNzIiwiYWNjZXNzX3Rva2VuIiwic2V0SXRlbSIsInVzZXIiLCJyZWdpc3RlciIsInVzZXJEYXRhIiwiZmlyc3RfbmFtZSIsImxhc3RfbmFtZSIsInBhc3N3b3JkX2NvbmZpcm1hdGlvbiIsInBob25lX251bWJlciIsImFkZHJlc3MiLCJkYXRlX29mX2JpcnRoIiwiY2l0eSIsInN0YXRlIiwiY291bnRyeSIsInVzZXJfdHlwZSIsImFkZGl0aW9uYWxfZGF0YSIsImFwcGx5QXNWb2x1bnRlZXIiLCJwcmVmZXJlbmNlcyIsInByb2ZpbGVfZGF0YSIsInByb2ZpbGVfY29tcGxldGVkIiwidXBkYXRlVXNlclByZWZlcmVuY2VzIiwibWVzc2FnZSIsImxvZ291dCIsImdldFVzZXIiLCJmb3Jnb3RQYXNzd29yZCIsImdldFByb2ZpbGUiLCJ1cGRhdGVQcm9maWxlIiwicHJvZmlsZURhdGEiLCJ1cGxvYWRBdmF0YXIiLCJmaWxlIiwiZm9ybURhdGEiLCJGb3JtRGF0YSIsImFwcGVuZCIsImNoYW5nZVBhc3N3b3JkIiwicGFzc3dvcmREYXRhIiwiZ2VuZXJhdGVRckNvZGUiLCJnZXRJZENhcmQiLCJnZXREYXNoYm9hcmRTdW1tYXJ5IiwiZ2V0Vm9sdW50ZWVyQXBwbGljYXRpb24iLCJnZXRWb2x1bnRlZXJIb3VycyIsImdldFZvbHVudGVlck9wcG9ydHVuaXRpZXMiLCJsb2dWb2x1bnRlZXJIb3VycyIsImhvdXJzRGF0YSIsImdldE15U2Nob2xhcnNoaXBBcHBsaWNhdGlvbnMiLCJnZXRTY2hvbGFyc2hpcHMiLCJhcHBseUZvclNjaG9sYXJzaGlwIiwic2Nob2xhcnNoaXBJZCIsImFwcGxpY2F0aW9uRGF0YSIsImdldE15RXZlbnRSZWdpc3RyYXRpb25zIiwiZ2V0VXBjb21pbmdFdmVudHMiLCJidWlsZFF1ZXJ5U3RyaW5nIiwicGFyYW1zIiwic2VhcmNoUGFyYW1zIiwiVVJMU2VhcmNoUGFyYW1zIiwiT2JqZWN0IiwiZW50cmllcyIsImZvckVhY2giLCJrZXkiLCJ2YWx1ZSIsInVuZGVmaW5lZCIsIml0ZW0iLCJ0b1N0cmluZyIsImdldEFkbWluRGFzaGJvYXJkIiwiZ2V0QWRtaW5BbmFseXRpY3MiLCJxdWVyeVN0cmluZyIsImdldEFkbWluU3RhdHMiLCJnZXRBZG1pblVzZXJzIiwiY3JlYXRlQWRtaW5Vc2VyIiwidXBkYXRlQWRtaW5Vc2VyIiwidXNlcklkIiwiZGVsZXRlQWRtaW5Vc2VyIiwiYnVsa0FjdGlvblVzZXJzIiwiZXhwb3J0VXNlcnMiLCJnZXRTY2hvbGFyc2hpcEFwcGxpY2F0aW9ucyIsInJldmlld1NjaG9sYXJzaGlwQXBwbGljYXRpb24iLCJhcHBsaWNhdGlvbklkIiwicmV2aWV3RGF0YSIsImJ1bGtBY3Rpb25TY2hvbGFyc2hpcEFwcGxpY2F0aW9ucyIsImV4cG9ydFNjaG9sYXJzaGlwQXBwbGljYXRpb25zIiwiZ2V0U2Nob2xhcnNoaXBTdGF0aXN0aWNzIiwiZ2V0QWRtaW5FdmVudHMiLCJjcmVhdGVBZG1pbkV2ZW50IiwiZXZlbnREYXRhIiwidXBkYXRlQWRtaW5FdmVudCIsImV2ZW50SWQiLCJkZWxldGVBZG1pbkV2ZW50IiwiYnVsa0FjdGlvbkV2ZW50cyIsImV4cG9ydEV2ZW50cyIsImdldEV2ZW50U3RhdGlzdGljcyIsImdldEFkbWluUHJvZ3JhbXMiLCJjcmVhdGVBZG1pblByb2dyYW0iLCJwcm9ncmFtRGF0YSIsInVwZGF0ZUFkbWluUHJvZ3JhbSIsInByb2dyYW1JZCIsImRlbGV0ZUFkbWluUHJvZ3JhbSIsImdldEFkbWluQmxvZ1Bvc3RzIiwiY3JlYXRlQWRtaW5CbG9nUG9zdCIsInBvc3REYXRhIiwidXBkYXRlQWRtaW5CbG9nUG9zdCIsInBvc3RJZCIsImRlbGV0ZUFkbWluQmxvZ1Bvc3QiLCJleHBvcnRCbG9nUG9zdHMiLCJnZXRNeURvbmF0aW9ucyIsImdldERvbmF0aW9uQ2FtcGFpZ25zIiwiZ2V0QmxvZ1Bvc3RzIiwicGFnZSIsImdldEJsb2dQb3N0Iiwic2x1ZyIsImdldEV2ZW50cyIsImdldEV2ZW50IiwiaWQiLCJyZWdpc3RlckZvckV2ZW50IiwiYWRkaXRpb25hbEluZm8iLCJhZGRpdGlvbmFsX2luZm8iLCJnZXRQcm9ncmFtcyIsImdldFByb2dyYW0iLCJnZXRTY2hvbGFyc2hpcCIsImdldFB1YmxpY1NjaG9sYXJzaGlwcyIsImdldFB1YmxpY1NjaG9sYXJzaGlwIiwic3VibWl0U2Nob2xhcnNoaXBBcHBsaWNhdGlvbiIsImdldFRva2VuIiwib2siLCJFcnJvciIsInN1Ym1pdENvbnRhY3RGb3JtIiwiY29udGFjdERhdGEiLCJzdWJzY3JpYmVUb05ld3NsZXR0ZXIiLCJnZXRTZXR0aW5ncyIsImNvbnN0cnVjdG9yIiwiYXBpQ2xpZW50IiwiaXNBdXRoZW50aWNhdGVkIiwiZ2V0Q3VycmVudFVzZXIiLCJ1c2VyU3RyIiwicGFyc2UiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/api.ts\n"));

/***/ })

});