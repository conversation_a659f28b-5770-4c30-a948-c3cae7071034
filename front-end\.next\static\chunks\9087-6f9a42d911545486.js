"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9087],{49087:(e,s,l)=>{l.r(s),l.d(s,{default:()=>A});var a=l(95155),t=l(12115),i=l(31886),r=l(88482),n=l(97168),c=l(88145),d=l(41397),x=l(34964),m=l(40646),o=l(85339),h=l(14186),u=l(51976),j=l(84616),p=l(16785),N=l(17580),v=l(69037),g=l(33109),f=l(4516),y=l(5040),w=l(6874),b=l.n(w);function A(e){var s,l,w,A,_;let{user:Z}=e,[B,E]=(0,t.useState)(null),[k,R]=(0,t.useState)(null),[V,W]=(0,t.useState)([]),[O,S]=(0,t.useState)([]),[H,$]=(0,t.useState)(!0);(0,t.useEffect)(()=>{(async()=>{try{let[e,s,l,a]=await Promise.all([i.uE.getVolunteerApplication(),i.uE.getVolunteerHours(),i.uE.getVolunteerOpportunities(),i.uE.getUpcomingEvents()]);if(e.success&&E(e.data),s.success&&R(s.data),l.success){let e=(0,i.B7)(l);W(e.slice(0,5))}if(a.success){let e=(0,i.B7)(a);S(e.slice(0,3))}}catch(e){console.error("Failed to fetch dashboard data:",e)}finally{$(!1)}})()},[]);let P=(null===(s=Z.preferences)||void 0===s?void 0:s.volunteer_data)||{},T=B?B.hours_logged/100*100:0;return(0,a.jsx)("div",{className:"min-h-screen bg-gray-50",children:(0,a.jsxs)("div",{className:"max-w-7xl mx-auto",children:[(0,a.jsx)("div",{className:"bg-white rounded-none sm:rounded-lg shadow-sm p-4 sm:p-6 mx-0 sm:mx-6 mt-0 sm:mt-6",children:(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3 sm:space-x-4",children:[(0,a.jsx)("div",{className:"h-12 w-12 sm:h-16 sm:w-16 bg-green-600 rounded-full flex items-center justify-center",children:(0,a.jsx)(u.A,{className:"h-6 w-6 sm:h-8 sm:w-8 text-white"})}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("h1",{className:"text-xl sm:text-2xl font-bold text-gray-900",children:["Welcome back, ",Z.first_name,"!"]}),(0,a.jsx)("p",{className:"text-sm sm:text-base text-gray-600",children:"Volunteer Dashboard"}),B&&(0,a.jsxs)("div",{className:"flex items-center mt-2",children:[(e=>{switch(e.toLowerCase()){case"approved":return(0,a.jsx)(m.A,{className:"h-4 w-4"});case"rejected":return(0,a.jsx)(o.A,{className:"h-4 w-4"});default:return(0,a.jsx)(h.A,{className:"h-4 w-4"})}})(B.application_status),(0,a.jsxs)("span",{className:"ml-2 text-xs sm:text-sm font-medium",children:["Status: ",B.application_status.replace("_"," ")]})]})]})]}),(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-3",children:[(0,a.jsx)(n.$,{variant:"outline",size:"sm",asChild:!0,className:"w-full sm:w-auto",children:(0,a.jsxs)(b(),{href:"/volunteer/opportunities",children:[(0,a.jsx)(j.A,{className:"h-4 w-4 mr-2"}),"Find Opportunities"]})}),!B&&(0,a.jsx)(n.$,{size:"sm",asChild:!0,className:"w-full sm:w-auto",children:(0,a.jsx)(b(),{href:"/volunteer/apply",children:"Apply as Volunteer"})})]})]})}),(0,a.jsxs)("div",{className:"grid grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-6 px-4 sm:px-6 mt-4 sm:mt-6",children:[(0,a.jsxs)(r.Zp,{className:"p-3 sm:p-4",children:[(0,a.jsxs)(r.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2 px-0",children:[(0,a.jsx)(r.ZB,{className:"text-xs sm:text-sm font-medium",children:"Hours Logged"}),(0,a.jsx)(h.A,{className:"h-3 w-3 sm:h-4 sm:w-4 text-muted-foreground"})]}),(0,a.jsxs)(r.Wu,{className:"px-0",children:[(0,a.jsxs)("div",{className:"text-lg sm:text-2xl font-bold",children:[(null==B?void 0:B.hours_logged)||0,"h"]}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:"Total volunteer hours"})]})]}),(0,a.jsxs)(r.Zp,{className:"p-3 sm:p-4",children:[(0,a.jsxs)(r.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2 px-0",children:[(0,a.jsx)(r.ZB,{className:"text-xs sm:text-sm font-medium",children:"Progress"}),(0,a.jsx)(p.A,{className:"h-3 w-3 sm:h-4 sm:w-4 text-muted-foreground"})]}),(0,a.jsxs)(r.Wu,{className:"px-0",children:[(0,a.jsxs)("div",{className:"text-lg sm:text-2xl font-bold",children:[Math.round(T),"%"]}),(0,a.jsxs)("p",{className:"text-xs text-muted-foreground",children:["Of ",100,"h goal"]})]})]}),(0,a.jsxs)(r.Zp,{className:"p-3 sm:p-4",children:[(0,a.jsxs)(r.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2 px-0",children:[(0,a.jsx)(r.ZB,{className:"text-xs sm:text-sm font-medium",children:"Opportunities"}),(0,a.jsx)(N.A,{className:"h-3 w-3 sm:h-4 sm:w-4 text-muted-foreground"})]}),(0,a.jsxs)(r.Wu,{className:"px-0",children:[(0,a.jsx)("div",{className:"text-lg sm:text-2xl font-bold",children:V.length}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:"Available to you"})]})]}),(0,a.jsxs)(r.Zp,{className:"p-3 sm:p-4",children:[(0,a.jsxs)(r.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2 px-0",children:[(0,a.jsx)(r.ZB,{className:"text-xs sm:text-sm font-medium",children:"Badges"}),(0,a.jsx)(v.A,{className:"h-3 w-3 sm:h-4 sm:w-4 text-muted-foreground"})]}),(0,a.jsxs)(r.Wu,{className:"px-0",children:[(0,a.jsx)("div",{className:"text-lg sm:text-2xl font-bold",children:(null==k?void 0:null===(l=k.badges_earned)||void 0===l?void 0:l.length)||0}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:"Earned badges"})]})]})]}),(0,a.jsxs)(x.tU,{defaultValue:"overview",className:"space-y-6",children:[(0,a.jsxs)(x.j7,{className:"grid w-full grid-cols-5",children:[(0,a.jsx)(x.Xi,{value:"overview",children:"Overview"}),(0,a.jsx)(x.Xi,{value:"hours",children:"Hours"}),(0,a.jsx)(x.Xi,{value:"opportunities",children:"Opportunities"}),(0,a.jsx)(x.Xi,{value:"events",children:"Events"}),(0,a.jsx)(x.Xi,{value:"profile",children:"Profile"})]}),(0,a.jsxs)(x.av,{value:"overview",className:"space-y-6",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,a.jsxs)(r.Zp,{children:[(0,a.jsx)(r.aR,{children:(0,a.jsxs)(r.ZB,{className:"flex items-center",children:[(0,a.jsx)(g.A,{className:"h-5 w-5 mr-2"}),"Volunteer Progress"]})}),(0,a.jsxs)(r.Wu,{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"flex justify-between text-sm mb-2",children:[(0,a.jsx)("span",{children:"Hours Goal Progress"}),(0,a.jsxs)("span",{children:[(null==B?void 0:B.hours_logged)||0,"h / ",100,"h"]})]}),(0,a.jsx)(d.k,{value:T,className:"h-2"})]}),(null==B?void 0:B.approved_at)&&(0,a.jsx)("div",{className:"bg-green-50 p-3 rounded-lg",children:(0,a.jsxs)("p",{className:"text-sm text-green-700",children:[(0,a.jsx)(m.A,{className:"h-4 w-4 inline mr-2"}),"Volunteer since ",new Date(B.approved_at).toLocaleDateString()]})}),(null==B?void 0:B.application_status)==="pending"&&(0,a.jsx)("div",{className:"bg-yellow-50 p-3 rounded-lg",children:(0,a.jsxs)("p",{className:"text-sm text-yellow-700",children:[(0,a.jsx)(h.A,{className:"h-4 w-4 inline mr-2"}),"Your application is being reviewed. We'll notify you once it's processed."]})})]})]}),(0,a.jsxs)(r.Zp,{children:[(0,a.jsx)(r.aR,{children:(0,a.jsxs)(r.ZB,{className:"flex items-center",children:[(0,a.jsx)(N.A,{className:"h-5 w-5 mr-2"}),"Available Opportunities"]})}),(0,a.jsxs)(r.Wu,{className:"space-y-4",children:[V.slice(0,3).map(e=>(0,a.jsxs)("div",{className:"p-3 border rounded-lg",children:[(0,a.jsx)("h4",{className:"font-medium",children:e.title}),(0,a.jsx)("p",{className:"text-sm text-gray-600 mt-1",children:e.description}),(0,a.jsxs)("div",{className:"flex items-center gap-4 mt-2 text-xs text-gray-500",children:[(0,a.jsxs)("span",{children:[(0,a.jsx)(f.A,{className:"h-3 w-3 inline mr-1"}),e.location]}),(0,a.jsxs)("span",{children:[(0,a.jsx)(h.A,{className:"h-3 w-3 inline mr-1"}),e.time_commitment]})]})]},e.id)),0===V.length&&(0,a.jsx)("p",{className:"text-gray-500 text-center py-4",children:"No opportunities available"}),(0,a.jsx)(n.$,{variant:"outline",className:"w-full",asChild:!0,children:(0,a.jsx)(b(),{href:"/volunteer/opportunities",children:"View All Opportunities"})})]})]})]}),B&&(0,a.jsxs)(r.Zp,{children:[(0,a.jsx)(r.aR,{children:(0,a.jsxs)(r.ZB,{className:"flex items-center",children:[(0,a.jsx)(y.A,{className:"h-5 w-5 mr-2"}),"Your Skills & Interests"]})}),(0,a.jsx)(r.Wu,{children:(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-medium mb-3",children:"Skills"}),(0,a.jsxs)("div",{className:"flex flex-wrap gap-2",children:[null===(w=B.skills)||void 0===w?void 0:w.map((e,s)=>(0,a.jsx)(c.E,{variant:"secondary",children:e},s)),(!B.skills||0===B.skills.length)&&(0,a.jsx)("p",{className:"text-gray-500",children:"No skills listed"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-medium mb-3",children:"Interests"}),(0,a.jsxs)("div",{className:"flex flex-wrap gap-2",children:[null===(A=B.interests)||void 0===A?void 0:A.map((e,s)=>(0,a.jsx)(c.E,{variant:"outline",children:e},s)),(!B.interests||0===B.interests.length)&&(0,a.jsx)("p",{className:"text-gray-500",children:"No interests listed"})]})]})]})})]})]}),(0,a.jsx)(x.av,{value:"hours",className:"space-y-6",children:(0,a.jsxs)(r.Zp,{children:[(0,a.jsxs)(r.aR,{children:[(0,a.jsx)(r.ZB,{children:"Volunteer Hours"}),(0,a.jsx)(r.BT,{children:"Track and log your volunteer hours"})]}),(0,a.jsx)(r.Wu,{children:(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,a.jsxs)("div",{className:"text-center p-4 bg-green-50 rounded-lg",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-green-600",children:(null==B?void 0:B.hours_logged)||0}),(0,a.jsx)("p",{className:"text-sm text-green-700",children:"Total Hours"})]}),(0,a.jsxs)("div",{className:"text-center p-4 bg-blue-50 rounded-lg",children:[(0,a.jsxs)("div",{className:"text-2xl font-bold text-blue-600",children:[Math.round(T),"%"]}),(0,a.jsx)("p",{className:"text-sm text-blue-700",children:"Goal Progress"})]}),(0,a.jsxs)("div",{className:"text-center p-4 bg-purple-50 rounded-lg",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-purple-600",children:(null==k?void 0:null===(_=k.badges_earned)||void 0===_?void 0:_.length)||0}),(0,a.jsx)("p",{className:"text-sm text-purple-700",children:"Badges Earned"})]})]}),(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsx)("h4",{className:"font-medium",children:"Recent Hours"}),(0,a.jsx)(n.$,{size:"sm",children:"Log New Hours"})]}),(0,a.jsxs)("div",{className:"text-center py-8 text-gray-500",children:[(0,a.jsx)(h.A,{className:"h-12 w-12 mx-auto mb-4 opacity-50"}),(0,a.jsx)("p",{children:"No logged hours yet"}),(0,a.jsx)(n.$,{className:"mt-4",children:"Log Your First Hours"})]})]})})]})}),(0,a.jsx)(x.av,{value:"opportunities",className:"space-y-6",children:(0,a.jsxs)(r.Zp,{children:[(0,a.jsxs)(r.aR,{children:[(0,a.jsx)(r.ZB,{children:"Volunteer Opportunities"}),(0,a.jsx)(r.BT,{children:"Find opportunities that match your skills and interests"})]}),(0,a.jsx)(r.Wu,{children:(0,a.jsxs)("div",{className:"space-y-4",children:[V.map(e=>{var s;return(0,a.jsxs)("div",{className:"border rounded-lg p-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,a.jsx)("h4",{className:"font-semibold",children:e.title}),(0,a.jsx)(n.$,{variant:"outline",size:"sm",children:"Learn More"})]}),(0,a.jsx)("p",{className:"text-sm text-gray-600 mb-3",children:e.description}),(0,a.jsxs)("div",{className:"flex items-center gap-4 text-sm text-gray-500",children:[(0,a.jsxs)("span",{children:[(0,a.jsx)(f.A,{className:"h-4 w-4 inline mr-1"}),e.location]}),(0,a.jsxs)("span",{children:[(0,a.jsx)(h.A,{className:"h-4 w-4 inline mr-1"}),e.time_commitment]})]}),(0,a.jsx)("div",{className:"flex flex-wrap gap-2 mt-3",children:null===(s=e.skills_required)||void 0===s?void 0:s.map((e,s)=>(0,a.jsx)(c.E,{variant:"secondary",className:"text-xs",children:e},s))})]},e.id)}),0===V.length&&(0,a.jsxs)("div",{className:"text-center py-8",children:[(0,a.jsx)(N.A,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-gray-500 mb-4",children:"No opportunities available right now"}),(0,a.jsx)(n.$,{asChild:!0,children:(0,a.jsx)(b(),{href:"/volunteer/opportunities",children:"Browse All Opportunities"})})]})]})})]})}),(0,a.jsx)(x.av,{value:"events",className:"space-y-6",children:(0,a.jsxs)(r.Zp,{children:[(0,a.jsxs)(r.aR,{children:[(0,a.jsx)(r.ZB,{children:"Volunteer Events"}),(0,a.jsx)(r.BT,{children:"Join volunteer events and training sessions"})]}),(0,a.jsx)(r.Wu,{children:(0,a.jsxs)("div",{className:"space-y-4",children:[O.map(e=>(0,a.jsxs)("div",{className:"border rounded-lg p-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,a.jsx)("h4",{className:"font-semibold",children:e.title}),(0,a.jsx)(n.$,{variant:"outline",size:"sm",children:"Register"})]}),(0,a.jsxs)("div",{className:"text-sm text-gray-600",children:[(0,a.jsxs)("p",{children:["Date: ",new Date(e.start_datetime).toLocaleDateString()]}),(0,a.jsxs)("p",{children:["Type: ",e.event_type]})]})]},e.id)),0===O.length&&(0,a.jsx)("p",{className:"text-gray-500 text-center py-4",children:"No upcoming events"})]})})]})}),(0,a.jsx)(x.av,{value:"profile",className:"space-y-6",children:(0,a.jsxs)(r.Zp,{children:[(0,a.jsxs)(r.aR,{children:[(0,a.jsx)(r.ZB,{children:"Volunteer Profile"}),(0,a.jsx)(r.BT,{children:"Manage your volunteer information and preferences"})]}),(0,a.jsxs)(r.Wu,{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("label",{className:"text-sm font-medium",children:"Full Name"}),(0,a.jsxs)("p",{className:"text-sm text-gray-600",children:[Z.first_name," ",Z.last_name]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("label",{className:"text-sm font-medium",children:"Email"}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:Z.email})]}),P.availability&&(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("label",{className:"text-sm font-medium",children:"Availability"}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:P.availability})]}),P.experience&&(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("label",{className:"text-sm font-medium",children:"Experience"}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:P.experience})]})]}),(null==B?void 0:B.motivation)&&(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("label",{className:"text-sm font-medium",children:"Motivation"}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:B.motivation})]}),(0,a.jsx)(n.$,{variant:"outline",children:"Edit Profile"})]})]})})]})]})})}}}]);