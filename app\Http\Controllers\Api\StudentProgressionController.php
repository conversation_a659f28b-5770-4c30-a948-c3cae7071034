<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Student;
use App\Models\StudentProgression;
use App\Models\PartnerOrganization;
use App\Services\StudentProgressionService;
use App\Services\ScholarshipEligibilityService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\Rule;
use Carbon\Carbon;

class StudentProgressionController extends Controller
{
    protected $progressionService;
    protected $eligibilityService;

    public function __construct(
        StudentProgressionService $progressionService,
        ScholarshipEligibilityService $eligibilityService
    ) {
        $this->progressionService = $progressionService;
        $this->eligibilityService = $eligibilityService;
    }

    /**
     * Get all progressions for the partner organization
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $user = Auth::user();
            $partnerOrg = PartnerOrganization::findOrFail($user->partner_organization_id);

            $academicYear = $request->get('academic_year');
            $status = $request->get('status');
            $perPage = $request->get('per_page', 15);

            $query = StudentProgression::with(['student'])
                ->whereHas('student', function ($q) use ($partnerOrg) {
                    $q->where('school_id', $partnerOrg->id);
                });

            if ($academicYear) {
                $query->where('academic_year', $academicYear);
            }

            if ($status) {
                $query->where('status', $status);
            }

            $progressions = $query->orderBy('created_at', 'desc')
                ->paginate($perPage);

            return response()->json([
                'success' => true,
                'data' => $progressions,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch progressions',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Create a new progression
     */
    public function store(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'student_id' => 'required|exists:students,id',
                'academic_year' => 'nullable|string',
                'progression_date' => 'nullable|date',
                'notes' => 'nullable|string|max:1000',
                'auto_approve' => 'boolean',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $user = Auth::user();
            $partnerOrg = PartnerOrganization::findOrFail($user->partner_organization_id);

            // Verify student belongs to this partner organization
            $student = Student::where('id', $request->student_id)
                ->where('school_id', $partnerOrg->id)
                ->firstOrFail();

            $options = [
                'academic_year' => $request->academic_year,
                'progression_date' => $request->progression_date,
                'notes' => $request->notes,
                'auto_approve' => $request->boolean('auto_approve', false),
                'created_by' => $user->id,
            ];

            $progression = $this->progressionService->advanceStudent($student, $options);

            return response()->json([
                'success' => true,
                'message' => 'Student progression created successfully',
                'data' => $progression->load('student'),
            ], 201);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to create progression',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get progression statistics
     */
    public function getStatistics(Request $request): JsonResponse
    {
        try {
            $user = Auth::user();
            $partnerOrg = PartnerOrganization::findOrFail($user->partner_organization_id);

            $academicYear = $request->get('academic_year');
            $statistics = $this->progressionService->getProgressionStatistics($partnerOrg, $academicYear);

            return response()->json([
                'success' => true,
                'data' => $statistics,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch statistics',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get students eligible for progression
     */
    public function getEligibleStudents(Request $request): JsonResponse
    {
        try {
            $user = Auth::user();

            if ($user->role === 'admin') {
                // Admin can see all eligible students
                $academicYear = $request->get('academic_year');
                $partnerOrgId = $request->get('partner_organization_id');

                $query = Student::query();
                if ($partnerOrgId) {
                    $query->where('school_id', $partnerOrgId);
                }

                $students = $query->get();
                $eligibleStudents = [];

                foreach ($students as $student) {
                    if ($student->school_id) {
                        $partnerOrg = PartnerOrganization::find($student->school_id);
                        if ($partnerOrg) {
                            $eligible = $this->progressionService->getEligibleStudents($partnerOrg, $academicYear);
                            $eligibleStudents = array_merge($eligibleStudents, $eligible);
                        }
                    }
                }

                return response()->json([
                    'success' => true,
                    'data' => [
                        'eligible_students' => $eligibleStudents,
                        'total_eligible' => count($eligibleStudents),
                        'criteria' => 'Students eligible for grade advancement based on completion status'
                    ],
                ]);
            } else {
                // Partner organization access
                $partnerOrg = PartnerOrganization::findOrFail($user->partner_organization_id);
                $academicYear = $request->get('academic_year');
                $eligibleStudents = $this->progressionService->getEligibleStudents($partnerOrg, $academicYear);

                return response()->json([
                    'success' => true,
                    'data' => [
                        'eligible_students' => $eligibleStudents,
                        'total_eligible' => count($eligibleStudents),
                        'criteria' => 'Students eligible for grade advancement based on completion status'
                    ],
                ]);
            }
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch eligible students',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get specific progression
     */
    public function show(string $id): JsonResponse
    {
        try {
            $user = Auth::user();
            $partnerOrg = PartnerOrganization::findOrFail($user->partner_organization_id);

            $progression = StudentProgression::with(['student'])
                ->whereHas('student', function ($q) use ($partnerOrg) {
                    $q->where('school_id', $partnerOrg->id);
                })
                ->findOrFail($id);

            return response()->json([
                'success' => true,
                'data' => $progression,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Progression not found',
                'error' => $e->getMessage()
            ], 404);
        }
    }

    /**
     * Update progression
     */
    public function update(Request $request, string $id): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'notes' => 'nullable|string|max:1000',
                'progression_date' => 'nullable|date',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $user = Auth::user();
            $partnerOrg = PartnerOrganization::findOrFail($user->partner_organization_id);

            $progression = StudentProgression::whereHas('student', function ($q) use ($partnerOrg) {
                $q->where('school_id', $partnerOrg->id);
            })->findOrFail($id);

            // Only allow updates if progression is still pending
            if ($progression->status !== 'pending') {
                return response()->json([
                    'success' => false,
                    'message' => 'Cannot update progression that is not pending',
                ], 422);
            }

            $progression->update($request->only(['notes', 'progression_date']));

            return response()->json([
                'success' => true,
                'message' => 'Progression updated successfully',
                'data' => $progression->load('student'),
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update progression',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Delete progression
     */
    public function destroy(string $id): JsonResponse
    {
        try {
            $user = Auth::user();
            $partnerOrg = PartnerOrganization::findOrFail($user->partner_organization_id);

            $progression = StudentProgression::whereHas('student', function ($q) use ($partnerOrg) {
                $q->where('school_id', $partnerOrg->id);
            })->findOrFail($id);

            // Only allow deletion if progression is pending
            if ($progression->status !== 'pending') {
                return response()->json([
                    'success' => false,
                    'message' => 'Cannot delete progression that is not pending',
                ], 422);
            }

            $progression->delete();

            return response()->json([
                'success' => true,
                'message' => 'Progression deleted successfully',
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to delete progression',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Approve progression
     */
    public function approve(string $id): JsonResponse
    {
        try {
            $user = Auth::user();
            $partnerOrg = PartnerOrganization::findOrFail($user->partner_organization_id);

            $progression = StudentProgression::whereHas('student', function ($q) use ($partnerOrg) {
                $q->where('school_id', $partnerOrg->id);
            })->findOrFail($id);

            if ($progression->status !== 'pending') {
                return response()->json([
                    'success' => false,
                    'message' => 'Progression is not pending approval',
                ], 422);
            }

            $this->progressionService->completeProgression($progression);

            return response()->json([
                'success' => true,
                'message' => 'Progression approved and completed successfully',
                'data' => $progression->fresh()->load('student'),
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to approve progression',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Complete progression (alias for approve)
     */
    public function complete(string $id): JsonResponse
    {
        return $this->approve($id);
    }

    /**
     * Reject progression
     */
    public function reject(Request $request, string $id): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'reason' => 'nullable|string|max:500',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $user = Auth::user();
            $partnerOrg = PartnerOrganization::findOrFail($user->partner_organization_id);

            $progression = StudentProgression::whereHas('student', function ($q) use ($partnerOrg) {
                $q->where('school_id', $partnerOrg->id);
            })->findOrFail($id);

            if ($progression->status !== 'pending') {
                return response()->json([
                    'success' => false,
                    'message' => 'Progression is not pending approval',
                ], 422);
            }

            $this->progressionService->rejectProgression($progression, $request->reason);

            return response()->json([
                'success' => true,
                'message' => 'Progression rejected successfully',
                'data' => $progression->fresh()->load('student'),
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to reject progression',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Bulk approve progressions
     */
    public function bulkApprove(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'progression_ids' => 'required|array',
                'progression_ids.*' => 'exists:student_progressions,id',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $user = Auth::user();
            $partnerOrg = PartnerOrganization::findOrFail($user->partner_organization_id);

            $progressions = StudentProgression::whereIn('id', $request->progression_ids)
                ->whereHas('student', function ($q) use ($partnerOrg) {
                    $q->where('school_id', $partnerOrg->id);
                })
                ->where('status', 'pending')
                ->get();

            $results = [
                'successful' => [],
                'failed' => [],
            ];

            foreach ($progressions as $progression) {
                try {
                    $this->progressionService->completeProgression($progression);
                    $results['successful'][] = [
                        'id' => $progression->id,
                        'student_name' => $progression->student->full_name,
                        'from_grade' => $progression->from_grade,
                        'to_grade' => $progression->to_grade,
                    ];
                } catch (\Exception $e) {
                    $results['failed'][] = [
                        'id' => $progression->id,
                        'student_name' => $progression->student->full_name,
                        'error' => $e->getMessage(),
                    ];
                }
            }

            return response()->json([
                'success' => true,
                'message' => 'Bulk approval completed',
                'data' => $results,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to process bulk approval',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get progression history for a specific student
     */
    public function getStudentHistory(Request $request, $studentId): JsonResponse
    {
        try {
            $user = Auth::user();

            // Check if user can access this student's data
            $student = Student::findOrFail($studentId);

            // Partner organizations can only access their own students
            if ($user->role === 'partner_organization' && $student->school_id !== $user->partner_organization_id) {
                return response()->json([
                    'success' => false,
                    'message' => 'Unauthorized access to student data'
                ], 403);
            }

            // Individual students can only access their own data
            if ($user->role === 'user' && $student->user_id !== $user->id) {
                return response()->json([
                    'success' => false,
                    'message' => 'Unauthorized access to student data'
                ], 403);
            }

            $progressions = StudentProgression::where('student_id', $studentId)
                ->with(['student'])
                ->orderBy('academic_year', 'desc')
                ->orderBy('created_at', 'desc')
                ->get();

            // Get current progression
            $currentProgression = $progressions->where('is_current', true)->first();

            // Get progression statistics
            $stats = [
                'total_progressions' => $progressions->count(),
                'completed_progressions' => $progressions->where('status', 'completed')->count(),
                'pending_progressions' => $progressions->where('status', 'pending')->count(),
                'current_grade' => $currentProgression ? $currentProgression->grade_level : null,
                'current_academic_year' => $currentProgression ? $currentProgression->academic_year : null,
                'years_in_system' => $progressions->pluck('academic_year')->unique()->count()
            ];

            return response()->json([
                'success' => true,
                'data' => [
                    'student' => $student,
                    'progressions' => $progressions,
                    'current_progression' => $currentProgression,
                    'statistics' => $stats
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve student progression history',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get progression analytics
     */
    public function getProgressionAnalytics(Request $request): JsonResponse
    {
        try {
            $user = Auth::user();
            $period = $request->get('period', '12months');

            $query = StudentProgression::query();

            // Filter by partner organization if applicable
            if ($user->role === 'partner_organization') {
                $query->whereHas('student', function ($q) use ($user) {
                    $q->where('school_id', $user->partner_organization_id);
                });
            }

            // Calculate date range
            $endDate = now();
            $startDate = match($period) {
                '3months' => $endDate->copy()->subMonths(3),
                '6months' => $endDate->copy()->subMonths(6),
                '12months' => $endDate->copy()->subMonths(12),
                '2years' => $endDate->copy()->subYears(2),
                default => $endDate->copy()->subMonths(12)
            };

            $progressions = $query->whereBetween('created_at', [$startDate, $endDate])
                ->with(['student'])
                ->get();

            // Calculate analytics
            $analytics = [
                'total_progressions' => $progressions->count(),
                'completed_progressions' => $progressions->where('status', 'completed')->count(),
                'pending_progressions' => $progressions->where('status', 'pending')->count(),
                'average_completion_time' => $this->calculateAverageCompletionTime($progressions),
                'grade_distribution' => $this->getGradeDistribution($progressions),
                'monthly_trends' => $this->getMonthlyTrends($progressions, $startDate, $endDate),
                'success_rate' => $progressions->count() > 0
                    ? round(($progressions->where('status', 'completed')->count() / $progressions->count()) * 100, 1)
                    : 0
            ];

            return response()->json([
                'success' => true,
                'data' => $analytics,
                'period' => $period
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve progression analytics',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Calculate average completion time for progressions
     */
    private function calculateAverageCompletionTime($progressions): ?float
    {
        $completedProgressions = $progressions->where('status', 'completed')
            ->whereNotNull('reviewed_at');

        if ($completedProgressions->isEmpty()) {
            return null;
        }

        $totalDays = $completedProgressions->sum(function ($progression) {
            return $progression->created_at->diffInDays($progression->reviewed_at);
        });

        return round($totalDays / $completedProgressions->count(), 1);
    }

    /**
     * Get grade distribution for progressions
     */
    private function getGradeDistribution($progressions): array
    {
        return $progressions->groupBy('to_grade')
            ->map(function ($group) {
                return $group->count();
            })
            ->toArray();
    }

    /**
     * Get monthly trends for progressions
     */
    private function getMonthlyTrends($progressions, $startDate, $endDate): array
    {
        $trends = [];
        $current = $startDate->copy()->startOfMonth();

        while ($current <= $endDate) {
            $monthProgressions = $progressions->filter(function ($progression) use ($current) {
                return $progression->created_at->format('Y-m') === $current->format('Y-m');
            });

            $trends[] = [
                'month' => $current->format('M Y'),
                'total' => $monthProgressions->count(),
                'completed' => $monthProgressions->where('status', 'completed')->count(),
                'pending' => $monthProgressions->where('status', 'pending')->count()
            ];

            $current->addMonth();
        }

        return $trends;
    }

    /**
     * Bulk advance multiple students
     */
    public function bulkAdvanceStudents(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'student_ids' => 'required|array|min:1',
                'student_ids.*' => 'required|exists:students,id',
                'academic_year' => 'nullable|string',
                'progression_date' => 'nullable|date',
                'notes' => 'nullable|string|max:1000',
                'auto_approve' => 'boolean',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $user = Auth::user();
            $studentIds = $request->student_ids;

            // Verify all students belong to the partner organization (if not admin)
            if ($user->role !== 'admin') {
                $partnerOrg = PartnerOrganization::findOrFail($user->partner_organization_id);
                $validStudents = Student::whereIn('id', $studentIds)
                    ->where('school_id', $partnerOrg->id)
                    ->pluck('id')
                    ->toArray();

                if (count($validStudents) !== count($studentIds)) {
                    return response()->json([
                        'success' => false,
                        'message' => 'Some students do not belong to your organization',
                    ], 403);
                }
            }

            $options = [
                'academic_year' => $request->academic_year,
                'progression_date' => $request->progression_date,
                'notes' => $request->notes,
                'auto_approve' => $request->boolean('auto_approve', false),
                'created_by' => $user->id,
            ];

            $results = $this->progressionService->bulkAdvanceStudents($studentIds, $options);

            return response()->json([
                'success' => true,
                'message' => 'Bulk progression completed',
                'data' => [
                    'successful' => $results['successful'],
                    'failed' => $results['failed'],
                    'summary' => [
                        'total_attempted' => count($studentIds),
                        'successful_count' => count($results['successful']),
                        'failed_count' => count($results['failed']),
                    ]
                ],
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to perform bulk advancement',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get progression notifications
     */
    public function getProgressionNotifications(Request $request): JsonResponse
    {
        try {
            $user = Auth::user();
            $status = $request->get('status', 'all'); // all, pending, sent, failed
            $limit = $request->get('limit', 20);

            // For now, return mock data since we don't have a notifications table
            // In a real implementation, you would query the progression_notifications table
            $notifications = collect([
                [
                    'id' => 1,
                    'progression_id' => 1,
                    'type' => 'progression_pending',
                    'status' => 'sent',
                    'message' => 'Student progression is pending approval',
                    'sent_at' => now()->subHours(2),
                    'student_name' => 'John Doe',
                    'from_grade' => 5,
                    'to_grade' => 6
                ],
                [
                    'id' => 2,
                    'progression_id' => 2,
                    'type' => 'progression_approved',
                    'status' => 'pending',
                    'message' => 'Student progression has been approved',
                    'sent_at' => null,
                    'student_name' => 'Jane Smith',
                    'from_grade' => 8,
                    'to_grade' => 9
                ]
            ]);

            if ($status !== 'all') {
                $notifications = $notifications->where('status', $status);
            }

            return response()->json([
                'success' => true,
                'data' => [
                    'notifications' => $notifications->take($limit)->values(),
                    'summary' => [
                        'total' => $notifications->count(),
                        'pending' => $notifications->where('status', 'pending')->count(),
                        'sent' => $notifications->where('status', 'sent')->count(),
                        'failed' => $notifications->where('status', 'failed')->count(),
                    ]
                ],
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch notifications',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Send progression notifications
     */
    public function sendProgressionNotifications(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'progression_ids' => 'required|array|min:1',
                'progression_ids.*' => 'required|exists:student_progressions,id',
                'notification_type' => 'required|in:progression_pending,progression_approved,progression_completed',
                'custom_message' => 'nullable|string|max:500',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $user = Auth::user();
            $progressionIds = $request->progression_ids;
            $notificationType = $request->notification_type;
            $customMessage = $request->custom_message;

            // Verify progressions belong to the partner organization (if not admin)
            if ($user->role !== 'admin') {
                $partnerOrg = PartnerOrganization::findOrFail($user->partner_organization_id);
                $validProgressions = StudentProgression::whereIn('id', $progressionIds)
                    ->whereHas('student', function ($q) use ($partnerOrg) {
                        $q->where('school_id', $partnerOrg->id);
                    })
                    ->pluck('id')
                    ->toArray();

                if (count($validProgressions) !== count($progressionIds)) {
                    return response()->json([
                        'success' => false,
                        'message' => 'Some progressions do not belong to your organization',
                    ], 403);
                }
            }

            $results = [
                'successful' => [],
                'failed' => [],
            ];

            foreach ($progressionIds as $progressionId) {
                try {
                    $progression = StudentProgression::findOrFail($progressionId);

                    // In a real implementation, you would use the notification service
                    // For now, we'll simulate successful notification sending
                    $results['successful'][] = $progressionId;
                } catch (\Exception $e) {
                    $results['failed'][] = [
                        'progression_id' => $progressionId,
                        'error' => $e->getMessage(),
                    ];
                }
            }

            return response()->json([
                'success' => true,
                'message' => 'Notifications sent',
                'data' => [
                    'successful' => $results['successful'],
                    'failed' => $results['failed'],
                    'summary' => [
                        'total_attempted' => count($progressionIds),
                        'successful_count' => count($results['successful']),
                        'failed_count' => count($results['failed']),
                    ]
                ],
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to send notifications',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
