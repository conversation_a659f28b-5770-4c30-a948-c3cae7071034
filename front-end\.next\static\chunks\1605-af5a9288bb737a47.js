"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1605],{5040:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("BookOpen",[["path",{d:"M12 7v14",key:"1akyts"}],["path",{d:"M3 18a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1h5a4 4 0 0 1 4 4 4 4 0 0 1 4-4h5a1 1 0 0 1 1 1v13a1 1 0 0 1-1 1h-6a3 3 0 0 0-3 3 3 3 0 0 0-3-3z",key:"ruj8y"}]])},5845:(e,t,r)=>{r.d(t,{i:()=>o});var n=r(12115),l=r(39033);function o({prop:e,defaultProp:t,onChange:r=()=>{}}){let[o,a]=function({defaultProp:e,onChange:t}){let r=n.useState(e),[o]=r,a=n.useRef(o),i=(0,l.c)(t);return n.useEffect(()=>{a.current!==o&&(i(o),a.current=o)},[o,a,i]),r}({defaultProp:t,onChange:r}),i=void 0!==e,u=i?e:o,c=(0,l.c)(r);return[u,n.useCallback(t=>{if(i){let r="function"==typeof t?t(e):t;r!==e&&c(r)}else a(t)},[i,e,a,c])]}},6101:(e,t,r)=>{r.d(t,{s:()=>a,t:()=>o});var n=r(12115);function l(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function o(...e){return t=>{let r=!1,n=e.map(e=>{let n=l(e,t);return r||"function"!=typeof n||(r=!0),n});if(r)return()=>{for(let t=0;t<n.length;t++){let r=n[t];"function"==typeof r?r():l(e[t],null)}}}}function a(...e){return n.useCallback(o(...e),e)}},17580:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]])},39033:(e,t,r)=>{r.d(t,{c:()=>l});var n=r(12115);function l(e){let t=n.useRef(e);return n.useEffect(()=>{t.current=e}),n.useMemo(()=>(...e)=>t.current?.(...e),[])}},46081:(e,t,r)=>{r.d(t,{A:()=>a,q:()=>o});var n=r(12115),l=r(95155);function o(e,t){let r=n.createContext(t),o=e=>{let{children:t,...o}=e,a=n.useMemo(()=>o,Object.values(o));return(0,l.jsx)(r.Provider,{value:a,children:t})};return o.displayName=e+"Provider",[o,function(l){let o=n.useContext(r);if(o)return o;if(void 0!==t)return t;throw Error(`\`${l}\` must be used within \`${e}\``)}]}function a(e,t=[]){let r=[],o=()=>{let t=r.map(e=>n.createContext(e));return function(r){let l=r?.[e]||t;return n.useMemo(()=>({[`__scope${e}`]:{...r,[e]:l}}),[r,l])}};return o.scopeName=e,[function(t,o){let a=n.createContext(o),i=r.length;r=[...r,o];let u=t=>{let{scope:r,children:o,...u}=t,c=r?.[e]?.[i]||a,f=n.useMemo(()=>u,Object.values(u));return(0,l.jsx)(c.Provider,{value:f,children:o})};return u.displayName=t+"Provider",[u,function(r,l){let u=l?.[e]?.[i]||a,c=n.useContext(u);if(c)return c;if(void 0!==o)return o;throw Error(`\`${r}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let r=()=>{let r=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let l=r.reduce((t,{useScope:r,scopeName:n})=>{let l=r(e)[`__scope${n}`];return{...t,...l}},{});return n.useMemo(()=>({[`__scope${t.scopeName}`]:l}),[l])}};return r.scopeName=t.scopeName,r}(o,...t)]}},47924:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]])},52712:(e,t,r)=>{r.d(t,{N:()=>l});var n=r(12115),l=globalThis?.document?n.useLayoutEffect:()=>{}},53896:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("School",[["path",{d:"M14 22v-4a2 2 0 1 0-4 0v4",key:"hhkicm"}],["path",{d:"m18 10 4 2v8a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2v-8l4-2",key:"1vwozw"}],["path",{d:"M18 5v17",key:"1sw6gf"}],["path",{d:"m4 6 8-4 8 4",key:"1q0ilc"}],["path",{d:"M6 5v17",key:"1xfsm0"}],["circle",{cx:"12",cy:"9",r:"2",key:"1092wv"}]])},55868:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("DollarSign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]])},61285:(e,t,r)=>{r.d(t,{B:()=>u});var n,l=r(12115),o=r(52712),a=(n||(n=r.t(l,2)))["useId".toString()]||(()=>void 0),i=0;function u(e){let[t,r]=l.useState(a());return(0,o.N)(()=>{e||r(e=>e??String(i++))},[e]),e||(t?`radix-${t}`:"")}},63655:(e,t,r)=>{r.d(t,{hO:()=>u,sG:()=>i});var n=r(12115),l=r(47650),o=r(99708),a=r(95155),i=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"].reduce((e,t)=>{let r=n.forwardRef((e,r)=>{let{asChild:n,...l}=e,i=n?o.DX:t;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,a.jsx)(i,{...l,ref:r})});return r.displayName=`Primitive.${t}`,{...e,[t]:r}},{});function u(e,t){e&&l.flushSync(()=>e.dispatchEvent(t))}},69074:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("Calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},82284:(e,t,r)=>{r.d(t,{N:()=>u});var n=r(12115),l=r(46081),o=r(6101),a=r(99708),i=r(95155);function u(e){let t=e+"CollectionProvider",[r,u]=(0,l.A)(t),[c,f]=r(t,{collectionRef:{current:null},itemMap:new Map}),d=e=>{let{scope:t,children:r}=e,l=n.useRef(null),o=n.useRef(new Map).current;return(0,i.jsx)(c,{scope:t,itemMap:o,collectionRef:l,children:r})};d.displayName=t;let s=e+"CollectionSlot",p=n.forwardRef((e,t)=>{let{scope:r,children:n}=e,l=f(s,r),u=(0,o.s)(t,l.collectionRef);return(0,i.jsx)(a.DX,{ref:u,children:n})});p.displayName=s;let y=e+"CollectionItemSlot",h="data-radix-collection-item",m=n.forwardRef((e,t)=>{let{scope:r,children:l,...u}=e,c=n.useRef(null),d=(0,o.s)(t,c),s=f(y,r);return n.useEffect(()=>(s.itemMap.set(c,{ref:c,...u}),()=>void s.itemMap.delete(c))),(0,i.jsx)(a.DX,{[h]:"",ref:d,children:l})});return m.displayName=y,[{Provider:d,Slot:p,ItemSlot:m},function(t){let r=f(e+"CollectionConsumer",t);return n.useCallback(()=>{let e=r.collectionRef.current;if(!e)return[];let t=Array.from(e.querySelectorAll("[".concat(h,"]")));return Array.from(r.itemMap.values()).sort((e,r)=>t.indexOf(e.ref.current)-t.indexOf(r.ref.current))},[r.collectionRef,r.itemMap])},u]}},85185:(e,t,r)=>{r.d(t,{m:()=>n});function n(e,t,{checkForDefaultPrevented:r=!0}={}){return function(n){if(e?.(n),!1===r||!n.defaultPrevented)return t?.(n)}}},85339:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("CircleAlert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},87949:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("GraduationCap",[["path",{d:"M21.42 10.922a1 1 0 0 0-.019-1.838L12.83 5.18a2 2 0 0 0-1.66 0L2.6 9.08a1 1 0 0 0 0 1.832l8.57 3.908a2 2 0 0 0 1.66 0z",key:"j76jl0"}],["path",{d:"M22 10v6",key:"1lu8f3"}],["path",{d:"M6 12.5V16a6 3 0 0 0 12 0v-3.5",key:"1r8lef"}]])},92138:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("ArrowRight",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]])},94315:(e,t,r)=>{r.d(t,{jH:()=>o});var n=r(12115);r(95155);var l=n.createContext(void 0);function o(e){let t=n.useContext(l);return e||t||"ltr"}},99708:(e,t,r)=>{r.d(t,{DX:()=>a,xV:()=>u});var n=r(12115),l=r(6101),o=r(95155),a=n.forwardRef((e,t)=>{let{children:r,...l}=e,a=n.Children.toArray(r),u=a.find(c);if(u){let e=u.props.children,r=a.map(t=>t!==u?t:n.Children.count(e)>1?n.Children.only(null):n.isValidElement(e)?e.props.children:null);return(0,o.jsx)(i,{...l,ref:t,children:n.isValidElement(e)?n.cloneElement(e,void 0,r):null})}return(0,o.jsx)(i,{...l,ref:t,children:r})});a.displayName="Slot";var i=n.forwardRef((e,t)=>{let{children:r,...o}=e;if(n.isValidElement(r)){let e=function(e){let t=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,r=t&&"isReactWarning"in t&&t.isReactWarning;return r?e.ref:(r=(t=Object.getOwnPropertyDescriptor(e,"ref")?.get)&&"isReactWarning"in t&&t.isReactWarning)?e.props.ref:e.props.ref||e.ref}(r);return n.cloneElement(r,{...function(e,t){let r={...t};for(let n in t){let l=e[n],o=t[n];/^on[A-Z]/.test(n)?l&&o?r[n]=(...e)=>{o(...e),l(...e)}:l&&(r[n]=l):"style"===n?r[n]={...l,...o}:"className"===n&&(r[n]=[l,o].filter(Boolean).join(" "))}return{...e,...r}}(o,r.props),ref:t?(0,l.t)(t,e):e})}return n.Children.count(r)>1?n.Children.only(null):null});i.displayName="SlotClone";var u=({children:e})=>(0,o.jsx)(o.Fragment,{children:e});function c(e){return n.isValidElement(e)&&e.type===u}}}]);