(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7316],{5040:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(19946).A)("BookOpen",[["path",{d:"M12 7v14",key:"1akyts"}],["path",{d:"M3 18a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1h5a4 4 0 0 1 4 4 4 4 0 0 1 4-4h5a1 1 0 0 1 1 1v13a1 1 0 0 1-1 1h-6a3 3 0 0 0-3 3 3 3 0 0 0-3-3z",key:"ruj8y"}]])},6101:(e,s,r)=>{"use strict";r.d(s,{s:()=>i,t:()=>l});var t=r(12115);function a(e,s){if("function"==typeof e)return e(s);null!=e&&(e.current=s)}function l(...e){return s=>{let r=!1,t=e.map(e=>{let t=a(e,s);return r||"function"!=typeof t||(r=!0),t});if(r)return()=>{for(let s=0;s<t.length;s++){let r=t[s];"function"==typeof r?r():a(e[s],null)}}}}function i(...e){return t.useCallback(l(...e),e)}},14186:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(19946).A)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},17580:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(19946).A)("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]])},35169:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(19946).A)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},35695:(e,s,r)=>{"use strict";var t=r(18999);r.o(t,"notFound")&&r.d(s,{notFound:function(){return t.notFound}}),r.o(t,"useParams")&&r.d(s,{useParams:function(){return t.useParams}}),r.o(t,"usePathname")&&r.d(s,{usePathname:function(){return t.usePathname}}),r.o(t,"useRouter")&&r.d(s,{useRouter:function(){return t.useRouter}}),r.o(t,"useSearchParams")&&r.d(s,{useSearchParams:function(){return t.useSearchParams}})},38564:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(19946).A)("Star",[["path",{d:"M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z",key:"r04s7s"}]])},40646:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(19946).A)("CircleCheckBig",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},40723:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>C});var t=r(95155),a=r(12115),l=r(35695),i=r(6874),n=r.n(i),c=r(88482),d=r(97168),o=r(88145),x=r(5040),u=r(53896),h=r(87949),m=r(85339),p=r(35169),g=r(38564),f=r(55868),y=r(69074),j=r(17580),v=r(92138),b=r(71007),N=r(40646),k=r(57434),A=r(14186),w=r(31886);let _={primary:{icon:x.A,title:"Primary School",color:"bg-blue-500",bgColor:"bg-blue-50",textColor:"text-blue-700",borderColor:"border-blue-200"},secondary:{icon:u.A,title:"Secondary School",color:"bg-green-500",bgColor:"bg-green-50",textColor:"text-green-700",borderColor:"border-green-200"},university:{icon:h.A,title:"University",color:"bg-purple-500",bgColor:"bg-purple-50",textColor:"text-purple-700",borderColor:"border-purple-200"}};function C(){let e=(0,l.useParams)(),s=(0,l.useRouter)(),[r,i]=(0,a.useState)(null),[x,u]=(0,a.useState)(!0),[h,C]=(0,a.useState)(null);(0,a.useEffect)(()=>{e.id&&R(e.id)},[e.id]);let R=async e=>{try{u(!0),C(null);let s=await w.uE.getPublicScholarship(e);if(s.success&&s.data)i(s.data);else throw Error(s.message||"Scholarship not found")}catch(e){console.error("Error fetching scholarship:",e),C("Failed to load scholarship details. Please try again later.")}finally{u(!1)}},M=e=>new Date(e).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"});if(x)return(0,t.jsx)("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900 py-8",children:(0,t.jsx)("div",{className:"container mx-auto px-4",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-green-600 mx-auto"}),(0,t.jsx)("p",{className:"mt-4 text-gray-600",children:"Loading scholarship details..."})]})})});if(h||!r)return(0,t.jsx)("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900 py-8",children:(0,t.jsx)("div",{className:"container mx-auto px-4",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)(m.A,{className:"h-12 w-12 text-red-500 mx-auto mb-4"}),(0,t.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-2",children:"Error Loading Scholarship"}),(0,t.jsx)("p",{className:"text-gray-600 mb-6",children:h||"Scholarship not found"}),(0,t.jsxs)("div",{className:"flex gap-4 justify-center",children:[(0,t.jsxs)(d.$,{onClick:()=>s.back(),variant:"outline",children:[(0,t.jsx)(p.A,{className:"mr-2 h-4 w-4"}),"Go Back"]}),(0,t.jsx)(d.$,{onClick:()=>R(e.id),children:"Try Again"})]})]})})});let S=_[r.category],E=S.icon,Z=r.max_applicants?r.max_applicants-r.current_applicants:null,q="active"===r.status||"open"===r.status;return(0,t.jsxs)("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900",children:[(0,t.jsx)("section",{className:"py-8 bg-white dark:bg-gray-900 border-b",children:(0,t.jsxs)("div",{className:"container mx-auto px-4 sm:px-6 lg:px-8",children:[(0,t.jsx)("div",{className:"flex items-center gap-4 mb-6",children:(0,t.jsxs)(d.$,{variant:"outline",onClick:()=>s.back(),className:"flex items-center gap-2",children:[(0,t.jsx)(p.A,{className:"h-4 w-4"}),"Back to Scholarships"]})}),(0,t.jsxs)("div",{className:"flex flex-col lg:flex-row gap-8",children:[(0,t.jsx)("div",{className:"flex-1",children:(0,t.jsxs)("div",{className:"flex items-start gap-4 mb-4",children:[(0,t.jsx)("div",{className:"p-3 rounded-lg ".concat(S.bgColor),children:(0,t.jsx)(E,{className:"h-8 w-8 ".concat(S.textColor)})}),(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:["closed"===r.status||"inactive"===r.status?(0,t.jsx)(o.E,{variant:"secondary",children:"Closed"}):r.max_applicants&&r.max_applicants-r.current_applicants<=5?(0,t.jsx)(o.E,{variant:"destructive",children:"Few Spots Left"}):r.is_featured?(0,t.jsx)(o.E,{variant:"default",className:"bg-amber-500",children:"Featured"}):(0,t.jsx)(o.E,{variant:"default",className:"bg-green-500",children:"Open"}),(0,t.jsx)(o.E,{variant:"outline",className:"rounded-full",children:S.title}),r.is_featured&&(0,t.jsxs)(o.E,{variant:"outline",className:"text-amber-600 border-amber-200",children:[(0,t.jsx)(g.A,{className:"h-3 w-3 mr-1"}),"Featured"]})]}),(0,t.jsx)("h1",{className:"text-3xl font-bold text-gray-900 dark:text-white mb-2",children:r.title}),(0,t.jsx)("p",{className:"text-gray-600 dark:text-gray-400 text-lg",children:r.description})]})]})}),(0,t.jsx)("div",{className:"lg:w-80",children:(0,t.jsxs)(c.Zp,{children:[(0,t.jsx)(c.aR,{children:(0,t.jsx)(c.ZB,{className:"text-lg",children:"Quick Info"})}),(0,t.jsxs)(c.Wu,{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex items-center gap-3",children:[(0,t.jsx)(f.A,{className:"h-5 w-5 text-green-600"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm text-gray-600",children:"Award Amount"}),(0,t.jsxs)("p",{className:"font-semibold text-green-600",children:["₦",r.amount.toLocaleString()]})]})]}),(0,t.jsxs)("div",{className:"flex items-center gap-3",children:[(0,t.jsx)(y.A,{className:"h-5 w-5 text-green-600"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm text-gray-600",children:"Application Deadline"}),(0,t.jsx)("p",{className:"font-semibold",children:M(r.application_deadline)})]})]}),null!==Z&&(0,t.jsxs)("div",{className:"flex items-center gap-3",children:[(0,t.jsx)(j.A,{className:"h-5 w-5 text-green-600"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm text-gray-600",children:"Applicants"}),(0,t.jsxs)("p",{className:"font-semibold",children:[r.current_applicants,"/",r.max_applicants]})]})]}),(0,t.jsx)("hr",{className:"my-4"}),(0,t.jsx)(n(),{href:"/scholarship-application?category=".concat(r.category,"&scholarship_id=").concat(r.id),className:"block",children:(0,t.jsxs)(d.$,{className:"w-full bg-green-600 hover:bg-green-700 text-white",disabled:!q||null!==Z&&Z<=0,size:"lg",children:[q?null!==Z&&Z<=0?"No Spots Available":"Apply Now":"Application Closed",q&&(null===Z||Z>0)&&(0,t.jsx)(v.A,{className:"ml-2 h-5 w-5"})]})})]})]})})]})]})}),(0,t.jsx)("section",{className:"py-12",children:(0,t.jsx)("div",{className:"container mx-auto px-4 sm:px-6 lg:px-8",children:(0,t.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[(0,t.jsxs)("div",{className:"lg:col-span-2 space-y-8",children:[r.category_instructions&&(0,t.jsxs)(c.Zp,{children:[(0,t.jsx)(c.aR,{children:(0,t.jsxs)(c.ZB,{className:"flex items-center gap-2",children:[(0,t.jsx)(b.A,{className:"h-5 w-5"}),"Application Instructions"]})}),(0,t.jsxs)(c.Wu,{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg",children:[(0,t.jsx)("h4",{className:"font-semibold text-blue-900 dark:text-blue-100 mb-2",children:r.category_instructions.title}),(0,t.jsx)("p",{className:"text-blue-800 dark:text-blue-200 text-sm mb-2",children:r.category_instructions.age_range}),(0,t.jsxs)("p",{className:"text-blue-700 dark:text-blue-300",children:[(0,t.jsx)("strong",{children:"Who fills this form:"})," ",r.category_instructions.filled_by]}),(0,t.jsx)("p",{className:"text-blue-700 dark:text-blue-300 mt-2",children:r.category_instructions.instruction})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h5",{className:"font-semibold mb-2",children:"Required Information:"}),(0,t.jsx)("ul",{className:"space-y-1",children:r.category_instructions.required_info.map((e,s)=>(0,t.jsxs)("li",{className:"flex items-center gap-2 text-sm",children:[(0,t.jsx)(N.A,{className:"h-4 w-4 text-green-600"}),e]},s))})]})]})]}),(r.eligibility_criteria||r.requirements)&&(0,t.jsxs)(c.Zp,{children:[(0,t.jsx)(c.aR,{children:(0,t.jsxs)(c.ZB,{className:"flex items-center gap-2",children:[(0,t.jsx)(N.A,{className:"h-5 w-5"}),"Eligibility & Requirements"]})}),(0,t.jsx)(c.Wu,{children:(0,t.jsx)("p",{className:"text-gray-700 dark:text-gray-300",children:r.eligibility_criteria||r.requirements})})]}),r.custom_fields&&r.custom_fields.length>0&&(0,t.jsxs)(c.Zp,{children:[(0,t.jsxs)(c.aR,{children:[(0,t.jsxs)(c.ZB,{className:"flex items-center gap-2",children:[(0,t.jsx)(k.A,{className:"h-5 w-5"}),"Application Form Fields"]}),(0,t.jsx)(c.BT,{children:"Preview of the information you'll need to provide"})]}),(0,t.jsx)(c.Wu,{children:(0,t.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:r.custom_fields.map((e,s)=>(0,t.jsxs)("div",{className:"border rounded-lg p-3",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2 mb-1",children:[(0,t.jsx)("span",{className:"font-medium text-sm",children:e.field_name}),e.is_required&&(0,t.jsx)(o.E,{variant:"outline",className:"text-xs",children:"Required"})]}),(0,t.jsxs)("p",{className:"text-xs text-gray-600 capitalize",children:[e.field_type.replace("_"," ")," field"]}),e.field_options&&e.field_options.length>0&&(0,t.jsxs)("p",{className:"text-xs text-gray-500 mt-1",children:["Options: ",e.field_options.join(", ")]})]},e.id))})})]}),r.documents_required&&r.documents_required.length>0&&(0,t.jsxs)(c.Zp,{children:[(0,t.jsx)(c.aR,{children:(0,t.jsxs)(c.ZB,{className:"flex items-center gap-2",children:[(0,t.jsx)(k.A,{className:"h-5 w-5"}),"Required Documents"]})}),(0,t.jsx)(c.Wu,{children:(0,t.jsx)("ul",{className:"space-y-2",children:r.documents_required.map((e,s)=>(0,t.jsxs)("li",{className:"flex items-center gap-2",children:[(0,t.jsx)(N.A,{className:"h-4 w-4 text-green-600"}),e]},s))})})]}),r.application_instructions&&(0,t.jsxs)(c.Zp,{children:[(0,t.jsx)(c.aR,{children:(0,t.jsx)(c.ZB,{children:"Additional Instructions"})}),(0,t.jsx)(c.Wu,{children:(0,t.jsx)("p",{className:"text-gray-700 dark:text-gray-300 whitespace-pre-line",children:r.application_instructions})})]}),r.terms_conditions&&(0,t.jsxs)(c.Zp,{children:[(0,t.jsx)(c.aR,{children:(0,t.jsx)(c.ZB,{children:"Terms & Conditions"})}),(0,t.jsx)(c.Wu,{children:(0,t.jsx)("p",{className:"text-gray-700 dark:text-gray-300 whitespace-pre-line text-sm",children:r.terms_conditions})})]})]}),(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)(c.Zp,{children:[(0,t.jsx)(c.aR,{children:(0,t.jsxs)(c.ZB,{className:"flex items-center gap-2",children:[(0,t.jsx)(A.A,{className:"h-5 w-5"}),"Timeline"]})}),(0,t.jsx)(c.Wu,{children:(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex items-center gap-3",children:[(0,t.jsx)("div",{className:"w-2 h-2 bg-green-500 rounded-full"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"font-medium text-sm",children:"Applications Open"}),(0,t.jsx)("p",{className:"text-xs text-gray-600",children:"Now accepting applications"})]})]}),(0,t.jsxs)("div",{className:"flex items-center gap-3",children:[(0,t.jsx)("div",{className:"w-2 h-2 bg-amber-500 rounded-full"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"font-medium text-sm",children:"Application Deadline"}),(0,t.jsx)("p",{className:"text-xs text-gray-600",children:M(r.application_deadline)})]})]}),(0,t.jsxs)("div",{className:"flex items-center gap-3",children:[(0,t.jsx)("div",{className:"w-2 h-2 bg-gray-400 rounded-full"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"font-medium text-sm",children:"Review Period"}),(0,t.jsx)("p",{className:"text-xs text-gray-600",children:"Applications under review"})]})]}),(0,t.jsxs)("div",{className:"flex items-center gap-3",children:[(0,t.jsx)("div",{className:"w-2 h-2 bg-gray-400 rounded-full"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"font-medium text-sm",children:"Results Announced"}),(0,t.jsx)("p",{className:"text-xs text-gray-600",children:"Winners notified"})]})]})]})})]}),(0,t.jsxs)(c.Zp,{children:[(0,t.jsx)(c.aR,{children:(0,t.jsx)(c.ZB,{children:"Need Help?"})}),(0,t.jsxs)(c.Wu,{className:"space-y-4",children:[(0,t.jsx)("p",{className:"text-sm text-gray-600",children:"Have questions about this scholarship or the application process?"}),(0,t.jsx)(n(),{href:"/contact",children:(0,t.jsx)(d.$,{variant:"outline",className:"w-full",children:"Contact Support"})})]})]})]})]})})})]})}},53896:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(19946).A)("School",[["path",{d:"M14 22v-4a2 2 0 1 0-4 0v4",key:"hhkicm"}],["path",{d:"m18 10 4 2v8a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2v-8l4-2",key:"1vwozw"}],["path",{d:"M18 5v17",key:"1sw6gf"}],["path",{d:"m4 6 8-4 8 4",key:"1q0ilc"}],["path",{d:"M6 5v17",key:"1xfsm0"}],["circle",{cx:"12",cy:"9",r:"2",key:"1092wv"}]])},53999:(e,s,r)=>{"use strict";r.d(s,{cn:()=>l});var t=r(52596),a=r(39688);function l(){for(var e=arguments.length,s=Array(e),r=0;r<e;r++)s[r]=arguments[r];return(0,a.QP)((0,t.$)(s))}},55868:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(19946).A)("DollarSign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]])},57434:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(19946).A)("FileText",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]])},69074:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(19946).A)("Calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},71007:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(19946).A)("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},81506:(e,s,r)=>{Promise.resolve().then(r.bind(r,40723))},85339:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(19946).A)("CircleAlert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},87949:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(19946).A)("GraduationCap",[["path",{d:"M21.42 10.922a1 1 0 0 0-.019-1.838L12.83 5.18a2 2 0 0 0-1.66 0L2.6 9.08a1 1 0 0 0 0 1.832l8.57 3.908a2 2 0 0 0 1.66 0z",key:"j76jl0"}],["path",{d:"M22 10v6",key:"1lu8f3"}],["path",{d:"M6 12.5V16a6 3 0 0 0 12 0v-3.5",key:"1r8lef"}]])},88145:(e,s,r)=>{"use strict";r.d(s,{E:()=>n});var t=r(95155);r(12115);var a=r(74466),l=r(53999);let i=(0,a.F)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function n(e){let{className:s,variant:r,...a}=e;return(0,t.jsx)("div",{className:(0,l.cn)(i({variant:r}),s),...a})}},88482:(e,s,r)=>{"use strict";r.d(s,{BT:()=>d,Wu:()=>o,ZB:()=>c,Zp:()=>i,aR:()=>n,wL:()=>x});var t=r(95155),a=r(12115),l=r(53999);let i=a.forwardRef((e,s)=>{let{className:r,...a}=e;return(0,t.jsx)("div",{ref:s,className:(0,l.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",r),...a})});i.displayName="Card";let n=a.forwardRef((e,s)=>{let{className:r,...a}=e;return(0,t.jsx)("div",{ref:s,className:(0,l.cn)("flex flex-col space-y-1.5 p-6",r),...a})});n.displayName="CardHeader";let c=a.forwardRef((e,s)=>{let{className:r,...a}=e;return(0,t.jsx)("div",{ref:s,className:(0,l.cn)("text-2xl font-semibold leading-none tracking-tight",r),...a})});c.displayName="CardTitle";let d=a.forwardRef((e,s)=>{let{className:r,...a}=e;return(0,t.jsx)("div",{ref:s,className:(0,l.cn)("text-sm text-muted-foreground",r),...a})});d.displayName="CardDescription";let o=a.forwardRef((e,s)=>{let{className:r,...a}=e;return(0,t.jsx)("div",{ref:s,className:(0,l.cn)("p-6 pt-0",r),...a})});o.displayName="CardContent";let x=a.forwardRef((e,s)=>{let{className:r,...a}=e;return(0,t.jsx)("div",{ref:s,className:(0,l.cn)("flex items-center p-6 pt-0",r),...a})});x.displayName="CardFooter"},92138:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(19946).A)("ArrowRight",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]])},97168:(e,s,r)=>{"use strict";r.d(s,{$:()=>d,r:()=>c});var t=r(95155),a=r(12115),l=r(99708),i=r(74466),n=r(53999);let c=(0,i.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-all duration-200 ease-in-out focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 transform hover:scale-105",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90 hover:brightness-110",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90 hover:brightness-110",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground hover:brightness-105",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80 hover:brightness-105",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),d=a.forwardRef((e,s)=>{let{className:r,variant:a,size:i,asChild:d=!1,...o}=e,x=d?l.DX:"button";return(0,t.jsx)(x,{className:(0,n.cn)(c({variant:a,size:i,className:r})),ref:s,suppressHydrationWarning:!0,...o})});d.displayName="Button"},99708:(e,s,r)=>{"use strict";r.d(s,{DX:()=>i,xV:()=>c});var t=r(12115),a=r(6101),l=r(95155),i=t.forwardRef((e,s)=>{let{children:r,...a}=e,i=t.Children.toArray(r),c=i.find(d);if(c){let e=c.props.children,r=i.map(s=>s!==c?s:t.Children.count(e)>1?t.Children.only(null):t.isValidElement(e)?e.props.children:null);return(0,l.jsx)(n,{...a,ref:s,children:t.isValidElement(e)?t.cloneElement(e,void 0,r):null})}return(0,l.jsx)(n,{...a,ref:s,children:r})});i.displayName="Slot";var n=t.forwardRef((e,s)=>{let{children:r,...l}=e;if(t.isValidElement(r)){let e=function(e){let s=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,r=s&&"isReactWarning"in s&&s.isReactWarning;return r?e.ref:(r=(s=Object.getOwnPropertyDescriptor(e,"ref")?.get)&&"isReactWarning"in s&&s.isReactWarning)?e.props.ref:e.props.ref||e.ref}(r);return t.cloneElement(r,{...function(e,s){let r={...s};for(let t in s){let a=e[t],l=s[t];/^on[A-Z]/.test(t)?a&&l?r[t]=(...e)=>{l(...e),a(...e)}:a&&(r[t]=a):"style"===t?r[t]={...a,...l}:"className"===t&&(r[t]=[a,l].filter(Boolean).join(" "))}return{...e,...r}}(l,r.props),ref:s?(0,a.t)(s,e):e})}return t.Children.count(r)>1?t.Children.only(null):null});n.displayName="SlotClone";var c=({children:e})=>(0,l.jsx)(l.Fragment,{children:e});function d(e){return t.isValidElement(e)&&e.type===c}}},e=>{var s=s=>e(e.s=s);e.O(0,[1778,6874,1886,8441,1684,7358],()=>s(81506)),_N_E=e.O()}]);