"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/auth/login/page",{

/***/ "(app-pages-browser)/./app/auth/login/page.tsx":
/*!*********************************!*\
  !*** ./app/auth/login/page.tsx ***!
  \*********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ LoginPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./components/ui/label.tsx\");\n/* harmony import */ var _components_ui_checkbox__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/checkbox */ \"(app-pages-browser)/./components/ui/checkbox.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_tabs__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/tabs */ \"(app-pages-browser)/./components/ui/tabs.tsx\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_BookOpen_Eye_EyeOff_Facebook_GraduationCap_Heart_Lock_Mail_Twitter_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,BookOpen,Eye,EyeOff,Facebook,GraduationCap,Heart,Lock,Mail,Twitter,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_BookOpen_Eye_EyeOff_Facebook_GraduationCap_Heart_Lock_Mail_Twitter_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,BookOpen,Eye,EyeOff,Facebook,GraduationCap,Heart,Lock,Mail,Twitter,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_BookOpen_Eye_EyeOff_Facebook_GraduationCap_Heart_Lock_Mail_Twitter_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,BookOpen,Eye,EyeOff,Facebook,GraduationCap,Heart,Lock,Mail,Twitter,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/lock.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_BookOpen_Eye_EyeOff_Facebook_GraduationCap_Heart_Lock_Mail_Twitter_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,BookOpen,Eye,EyeOff,Facebook,GraduationCap,Heart,Lock,Mail,Twitter,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye-off.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_BookOpen_Eye_EyeOff_Facebook_GraduationCap_Heart_Lock_Mail_Twitter_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,BookOpen,Eye,EyeOff,Facebook,GraduationCap,Heart,Lock,Mail,Twitter,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_BookOpen_Eye_EyeOff_Facebook_GraduationCap_Heart_Lock_Mail_Twitter_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,BookOpen,Eye,EyeOff,Facebook,GraduationCap,Heart,Lock,Mail,Twitter,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/facebook.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_BookOpen_Eye_EyeOff_Facebook_GraduationCap_Heart_Lock_Mail_Twitter_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,BookOpen,Eye,EyeOff,Facebook,GraduationCap,Heart,Lock,Mail,Twitter,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/twitter.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_BookOpen_Eye_EyeOff_Facebook_GraduationCap_Heart_Lock_Mail_Twitter_Users_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,BookOpen,Eye,EyeOff,Facebook,GraduationCap,Heart,Lock,Mail,Twitter,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_BookOpen_Eye_EyeOff_Facebook_GraduationCap_Heart_Lock_Mail_Twitter_Users_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,BookOpen,Eye,EyeOff,Facebook,GraduationCap,Heart,Lock,Mail,Twitter,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/graduation-cap.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_BookOpen_Eye_EyeOff_Facebook_GraduationCap_Heart_Lock_Mail_Twitter_Users_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,BookOpen,Eye,EyeOff,Facebook,GraduationCap,Heart,Lock,Mail,Twitter,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/book-open.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_BookOpen_Eye_EyeOff_Facebook_GraduationCap_Heart_Lock_Mail_Twitter_Users_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,BookOpen,Eye,EyeOff,Facebook,GraduationCap,Heart,Lock,Mail,Twitter,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./lib/api.ts\");\n/* harmony import */ var _hooks_useSettings__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/hooks/useSettings */ \"(app-pages-browser)/./hooks/useSettings.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction LoginPage() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_4__.useRouter)();\n    const { settings, loading: settingsLoading } = (0,_hooks_useSettings__WEBPACK_IMPORTED_MODULE_12__.useSettings)();\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showPassword, setShowPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showConfirmPassword, setShowConfirmPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [email, setEmail] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [password, setPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [rememberMe, setRememberMe] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [acceptTerms, setAcceptTerms] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isClient, setIsClient] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Registration form state\n    const [registerData, setRegisterData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        firstName: \"\",\n        lastName: \"\",\n        email: \"\",\n        password: \"\",\n        confirmPassword: \"\"\n    });\n    // Handle client-side hydration\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"LoginPage.useEffect\": ()=>{\n            setIsClient(true);\n        }\n    }[\"LoginPage.useEffect\"], []);\n    const handleLogin = async (e)=>{\n        e.preventDefault();\n        setIsLoading(true);\n        try {\n            var _data_data;\n            console.log('Attempting login for:', email) // Debug logging\n            ;\n            const data = await _lib_api__WEBPACK_IMPORTED_MODULE_11__.apiClient.login(email, password);\n            console.log('Login response:', data) // Debug logging\n            ;\n            if (data.success && ((_data_data = data.data) === null || _data_data === void 0 ? void 0 : _data_data.access_token)) {\n                // Store the token and user data\n                localStorage.setItem(\"authToken\", data.data.access_token);\n                // Handle different user data formats from Laravel response\n                const userData = data.data.user || data.data;\n                if (userData) {\n                    localStorage.setItem('user', JSON.stringify(userData));\n                    console.log('User data stored:', userData) // Debug logging\n                    ;\n                } else {\n                    console.warn('No user data received in login response');\n                }\n                // Handle successful login\n                console.log('Login successful, redirecting...');\n                alert(data.message || \"Login successful!\") // Or use a toast notification\n                ;\n                // Check for stored redirect URL\n                const redirectUrl = localStorage.getItem('redirectAfterLogin');\n                if (redirectUrl) {\n                    localStorage.removeItem('redirectAfterLogin');\n                    router.push(redirectUrl);\n                } else {\n                    router.push(\"/dashboard\") // Default redirect to dashboard\n                    ;\n                }\n            } else {\n                // Handle errors\n                console.error('Login failed:', data);\n                alert(data.message || \"Login failed. Please check your credentials.\") // Or use a toast notification\n                ;\n                if (data.errors) {\n                    console.error(\"Validation errors:\", data.errors);\n                // You could display these errors to the user\n                }\n            }\n        } catch (error) {\n            console.error(\"Login error:\", error);\n            alert(\"An unexpected error occurred. Please try again.\") // Or use a toast notification\n            ;\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const handleRegister = async (e)=>{\n        e.preventDefault();\n        // Validation\n        if (!registerData.firstName || !registerData.lastName || !registerData.email || !registerData.password) {\n            alert(\"Please fill in all required fields\");\n            return;\n        }\n        if (registerData.password !== registerData.confirmPassword) {\n            alert(\"Passwords do not match\");\n            return;\n        }\n        if (registerData.password.length < 8) {\n            alert(\"Password must be at least 8 characters long\");\n            return;\n        }\n        if (!acceptTerms) {\n            alert(\"Please accept the Terms of Service and Privacy Policy\");\n            return;\n        }\n        setIsLoading(true);\n        try {\n            console.log('Attempting registration for:', registerData.email);\n            const userData = {\n                first_name: registerData.firstName,\n                last_name: registerData.lastName,\n                email: registerData.email,\n                password: registerData.password,\n                password_confirmation: registerData.confirmPassword\n            };\n            const data = await _lib_api__WEBPACK_IMPORTED_MODULE_11__.apiClient.register(userData);\n            console.log('Registration response:', data);\n            if (data.success) {\n                alert(\"Registration successful! Please check your email for verification.\");\n                // Clear form\n                setRegisterData({\n                    firstName: \"\",\n                    lastName: \"\",\n                    email: \"\",\n                    password: \"\",\n                    confirmPassword: \"\"\n                });\n                setAcceptTerms(false);\n            // Optionally redirect to login tab or dashboard\n            // router.push(\"/auth/login\")\n            } else {\n                console.error('Registration failed:', data);\n                alert(data.message || \"Registration failed. Please try again.\");\n                if (data.errors) {\n                    console.error(\"Validation errors:\", data.errors);\n                    // Display specific field errors\n                    const errorMessages = Object.values(data.errors).flat().join('\\n');\n                    alert(\"Validation errors:\\n\".concat(errorMessages));\n                }\n            }\n        } catch (error) {\n            console.error(\"Registration error:\", error);\n            alert(\"An unexpected error occurred. Please try again.\");\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const handleRegisterInputChange = (field, value)=>{\n        setRegisterData((prev)=>({\n                ...prev,\n                [field]: value\n            }));\n    };\n    // Get app name and logo from settings or use defaults\n    const appName = (settings === null || settings === void 0 ? void 0 : settings.app_name) || 'Laravel NGO Foundation';\n    const appLogo = settings === null || settings === void 0 ? void 0 : settings.app_logo;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col bg-gradient-to-br from-green-50 to-green-100 dark:from-green-950 dark:to-green-900\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n            className: \"flex-1 flex items-center justify-center py-12 px-4\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-full max-w-6xl grid grid-cols-1 lg:grid-cols-2 gap-12 items-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_9__.Card, {\n                        className: \"w-full max-w-md mx-auto shadow-2xl border-green-200 dark:border-green-800 rounded-3xl overflow-hidden backdrop-blur-sm bg-white/95 dark:bg-gray-900/95\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_10__.Tabs, {\n                                defaultValue: \"login\",\n                                className: \"w-full\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_10__.TabsList, {\n                                        className: \"grid w-full grid-cols-2 bg-green-100 dark:bg-green-900\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_10__.TabsTrigger, {\n                                                value: \"login\",\n                                                className: \"rounded-xl\",\n                                                children: \"Login\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                lineNumber: 200,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_10__.TabsTrigger, {\n                                                value: \"register\",\n                                                className: \"rounded-xl\",\n                                                children: \"Register\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                lineNumber: 203,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                        lineNumber: 199,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_10__.TabsContent, {\n                                        value: \"login\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_9__.CardHeader, {\n                                                className: \"text-center pb-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-center mb-4\",\n                                                        children: appLogo ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"relative h-16 w-16 overflow-hidden rounded-full shadow-lg animate-float\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                src: appLogo,\n                                                                alt: \"\".concat(appName, \" Logo\"),\n                                                                className: \"w-full h-full object-cover\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                                lineNumber: 213,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                            lineNumber: 212,\n                                                            columnNumber: 23\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"relative h-16 w-16 overflow-hidden rounded-full bg-gradient-to-br from-green-500 to-green-700 shadow-lg animate-float\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BookOpen_Eye_EyeOff_Facebook_GraduationCap_Heart_Lock_Mail_Twitter_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                className: \"absolute inset-0 m-auto h-8 w-8 text-white drop-shadow-md\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                                lineNumber: 221,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                            lineNumber: 220,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                        lineNumber: 210,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_9__.CardTitle, {\n                                                        className: \"text-2xl font-bold text-green-800 dark:text-green-200\",\n                                                        children: \"Welcome Back\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                        lineNumber: 225,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_9__.CardDescription, {\n                                                        className: \"text-green-600 dark:text-green-400\",\n                                                        children: [\n                                                            \"Sign in to continue your educational journey with \",\n                                                            appName\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                        lineNumber: 226,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                lineNumber: 209,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_9__.CardContent, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                                        onSubmit: handleLogin,\n                                                        className: \"space-y-6\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"space-y-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                                        htmlFor: \"email\",\n                                                                        className: \"text-green-800 dark:text-green-200\",\n                                                                        children: \"Email\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                                        lineNumber: 234,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"relative group\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BookOpen_Eye_EyeOff_Facebook_GraduationCap_Heart_Lock_Mail_Twitter_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                                className: \"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-green-500 group-hover:text-green-600 transition-colors duration-200\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                                                lineNumber: 238,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                                                id: \"email\",\n                                                                                type: \"email\",\n                                                                                placeholder: \"<EMAIL>\",\n                                                                                value: email,\n                                                                                onChange: (e)=>setEmail(e.target.value),\n                                                                                className: \"pl-10 rounded-xl border-green-200 dark:border-green-700 focus:border-green-500 dark:focus:border-green-400 transition-all duration-200 hover:border-green-300 dark:hover:border-green-600\",\n                                                                                required: true\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                                                lineNumber: 239,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                                        lineNumber: 237,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                                lineNumber: 233,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"space-y-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex justify-between items-center\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                                                htmlFor: \"password\",\n                                                                                className: \"text-green-800 dark:text-green-200\",\n                                                                                children: \"Password\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                                                lineNumber: 253,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                                                href: \"#\",\n                                                                                onClick: ()=>alert(\"Password reset instructions will be sent to your email\"),\n                                                                                className: \"text-xs text-green-600 hover:text-green-700 dark:text-green-400 dark:hover:text-green-300 transition-colors duration-200 hover:underline\",\n                                                                                children: \"Forgot password?\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                                                lineNumber: 256,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                                        lineNumber: 252,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"relative group\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BookOpen_Eye_EyeOff_Facebook_GraduationCap_Heart_Lock_Mail_Twitter_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                                className: \"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-green-500 group-hover:text-green-600 transition-colors duration-200\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                                                lineNumber: 265,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                                                id: \"password\",\n                                                                                type: showPassword ? \"text\" : \"password\",\n                                                                                placeholder: \"••••••••\",\n                                                                                value: password,\n                                                                                onChange: (e)=>setPassword(e.target.value),\n                                                                                className: \"pl-10 pr-10 rounded-xl border-green-200 dark:border-green-700 focus:border-green-500 dark:focus:border-green-400 transition-all duration-200 hover:border-green-300 dark:hover:border-green-600\",\n                                                                                required: true\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                                                lineNumber: 266,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            isClient && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                type: \"button\",\n                                                                                onClick: ()=>setShowPassword(!showPassword),\n                                                                                className: \"absolute right-3 top-1/2 transform -translate-y-1/2 text-green-500 hover:text-green-600 transition-colors duration-200\",\n                                                                                suppressHydrationWarning: true,\n                                                                                children: [\n                                                                                    showPassword ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BookOpen_Eye_EyeOff_Facebook_GraduationCap_Heart_Lock_Mail_Twitter_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                                        className: \"h-4 w-4\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                                                        lineNumber: 282,\n                                                                                        columnNumber: 45\n                                                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BookOpen_Eye_EyeOff_Facebook_GraduationCap_Heart_Lock_Mail_Twitter_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                                        className: \"h-4 w-4\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                                                        lineNumber: 282,\n                                                                                        columnNumber: 78\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"sr-only\",\n                                                                                        children: showPassword ? \"Hide password\" : \"Show password\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                                                        lineNumber: 283,\n                                                                                        columnNumber: 29\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                                                lineNumber: 276,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                                        lineNumber: 264,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                                lineNumber: 251,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            isClient && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-2\",\n                                                                suppressHydrationWarning: true,\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_checkbox__WEBPACK_IMPORTED_MODULE_8__.Checkbox, {\n                                                                        id: \"remember\",\n                                                                        checked: rememberMe,\n                                                                        onCheckedChange: (checked)=>setRememberMe(checked),\n                                                                        className: \"border-green-300 data-[state=checked]:bg-green-600 data-[state=checked]:border-green-600\",\n                                                                        suppressHydrationWarning: true\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                                        lineNumber: 291,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                                        htmlFor: \"remember\",\n                                                                        className: \"text-sm font-normal text-green-700 dark:text-green-300\",\n                                                                        children: \"Remember me for 30 days\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                                        lineNumber: 298,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                                lineNumber: 290,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                                type: \"submit\",\n                                                                className: \"w-full bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 text-white rounded-xl py-3 font-semibold transition-all duration-300 hover:scale-105 hover:shadow-lg\",\n                                                                disabled: isLoading,\n                                                                children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center gap-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                                            lineNumber: 311,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        \"Signing in...\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                                    lineNumber: 310,\n                                                                    columnNumber: 25\n                                                                }, this) : \"Sign In\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                                lineNumber: 304,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                        lineNumber: 232,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mt-6\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"relative\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"absolute inset-0 flex items-center\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"w-full border-t border-green-200 dark:border-green-700\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                                            lineNumber: 323,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                                        lineNumber: 322,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"relative flex justify-center text-xs uppercase\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"bg-white dark:bg-gray-900 px-2 text-green-600 dark:text-green-400\",\n                                                                            children: \"Or continue with\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                                            lineNumber: 326,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                                        lineNumber: 325,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                                lineNumber: 321,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            isClient && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"mt-4 grid grid-cols-2 gap-3\",\n                                                                suppressHydrationWarning: true,\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                                        variant: \"outline\",\n                                                                        className: \"rounded-xl border-green-200 dark:border-green-700 hover:bg-green-50 dark:hover:bg-green-900/20 transition-all duration-200 hover:scale-105\",\n                                                                        suppressHydrationWarning: true,\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BookOpen_Eye_EyeOff_Facebook_GraduationCap_Heart_Lock_Mail_Twitter_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                                className: \"mr-2 h-4 w-4 text-blue-600\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                                                lineNumber: 339,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            \"Facebook\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                                        lineNumber: 334,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                                        variant: \"outline\",\n                                                                        className: \"rounded-xl border-green-200 dark:border-green-700 hover:bg-green-50 dark:hover:bg-green-900/20 transition-all duration-200 hover:scale-105\",\n                                                                        suppressHydrationWarning: true,\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BookOpen_Eye_EyeOff_Facebook_GraduationCap_Heart_Lock_Mail_Twitter_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                                className: \"mr-2 h-4 w-4 text-blue-400\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                                                lineNumber: 347,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            \"Twitter\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                                        lineNumber: 342,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                                lineNumber: 333,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                        lineNumber: 320,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    isClient && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mt-6 text-center\",\n                                                        suppressHydrationWarning: true,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-green-600 dark:text-green-400 mb-3\",\n                                                                children: \"Having trouble signing in?\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                                lineNumber: 355,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"space-y-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                                        variant: \"ghost\",\n                                                                        className: \"w-full text-green-600 hover:text-green-700 dark:text-green-400 dark:hover:text-green-300\",\n                                                                        onClick: ()=>alert(\"Password reset link will be sent to your email\"),\n                                                                        suppressHydrationWarning: true,\n                                                                        children: \"Reset Password\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                                        lineNumber: 357,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                                        variant: \"ghost\",\n                                                                        className: \"w-full text-green-600 hover:text-green-700 dark:text-green-400 dark:hover:text-green-300\",\n                                                                        onClick: ()=>alert(\"Please contact support for account recovery\"),\n                                                                        suppressHydrationWarning: true,\n                                                                        children: \"Account Recovery\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                                        lineNumber: 365,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                                lineNumber: 356,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                        lineNumber: 354,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                lineNumber: 231,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                        lineNumber: 208,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_10__.TabsContent, {\n                                        value: \"register\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_9__.CardHeader, {\n                                                className: \"text-center pb-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-center mb-4\",\n                                                        children: appLogo ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"relative h-16 w-16 overflow-hidden rounded-full shadow-lg animate-float\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                src: appLogo,\n                                                                alt: \"\".concat(appName, \" Logo\"),\n                                                                className: \"w-full h-full object-cover\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                                lineNumber: 384,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                            lineNumber: 383,\n                                                            columnNumber: 23\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"relative h-16 w-16 overflow-hidden rounded-full bg-gradient-to-br from-green-500 to-green-700 shadow-lg animate-float\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BookOpen_Eye_EyeOff_Facebook_GraduationCap_Heart_Lock_Mail_Twitter_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                className: \"absolute inset-0 m-auto h-8 w-8 text-white drop-shadow-md\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                                lineNumber: 392,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                            lineNumber: 391,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                        lineNumber: 381,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_9__.CardTitle, {\n                                                        className: \"text-2xl font-bold text-green-800 dark:text-green-200\",\n                                                        children: [\n                                                            \"Join \",\n                                                            appName\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                        lineNumber: 396,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_9__.CardDescription, {\n                                                        className: \"text-green-600 dark:text-green-400\",\n                                                        children: \"Create an account to support education across Nigeria, helping students, underprivileged people, and those in need.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                        lineNumber: 399,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                lineNumber: 380,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_9__.CardContent, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                                    onSubmit: handleRegister,\n                                                    className: \"space-y-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"grid grid-cols-2 gap-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"space-y-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                                            htmlFor: \"first-name\",\n                                                                            className: \"text-green-800 dark:text-green-200\",\n                                                                            children: \"First Name *\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                                            lineNumber: 408,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                                            id: \"first-name\",\n                                                                            placeholder: \"John\",\n                                                                            value: registerData.firstName,\n                                                                            onChange: (e)=>handleRegisterInputChange('firstName', e.target.value),\n                                                                            className: \"rounded-xl border-green-200 dark:border-green-700 focus:border-green-500 dark:focus:border-green-400 transition-all duration-200\",\n                                                                            required: true\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                                            lineNumber: 411,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                                    lineNumber: 407,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"space-y-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                                            htmlFor: \"last-name\",\n                                                                            className: \"text-green-800 dark:text-green-200\",\n                                                                            children: \"Last Name *\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                                            lineNumber: 421,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                                            id: \"last-name\",\n                                                                            placeholder: \"Doe\",\n                                                                            value: registerData.lastName,\n                                                                            onChange: (e)=>handleRegisterInputChange('lastName', e.target.value),\n                                                                            className: \"rounded-xl border-green-200 dark:border-green-700 focus:border-green-500 dark:focus:border-green-400 transition-all duration-200\",\n                                                                            required: true\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                                            lineNumber: 424,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                                    lineNumber: 420,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                            lineNumber: 406,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                                    htmlFor: \"register-email\",\n                                                                    className: \"text-green-800 dark:text-green-200\",\n                                                                    children: \"Email *\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                                    lineNumber: 436,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"relative\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BookOpen_Eye_EyeOff_Facebook_GraduationCap_Heart_Lock_Mail_Twitter_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                            className: \"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-green-500\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                                            lineNumber: 440,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                                            id: \"register-email\",\n                                                                            type: \"email\",\n                                                                            placeholder: \"<EMAIL>\",\n                                                                            value: registerData.email,\n                                                                            onChange: (e)=>handleRegisterInputChange('email', e.target.value),\n                                                                            className: \"pl-10 rounded-xl border-green-200 dark:border-green-700 focus:border-green-500 dark:focus:border-green-400 transition-all duration-200\",\n                                                                            required: true\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                                            lineNumber: 441,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                                    lineNumber: 439,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                            lineNumber: 435,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                                    htmlFor: \"register-password\",\n                                                                    className: \"text-green-800 dark:text-green-200\",\n                                                                    children: \"Password * (min. 8 characters)\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                                    lineNumber: 454,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"relative\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BookOpen_Eye_EyeOff_Facebook_GraduationCap_Heart_Lock_Mail_Twitter_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                            className: \"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-green-500\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                                            lineNumber: 458,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                                            id: \"register-password\",\n                                                                            type: showPassword ? \"text\" : \"password\",\n                                                                            placeholder: \"••••••••\",\n                                                                            value: registerData.password,\n                                                                            onChange: (e)=>handleRegisterInputChange('password', e.target.value),\n                                                                            className: \"pl-10 pr-10 rounded-xl border-green-200 dark:border-green-700 focus:border-green-500 dark:focus:border-green-400 transition-all duration-200\",\n                                                                            required: true,\n                                                                            minLength: 8\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                                            lineNumber: 459,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        isClient && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            type: \"button\",\n                                                                            onClick: ()=>setShowPassword(!showPassword),\n                                                                            className: \"absolute right-3 top-1/2 transform -translate-y-1/2 text-green-500 hover:text-green-600 transition-colors duration-200\",\n                                                                            suppressHydrationWarning: true,\n                                                                            children: [\n                                                                                showPassword ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BookOpen_Eye_EyeOff_Facebook_GraduationCap_Heart_Lock_Mail_Twitter_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                                    className: \"h-4 w-4\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                                                    lineNumber: 476,\n                                                                                    columnNumber: 45\n                                                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BookOpen_Eye_EyeOff_Facebook_GraduationCap_Heart_Lock_Mail_Twitter_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                                    className: \"h-4 w-4\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                                                    lineNumber: 476,\n                                                                                    columnNumber: 78\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"sr-only\",\n                                                                                    children: showPassword ? \"Hide password\" : \"Show password\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                                                    lineNumber: 477,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                                            lineNumber: 470,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                                    lineNumber: 457,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                            lineNumber: 453,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                                    htmlFor: \"confirm-password\",\n                                                                    className: \"text-green-800 dark:text-green-200\",\n                                                                    children: \"Confirm Password *\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                                    lineNumber: 484,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"relative\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BookOpen_Eye_EyeOff_Facebook_GraduationCap_Heart_Lock_Mail_Twitter_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                            className: \"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-green-500\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                                            lineNumber: 488,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                                            id: \"confirm-password\",\n                                                                            type: showConfirmPassword ? \"text\" : \"password\",\n                                                                            placeholder: \"••••••••\",\n                                                                            value: registerData.confirmPassword,\n                                                                            onChange: (e)=>handleRegisterInputChange('confirmPassword', e.target.value),\n                                                                            className: \"pl-10 pr-10 rounded-xl border-green-200 dark:border-green-700 focus:border-green-500 dark:focus:border-green-400 transition-all duration-200\",\n                                                                            required: true\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                                            lineNumber: 489,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        isClient && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            type: \"button\",\n                                                                            onClick: ()=>setShowConfirmPassword(!showConfirmPassword),\n                                                                            className: \"absolute right-3 top-1/2 transform -translate-y-1/2 text-green-500 hover:text-green-600 transition-colors duration-200\",\n                                                                            suppressHydrationWarning: true,\n                                                                            children: [\n                                                                                showConfirmPassword ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BookOpen_Eye_EyeOff_Facebook_GraduationCap_Heart_Lock_Mail_Twitter_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                                    className: \"h-4 w-4\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                                                    lineNumber: 505,\n                                                                                    columnNumber: 52\n                                                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BookOpen_Eye_EyeOff_Facebook_GraduationCap_Heart_Lock_Mail_Twitter_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                                    className: \"h-4 w-4\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                                                    lineNumber: 505,\n                                                                                    columnNumber: 85\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"sr-only\",\n                                                                                    children: showConfirmPassword ? \"Hide password\" : \"Show password\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                                                    lineNumber: 506,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                                            lineNumber: 499,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                                    lineNumber: 487,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                            lineNumber: 483,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        isClient && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-2\",\n                                                            suppressHydrationWarning: true,\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_checkbox__WEBPACK_IMPORTED_MODULE_8__.Checkbox, {\n                                                                    id: \"terms\",\n                                                                    checked: acceptTerms,\n                                                                    onCheckedChange: (checked)=>setAcceptTerms(checked),\n                                                                    className: \"border-green-300 data-[state=checked]:bg-green-600 data-[state=checked]:border-green-600\",\n                                                                    suppressHydrationWarning: true,\n                                                                    required: true\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                                    lineNumber: 514,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                                    htmlFor: \"terms\",\n                                                                    className: \"text-sm font-normal text-green-700 dark:text-green-300\",\n                                                                    children: [\n                                                                        \"I agree to the\",\n                                                                        \" \",\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                                            href: \"/terms\",\n                                                                            className: \"text-green-600 hover:text-green-700 dark:text-green-400 dark:hover:text-green-300 underline\",\n                                                                            children: \"Terms of Service\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                                            lineNumber: 524,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        \" \",\n                                                                        \"and\",\n                                                                        \" \",\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                                            href: \"/privacy\",\n                                                                            className: \"text-green-600 hover:text-green-700 dark:text-green-400 dark:hover:text-green-300 underline\",\n                                                                            children: \"Privacy Policy\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                                            lineNumber: 531,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                                    lineNumber: 522,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                            lineNumber: 513,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                            type: \"submit\",\n                                                            className: \"w-full bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 text-white rounded-xl py-3 font-semibold transition-all duration-300 hover:scale-105 hover:shadow-lg\",\n                                                            disabled: isLoading,\n                                                            children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                                        lineNumber: 548,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    \"Creating Account...\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                                lineNumber: 547,\n                                                                columnNumber: 25\n                                                            }, this) : \"Create Account\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                            lineNumber: 541,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                    lineNumber: 405,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                lineNumber: 404,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                        lineNumber: 379,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                lineNumber: 198,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_9__.CardFooter, {\n                                className: \"flex justify-center pb-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/\",\n                                    className: \"flex items-center text-sm text-green-600 hover:text-green-700 dark:text-green-400 dark:hover:text-green-300 transition-colors duration-200 group\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BookOpen_Eye_EyeOff_Facebook_GraduationCap_Heart_Lock_Mail_Twitter_Users_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                            className: \"mr-2 h-4 w-4 group-hover:-translate-x-1 transition-transform duration-200\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                            lineNumber: 565,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"Back to home\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                    lineNumber: 561,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                lineNumber: 560,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                        lineNumber: 197,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"hidden lg:flex flex-col items-center justify-center space-y-8 text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute inset-0 bg-gradient-to-br from-green-500/30 to-green-700/30 rounded-full blur-3xl scale-150\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                        lineNumber: 575,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative h-48 w-48 flex items-center justify-center\",\n                                        children: appLogo ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative h-32 w-32 overflow-hidden rounded-full shadow-2xl animate-float\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                src: appLogo,\n                                                alt: \"\".concat(appName, \" Logo\"),\n                                                className: \"w-full h-full object-cover\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                lineNumber: 579,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                            lineNumber: 578,\n                                            columnNumber: 19\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative h-32 w-32 overflow-hidden rounded-full bg-gradient-to-br from-green-500 to-green-700 shadow-2xl animate-float\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BookOpen_Eye_EyeOff_Facebook_GraduationCap_Heart_Lock_Mail_Twitter_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                className: \"absolute inset-0 m-auto h-16 w-16 text-white drop-shadow-lg\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                lineNumber: 587,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                            lineNumber: 586,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                        lineNumber: 576,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                lineNumber: 574,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"max-w-md space-y-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-4xl font-bold text-green-800 dark:text-green-200\",\n                                        children: appName\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                        lineNumber: 595,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-lg text-green-700 dark:text-green-300 leading-relaxed\",\n                                        children: (settings === null || settings === void 0 ? void 0 : settings.site_description) || \"Join our mission to empower Nigeria through education. By creating an account, you'll be able to track your donations, apply for scholarships, and stay updated on our impact as we support students, underprivileged people, and those in need across the nation.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                        lineNumber: 598,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 gap-4 pt-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-3 p-3 bg-white/50 dark:bg-gray-800/50 rounded-xl backdrop-blur-sm border border-green-200 dark:border-green-700\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-10 h-10 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BookOpen_Eye_EyeOff_Facebook_GraduationCap_Heart_Lock_Mail_Twitter_Users_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                            className: \"h-5 w-5 text-green-600 dark:text-green-400\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                            lineNumber: 606,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                        lineNumber: 605,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-left\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"font-semibold text-green-800 dark:text-green-200\",\n                                                                children: \"Scholarship Applications\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                                lineNumber: 609,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-sm text-green-600 dark:text-green-400\",\n                                                                children: \"Apply for educational grants\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                                lineNumber: 610,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                        lineNumber: 608,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                lineNumber: 604,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-3 p-3 bg-white/50 dark:bg-gray-800/50 rounded-xl backdrop-blur-sm border border-green-200 dark:border-green-700\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-10 h-10 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BookOpen_Eye_EyeOff_Facebook_GraduationCap_Heart_Lock_Mail_Twitter_Users_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                            className: \"h-5 w-5 text-green-600 dark:text-green-400\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                            lineNumber: 616,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                        lineNumber: 615,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-left\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"font-semibold text-green-800 dark:text-green-200\",\n                                                                children: \"Impact Tracking\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                                lineNumber: 619,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-sm text-green-600 dark:text-green-400\",\n                                                                children: \"See your donation impact\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                                lineNumber: 620,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                        lineNumber: 618,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                lineNumber: 614,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-3 p-3 bg-white/50 dark:bg-gray-800/50 rounded-xl backdrop-blur-sm border border-green-200 dark:border-green-700\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-10 h-10 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BookOpen_Eye_EyeOff_Facebook_GraduationCap_Heart_Lock_Mail_Twitter_Users_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                            className: \"h-5 w-5 text-green-600 dark:text-green-400\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                            lineNumber: 626,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                        lineNumber: 625,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-left\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"font-semibold text-green-800 dark:text-green-200\",\n                                                                children: \"Community Access\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                                lineNumber: 629,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-sm text-green-600 dark:text-green-400\",\n                                                                children: \"Connect with beneficiaries\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                                lineNumber: 630,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                        lineNumber: 628,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                lineNumber: 624,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                        lineNumber: 603,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"pt-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex -space-x-2 overflow-hidden justify-center mb-3\",\n                                                children: [\n                                                    1,\n                                                    2,\n                                                    3,\n                                                    4,\n                                                    5\n                                                ].map((i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"inline-block h-12 w-12 rounded-full border-2 border-white dark:border-gray-800 overflow-hidden shadow-lg\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                            src: \"/placeholder.svg?height=100&width=100&text=User\".concat(i),\n                                                            alt: \"Community member \".concat(i),\n                                                            width: 48,\n                                                            height: 48,\n                                                            className: \"h-full w-full object-cover\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                            lineNumber: 643,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, i, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                        lineNumber: 639,\n                                                        columnNumber: 21\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                lineNumber: 637,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-green-600 dark:text-green-400\",\n                                                children: [\n                                                    \"Join over \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-bold text-green-700 dark:text-green-300\",\n                                                        children: \"1,200+\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                        lineNumber: 654,\n                                                        columnNumber: 29\n                                                    }, this),\n                                                    \" students and supporters\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                lineNumber: 653,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                        lineNumber: 636,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                lineNumber: 594,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                        lineNumber: 572,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                lineNumber: 195,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\login\\\\page.tsx\",\n            lineNumber: 194,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\auth\\\\login\\\\page.tsx\",\n        lineNumber: 192,\n        columnNumber: 5\n    }, this);\n}\n_s(LoginPage, \"nNvucwnUOFf5CYHid2zkSJpstQ4=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_4__.useRouter,\n        _hooks_useSettings__WEBPACK_IMPORTED_MODULE_12__.useSettings\n    ];\n});\n_c = LoginPage;\nvar _c;\n$RefreshReg$(_c, \"LoginPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/auth/login/page.tsx\n"));

/***/ })

});