<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Student;
use App\Models\PartnerOrganization;
use App\Models\StudentProgression;
use Carbon\Carbon;

class StudentSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get partner organizations
        $primarySchools = PartnerOrganization::where('type', 'primary_school')->get();
        $secondarySchools = PartnerOrganization::where('type', 'secondary_school')->get();

        // Sample student data for primary schools
        $primaryStudents = [
            [
                'first_name' => 'Adebayo',
                'last_name' => 'Johnson',
                'middle_name' => 'Olumide',
                'date_of_birth' => '2015-03-15',
                'gender' => 'male',
                'phone_number' => '+234-************',
                'address' => '12 Johnson Street, Ikeja',
                'city' => 'Lagos',
                'state' => 'Lagos',
                'current_grade' => 'primary_3',
                'academic_year' => '2024/2025',
                'student_type' => 'primary',
                'guardian_name' => 'Mr. <PERSON><PERSON>',
                'guardian_phone' => '+234-************',
                'guardian_email' => '<EMAIL>',
                'guardian_relationship' => 'father',
                'status' => 'active',
                'enrollment_date' => '2022-09-01',
            ],
            [
                'first_name' => 'Fatima',
                'last_name' => 'Abdullahi',
                'date_of_birth' => '2014-07-22',
                'gender' => 'female',
                'phone_number' => '+234-************',
                'address' => '45 Ahmadu Bello Way, Kano',
                'city' => 'Kano',
                'state' => 'Kano',
                'current_grade' => 'primary_4',
                'academic_year' => '2024/2025',
                'student_type' => 'primary',
                'guardian_name' => 'Hajiya Aisha Abdullahi',
                'guardian_phone' => '+234-************',
                'guardian_email' => '<EMAIL>',
                'guardian_relationship' => 'mother',
                'status' => 'active',
                'enrollment_date' => '2021-09-01',
            ],
            [
                'first_name' => 'Chioma',
                'last_name' => 'Okafor',
                'middle_name' => 'Grace',
                'date_of_birth' => '2013-11-08',
                'gender' => 'female',
                'address' => '78 Independence Layout, Enugu',
                'city' => 'Enugu',
                'state' => 'Enugu',
                'current_grade' => 'primary_5',
                'academic_year' => '2024/2025',
                'student_type' => 'primary',
                'guardian_name' => 'Mrs. Ngozi Okafor',
                'guardian_phone' => '+234-************',
                'guardian_email' => '<EMAIL>',
                'guardian_relationship' => 'mother',
                'status' => 'active',
                'enrollment_date' => '2020-09-01',
            ],
        ];

        // Sample student data for secondary schools
        $secondaryStudents = [
            [
                'first_name' => 'Emmanuel',
                'last_name' => 'Okonkwo',
                'middle_name' => 'Chukwu',
                'date_of_birth' => '2009-05-12',
                'gender' => 'male',
                'phone_number' => '+234-************',
                'email' => '<EMAIL>',
                'address' => '23 New Haven, Enugu',
                'city' => 'Enugu',
                'state' => 'Enugu',
                'current_grade' => 'secondary_3',
                'academic_year' => '2024/2025',
                'student_type' => 'secondary',
                'guardian_name' => 'Chief Emeka Okonkwo',
                'guardian_phone' => '+234-************',
                'guardian_email' => '<EMAIL>',
                'guardian_relationship' => 'father',
                'status' => 'active',
                'enrollment_date' => '2022-09-01',
            ],
            [
                'first_name' => 'Blessing',
                'last_name' => 'Okoro',
                'date_of_birth' => '2008-09-30',
                'gender' => 'female',
                'phone_number' => '+234-************',
                'email' => '<EMAIL>',
                'address' => '67 Trans Amadi, Port Harcourt',
                'city' => 'Port Harcourt',
                'state' => 'Rivers',
                'current_grade' => 'secondary_4',
                'academic_year' => '2024/2025',
                'student_type' => 'secondary',
                'guardian_name' => 'Mrs. Joy Okoro',
                'guardian_phone' => '+234-************',
                'guardian_email' => '<EMAIL>',
                'guardian_relationship' => 'mother',
                'status' => 'active',
                'enrollment_date' => '2021-09-01',
            ],
            [
                'first_name' => 'Abdulrahman',
                'last_name' => 'Bello',
                'date_of_birth' => '2007-12-03',
                'gender' => 'male',
                'phone_number' => '+234-************',
                'email' => '<EMAIL>',
                'address' => '89 Garki Area, Abuja',
                'city' => 'Abuja',
                'state' => 'FCT',
                'current_grade' => 'secondary_5',
                'academic_year' => '2024/2025',
                'student_type' => 'secondary',
                'guardian_name' => 'Alhaji Musa Bello',
                'guardian_phone' => '+234-************',
                'guardian_email' => '<EMAIL>',
                'guardian_relationship' => 'father',
                'status' => 'active',
                'enrollment_date' => '2020-09-01',
            ],
        ];

        // Create primary school students
        foreach ($primaryStudents as $index => $studentData) {
            $school = $primarySchools->get($index % $primarySchools->count());
            $studentData['school_id'] = $school->id;
            
            $student = Student::create($studentData);

            // Create school-student relationship
            $student->schools()->attach($school->id, [
                'enrollment_date' => $studentData['enrollment_date'],
                'status' => 'active',
                'enrollment_grade' => 'primary_1', // Assuming they started at primary 1
                'current_grade' => $studentData['current_grade'],
                'academic_year' => $studentData['academic_year'],
            ]);

            // Create some progression records
            $this->createProgressionRecords($student, $school);
        }

        // Create secondary school students
        foreach ($secondaryStudents as $index => $studentData) {
            $school = $secondarySchools->get($index % $secondarySchools->count());
            $studentData['school_id'] = $school->id;
            
            $student = Student::create($studentData);

            // Create school-student relationship
            $student->schools()->attach($school->id, [
                'enrollment_date' => $studentData['enrollment_date'],
                'status' => 'active',
                'enrollment_grade' => 'secondary_1', // Assuming they started at secondary 1
                'current_grade' => $studentData['current_grade'],
                'academic_year' => $studentData['academic_year'],
            ]);

            // Create some progression records
            $this->createProgressionRecords($student, $school);
        }

        // Create some additional students for variety
        $this->createAdditionalStudents($primarySchools, $secondarySchools);
    }

    /**
     * Create progression records for a student
     */
    private function createProgressionRecords(Student $student, PartnerOrganization $school)
    {
        $currentGrade = $student->current_grade;
        $gradeNumber = (int) substr($currentGrade, -1);
        $gradeType = str_replace('_' . $gradeNumber, '', $currentGrade);

        // Create progression records for previous grades
        for ($i = 1; $i < $gradeNumber; $i++) {
            $fromGrade = $gradeType . '_' . $i;
            $toGrade = $gradeType . '_' . ($i + 1);
            $academicYear = (2020 + $i) . '/' . (2021 + $i);

            StudentProgression::create([
                'student_id' => $student->id,
                'from_grade' => $fromGrade,
                'to_grade' => $toGrade,
                'from_academic_year' => $academicYear,
                'to_academic_year' => (2021 + $i) . '/' . (2022 + $i),
                'status' => 'completed',
                'progression_date' => Carbon::create(2021 + $i, 7, 15),
                'final_grade' => rand(70, 95),
                'approved_by' => 1, // Assuming admin user exists
                'approved_at' => Carbon::create(2021 + $i, 7, 20),
            ]);
        }
    }

    /**
     * Create additional students for variety
     */
    private function createAdditionalStudents($primarySchools, $secondarySchools)
    {
        // Create more primary students
        for ($i = 0; $i < 10; $i++) {
            $school = $primarySchools->random();
            $grade = 'primary_' . rand(1, 6);
            
            Student::create([
                'first_name' => 'Student' . ($i + 10),
                'last_name' => 'Primary',
                'date_of_birth' => Carbon::now()->subYears(rand(6, 12)),
                'gender' => rand(0, 1) ? 'male' : 'female',
                'address' => 'Address ' . ($i + 10),
                'city' => $school->city,
                'state' => $school->state,
                'current_grade' => $grade,
                'academic_year' => '2024/2025',
                'school_id' => $school->id,
                'student_type' => 'primary',
                'guardian_name' => 'Guardian ' . ($i + 10),
                'guardian_phone' => '+234-80' . rand(1, 9) . '-' . rand(100, 999) . '-' . rand(1000, 9999),
                'guardian_email' => 'guardian' . ($i + 10) . '@email.com',
                'guardian_relationship' => rand(0, 1) ? 'father' : 'mother',
                'status' => 'active',
                'enrollment_date' => Carbon::now()->subYears(rand(1, 5)),
            ]);
        }

        // Create more secondary students
        for ($i = 0; $i < 10; $i++) {
            $school = $secondarySchools->random();
            $grade = 'secondary_' . rand(1, 6);
            
            Student::create([
                'first_name' => 'Student' . ($i + 20),
                'last_name' => 'Secondary',
                'date_of_birth' => Carbon::now()->subYears(rand(12, 18)),
                'gender' => rand(0, 1) ? 'male' : 'female',
                'email' => 'student' . ($i + 20) . '@student.email.com',
                'address' => 'Address ' . ($i + 20),
                'city' => $school->city,
                'state' => $school->state,
                'current_grade' => $grade,
                'academic_year' => '2024/2025',
                'school_id' => $school->id,
                'student_type' => 'secondary',
                'guardian_name' => 'Guardian ' . ($i + 20),
                'guardian_phone' => '+234-80' . rand(1, 9) . '-' . rand(100, 999) . '-' . rand(1000, 9999),
                'guardian_email' => 'guardian' . ($i + 20) . '@email.com',
                'guardian_relationship' => rand(0, 1) ? 'father' : 'mother',
                'status' => 'active',
                'enrollment_date' => Carbon::now()->subYears(rand(1, 5)),
            ]);
        }
    }
}
