"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/scholarships/[id]/page",{

/***/ "(app-pages-browser)/./lib/api.ts":
/*!********************!*\
  !*** ./lib/api.ts ***!
  \********************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ApiClient: () => (/* binding */ ApiClient),\n/* harmony export */   apiClient: () => (/* binding */ apiClient),\n/* harmony export */   extractArrayData: () => (/* binding */ extractArrayData),\n/* harmony export */   getCurrentUser: () => (/* binding */ getCurrentUser),\n/* harmony export */   isAuthenticated: () => (/* binding */ isAuthenticated),\n/* harmony export */   logout: () => (/* binding */ logout)\n/* harmony export */ });\nconst API_BASE_URL = \"http://localhost:8000/api/v1\" || 0;\n// Utility function to extract array data from paginated or direct responses\nconst extractArrayData = (response)=>{\n    if (!response || !response.data) return [];\n    // If data is already an array, return it\n    if (Array.isArray(response.data)) {\n        return response.data;\n    }\n    // If data has a data property (paginated response), return that array\n    if (response.data.data && Array.isArray(response.data.data)) {\n        return response.data.data;\n    }\n    // Default to empty array\n    return [];\n};\nclass ApiClient {\n    async request(endpoint) {\n        let options = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};\n        // Use the base URL directly if it already contains /api/v1, otherwise add it\n        const url = this.baseURL.includes('/api/v1') ? \"\".concat(this.baseURL).concat(endpoint) : \"\".concat(this.baseURL, \"/api/v1\").concat(endpoint);\n        const config = {\n            headers: {\n                'Content-Type': 'application/json',\n                'Accept': 'application/json',\n                ...options.headers\n            },\n            ...options\n        };\n        // Add authentication header if token exists\n        const token =  true ? localStorage.getItem('authToken') : 0;\n        if (token) {\n            config.headers = {\n                ...config.headers,\n                'Authorization': \"Bearer \".concat(token)\n            };\n        }\n        try {\n            console.log(\"API Request: \".concat(options.method || 'GET', \" \").concat(url)) // Debug logging\n            ;\n            const response = await fetch(url, config);\n            const data = await response.json();\n            console.log(\"API Response for \".concat(endpoint, \":\"), data) // Debug logging\n            ;\n            // Handle 401 Unauthorized - redirect to login\n            if (response.status === 401) {\n                if (true) {\n                    localStorage.removeItem('authToken');\n                    localStorage.removeItem('user');\n                    window.location.href = '/auth/login';\n                }\n            }\n            return data;\n        } catch (error) {\n            console.error('API request failed:', error);\n            throw error;\n        }\n    }\n    // Auth endpoints\n    async login(email, password) {\n        var _response_data;\n        const response = await this.request('/login', {\n            method: 'POST',\n            body: JSON.stringify({\n                email,\n                password\n            })\n        });\n        // Store token and user data if login successful\n        if (response.success && ((_response_data = response.data) === null || _response_data === void 0 ? void 0 : _response_data.access_token)) {\n            if (true) {\n                localStorage.setItem('authToken', response.data.access_token);\n                localStorage.setItem('user', JSON.stringify(response.data.user));\n            }\n        }\n        return response;\n    }\n    async register(userData) {\n        var _response_data;\n        // First, register the basic user\n        const response = await this.request('/register', {\n            method: 'POST',\n            body: JSON.stringify({\n                first_name: userData.first_name,\n                last_name: userData.last_name,\n                email: userData.email,\n                password: userData.password,\n                password_confirmation: userData.password_confirmation,\n                phone_number: userData.phone_number,\n                address: userData.address,\n                date_of_birth: userData.date_of_birth,\n                city: userData.city,\n                state: userData.state,\n                country: userData.country\n            })\n        });\n        // Store token and user data if registration successful\n        if (response.success && ((_response_data = response.data) === null || _response_data === void 0 ? void 0 : _response_data.token)) {\n            if (true) {\n                localStorage.setItem('authToken', response.data.token);\n                localStorage.setItem('user', JSON.stringify(response.data.user));\n            }\n        }\n        // If registration is successful, handle user type specific data\n        if (response.success && userData.user_type && userData.additional_data) {\n            try {\n                if (userData.user_type === 'volunteer') {\n                    // Submit volunteer application\n                    await this.applyAsVolunteer(userData.additional_data);\n                } else if (userData.user_type === 'student' || userData.user_type === 'partner') {\n                    // Store additional data in user preferences\n                    const preferences = {\n                        user_type: userData.user_type,\n                        profile_data: userData.additional_data,\n                        profile_completed: true\n                    };\n                    await this.updateUserPreferences(preferences);\n                }\n            } catch (error) {\n                console.error('Additional data submission failed:', error);\n                // Return the user registration success but note additional data failed\n                return {\n                    ...response,\n                    message: response.message + ' However, additional profile information could not be saved. You can complete your profile later.'\n                };\n            }\n        }\n        return response;\n    }\n    async logout() {\n        const response = await this.request('/logout', {\n            method: 'POST'\n        });\n        // Clear stored token and user data on logout\n        if (true) {\n            localStorage.removeItem('authToken');\n            localStorage.removeItem('user');\n        }\n        return response;\n    }\n    async getUser() {\n        return this.request('/user');\n    }\n    async forgotPassword(email) {\n        return this.request('/forgot-password', {\n            method: 'POST',\n            body: JSON.stringify({\n                email\n            })\n        });\n    }\n    // Profile endpoints\n    async getProfile() {\n        return this.request('/profile');\n    }\n    async updateProfile(profileData) {\n        return this.request('/profile', {\n            method: 'PUT',\n            body: JSON.stringify(profileData)\n        });\n    }\n    async updateUserPreferences(preferences) {\n        return this.request('/profile', {\n            method: 'PUT',\n            body: JSON.stringify({\n                preferences\n            })\n        });\n    }\n    async uploadAvatar(file) {\n        const formData = new FormData();\n        formData.append('avatar', file);\n        // Use the base URL directly if it already contains /api/v1, otherwise add it\n        const url = this.baseURL.includes('/api/v1') ? \"\".concat(this.baseURL, \"/profile/avatar\") : \"\".concat(this.baseURL, \"/api/v1/profile/avatar\");\n        const token =  true ? localStorage.getItem('authToken') : 0;\n        try {\n            const response = await fetch(url, {\n                method: 'POST',\n                headers: {\n                    'Authorization': token ? \"Bearer \".concat(token) : '',\n                    'Accept': 'application/json'\n                },\n                body: formData\n            });\n            const data = await response.json();\n            if (response.status === 401) {\n                if (true) {\n                    localStorage.removeItem('authToken');\n                    localStorage.removeItem('user');\n                    window.location.href = '/auth/login';\n                }\n            }\n            return data;\n        } catch (error) {\n            console.error('Avatar upload failed:', error);\n            throw error;\n        }\n    }\n    async changePassword(passwordData) {\n        return this.request('/profile/password', {\n            method: 'PUT',\n            body: JSON.stringify(passwordData)\n        });\n    }\n    async generateQrCode() {\n        return this.request('/profile/generate-qr', {\n            method: 'POST'\n        });\n    }\n    async getIdCard() {\n        return this.request('/profile/id-card');\n    }\n    // Dashboard endpoints\n    async getDashboardSummary() {\n        return this.request('/dashboard/summary');\n    }\n    // Volunteer endpoints\n    async getVolunteerApplication() {\n        return this.request('/volunteer/application');\n    }\n    async getVolunteerHours() {\n        return this.request('/volunteer/hours');\n    }\n    async getVolunteerOpportunities() {\n        return this.request('/volunteer/opportunities');\n    }\n    async logVolunteerHours(hoursData) {\n        return this.request('/volunteer/hours', {\n            method: 'POST',\n            body: JSON.stringify(hoursData)\n        });\n    }\n    // Scholarship endpoints\n    async getMyScholarshipApplications() {\n        return this.request('/scholarships/my-applications');\n    }\n    async getScholarships() {\n        return this.request('/scholarships');\n    }\n    async applyForScholarship(scholarshipId, applicationData) {\n        return this.request(\"/scholarships/\".concat(scholarshipId, \"/apply\"), {\n            method: 'POST',\n            body: JSON.stringify(applicationData)\n        });\n    }\n    // Event endpoints\n    async getMyEventRegistrations() {\n        return this.request('/events/my-registrations');\n    }\n    async getUpcomingEvents() {\n        return this.request('/events/upcoming');\n    }\n    // Helper method for building query strings\n    buildQueryString(params) {\n        const searchParams = new URLSearchParams();\n        Object.entries(params).forEach((param)=>{\n            let [key, value] = param;\n            if (value !== undefined && value !== null && value !== '') {\n                if (Array.isArray(value)) {\n                    value.forEach((item)=>searchParams.append(\"\".concat(key, \"[]\"), item));\n                } else {\n                    searchParams.append(key, value.toString());\n                }\n            }\n        });\n        return searchParams.toString();\n    }\n    // Admin Dashboard endpoints\n    async getAdminDashboard() {\n        return this.request('/admin/dashboard');\n    }\n    async getAdminAnalytics(params) {\n        const queryString = params ? \"?\".concat(this.buildQueryString(params)) : '';\n        return this.request(\"/admin/dashboard/analytics\".concat(queryString));\n    }\n    async getAdminStats(params) {\n        const queryString = params ? \"?\".concat(this.buildQueryString(params)) : '';\n        return this.request(\"/admin/dashboard/stats\".concat(queryString));\n    }\n    // Admin User Management\n    async getAdminUsers(params) {\n        const queryString = params ? \"?\".concat(this.buildQueryString(params)) : '';\n        return this.request(\"/admin/users\".concat(queryString));\n    }\n    async createAdminUser(userData) {\n        return this.request('/admin/users', {\n            method: 'POST',\n            body: JSON.stringify(userData)\n        });\n    }\n    async updateAdminUser(userId, userData) {\n        return this.request(\"/admin/users/\".concat(userId), {\n            method: 'PUT',\n            body: JSON.stringify(userData)\n        });\n    }\n    async deleteAdminUser(userId) {\n        return this.request(\"/admin/users/\".concat(userId), {\n            method: 'DELETE'\n        });\n    }\n    async bulkActionUsers(data) {\n        return this.request('/admin/users/bulk-action', {\n            method: 'POST',\n            body: JSON.stringify(data)\n        });\n    }\n    async exportUsers(params) {\n        const queryString = params ? \"?\".concat(this.buildQueryString(params)) : '';\n        return this.request(\"/admin/users/export\".concat(queryString));\n    }\n    // Admin Scholarship Management\n    async getScholarshipApplications(params) {\n        const queryString = params ? \"?\".concat(this.buildQueryString(params)) : '';\n        return this.request(\"/admin/scholarship-applications\".concat(queryString));\n    }\n    async reviewScholarshipApplication(applicationId, reviewData) {\n        return this.request(\"/admin/scholarship-applications/\".concat(applicationId, \"/review\"), {\n            method: 'PUT',\n            body: JSON.stringify(reviewData)\n        });\n    }\n    async bulkActionScholarshipApplications(data) {\n        return this.request('/admin/scholarship-applications/bulk-action', {\n            method: 'POST',\n            body: JSON.stringify(data)\n        });\n    }\n    async exportScholarshipApplications(params) {\n        const queryString = params ? \"?\".concat(this.buildQueryString(params)) : '';\n        return this.request(\"/admin/scholarship-applications/export\".concat(queryString));\n    }\n    async getScholarshipStatistics() {\n        return this.request('/admin/scholarships/statistics');\n    }\n    // Admin Event Management\n    async getAdminEvents(params) {\n        const queryString = params ? \"?\".concat(this.buildQueryString(params)) : '';\n        return this.request(\"/admin/events\".concat(queryString));\n    }\n    async createAdminEvent(eventData) {\n        return this.request('/admin/events', {\n            method: 'POST',\n            body: JSON.stringify(eventData)\n        });\n    }\n    async updateAdminEvent(eventId, eventData) {\n        return this.request(\"/admin/events/\".concat(eventId), {\n            method: 'PUT',\n            body: JSON.stringify(eventData)\n        });\n    }\n    async deleteAdminEvent(eventId) {\n        return this.request(\"/admin/events/\".concat(eventId), {\n            method: 'DELETE'\n        });\n    }\n    async bulkActionEvents(data) {\n        return this.request('/admin/events/bulk-action', {\n            method: 'POST',\n            body: JSON.stringify(data)\n        });\n    }\n    async exportEvents(params) {\n        const queryString = params ? \"?\".concat(this.buildQueryString(params)) : '';\n        return this.request(\"/admin/events/export\".concat(queryString));\n    }\n    async getEventStatistics() {\n        return this.request('/admin/events/statistics');\n    }\n    // Admin Program Management\n    async getAdminPrograms(params) {\n        const queryString = params ? \"?\".concat(this.buildQueryString(params)) : '';\n        return this.request(\"/admin/programs\".concat(queryString));\n    }\n    async createAdminProgram(programData) {\n        return this.request('/admin/programs', {\n            method: 'POST',\n            body: JSON.stringify(programData)\n        });\n    }\n    async updateAdminProgram(programId, programData) {\n        return this.request(\"/admin/programs/\".concat(programId), {\n            method: 'PUT',\n            body: JSON.stringify(programData)\n        });\n    }\n    async deleteAdminProgram(programId) {\n        return this.request(\"/admin/programs/\".concat(programId), {\n            method: 'DELETE'\n        });\n    }\n    // Admin Blog Management\n    async getAdminBlogPosts(params) {\n        const queryString = params ? \"?\".concat(this.buildQueryString(params)) : '';\n        return this.request(\"/admin/blog/posts\".concat(queryString));\n    }\n    async createAdminBlogPost(postData) {\n        return this.request('/admin/blog/posts', {\n            method: 'POST',\n            body: JSON.stringify(postData)\n        });\n    }\n    async updateAdminBlogPost(postId, postData) {\n        return this.request(\"/admin/blog/posts/\".concat(postId), {\n            method: 'PUT',\n            body: JSON.stringify(postData)\n        });\n    }\n    async deleteAdminBlogPost(postId) {\n        return this.request(\"/admin/blog/posts/\".concat(postId), {\n            method: 'DELETE'\n        });\n    }\n    async exportBlogPosts(params) {\n        const queryString = params ? \"?\".concat(this.buildQueryString(params)) : '';\n        return this.request(\"/admin/blog/posts/export\".concat(queryString));\n    }\n    // Donation endpoints\n    async getMyDonations() {\n        return this.request('/donations/my-donations');\n    }\n    async getDonationCampaigns() {\n        return this.request('/donations/campaigns');\n    }\n    // Blog endpoints\n    async getBlogPosts() {\n        let page = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 1;\n        return this.request(\"/blog/posts?page=\".concat(page));\n    }\n    async getBlogPost(slug) {\n        return this.request(\"/blog/posts/\".concat(slug));\n    }\n    // Events endpoints\n    async getEvents() {\n        let page = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 1;\n        return this.request(\"/events?page=\".concat(page));\n    }\n    async getEvent(id) {\n        return this.request(\"/events/\".concat(id));\n    }\n    async registerForEvent(eventId, additionalInfo) {\n        return this.request(\"/events/\".concat(eventId, \"/register\"), {\n            method: 'POST',\n            body: JSON.stringify({\n                additional_info: additionalInfo\n            })\n        });\n    }\n    // Programs endpoints\n    async getPrograms() {\n        let page = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 1;\n        return this.request(\"/programs?page=\".concat(page));\n    }\n    async getProgram(slug) {\n        return this.request(\"/programs/\".concat(slug));\n    }\n    // Additional scholarship endpoint\n    async getScholarship(id) {\n        return this.request(\"/scholarships/\".concat(id));\n    }\n    // Public scholarships endpoints (no authentication required)\n    async getPublicScholarships(params) {\n        const queryString = params ? \"?\".concat(this.buildQueryString(params)) : '';\n        return this.request(\"/public-scholarships\".concat(queryString));\n    }\n    async getPublicScholarship(id) {\n        return this.request(\"/public-scholarships/\".concat(id));\n    }\n    async submitScholarshipApplication(scholarshipId, formData) {\n        const token =  true ? localStorage.getItem('authToken') : 0;\n        const url = \"\".concat(this.baseURL, \"/api/v1/apply-scholarship/\").concat(scholarshipId);\n        try {\n            const response = await fetch(url, {\n                method: 'POST',\n                headers: {\n                    'Authorization': token ? \"Bearer \".concat(token) : '',\n                    'Accept': 'application/json'\n                },\n                body: formData\n            });\n            const data = await response.json();\n            console.log(\"API Response for scholarship application:\", data);\n            if (!response.ok) {\n                throw new Error(data.message || \"HTTP error! status: \".concat(response.status));\n            }\n            return data;\n        } catch (error) {\n            console.error('Error submitting scholarship application:', error);\n            throw error;\n        }\n    }\n    async applyAsVolunteer(applicationData) {\n        return this.request('/volunteer/apply', {\n            method: 'POST',\n            body: JSON.stringify(applicationData)\n        });\n    }\n    // Contact endpoints\n    async submitContactForm(contactData) {\n        return this.request('/contact', {\n            method: 'POST',\n            body: JSON.stringify(contactData)\n        });\n    }\n    // Newsletter endpoints\n    async subscribeToNewsletter(email) {\n        return this.request('/newsletter/subscribe', {\n            method: 'POST',\n            body: JSON.stringify({\n                email\n            })\n        });\n    }\n    // Settings endpoint\n    async getSettings() {\n        return this.request('/settings');\n    }\n    constructor(baseURL = API_BASE_URL !== null && API_BASE_URL !== void 0 ? API_BASE_URL : \"\"){\n        this.baseURL = baseURL;\n    }\n}\n// Create and export a default instance\nconst apiClient = new ApiClient();\n// Export the class for custom instances\n\n// Export the extractArrayData utility function\n\n// Helper function to check if user is authenticated\nconst isAuthenticated = ()=>{\n    if (false) {}\n    return !!localStorage.getItem('authToken');\n};\n// Helper function to get current user\nconst getCurrentUser = ()=>{\n    if (false) {}\n    const userStr = localStorage.getItem('user');\n    return userStr ? JSON.parse(userStr) : null;\n};\n// Helper function to logout\nconst logout = async ()=>{\n    try {\n        await apiClient.logout();\n    } catch (error) {\n        console.error('Logout error:', error);\n    } finally{\n        if (true) {\n            localStorage.removeItem('authToken');\n            localStorage.removeItem('user');\n            window.location.href = '/auth/login';\n        }\n    }\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/api.ts\n"));

/***/ })

});