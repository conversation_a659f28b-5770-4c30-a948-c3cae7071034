(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4859],{4659:(e,r,s)=>{Promise.resolve().then(s.bind(s,9598))},9598:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>P});var a=s(95155),t=s(12115),n=s(6874),l=s.n(n),o=s(66766),i=s(35695),d=s(97168),c=s(89852),g=s(82714),m=s(95139),u=s(88482),x=s(34964),h=s(51976),f=s(28883),p=s(32919),b=s(78749),v=s(92657),N=s(10488),j=s(18175),w=s(35169),y=s(87949),k=s(5040),C=s(17580),A=s(31886),S=s(44531);function P(){let e=(0,i.useRouter)(),{settings:r,loading:s}=(0,S.t)(),[n,P]=(0,t.useState)(!1),[R,H]=(0,t.useState)(!1),[_,L]=(0,t.useState)(!1),[W,F]=(0,t.useState)(""),[J,B]=(0,t.useState)(""),[E,T]=(0,t.useState)(!1),[q,$]=(0,t.useState)(!1),[I,U]=(0,t.useState)(!1),[O,D]=(0,t.useState)({firstName:"",lastName:"",email:"",password:"",confirmPassword:""});(0,t.useEffect)(()=>{U(!0)},[]);let V=async r=>{r.preventDefault(),P(!0);try{var s;console.log("Attempting login for:",W);let r=await A.uE.login(W,J);if(console.log("Login response:",r),r.success&&(null===(s=r.data)||void 0===s?void 0:s.access_token)){localStorage.setItem("authToken",r.data.access_token);let s=r.data.user||r.data;s?(localStorage.setItem("user",JSON.stringify(s)),console.log("User data stored:",s)):console.warn("No user data received in login response"),console.log("Login successful, redirecting to dashboard"),alert(r.message||"Login successful!"),e.push("/dashboard")}else console.error("Login failed:",r),alert(r.message||"Login failed. Please check your credentials."),r.errors&&console.error("Validation errors:",r.errors)}catch(e){console.error("Login error:",e),alert("An unexpected error occurred. Please try again.")}finally{P(!1)}},Z=async e=>{if(e.preventDefault(),!O.firstName||!O.lastName||!O.email||!O.password){alert("Please fill in all required fields");return}if(O.password!==O.confirmPassword){alert("Passwords do not match");return}if(O.password.length<8){alert("Password must be at least 8 characters long");return}if(!q){alert("Please accept the Terms of Service and Privacy Policy");return}P(!0);try{console.log("Attempting registration for:",O.email);let e={first_name:O.firstName,last_name:O.lastName,email:O.email,password:O.password,password_confirmation:O.confirmPassword},r=await A.uE.register(e);if(console.log("Registration response:",r),r.success)alert("Registration successful! Please check your email for verification."),D({firstName:"",lastName:"",email:"",password:"",confirmPassword:""}),$(!1);else if(console.error("Registration failed:",r),alert(r.message||"Registration failed. Please try again."),r.errors){console.error("Validation errors:",r.errors);let e=Object.values(r.errors).flat().join("\n");alert("Validation errors:\n".concat(e))}}catch(e){console.error("Registration error:",e),alert("An unexpected error occurred. Please try again.")}finally{P(!1)}},X=(e,r)=>{D(s=>({...s,[e]:r}))},z=(null==r?void 0:r.app_name)||"Laravel NGO Foundation",K=null==r?void 0:r.app_logo;return(0,a.jsx)("div",{className:"flex flex-col bg-gradient-to-br from-green-50 to-green-100 dark:from-green-950 dark:to-green-900",children:(0,a.jsx)("main",{className:"flex-1 flex items-center justify-center py-12 px-4",children:(0,a.jsxs)("div",{className:"w-full max-w-6xl grid grid-cols-1 lg:grid-cols-2 gap-12 items-center",children:[(0,a.jsxs)(u.Zp,{className:"w-full max-w-md mx-auto shadow-2xl border-green-200 dark:border-green-800 rounded-3xl overflow-hidden backdrop-blur-sm bg-white/95 dark:bg-gray-900/95",children:[(0,a.jsxs)(x.tU,{defaultValue:"login",className:"w-full",children:[(0,a.jsxs)(x.j7,{className:"grid w-full grid-cols-2 bg-green-100 dark:bg-green-900",children:[(0,a.jsx)(x.Xi,{value:"login",className:"rounded-xl",children:"Login"}),(0,a.jsx)(x.Xi,{value:"register",className:"rounded-xl",children:"Register"})]}),(0,a.jsxs)(x.av,{value:"login",children:[(0,a.jsxs)(u.aR,{className:"text-center pb-6",children:[(0,a.jsx)("div",{className:"flex justify-center mb-4",children:K?(0,a.jsx)("div",{className:"relative h-16 w-16 overflow-hidden rounded-full shadow-lg animate-float",children:(0,a.jsx)("img",{src:K,alt:"".concat(z," Logo"),className:"w-full h-full object-cover"})}):(0,a.jsx)("div",{className:"relative h-16 w-16 overflow-hidden rounded-full bg-gradient-to-br from-green-500 to-green-700 shadow-lg animate-float",children:(0,a.jsx)(h.A,{className:"absolute inset-0 m-auto h-8 w-8 text-white drop-shadow-md"})})}),(0,a.jsx)(u.ZB,{className:"text-2xl font-bold text-green-800 dark:text-green-200",children:"Welcome Back"}),(0,a.jsxs)(u.BT,{className:"text-green-600 dark:text-green-400",children:["Sign in to continue your educational journey with ",z]})]}),(0,a.jsxs)(u.Wu,{children:[(0,a.jsxs)("form",{onSubmit:V,className:"space-y-6",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(g.J,{htmlFor:"email",className:"text-green-800 dark:text-green-200",children:"Email"}),(0,a.jsxs)("div",{className:"relative group",children:[(0,a.jsx)(f.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-green-500 group-hover:text-green-600 transition-colors duration-200"}),(0,a.jsx)(c.p,{id:"email",type:"email",placeholder:"<EMAIL>",value:W,onChange:e=>F(e.target.value),className:"pl-10 rounded-xl border-green-200 dark:border-green-700 focus:border-green-500 dark:focus:border-green-400 transition-all duration-200 hover:border-green-300 dark:hover:border-green-600",required:!0})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsx)(g.J,{htmlFor:"password",className:"text-green-800 dark:text-green-200",children:"Password"}),(0,a.jsx)(l(),{href:"#",onClick:()=>alert("Password reset instructions will be sent to your email"),className:"text-xs text-green-600 hover:text-green-700 dark:text-green-400 dark:hover:text-green-300 transition-colors duration-200 hover:underline",children:"Forgot password?"})]}),(0,a.jsxs)("div",{className:"relative group",children:[(0,a.jsx)(p.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-green-500 group-hover:text-green-600 transition-colors duration-200"}),(0,a.jsx)(c.p,{id:"password",type:R?"text":"password",placeholder:"••••••••",value:J,onChange:e=>B(e.target.value),className:"pl-10 pr-10 rounded-xl border-green-200 dark:border-green-700 focus:border-green-500 dark:focus:border-green-400 transition-all duration-200 hover:border-green-300 dark:hover:border-green-600",required:!0}),I&&(0,a.jsxs)("button",{type:"button",onClick:()=>H(!R),className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-green-500 hover:text-green-600 transition-colors duration-200",suppressHydrationWarning:!0,children:[R?(0,a.jsx)(b.A,{className:"h-4 w-4"}):(0,a.jsx)(v.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{className:"sr-only",children:R?"Hide password":"Show password"})]})]})]}),I&&(0,a.jsxs)("div",{className:"flex items-center space-x-2",suppressHydrationWarning:!0,children:[(0,a.jsx)(m.S,{id:"remember",checked:E,onCheckedChange:e=>T(e),className:"border-green-300 data-[state=checked]:bg-green-600 data-[state=checked]:border-green-600",suppressHydrationWarning:!0}),(0,a.jsx)(g.J,{htmlFor:"remember",className:"text-sm font-normal text-green-700 dark:text-green-300",children:"Remember me for 30 days"})]}),(0,a.jsx)(d.$,{type:"submit",className:"w-full bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 text-white rounded-xl py-3 font-semibold transition-all duration-300 hover:scale-105 hover:shadow-lg",disabled:n,children:n?(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)("div",{className:"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"}),"Signing in..."]}):"Sign In"})]}),(0,a.jsxs)("div",{className:"mt-6",children:[(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("div",{className:"absolute inset-0 flex items-center",children:(0,a.jsx)("div",{className:"w-full border-t border-green-200 dark:border-green-700"})}),(0,a.jsx)("div",{className:"relative flex justify-center text-xs uppercase",children:(0,a.jsx)("span",{className:"bg-white dark:bg-gray-900 px-2 text-green-600 dark:text-green-400",children:"Or continue with"})})]}),I&&(0,a.jsxs)("div",{className:"mt-4 grid grid-cols-2 gap-3",suppressHydrationWarning:!0,children:[(0,a.jsxs)(d.$,{variant:"outline",className:"rounded-xl border-green-200 dark:border-green-700 hover:bg-green-50 dark:hover:bg-green-900/20 transition-all duration-200 hover:scale-105",suppressHydrationWarning:!0,children:[(0,a.jsx)(N.A,{className:"mr-2 h-4 w-4 text-blue-600"}),"Facebook"]}),(0,a.jsxs)(d.$,{variant:"outline",className:"rounded-xl border-green-200 dark:border-green-700 hover:bg-green-50 dark:hover:bg-green-900/20 transition-all duration-200 hover:scale-105",suppressHydrationWarning:!0,children:[(0,a.jsx)(j.A,{className:"mr-2 h-4 w-4 text-blue-400"}),"Twitter"]})]})]}),I&&(0,a.jsxs)("div",{className:"mt-6 text-center",suppressHydrationWarning:!0,children:[(0,a.jsx)("p",{className:"text-sm text-green-600 dark:text-green-400 mb-3",children:"Having trouble signing in?"}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(d.$,{variant:"ghost",className:"w-full text-green-600 hover:text-green-700 dark:text-green-400 dark:hover:text-green-300",onClick:()=>alert("Password reset link will be sent to your email"),suppressHydrationWarning:!0,children:"Reset Password"}),(0,a.jsx)(d.$,{variant:"ghost",className:"w-full text-green-600 hover:text-green-700 dark:text-green-400 dark:hover:text-green-300",onClick:()=>alert("Please contact support for account recovery"),suppressHydrationWarning:!0,children:"Account Recovery"})]})]})]})]}),(0,a.jsxs)(x.av,{value:"register",children:[(0,a.jsxs)(u.aR,{className:"text-center pb-6",children:[(0,a.jsx)("div",{className:"flex justify-center mb-4",children:K?(0,a.jsx)("div",{className:"relative h-16 w-16 overflow-hidden rounded-full shadow-lg animate-float",children:(0,a.jsx)("img",{src:K,alt:"".concat(z," Logo"),className:"w-full h-full object-cover"})}):(0,a.jsx)("div",{className:"relative h-16 w-16 overflow-hidden rounded-full bg-gradient-to-br from-green-500 to-green-700 shadow-lg animate-float",children:(0,a.jsx)(h.A,{className:"absolute inset-0 m-auto h-8 w-8 text-white drop-shadow-md"})})}),(0,a.jsxs)(u.ZB,{className:"text-2xl font-bold text-green-800 dark:text-green-200",children:["Join ",z]}),(0,a.jsx)(u.BT,{className:"text-green-600 dark:text-green-400",children:"Create an account to support education across Nigeria, helping students, underprivileged people, and those in need."})]}),(0,a.jsx)(u.Wu,{children:(0,a.jsxs)("form",{onSubmit:Z,className:"space-y-4",children:[(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(g.J,{htmlFor:"first-name",className:"text-green-800 dark:text-green-200",children:"First Name *"}),(0,a.jsx)(c.p,{id:"first-name",placeholder:"John",value:O.firstName,onChange:e=>X("firstName",e.target.value),className:"rounded-xl border-green-200 dark:border-green-700 focus:border-green-500 dark:focus:border-green-400 transition-all duration-200",required:!0})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(g.J,{htmlFor:"last-name",className:"text-green-800 dark:text-green-200",children:"Last Name *"}),(0,a.jsx)(c.p,{id:"last-name",placeholder:"Doe",value:O.lastName,onChange:e=>X("lastName",e.target.value),className:"rounded-xl border-green-200 dark:border-green-700 focus:border-green-500 dark:focus:border-green-400 transition-all duration-200",required:!0})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(g.J,{htmlFor:"register-email",className:"text-green-800 dark:text-green-200",children:"Email *"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(f.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-green-500"}),(0,a.jsx)(c.p,{id:"register-email",type:"email",placeholder:"<EMAIL>",value:O.email,onChange:e=>X("email",e.target.value),className:"pl-10 rounded-xl border-green-200 dark:border-green-700 focus:border-green-500 dark:focus:border-green-400 transition-all duration-200",required:!0})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(g.J,{htmlFor:"register-password",className:"text-green-800 dark:text-green-200",children:"Password * (min. 8 characters)"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(p.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-green-500"}),(0,a.jsx)(c.p,{id:"register-password",type:R?"text":"password",placeholder:"••••••••",value:O.password,onChange:e=>X("password",e.target.value),className:"pl-10 pr-10 rounded-xl border-green-200 dark:border-green-700 focus:border-green-500 dark:focus:border-green-400 transition-all duration-200",required:!0,minLength:8}),I&&(0,a.jsxs)("button",{type:"button",onClick:()=>H(!R),className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-green-500 hover:text-green-600 transition-colors duration-200",suppressHydrationWarning:!0,children:[R?(0,a.jsx)(b.A,{className:"h-4 w-4"}):(0,a.jsx)(v.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{className:"sr-only",children:R?"Hide password":"Show password"})]})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(g.J,{htmlFor:"confirm-password",className:"text-green-800 dark:text-green-200",children:"Confirm Password *"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(p.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-green-500"}),(0,a.jsx)(c.p,{id:"confirm-password",type:_?"text":"password",placeholder:"••••••••",value:O.confirmPassword,onChange:e=>X("confirmPassword",e.target.value),className:"pl-10 pr-10 rounded-xl border-green-200 dark:border-green-700 focus:border-green-500 dark:focus:border-green-400 transition-all duration-200",required:!0}),I&&(0,a.jsxs)("button",{type:"button",onClick:()=>L(!_),className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-green-500 hover:text-green-600 transition-colors duration-200",suppressHydrationWarning:!0,children:[_?(0,a.jsx)(b.A,{className:"h-4 w-4"}):(0,a.jsx)(v.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{className:"sr-only",children:_?"Hide password":"Show password"})]})]})]}),I&&(0,a.jsxs)("div",{className:"flex items-center space-x-2",suppressHydrationWarning:!0,children:[(0,a.jsx)(m.S,{id:"terms",checked:q,onCheckedChange:e=>$(e),className:"border-green-300 data-[state=checked]:bg-green-600 data-[state=checked]:border-green-600",suppressHydrationWarning:!0,required:!0}),(0,a.jsxs)(g.J,{htmlFor:"terms",className:"text-sm font-normal text-green-700 dark:text-green-300",children:["I agree to the"," ",(0,a.jsx)(l(),{href:"/terms",className:"text-green-600 hover:text-green-700 dark:text-green-400 dark:hover:text-green-300 underline",children:"Terms of Service"})," ","and"," ",(0,a.jsx)(l(),{href:"/privacy",className:"text-green-600 hover:text-green-700 dark:text-green-400 dark:hover:text-green-300 underline",children:"Privacy Policy"})]})]}),(0,a.jsx)(d.$,{type:"submit",className:"w-full bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 text-white rounded-xl py-3 font-semibold transition-all duration-300 hover:scale-105 hover:shadow-lg",disabled:n,children:n?(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)("div",{className:"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"}),"Creating Account..."]}):"Create Account"})]})})]})]}),(0,a.jsx)(u.wL,{className:"flex justify-center pb-6",children:(0,a.jsxs)(l(),{href:"/",className:"flex items-center text-sm text-green-600 hover:text-green-700 dark:text-green-400 dark:hover:text-green-300 transition-colors duration-200 group",children:[(0,a.jsx)(w.A,{className:"mr-2 h-4 w-4 group-hover:-translate-x-1 transition-transform duration-200"}),"Back to home"]})})]}),(0,a.jsxs)("div",{className:"hidden lg:flex flex-col items-center justify-center space-y-8 text-center",children:[(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("div",{className:"absolute inset-0 bg-gradient-to-br from-green-500/30 to-green-700/30 rounded-full blur-3xl scale-150"}),(0,a.jsx)("div",{className:"relative h-48 w-48 flex items-center justify-center",children:K?(0,a.jsx)("div",{className:"relative h-32 w-32 overflow-hidden rounded-full shadow-2xl animate-float",children:(0,a.jsx)("img",{src:K,alt:"".concat(z," Logo"),className:"w-full h-full object-cover"})}):(0,a.jsx)("div",{className:"relative h-32 w-32 overflow-hidden rounded-full bg-gradient-to-br from-green-500 to-green-700 shadow-2xl animate-float",children:(0,a.jsx)(h.A,{className:"absolute inset-0 m-auto h-16 w-16 text-white drop-shadow-lg"})})})]}),(0,a.jsxs)("div",{className:"max-w-md space-y-6",children:[(0,a.jsx)("h2",{className:"text-4xl font-bold text-green-800 dark:text-green-200",children:z}),(0,a.jsx)("p",{className:"text-lg text-green-700 dark:text-green-300 leading-relaxed",children:(null==r?void 0:r.site_description)||"Join our mission to empower Nigeria through education. By creating an account, you'll be able to track your donations, apply for scholarships, and stay updated on our impact as we support students, underprivileged people, and those in need across the nation."}),(0,a.jsxs)("div",{className:"grid grid-cols-1 gap-4 pt-6",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3 p-3 bg-white/50 dark:bg-gray-800/50 rounded-xl backdrop-blur-sm border border-green-200 dark:border-green-700",children:[(0,a.jsx)("div",{className:"w-10 h-10 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center",children:(0,a.jsx)(y.A,{className:"h-5 w-5 text-green-600 dark:text-green-400"})}),(0,a.jsxs)("div",{className:"text-left",children:[(0,a.jsx)("div",{className:"font-semibold text-green-800 dark:text-green-200",children:"Scholarship Applications"}),(0,a.jsx)("div",{className:"text-sm text-green-600 dark:text-green-400",children:"Apply for educational grants"})]})]}),(0,a.jsxs)("div",{className:"flex items-center gap-3 p-3 bg-white/50 dark:bg-gray-800/50 rounded-xl backdrop-blur-sm border border-green-200 dark:border-green-700",children:[(0,a.jsx)("div",{className:"w-10 h-10 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center",children:(0,a.jsx)(k.A,{className:"h-5 w-5 text-green-600 dark:text-green-400"})}),(0,a.jsxs)("div",{className:"text-left",children:[(0,a.jsx)("div",{className:"font-semibold text-green-800 dark:text-green-200",children:"Impact Tracking"}),(0,a.jsx)("div",{className:"text-sm text-green-600 dark:text-green-400",children:"See your donation impact"})]})]}),(0,a.jsxs)("div",{className:"flex items-center gap-3 p-3 bg-white/50 dark:bg-gray-800/50 rounded-xl backdrop-blur-sm border border-green-200 dark:border-green-700",children:[(0,a.jsx)("div",{className:"w-10 h-10 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center",children:(0,a.jsx)(C.A,{className:"h-5 w-5 text-green-600 dark:text-green-400"})}),(0,a.jsxs)("div",{className:"text-left",children:[(0,a.jsx)("div",{className:"font-semibold text-green-800 dark:text-green-200",children:"Community Access"}),(0,a.jsx)("div",{className:"text-sm text-green-600 dark:text-green-400",children:"Connect with beneficiaries"})]})]})]}),(0,a.jsxs)("div",{className:"pt-6",children:[(0,a.jsx)("div",{className:"flex -space-x-2 overflow-hidden justify-center mb-3",children:[1,2,3,4,5].map(e=>(0,a.jsx)("div",{className:"inline-block h-12 w-12 rounded-full border-2 border-white dark:border-gray-800 overflow-hidden shadow-lg",children:(0,a.jsx)(o.default,{src:"/placeholder.svg?height=100&width=100&text=User".concat(e),alt:"Community member ".concat(e),width:48,height:48,className:"h-full w-full object-cover"})},e))}),(0,a.jsxs)("p",{className:"text-sm text-green-600 dark:text-green-400",children:["Join over ",(0,a.jsx)("span",{className:"font-bold text-green-700 dark:text-green-300",children:"1,200+"})," students and supporters"]})]})]})]})]})})})}},34964:(e,r,s)=>{"use strict";s.d(r,{Xi:()=>d,av:()=>c,j7:()=>i,tU:()=>o});var a=s(95155),t=s(12115),n=s(60704),l=s(53999);let o=n.bL,i=t.forwardRef((e,r)=>{let{className:s,...t}=e;return(0,a.jsx)(n.B8,{ref:r,className:(0,l.cn)("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",s),...t})});i.displayName=n.B8.displayName;let d=t.forwardRef((e,r)=>{let{className:s,...t}=e;return(0,a.jsx)(n.l9,{ref:r,className:(0,l.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",s),...t})});d.displayName=n.l9.displayName;let c=t.forwardRef((e,r)=>{let{className:s,...t}=e;return(0,a.jsx)(n.UC,{ref:r,className:(0,l.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",s),...t})});c.displayName=n.UC.displayName},44531:(e,r,s)=>{"use strict";s.d(r,{t:()=>n});var a=s(12115),t=s(31886);let n=()=>{let[e,r]=(0,a.useState)(null),[s,n]=(0,a.useState)(!0),[l,o]=(0,a.useState)(null);return(0,a.useEffect)(()=>{(async()=>{try{let e=await t.uE.getSettings();if(e.success)console.log("Settings loaded:",e.data),r(e.data);else throw Error("Invalid settings response")}catch(e){console.error("Error fetching settings:",e),o(e instanceof Error?e.message:"Unknown error"),r({app_name:"HLTKKQ Foundation",site_description:"Transforming Lives, Building Communities",contact_email:"<EMAIL>",contact_phone:"+234 ************"})}finally{n(!1)}})()},[]),{settings:e,loading:s,error:l}}},53999:(e,r,s)=>{"use strict";s.d(r,{cn:()=>n});var a=s(52596),t=s(39688);function n(){for(var e=arguments.length,r=Array(e),s=0;s<e;s++)r[s]=arguments[s];return(0,t.QP)((0,a.$)(r))}},82714:(e,r,s)=>{"use strict";s.d(r,{J:()=>d});var a=s(95155),t=s(12115),n=s(40968),l=s(74466),o=s(53999);let i=(0,l.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),d=t.forwardRef((e,r)=>{let{className:s,...t}=e;return(0,a.jsx)(n.b,{ref:r,className:(0,o.cn)(i(),s),...t})});d.displayName=n.b.displayName},88482:(e,r,s)=>{"use strict";s.d(r,{BT:()=>d,Wu:()=>c,ZB:()=>i,Zp:()=>l,aR:()=>o,wL:()=>g});var a=s(95155),t=s(12115),n=s(53999);let l=t.forwardRef((e,r)=>{let{className:s,...t}=e;return(0,a.jsx)("div",{ref:r,className:(0,n.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",s),...t})});l.displayName="Card";let o=t.forwardRef((e,r)=>{let{className:s,...t}=e;return(0,a.jsx)("div",{ref:r,className:(0,n.cn)("flex flex-col space-y-1.5 p-6",s),...t})});o.displayName="CardHeader";let i=t.forwardRef((e,r)=>{let{className:s,...t}=e;return(0,a.jsx)("div",{ref:r,className:(0,n.cn)("text-2xl font-semibold leading-none tracking-tight",s),...t})});i.displayName="CardTitle";let d=t.forwardRef((e,r)=>{let{className:s,...t}=e;return(0,a.jsx)("div",{ref:r,className:(0,n.cn)("text-sm text-muted-foreground",s),...t})});d.displayName="CardDescription";let c=t.forwardRef((e,r)=>{let{className:s,...t}=e;return(0,a.jsx)("div",{ref:r,className:(0,n.cn)("p-6 pt-0",s),...t})});c.displayName="CardContent";let g=t.forwardRef((e,r)=>{let{className:s,...t}=e;return(0,a.jsx)("div",{ref:r,className:(0,n.cn)("flex items-center p-6 pt-0",s),...t})});g.displayName="CardFooter"},89852:(e,r,s)=>{"use strict";s.d(r,{p:()=>l});var a=s(95155),t=s(12115),n=s(53999);let l=t.forwardRef((e,r)=>{let{className:s,type:t,...l}=e;return(0,a.jsx)("input",{type:t,className:(0,n.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",s),ref:r,suppressHydrationWarning:!0,...l})});l.displayName="Input"},95139:(e,r,s)=>{"use strict";s.d(r,{S:()=>i});var a=s(95155),t=s(12115),n=s(76981),l=s(5196),o=s(53999);let i=t.forwardRef((e,r)=>{let{className:s,...t}=e;return(0,a.jsx)(n.bL,{ref:r,className:(0,o.cn)("peer h-4 w-4 shrink-0 rounded-sm border border-primary ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground",s),suppressHydrationWarning:!0,...t,children:(0,a.jsx)(n.C1,{className:(0,o.cn)("flex items-center justify-center text-current"),children:(0,a.jsx)(l.A,{className:"h-4 w-4"})})})});i.displayName=n.bL.displayName},97168:(e,r,s)=>{"use strict";s.d(r,{$:()=>d,r:()=>i});var a=s(95155),t=s(12115),n=s(99708),l=s(74466),o=s(53999);let i=(0,l.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-all duration-200 ease-in-out focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 transform hover:scale-105",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90 hover:brightness-110",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90 hover:brightness-110",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground hover:brightness-105",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80 hover:brightness-105",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),d=t.forwardRef((e,r)=>{let{className:s,variant:t,size:l,asChild:d=!1,...c}=e,g=d?n.DX:"button";return(0,a.jsx)(g,{className:(0,o.cn)(i({variant:t,size:l,className:s})),ref:r,suppressHydrationWarning:!0,...c})});d.displayName="Button"}},e=>{var r=r=>e(e.s=r);e.O(0,[1778,6874,598,3063,8143,1886,8441,1684,7358],()=>r(4659)),_N_E=e.O()}]);