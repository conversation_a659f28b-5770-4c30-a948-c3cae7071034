'use client'

import React, { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON>bsList, TabsTrigger } from "@/components/ui/tabs"
import {
  BookOpen,
  GraduationCap,
  School,
  User,
  Phone,
  Mail,
  MapPin,
  Building,
  CreditCard,
  Camera,
  FileUp,
  Loader2,
  Upload,
  CheckCircle,
  AlertTriangle,
  MessageSquare
} from 'lucide-react'
import { useToast } from "@/hooks/use-toast"
import { apiClient } from '@/lib/api'
import SuccessMessage from './SuccessMessage'

// Form data interfaces for different categories
interface PrimarySchoolFormData {
  student_full_name: string
  age: string
  current_class: string
  father_name: string
  mother_name: string
  parent_phone: string
  home_address: string
  school_name: string
  headmaster_name: string
  school_account_number: string
  reason_for_scholarship: string
  current_school_fee: string
  supporting_information: string
}

interface SecondarySchoolFormData {
  student_full_name: string
  age: string
  class: string
  parent_phone: string
  address: string
  school_name: string
  principal_name: string
  principal_account_number: string
  reason_for_scholarship: string
  school_fee_amount: string
}

interface UniversityFormData {
  full_name: string
  age: string
  course_of_study: string
  current_level: string
  phone_number: string
  email_address: string
  matriculation_number: string
  reason_for_scholarship: string
}

export default function ScholarshipApplicationPage() {
  const [selectedCategory, setSelectedCategory] = useState<'primary' | 'secondary' | 'university'>('primary')
  const [submitting, setSubmitting] = useState(false)
  const [showSuccess, setShowSuccess] = useState(false)
  const { toast } = useToast()

  // Form data for different categories
  const [primaryFormData, setPrimaryFormData] = useState<PrimarySchoolFormData>({
    student_full_name: '',
    age: '',
    current_class: '',
    father_name: '',
    mother_name: '',
    parent_phone: '',
    home_address: '',
    school_name: '',
    headmaster_name: '',
    school_account_number: '',
    reason_for_scholarship: '',
    current_school_fee: '',
    supporting_information: ''
  })

  const [secondaryFormData, setSecondaryFormData] = useState<SecondarySchoolFormData>({
    student_full_name: '',
    age: '',
    class: '',
    parent_phone: '',
    address: '',
    school_name: '',
    principal_name: '',
    principal_account_number: '',
    reason_for_scholarship: '',
    school_fee_amount: ''
  })

  const [universityFormData, setUniversityFormData] = useState<UniversityFormData>({
    full_name: '',
    age: '',
    course_of_study: '',
    current_level: '',
    phone_number: '',
    email_address: '',
    matriculation_number: '',
    reason_for_scholarship: ''
  })

  // File uploads for different categories
  const [primaryFiles, setPrimaryFiles] = useState({
    student_picture: null as File | null
  })

  const [secondaryFiles, setSecondaryFiles] = useState({
    student_picture: null as File | null
  })

  const [universityFiles, setUniversityFiles] = useState({
    student_id_card: null as File | null,
    payment_evidence: null as File | null,
    supporting_documents: [] as File[]
  })

  // Category configuration - Updated to match platform design system
  const categoryConfig = {
    primary: {
      icon: BookOpen,
      title: "Primary School Scholarship",
      description: "For Primary 1-6 students (Filled by Parent/Guardian)",
      color: "bg-blue-500",
      bgColor: "bg-blue-50",
      textColor: "text-blue-700",
      borderColor: "border-blue-200"
    },
    secondary: {
      icon: School,
      title: "Secondary School Scholarship",
      description: "For Secondary school students (Filled by Student)",
      color: "bg-green-500",
      bgColor: "bg-green-50",
      textColor: "text-green-700",
      borderColor: "border-green-200"
    },
    university: {
      icon: GraduationCap,
      title: "University Scholarship",
      description: "For University students (Filled by Student)",
      color: "bg-purple-500",
      bgColor: "bg-purple-50",
      textColor: "text-purple-700",
      borderColor: "border-purple-200"
    }
  }

  const handleFileChange = (category: string, field: string, file: File | null) => {
    if (category === 'primary') {
      setPrimaryFiles(prev => ({ ...prev, [field]: file }))
    } else if (category === 'secondary') {
      setSecondaryFiles(prev => ({ ...prev, [field]: file }))
    } else if (category === 'university') {
      if (field === 'supporting_documents' && file) {
        setUniversityFiles(prev => ({ 
          ...prev, 
          supporting_documents: [...prev.supporting_documents, file] 
        }))
      } else {
        setUniversityFiles(prev => ({ ...prev, [field]: file }))
      }
    }
  }

  const handleMultipleFileChange = (files: FileList | null) => {
    if (files && selectedCategory === 'university') {
      const fileArray = Array.from(files)
      setUniversityFiles(prev => ({ 
        ...prev, 
        supporting_documents: [...prev.supporting_documents, ...fileArray] 
      }))
    }
  }

  const removeFile = (index: number) => {
    setUniversityFiles(prev => ({
      ...prev,
      supporting_documents: prev.supporting_documents.filter((_, i) => i !== index)
    }))
  }

  const validateForm = () => {
    if (selectedCategory === 'primary') {
      const required = ['student_full_name', 'age', 'current_class', 'father_name', 'mother_name', 'parent_phone', 'home_address', 'school_name', 'headmaster_name', 'school_account_number', 'reason_for_scholarship', 'current_school_fee']
      return required.every(field => primaryFormData[field as keyof PrimarySchoolFormData].trim() !== '') && primaryFiles.student_picture
    } else if (selectedCategory === 'secondary') {
      const required = ['student_full_name', 'age', 'class', 'parent_phone', 'address', 'school_name', 'principal_name', 'principal_account_number', 'reason_for_scholarship', 'school_fee_amount']
      return required.every(field => secondaryFormData[field as keyof SecondarySchoolFormData].trim() !== '') && secondaryFiles.student_picture
    } else if (selectedCategory === 'university') {
      const required = ['full_name', 'age', 'course_of_study', 'current_level', 'phone_number', 'email_address', 'matriculation_number', 'reason_for_scholarship']
      return required.every(field => universityFormData[field as keyof UniversityFormData].trim() !== '') && 
             universityFiles.student_id_card && universityFiles.payment_evidence
    }
    return false
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!validateForm()) {
      toast({
        title: "Validation Error",
        description: "Please fill in all required fields and upload required documents",
        variant: "destructive"
      })
      return
    }

    try {
      setSubmitting(true)
      
      const formData = new FormData()
      formData.append('category', selectedCategory)
      
      // Add form data based on category
      if (selectedCategory === 'primary') {
        formData.append('form_data', JSON.stringify(primaryFormData))
        if (primaryFiles.student_picture) {
          formData.append('student_picture', primaryFiles.student_picture)
        }
      } else if (selectedCategory === 'secondary') {
        formData.append('form_data', JSON.stringify(secondaryFormData))
        if (secondaryFiles.student_picture) {
          formData.append('student_picture', secondaryFiles.student_picture)
        }
      } else if (selectedCategory === 'university') {
        formData.append('form_data', JSON.stringify(universityFormData))
        if (universityFiles.student_id_card) {
          formData.append('student_id_card', universityFiles.student_id_card)
        }
        if (universityFiles.payment_evidence) {
          formData.append('payment_evidence', universityFiles.payment_evidence)
        }
        universityFiles.supporting_documents.forEach((file, index) => {
          formData.append(`supporting_documents[${index}]`, file)
        })
      }

      const response = await apiClient.post('/scholarships/apply', formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      })

      if (response.success) {
        // Show success message
        setShowSuccess(true)

        // Reset form
        if (selectedCategory === 'primary') {
          setPrimaryFormData({
            student_full_name: '', age: '', current_class: '', father_name: '', mother_name: '',
            parent_phone: '', home_address: '', school_name: '', headmaster_name: '',
            school_account_number: '', reason_for_scholarship: '', current_school_fee: '',
            supporting_information: ''
          })
          setPrimaryFiles({ student_picture: null })
        } else if (selectedCategory === 'secondary') {
          setSecondaryFormData({
            student_full_name: '', age: '', class: '', parent_phone: '', address: '',
            school_name: '', principal_name: '', principal_account_number: '',
            reason_for_scholarship: '', school_fee_amount: ''
          })
          setSecondaryFiles({ student_picture: null })
        } else if (selectedCategory === 'university') {
          setUniversityFormData({
            full_name: '', age: '', course_of_study: '', current_level: '',
            phone_number: '', email_address: '', matriculation_number: '',
            reason_for_scholarship: ''
          })
          setUniversityFiles({ student_id_card: null, payment_evidence: null, supporting_documents: [] })
        }
      } else {
        toast({
          title: "Error",
          description: response.message || "Failed to submit application",
          variant: "destructive"
        })
      }
    } catch (error: any) {
      console.error('Error submitting application:', error)
      toast({
        title: "Error",
        description: error.response?.data?.message || "Failed to submit application",
        variant: "destructive"
      })
    } finally {
      setSubmitting(false)
    }
  }

  const handleBackToForm = () => {
    setShowSuccess(false)
  }

  const handleGoHome = () => {
    // Navigate to dashboard or home page
    window.location.href = '/dashboard'
  }

  if (showSuccess) {
    return <SuccessMessage onBackToForm={handleBackToForm} onGoHome={handleGoHome} />
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50">
      {/* Hero Section - Matching platform design */}
      <div className="bg-gradient-to-r from-blue-600 via-indigo-600 to-purple-600 text-white py-16">
        <div className="container mx-auto px-4 text-center">
          <h1 className="text-4xl md:text-6xl font-bold mb-6">
            Scholarship Application
          </h1>
          <p className="text-xl md:text-2xl mb-8 text-blue-100">
            Choose your scholarship category and complete the application form
          </p>
        </div>
      </div>

      {/* Main Content */}
      <div className="container mx-auto px-4 py-8">
        <Card className="bg-white/80 backdrop-blur-sm border-gray-200 shadow-xl overflow-hidden">
          <CardHeader className="bg-white border-b border-gray-200">
            <CardTitle className="text-2xl md:text-3xl font-bold text-gray-900">Apply for Scholarship</CardTitle>
            <CardDescription className="text-gray-600 text-base md:text-lg">
              Select your education level and fill out the appropriate form
            </CardDescription>
          </CardHeader>
          <CardContent className="p-6 md:p-8">
            {/* Category Selection - Updated to match platform design */}
            <Tabs value={selectedCategory} onValueChange={(value) => setSelectedCategory(value as any)} className="w-full">
              <TabsList className="grid w-full grid-cols-1 md:grid-cols-3 mb-8 h-auto p-2 gap-2 bg-gray-100 rounded-lg">
                {Object.entries(categoryConfig).map(([key, config]) => {
                  const IconComponent = config.icon
                  return (
                    <TabsTrigger
                      key={key}
                      value={key}
                      className={`flex flex-col items-center p-4 space-y-3 rounded-lg transition-all duration-300 hover:scale-105 data-[state=active]:bg-white data-[state=active]:shadow-lg data-[state=active]:${config.borderColor} data-[state=active]:border-2`}
                    >
                      <div className={`p-3 rounded-full ${config.color} text-white shadow-lg`}>
                        <IconComponent className="h-6 w-6" />
                      </div>
                      <div className="text-center">
                        <div className="font-bold text-sm text-gray-900 leading-tight">{config.title}</div>
                        <div className="text-xs text-gray-600 mt-1">{config.description}</div>
                      </div>
                    </TabsTrigger>
                  )
                })}
              </TabsList>

              {/* Primary School Form - Updated design */}
              <TabsContent value="primary" className="space-y-8">
                <form onSubmit={handleSubmit} className="space-y-8">
                  <div className="bg-blue-50 p-6 rounded-lg border-2 border-blue-200 shadow-sm">
                    <div className="flex items-center">
                      <AlertTriangle className="h-6 w-6 text-blue-600 mr-3" />
                      <p className="text-blue-800 font-semibold text-lg">
                        This form should be filled by a Parent or Guardian on behalf of the student
                      </p>
                    </div>
                  </div>

                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                    {/* Student Information Card */}
                    <Card className="border-2 border-blue-200 bg-blue-50/50 shadow-sm hover:shadow-md transition-shadow">
                      <CardHeader className="pb-4">
                        <CardTitle className="text-xl font-bold text-gray-900 flex items-center">
                          <User className="h-6 w-6 mr-3 text-blue-600" />
                          Student Information
                        </CardTitle>
                      </CardHeader>
                      <CardContent className="space-y-4">

                      <div>
                        <Label htmlFor="student_full_name">Student Full Name *</Label>
                        <Input
                          id="student_full_name"
                          value={primaryFormData.student_full_name}
                          onChange={(e) => setPrimaryFormData(prev => ({ ...prev, student_full_name: e.target.value }))}
                          placeholder="Enter student's full name"
                          className="mt-1"
                          required
                        />
                      </div>

                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <Label htmlFor="age">Age *</Label>
                          <Input
                            id="age"
                            type="number"
                            value={primaryFormData.age}
                            onChange={(e) => setPrimaryFormData(prev => ({ ...prev, age: e.target.value }))}
                            placeholder="Age"
                            className="mt-1"
                            required
                          />
                        </div>
                        <div>
                          <Label htmlFor="current_class">Current Class *</Label>
                          <Select value={primaryFormData.current_class} onValueChange={(value) => setPrimaryFormData(prev => ({ ...prev, current_class: value }))}>
                            <SelectTrigger className="mt-1">
                              <SelectValue placeholder="Select class" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="Primary 1">Primary 1</SelectItem>
                              <SelectItem value="Primary 2">Primary 2</SelectItem>
                              <SelectItem value="Primary 3">Primary 3</SelectItem>
                              <SelectItem value="Primary 4">Primary 4</SelectItem>
                              <SelectItem value="Primary 5">Primary 5</SelectItem>
                              <SelectItem value="Primary 6">Primary 6</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>
                      </div>
                      </CardContent>
                    </Card>

                    {/* Parent Information Card */}
                    <Card className="border-2 border-green-200 bg-green-50/50 shadow-sm hover:shadow-md transition-shadow">
                      <CardHeader className="pb-4">
                        <CardTitle className="text-xl font-bold text-gray-900 flex items-center">
                          <User className="h-6 w-6 mr-3 text-green-600" />
                          Parent/Guardian Information
                        </CardTitle>
                      </CardHeader>
                      <CardContent className="space-y-4">

                      <div>
                        <Label htmlFor="father_name">Father's Name *</Label>
                        <Input
                          id="father_name"
                          value={primaryFormData.father_name}
                          onChange={(e) => setPrimaryFormData(prev => ({ ...prev, father_name: e.target.value }))}
                          placeholder="Enter father's name"
                          className="mt-1"
                          required
                        />
                      </div>

                      <div>
                        <Label htmlFor="mother_name">Mother's Name *</Label>
                        <Input
                          id="mother_name"
                          value={primaryFormData.mother_name}
                          onChange={(e) => setPrimaryFormData(prev => ({ ...prev, mother_name: e.target.value }))}
                          placeholder="Enter mother's name"
                          className="mt-1"
                          required
                        />
                      </div>

                      <div>
                        <Label htmlFor="parent_phone">Father's or Mother's Phone Number *</Label>
                        <Input
                          id="parent_phone"
                          type="tel"
                          value={primaryFormData.parent_phone}
                          onChange={(e) => setPrimaryFormData(prev => ({ ...prev, parent_phone: e.target.value }))}
                          placeholder="Enter phone number"
                          className="mt-1"
                          required
                        />
                      </div>
                      </CardContent>
                    </Card>
                  </div>

                  {/* Address and School Information */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                    <Card className="border-2 border-red-200 bg-red-50/50 shadow-sm hover:shadow-md transition-shadow">
                      <CardHeader className="pb-4">
                        <CardTitle className="text-xl font-bold text-gray-900 flex items-center">
                          <MapPin className="h-6 w-6 mr-3 text-red-600" />
                          Address Information
                        </CardTitle>
                      </CardHeader>
                      <CardContent className="space-y-4">
                        <div>
                          <Label htmlFor="home_address">Home Address *</Label>
                          <Textarea
                            id="home_address"
                            value={primaryFormData.home_address}
                            onChange={(e) => setPrimaryFormData(prev => ({ ...prev, home_address: e.target.value }))}
                            placeholder="Enter complete home address"
                            className="mt-1"
                            rows={3}
                            required
                          />
                        </div>
                      </CardContent>
                    </Card>

                    <Card className="border-2 border-purple-200 bg-purple-50/50 shadow-sm hover:shadow-md transition-shadow">
                      <CardHeader className="pb-4">
                        <CardTitle className="text-xl font-bold text-gray-900 flex items-center">
                          <Building className="h-6 w-6 mr-3 text-purple-600" />
                          School Information
                        </CardTitle>
                      </CardHeader>
                      <CardContent className="space-y-4">

                      <div>
                        <Label htmlFor="school_name">Name of the School *</Label>
                        <Input
                          id="school_name"
                          value={primaryFormData.school_name}
                          onChange={(e) => setPrimaryFormData(prev => ({ ...prev, school_name: e.target.value }))}
                          placeholder="Enter school name"
                          className="mt-1"
                          required
                        />
                      </div>

                      <div>
                        <Label htmlFor="headmaster_name">Name of Headmaster/Director *</Label>
                        <Input
                          id="headmaster_name"
                          value={primaryFormData.headmaster_name}
                          onChange={(e) => setPrimaryFormData(prev => ({ ...prev, headmaster_name: e.target.value }))}
                          placeholder="Enter headmaster's name"
                          className="mt-1"
                          required
                        />
                      </div>

                      <div>
                        <Label htmlFor="school_account_number">School Account Number *</Label>
                        <Input
                          id="school_account_number"
                          value={primaryFormData.school_account_number}
                          onChange={(e) => setPrimaryFormData(prev => ({ ...prev, school_account_number: e.target.value }))}
                          placeholder="Enter account number for scholarship payment"
                          className="mt-1"
                          required
                        />
                      </div>
                      </CardContent>
                    </Card>
                  </div>

                  {/* Financial and Additional Information */}
                  <Card className="border-2 border-orange-200 bg-orange-50/50 shadow-sm hover:shadow-md transition-shadow">
                    <CardHeader className="pb-4">
                      <CardTitle className="text-xl font-bold text-gray-900 flex items-center">
                        <CreditCard className="h-6 w-6 mr-3 text-orange-600" />
                        Financial & Additional Information
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-6">

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div>
                        <Label htmlFor="current_school_fee">Current School Fee Amount *</Label>
                        <Input
                          id="current_school_fee"
                          type="number"
                          value={primaryFormData.current_school_fee}
                          onChange={(e) => setPrimaryFormData(prev => ({ ...prev, current_school_fee: e.target.value }))}
                          placeholder="Enter amount in Naira"
                          className="mt-1"
                          required
                        />
                      </div>

                      <div>
                        <Label htmlFor="student_picture">Upload Student Picture *</Label>
                        <div className="mt-1">
                          <Input
                            id="student_picture"
                            type="file"
                            accept="image/*"
                            onChange={(e) => handleFileChange('primary', 'student_picture', e.target.files?.[0] || null)}
                            className="file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"
                            required
                          />
                        </div>
                      </div>
                    </div>

                    <div>
                      <Label htmlFor="reason_for_scholarship">Reason for the Scholarship *</Label>
                      <Textarea
                        id="reason_for_scholarship"
                        value={primaryFormData.reason_for_scholarship}
                        onChange={(e) => setPrimaryFormData(prev => ({ ...prev, reason_for_scholarship: e.target.value }))}
                        placeholder="Please explain why your child needs this scholarship"
                        className="mt-1"
                        rows={4}
                        required
                      />
                    </div>

                    <div>
                      <Label htmlFor="supporting_information">Any Other Supporting Information</Label>
                      <Textarea
                        id="supporting_information"
                        value={primaryFormData.supporting_information}
                        onChange={(e) => setPrimaryFormData(prev => ({ ...prev, supporting_information: e.target.value }))}
                        placeholder="Any additional information that supports your application"
                        className="mt-1"
                        rows={3}
                      />
                    </div>
                    </CardContent>
                  </Card>

                  <div className="flex justify-center pt-8">
                    <Button
                      type="submit"
                      disabled={submitting || !validateForm()}
                      size="lg"
                      className="bg-gradient-to-r from-blue-600 via-indigo-600 to-purple-600 hover:from-blue-700 hover:via-indigo-700 hover:to-purple-700 text-white px-12 py-4 text-lg font-bold shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105"
                    >
                      {submitting ? (
                        <>
                          <Loader2 className="mr-3 h-6 w-6 animate-spin" />
                          Submitting Application...
                        </>
                      ) : (
                        <>
                          <CheckCircle className="mr-3 h-6 w-6" />
                          Submit Primary School Application
                        </>
                      )}
                    </Button>
                  </div>
                </form>
              </TabsContent>

              {/* Secondary School Form */}
              <TabsContent value="secondary" className="space-y-8">
                <form onSubmit={handleSubmit} className="space-y-8">
                  <div className="bg-green-50 p-6 rounded-lg border-2 border-green-200 shadow-sm">
                    <div className="flex items-center">
                      <AlertTriangle className="h-6 w-6 text-green-600 mr-3" />
                      <p className="text-green-800 font-semibold text-lg">
                        This form should be filled by the Student directly
                      </p>
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                    {/* Student Information Card */}
                    <Card className="border-2 border-green-200 bg-green-50/50 shadow-sm hover:shadow-md transition-shadow">
                      <CardHeader className="pb-4">
                        <CardTitle className="text-xl font-bold text-gray-900 flex items-center">
                          <User className="h-6 w-6 mr-3 text-green-600" />
                          Student Information
                        </CardTitle>
                      </CardHeader>
                      <CardContent className="space-y-4">

                      <div>
                        <Label htmlFor="sec_student_full_name">Student Full Name *</Label>
                        <Input
                          id="sec_student_full_name"
                          value={secondaryFormData.student_full_name}
                          onChange={(e) => setSecondaryFormData(prev => ({ ...prev, student_full_name: e.target.value }))}
                          placeholder="Enter your full name"
                          className="mt-1"
                          required
                        />
                      </div>

                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <Label htmlFor="sec_age">Age *</Label>
                          <Input
                            id="sec_age"
                            type="number"
                            value={secondaryFormData.age}
                            onChange={(e) => setSecondaryFormData(prev => ({ ...prev, age: e.target.value }))}
                            placeholder="Your age"
                            className="mt-1"
                            required
                          />
                        </div>
                        <div>
                          <Label htmlFor="sec_class">Class *</Label>
                          <Select value={secondaryFormData.class} onValueChange={(value) => setSecondaryFormData(prev => ({ ...prev, class: value }))}>
                            <SelectTrigger className="mt-1">
                              <SelectValue placeholder="Select class" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="JSS 1">JSS 1</SelectItem>
                              <SelectItem value="JSS 2">JSS 2</SelectItem>
                              <SelectItem value="JSS 3">JSS 3</SelectItem>
                              <SelectItem value="SS 1">SS 1</SelectItem>
                              <SelectItem value="SS 2">SS 2</SelectItem>
                              <SelectItem value="SS 3">SS 3</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>
                      </div>

                      <div>
                        <Label htmlFor="sec_parent_phone">Parent's Phone Number *</Label>
                        <Input
                          id="sec_parent_phone"
                          type="tel"
                          value={secondaryFormData.parent_phone}
                          onChange={(e) => setSecondaryFormData(prev => ({ ...prev, parent_phone: e.target.value }))}
                          placeholder="Enter parent's phone number"
                          className="mt-1"
                          required
                        />
                      </div>
                      </CardContent>
                    </Card>

                    {/* Address and School Information Card */}
                    <Card className="border-2 border-red-200 bg-red-50/50 shadow-sm hover:shadow-md transition-shadow">
                      <CardHeader className="pb-4">
                        <CardTitle className="text-xl font-bold text-gray-900 flex items-center">
                          <MapPin className="h-6 w-6 mr-3 text-red-600" />
                          Address & School Information
                        </CardTitle>
                      </CardHeader>
                      <CardContent className="space-y-4">

                      <div>
                        <Label htmlFor="sec_address">Address *</Label>
                        <Textarea
                          id="sec_address"
                          value={secondaryFormData.address}
                          onChange={(e) => setSecondaryFormData(prev => ({ ...prev, address: e.target.value }))}
                          placeholder="Enter your complete address"
                          className="mt-1"
                          rows={3}
                          required
                        />
                      </div>

                      <div>
                        <Label htmlFor="sec_school_name">School Name *</Label>
                        <Input
                          id="sec_school_name"
                          value={secondaryFormData.school_name}
                          onChange={(e) => setSecondaryFormData(prev => ({ ...prev, school_name: e.target.value }))}
                          placeholder="Enter your school name"
                          className="mt-1"
                          required
                        />
                      </div>

                      <div>
                        <Label htmlFor="sec_principal_name">School Principal or Director Name *</Label>
                        <Input
                          id="sec_principal_name"
                          value={secondaryFormData.principal_name}
                          onChange={(e) => setSecondaryFormData(prev => ({ ...prev, principal_name: e.target.value }))}
                          placeholder="Enter principal's name"
                          className="mt-1"
                          required
                        />
                      </div>
                      </CardContent>
                    </Card>
                  </div>

                  {/* Financial Information */}
                  <Card className="border-2 border-orange-200 bg-orange-50/50 shadow-sm hover:shadow-md transition-shadow">
                    <CardHeader className="pb-4">
                      <CardTitle className="text-xl font-bold text-gray-900 flex items-center">
                        <CreditCard className="h-6 w-6 mr-3 text-orange-600" />
                        Financial Information
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-6">

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div>
                        <Label htmlFor="sec_principal_account_number">Principal or Financial Officer Account Number *</Label>
                        <Input
                          id="sec_principal_account_number"
                          value={secondaryFormData.principal_account_number}
                          onChange={(e) => setSecondaryFormData(prev => ({ ...prev, principal_account_number: e.target.value }))}
                          placeholder="Enter account number for scholarship payment"
                          className="mt-1"
                          required
                        />
                      </div>

                      <div>
                        <Label htmlFor="sec_school_fee_amount">School Fee Amount *</Label>
                        <Input
                          id="sec_school_fee_amount"
                          type="number"
                          value={secondaryFormData.school_fee_amount}
                          onChange={(e) => setSecondaryFormData(prev => ({ ...prev, school_fee_amount: e.target.value }))}
                          placeholder="Enter amount in Naira"
                          className="mt-1"
                          required
                        />
                      </div>
                    </div>
                    </CardContent>
                  </Card>

                  {/* Picture and Reason */}
                  <Card className="border-2 border-indigo-200 bg-indigo-50/50 shadow-sm hover:shadow-md transition-shadow">
                    <CardHeader className="pb-4">
                      <CardTitle className="text-xl font-bold text-gray-900 flex items-center">
                        <FileUp className="h-6 w-6 mr-3 text-indigo-600" />
                        Picture & Application Reason
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-6">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div>
                        <Label htmlFor="sec_student_picture">Upload Student Picture *</Label>
                        <div className="mt-1">
                          <Input
                            id="sec_student_picture"
                            type="file"
                            accept="image/*"
                            onChange={(e) => handleFileChange('secondary', 'student_picture', e.target.files?.[0] || null)}
                            className="file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-green-50 file:text-green-700 hover:file:bg-green-100"
                            required
                          />
                        </div>
                      </div>
                    </div>

                    <div>
                      <Label htmlFor="sec_reason_for_scholarship">Reason for the Scholarship *</Label>
                      <Textarea
                        id="sec_reason_for_scholarship"
                        value={secondaryFormData.reason_for_scholarship}
                        onChange={(e) => setSecondaryFormData(prev => ({ ...prev, reason_for_scholarship: e.target.value }))}
                        placeholder="Please explain why you need this scholarship"
                        className="mt-1"
                        rows={4}
                        required
                      />
                    </div>
                    </CardContent>
                  </Card>

                  <div className="flex justify-center pt-8">
                    <Button
                      type="submit"
                      disabled={submitting || !validateForm()}
                      size="lg"
                      className="bg-gradient-to-r from-green-600 via-emerald-600 to-teal-600 hover:from-green-700 hover:via-emerald-700 hover:to-teal-700 text-white px-12 py-4 text-lg font-bold shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105"
                    >
                      {submitting ? (
                        <>
                          <Loader2 className="mr-3 h-6 w-6 animate-spin" />
                          Submitting Application...
                        </>
                      ) : (
                        <>
                          <CheckCircle className="mr-3 h-6 w-6" />
                          Submit Secondary School Application
                        </>
                      )}
                    </Button>
                  </div>
                </form>
              </TabsContent>

              {/* University Form */}
              <TabsContent value="university" className="space-y-8">
                <form onSubmit={handleSubmit} className="space-y-8">
                  <div className="bg-purple-50 p-6 rounded-lg border-2 border-purple-200 shadow-sm">
                    <div className="flex items-center">
                      <AlertTriangle className="h-6 w-6 text-purple-600 mr-3" />
                      <p className="text-purple-800 font-semibold text-lg">
                        This form should be filled by the University Student directly
                      </p>
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                    {/* Personal Information Card */}
                    <Card className="border-2 border-purple-200 bg-purple-50/50 shadow-sm hover:shadow-md transition-shadow">
                      <CardHeader className="pb-4">
                        <CardTitle className="text-xl font-bold text-gray-900 flex items-center">
                          <User className="h-6 w-6 mr-3 text-purple-600" />
                          Personal Information
                        </CardTitle>
                      </CardHeader>
                      <CardContent className="space-y-4">

                      <div>
                        <Label htmlFor="uni_full_name">Full Name *</Label>
                        <Input
                          id="uni_full_name"
                          value={universityFormData.full_name}
                          onChange={(e) => setUniversityFormData(prev => ({ ...prev, full_name: e.target.value }))}
                          placeholder="Enter your full name"
                          className="mt-1"
                          required
                        />
                      </div>

                      <div>
                        <Label htmlFor="uni_age">Age *</Label>
                        <Input
                          id="uni_age"
                          type="number"
                          value={universityFormData.age}
                          onChange={(e) => setUniversityFormData(prev => ({ ...prev, age: e.target.value }))}
                          placeholder="Your age"
                          className="mt-1"
                          required
                        />
                      </div>

                      <div>
                        <Label htmlFor="uni_phone_number">Phone Number *</Label>
                        <Input
                          id="uni_phone_number"
                          type="tel"
                          value={universityFormData.phone_number}
                          onChange={(e) => setUniversityFormData(prev => ({ ...prev, phone_number: e.target.value }))}
                          placeholder="Enter your phone number"
                          className="mt-1"
                          required
                        />
                      </div>

                      <div>
                        <Label htmlFor="uni_email_address">Email Address *</Label>
                        <Input
                          id="uni_email_address"
                          type="email"
                          value={universityFormData.email_address}
                          onChange={(e) => setUniversityFormData(prev => ({ ...prev, email_address: e.target.value }))}
                          placeholder="Enter your email address"
                          className="mt-1"
                          required
                        />
                      </div>
                      </CardContent>
                    </Card>

                    {/* Academic Information Card */}
                    <Card className="border-2 border-blue-200 bg-blue-50/50 shadow-sm hover:shadow-md transition-shadow">
                      <CardHeader className="pb-4">
                        <CardTitle className="text-xl font-bold text-gray-900 flex items-center">
                          <GraduationCap className="h-6 w-6 mr-3 text-blue-600" />
                          Academic Information
                        </CardTitle>
                      </CardHeader>
                      <CardContent className="space-y-4">

                      <div>
                        <Label htmlFor="uni_course_of_study">Course of Study *</Label>
                        <Input
                          id="uni_course_of_study"
                          value={universityFormData.course_of_study}
                          onChange={(e) => setUniversityFormData(prev => ({ ...prev, course_of_study: e.target.value }))}
                          placeholder="Enter your course of study"
                          className="mt-1"
                          required
                        />
                      </div>

                      <div>
                        <Label htmlFor="uni_current_level">Current Level *</Label>
                        <Select value={universityFormData.current_level} onValueChange={(value) => setUniversityFormData(prev => ({ ...prev, current_level: value }))}>
                          <SelectTrigger className="mt-1">
                            <SelectValue placeholder="Select your current level" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="100L">100 Level</SelectItem>
                            <SelectItem value="200L">200 Level</SelectItem>
                            <SelectItem value="300L">300 Level</SelectItem>
                            <SelectItem value="400L">400 Level</SelectItem>
                            <SelectItem value="500L">500 Level</SelectItem>
                            <SelectItem value="600L">600 Level</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>

                      <div>
                        <Label htmlFor="uni_matriculation_number">Matriculation Number *</Label>
                        <Input
                          id="uni_matriculation_number"
                          value={universityFormData.matriculation_number}
                          onChange={(e) => setUniversityFormData(prev => ({ ...prev, matriculation_number: e.target.value }))}
                          placeholder="Enter your matriculation number"
                          className="mt-1"
                          required
                        />
                      </div>
                      </CardContent>
                    </Card>
                  </div>

                  {/* File Uploads */}
                  <Card className="border-2 border-orange-200 bg-orange-50/50 shadow-sm hover:shadow-md transition-shadow">
                    <CardHeader className="pb-4">
                      <CardTitle className="text-xl font-bold text-gray-900 flex items-center">
                        <FileUp className="h-6 w-6 mr-3 text-orange-600" />
                        Required Documents
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-6">

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div>
                        <Label htmlFor="uni_student_id_card">Upload Student ID Card *</Label>
                        <div className="mt-1">
                          <Input
                            id="uni_student_id_card"
                            type="file"
                            accept="image/*,.pdf"
                            onChange={(e) => handleFileChange('university', 'student_id_card', e.target.files?.[0] || null)}
                            className="file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-purple-50 file:text-purple-700 hover:file:bg-purple-100"
                            required
                          />
                        </div>
                      </div>

                      <div>
                        <Label htmlFor="uni_payment_evidence">Upload Payment Evidence (Remita/Screenshot) *</Label>
                        <div className="mt-1">
                          <Input
                            id="uni_payment_evidence"
                            type="file"
                            accept="image/*,.pdf"
                            onChange={(e) => handleFileChange('university', 'payment_evidence', e.target.files?.[0] || null)}
                            className="file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-purple-50 file:text-purple-700 hover:file:bg-purple-100"
                            required
                          />
                        </div>
                      </div>
                    </div>

                    <div>
                      <Label htmlFor="uni_supporting_documents">Upload Supporting Documents (PDF, DOCX, JPG)</Label>
                      <div className="mt-1">
                        <Input
                          id="uni_supporting_documents"
                          type="file"
                          accept=".pdf,.docx,.doc,.jpg,.jpeg,.png"
                          multiple
                          onChange={(e) => handleMultipleFileChange(e.target.files)}
                          className="file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-purple-50 file:text-purple-700 hover:file:bg-purple-100"
                        />
                      </div>
                      {universityFiles.supporting_documents.length > 0 && (
                        <div className="mt-2 space-y-2">
                          <p className="text-sm text-gray-600">Selected files:</p>
                          {universityFiles.supporting_documents.map((file, index) => (
                            <div key={index} className="flex items-center justify-between bg-gray-50 p-2 rounded">
                              <span className="text-sm text-gray-700">{file.name}</span>
                              <Button
                                type="button"
                                variant="ghost"
                                size="sm"
                                onClick={() => removeFile(index)}
                                className="text-red-600 hover:text-red-800"
                              >
                                Remove
                              </Button>
                            </div>
                          ))}
                        </div>
                      )}
                    </div>
                    </CardContent>
                  </Card>

                  {/* Reason for Scholarship */}
                  <Card className="border-2 border-indigo-200 bg-indigo-50/50 shadow-sm hover:shadow-md transition-shadow">
                    <CardHeader className="pb-4">
                      <CardTitle className="text-xl font-bold text-gray-900 flex items-center">
                        <MessageSquare className="h-6 w-6 mr-3 text-indigo-600" />
                        Application Reason
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div>
                        <Label htmlFor="uni_reason_for_scholarship">Reason for the Scholarship *</Label>
                        <Textarea
                          id="uni_reason_for_scholarship"
                          value={universityFormData.reason_for_scholarship}
                          onChange={(e) => setUniversityFormData(prev => ({ ...prev, reason_for_scholarship: e.target.value }))}
                          placeholder="Please explain why you need this scholarship"
                          className="mt-1"
                          rows={4}
                          required
                        />
                      </div>
                    </CardContent>
                  </Card>

                  <div className="flex justify-center pt-8">
                    <Button
                      type="submit"
                      disabled={submitting || !validateForm()}
                      size="lg"
                      className="bg-gradient-to-r from-purple-600 via-violet-600 to-indigo-600 hover:from-purple-700 hover:via-violet-700 hover:to-indigo-700 text-white px-12 py-4 text-lg font-bold shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105"
                    >
                      {submitting ? (
                        <>
                          <Loader2 className="mr-3 h-6 w-6 animate-spin" />
                          Submitting Application...
                        </>
                      ) : (
                        <>
                          <CheckCircle className="mr-3 h-6 w-6" />
                          Submit University Application
                        </>
                      )}
                    </Button>
                  </div>
                </form>
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
