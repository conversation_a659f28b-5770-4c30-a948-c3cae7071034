'use client'

import React, { useState, useEffect } from 'react'
import { useSearchParams } from 'next/navigation'

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { <PERSON><PERSON>, <PERSON>bs<PERSON><PERSON>nt, <PERSON>bsList, TabsTrigger } from "@/components/ui/tabs"
import {
  BookOpen,
  GraduationCap,
  School,
  User,
  Phone,
  Mail,
  MapPin,
  Building,
  CreditCard,
  Camera,
  FileUp,
  Loader2,
  Upload,
  CheckCircle,
  AlertTriangle,
  MessageSquare
} from 'lucide-react'
import { useToast } from "@/hooks/use-toast"
import { apiClient } from '@/lib/api'
import SuccessMessage from './SuccessMessage'

// Form data interfaces for different categories
interface PrimarySchoolFormData {
  student_full_name: string
  age: string
  current_class: string
  father_name: string
  mother_name: string
  parent_phone: string
  home_address: string
  school_name: string
  headmaster_name: string
  school_account_number: string
  reason_for_scholarship: string
  current_school_fee: string
  supporting_information: string
}

interface SecondarySchoolFormData {
  student_full_name: string
  age: string
  class: string
  parent_phone: string
  address: string
  school_name: string
  principal_name: string
  principal_account_number: string
  reason_for_scholarship: string
  school_fee_amount: string
}

interface UniversityFormData {
  full_name: string
  age: string
  course_of_study: string
  current_level: string
  phone_number: string
  email_address: string
  matriculation_number: string
  reason_for_scholarship: string
}

export default function ScholarshipApplicationPage() {
  const searchParams = useSearchParams()
  const [selectedCategory, setSelectedCategory] = useState<'primary' | 'secondary' | 'university'>('primary')
  const [submitting, setSubmitting] = useState(false)
  const [showSuccess, setShowSuccess] = useState(false)
  const [scholarshipId, setScholarshipId] = useState<string | null>(null)
  const { toast } = useToast()

  // Handle URL parameters for category and scholarship ID
  useEffect(() => {
    const categoryParam = searchParams.get('category')
    const scholarshipIdParam = searchParams.get('scholarship_id')

    if (categoryParam && ['primary', 'secondary', 'university'].includes(categoryParam)) {
      setSelectedCategory(categoryParam as 'primary' | 'secondary' | 'university')
    }

    if (scholarshipIdParam) {
      setScholarshipId(scholarshipIdParam)
    }
  }, [searchParams])

  // Form data for different categories
  const [primaryFormData, setPrimaryFormData] = useState<PrimarySchoolFormData>({
    student_full_name: '',
    age: '',
    current_class: '',
    father_name: '',
    mother_name: '',
    parent_phone: '',
    home_address: '',
    school_name: '',
    headmaster_name: '',
    school_account_number: '',
    reason_for_scholarship: '',
    current_school_fee: '',
    supporting_information: ''
  })

  const [secondaryFormData, setSecondaryFormData] = useState<SecondarySchoolFormData>({
    student_full_name: '',
    age: '',
    class: '',
    parent_phone: '',
    address: '',
    school_name: '',
    principal_name: '',
    principal_account_number: '',
    reason_for_scholarship: '',
    school_fee_amount: ''
  })

  const [universityFormData, setUniversityFormData] = useState<UniversityFormData>({
    full_name: '',
    age: '',
    course_of_study: '',
    current_level: '',
    phone_number: '',
    email_address: '',
    matriculation_number: '',
    reason_for_scholarship: ''
  })

  // File uploads for different categories
  const [primaryFiles, setPrimaryFiles] = useState({
    student_picture: null as File | null
  })

  const [secondaryFiles, setSecondaryFiles] = useState({
    student_picture: null as File | null
  })

  const [universityFiles, setUniversityFiles] = useState({
    student_id_card: null as File | null,
    payment_evidence: null as File | null,
    supporting_documents: [] as File[]
  })

  // Category configuration - Updated to match platform design system
  const categoryConfig = {
    primary: {
      icon: BookOpen,
      title: "Primary School Scholarship",
      description: "For Primary 1-6 students (Filled by Parent/Guardian)",
      color: "bg-blue-500",
      bgColor: "bg-blue-50",
      textColor: "text-blue-700",
      borderColor: "border-blue-200"
    },
    secondary: {
      icon: School,
      title: "Secondary School Scholarship",
      description: "For Secondary school students (Filled by Student)",
      color: "bg-green-500",
      bgColor: "bg-green-50",
      textColor: "text-green-700",
      borderColor: "border-green-200"
    },
    university: {
      icon: GraduationCap,
      title: "University Scholarship",
      description: "For University students (Filled by Student)",
      color: "bg-purple-500",
      bgColor: "bg-purple-50",
      textColor: "text-purple-700",
      borderColor: "border-purple-200"
    }
  }

  const handleFileChange = (category: string, field: string, file: File | null) => {
    if (category === 'primary') {
      setPrimaryFiles(prev => ({ ...prev, [field]: file }))
    } else if (category === 'secondary') {
      setSecondaryFiles(prev => ({ ...prev, [field]: file }))
    } else if (category === 'university') {
      if (field === 'supporting_documents' && file) {
        setUniversityFiles(prev => ({ 
          ...prev, 
          supporting_documents: [...prev.supporting_documents, file] 
        }))
      } else {
        setUniversityFiles(prev => ({ ...prev, [field]: file }))
      }
    }
  }

  const handleMultipleFileChange = (files: FileList | null) => {
    if (files && selectedCategory === 'university') {
      const fileArray = Array.from(files)
      setUniversityFiles(prev => ({ 
        ...prev, 
        supporting_documents: [...prev.supporting_documents, ...fileArray] 
      }))
    }
  }

  const removeFile = (index: number) => {
    setUniversityFiles(prev => ({
      ...prev,
      supporting_documents: prev.supporting_documents.filter((_, i) => i !== index)
    }))
  }

  const validateForm = () => {
    if (selectedCategory === 'primary') {
      const required = ['student_full_name', 'age', 'current_class', 'father_name', 'mother_name', 'parent_phone', 'home_address', 'school_name', 'headmaster_name', 'school_account_number', 'reason_for_scholarship', 'current_school_fee']
      return required.every(field => primaryFormData[field as keyof PrimarySchoolFormData].trim() !== '') && primaryFiles.student_picture
    } else if (selectedCategory === 'secondary') {
      const required = ['student_full_name', 'age', 'class', 'parent_phone', 'address', 'school_name', 'principal_name', 'principal_account_number', 'reason_for_scholarship', 'school_fee_amount']
      return required.every(field => secondaryFormData[field as keyof SecondarySchoolFormData].trim() !== '') && secondaryFiles.student_picture
    } else if (selectedCategory === 'university') {
      const required = ['full_name', 'age', 'course_of_study', 'current_level', 'phone_number', 'email_address', 'matriculation_number', 'reason_for_scholarship']
      return required.every(field => universityFormData[field as keyof UniversityFormData].trim() !== '') && 
             universityFiles.student_id_card && universityFiles.payment_evidence
    }
    return false
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!validateForm()) {
      toast({
        title: "Validation Error",
        description: "Please fill in all required fields and upload required documents",
        variant: "destructive"
      })
      return
    }

    try {
      setSubmitting(true)
      
      const formData = new FormData()
      formData.append('category', selectedCategory)
      
      // Add form data based on category
      if (selectedCategory === 'primary') {
        formData.append('form_data', JSON.stringify(primaryFormData))
        if (primaryFiles.student_picture) {
          formData.append('student_picture', primaryFiles.student_picture)
        }
      } else if (selectedCategory === 'secondary') {
        formData.append('form_data', JSON.stringify(secondaryFormData))
        if (secondaryFiles.student_picture) {
          formData.append('student_picture', secondaryFiles.student_picture)
        }
      } else if (selectedCategory === 'university') {
        formData.append('form_data', JSON.stringify(universityFormData))
        if (universityFiles.student_id_card) {
          formData.append('student_id_card', universityFiles.student_id_card)
        }
        if (universityFiles.payment_evidence) {
          formData.append('payment_evidence', universityFiles.payment_evidence)
        }
        universityFiles.supporting_documents.forEach((file, index) => {
          formData.append(`supporting_documents[${index}]`, file)
        })
      }

      // Check if scholarship ID is available
      if (!scholarshipId) {
        toast({
          title: "Error",
          description: "Scholarship ID is required for application submission.",
          variant: "destructive"
        })
        return
      }

      const response = await apiClient.submitScholarshipApplication(scholarshipId, formData)

      if (response.success) {
        // Show success message
        setShowSuccess(true)

        // Reset form
        if (selectedCategory === 'primary') {
          setPrimaryFormData({
            student_full_name: '', age: '', current_class: '', father_name: '', mother_name: '',
            parent_phone: '', home_address: '', school_name: '', headmaster_name: '',
            school_account_number: '', reason_for_scholarship: '', current_school_fee: '',
            supporting_information: ''
          })
          setPrimaryFiles({ student_picture: null })
        } else if (selectedCategory === 'secondary') {
          setSecondaryFormData({
            student_full_name: '', age: '', class: '', parent_phone: '', address: '',
            school_name: '', principal_name: '', principal_account_number: '',
            reason_for_scholarship: '', school_fee_amount: ''
          })
          setSecondaryFiles({ student_picture: null })
        } else if (selectedCategory === 'university') {
          setUniversityFormData({
            full_name: '', age: '', course_of_study: '', current_level: '',
            phone_number: '', email_address: '', matriculation_number: '',
            reason_for_scholarship: ''
          })
          setUniversityFiles({ student_id_card: null, payment_evidence: null, supporting_documents: [] })
        }
      } else {
        toast({
          title: "Error",
          description: response.message || "Failed to submit application",
          variant: "destructive"
        })
      }
    } catch (error: any) {
      console.error('Error submitting application:', error)
      toast({
        title: "Error",
        description: error.response?.data?.message || "Failed to submit application",
        variant: "destructive"
      })
    } finally {
      setSubmitting(false)
    }
  }

  const handleBackToForm = () => {
    setShowSuccess(false)
  }

  const handleGoHome = () => {
    // Navigate to dashboard or home page
    window.location.href = '/dashboard'
  }

  if (showSuccess) {
    return <SuccessMessage onBackToForm={handleBackToForm} onGoHome={handleGoHome} />
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* Hero Section */}
      <section className="relative py-12 sm:py-16 md:py-20 bg-gradient-to-br from-green-600 to-green-800 text-white">
        <div className="container mx-auto px-3 sm:px-4 md:px-6 lg:px-8">
          <div className="max-w-4xl mx-auto text-center space-y-4 sm:space-y-6">
            <h1 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl xl:text-6xl font-bold tracking-tight leading-tight">
              Scholarship Application
            </h1>
            <p className="text-base sm:text-lg md:text-xl text-green-100 max-w-3xl mx-auto px-2">
              Choose your scholarship category and complete the application form
            </p>
          </div>
        </div>
      </section>

      {/* Main Content */}
      <section className="py-6 sm:py-8 md:py-12 bg-white dark:bg-gray-900">
        <div className="container mx-auto px-3 sm:px-4 md:px-6 lg:px-8">
          <div className="max-w-4xl mx-auto">
            <div className="mb-4 sm:mb-6 md:mb-8">
              <h2 className="text-xl sm:text-2xl md:text-3xl font-bold tracking-tight mb-2 text-center sm:text-left">Apply for Scholarship</h2>
              <p className="text-sm sm:text-base text-gray-600 dark:text-gray-400 text-center sm:text-left">
                Select your education level and fill out the appropriate form
              </p>
            </div>
            {/* Category Selection */}
            <Tabs value={selectedCategory} onValueChange={(value) => setSelectedCategory(value as any)} className="w-full">
              <TabsList className="grid w-full grid-cols-1 sm:grid-cols-3 mb-4 sm:mb-6 h-auto p-1 gap-1 sm:gap-2 bg-gray-100 rounded-lg">
                {Object.entries(categoryConfig).map(([key, config]) => {
                  const IconComponent = config.icon
                  return (
                    <TabsTrigger
                      key={key}
                      value={key}
                      className="flex flex-col sm:flex-col items-center p-2 sm:p-3 space-y-1 sm:space-y-2 rounded-md transition-all duration-300 data-[state=active]:bg-white data-[state=active]:shadow-md min-h-[60px] sm:min-h-[80px]"
                    >
                      <div className="p-1.5 sm:p-2 rounded-full bg-green-600 text-white shadow-sm">
                        <IconComponent className="h-3 w-3 sm:h-4 sm:w-4" />
                      </div>
                      <div className="text-center">
                        <div className="font-medium text-xs sm:text-xs text-gray-900 leading-tight">{config.title}</div>
                        <div className="text-xs sm:text-xs text-gray-600 mt-0.5 hidden sm:block">{config.description}</div>
                      </div>
                    </TabsTrigger>
                  )
                })}
              </TabsList>

              {/* Primary School Form */}
              <TabsContent value="primary" className="space-y-4 sm:space-y-6 md:space-y-8">
                <form onSubmit={handleSubmit} className="space-y-4 sm:space-y-6 md:space-y-8">
                  <div className="bg-green-50 p-3 sm:p-4 md:p-6 rounded-lg border border-green-200">
                    <div className="flex items-start sm:items-center">
                      <AlertTriangle className="h-4 w-4 sm:h-5 sm:w-5 text-green-600 mr-2 sm:mr-3 mt-0.5 sm:mt-0 flex-shrink-0" />
                      <p className="text-green-800 font-medium text-sm sm:text-base">
                        This form should be filled by a Parent or Guardian on behalf of the student
                      </p>
                    </div>
                  </div>

                  {/* Student Information */}
                  <div className="space-y-4 sm:space-y-6">
                    <div>
                      <h3 className="text-base sm:text-lg font-semibold text-green-800 dark:text-green-200 mb-3 sm:mb-4 flex items-center">
                        <User className="h-4 w-4 sm:h-5 sm:w-5 mr-2 text-green-600" />
                        Student Information
                      </h3>
                      <div className="space-y-3 sm:space-y-4">
                        <div className="space-y-2">
                          <Label htmlFor="student_full_name" className="text-green-800 dark:text-green-200 text-sm sm:text-base">
                            Student Full Name *
                          </Label>
                          <div className="relative">
                            <User className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-green-500" />
                            <Input
                              id="student_full_name"
                              value={primaryFormData.student_full_name}
                              onChange={(e) => setPrimaryFormData(prev => ({ ...prev, student_full_name: e.target.value }))}
                              placeholder="Enter student's full name"
                              className="pl-10 rounded-xl border-green-200 dark:border-green-700 focus:border-green-500 text-sm sm:text-base"
                              required
                            />
                          </div>
                        </div>

                        <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4">
                          <div className="space-y-2">
                            <Label htmlFor="age" className="text-green-800 dark:text-green-200 text-sm sm:text-base">
                              Age *
                            </Label>
                            <Input
                              id="age"
                              type="number"
                              value={primaryFormData.age}
                              onChange={(e) => setPrimaryFormData(prev => ({ ...prev, age: e.target.value }))}
                              placeholder="Age"
                              className="rounded-xl border-green-200 dark:border-green-700 focus:border-green-500 text-sm sm:text-base"
                              required
                            />
                          </div>
                          <div className="space-y-2">
                            <Label htmlFor="current_class" className="text-green-800 dark:text-green-200 text-sm sm:text-base">
                              Current Class *
                            </Label>
                            <Select value={primaryFormData.current_class} onValueChange={(value) => setPrimaryFormData(prev => ({ ...prev, current_class: value }))}>
                              <SelectTrigger className="rounded-xl border-green-200 dark:border-green-700 focus:border-green-500 text-sm sm:text-base">
                                <SelectValue placeholder="Select class" />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="Primary 1">Primary 1</SelectItem>
                                <SelectItem value="Primary 2">Primary 2</SelectItem>
                                <SelectItem value="Primary 3">Primary 3</SelectItem>
                                <SelectItem value="Primary 4">Primary 4</SelectItem>
                                <SelectItem value="Primary 5">Primary 5</SelectItem>
                                <SelectItem value="Primary 6">Primary 6</SelectItem>
                              </SelectContent>
                            </Select>
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Parent Information */}
                    <div>
                      <h3 className="text-lg font-semibold text-green-800 dark:text-green-200 mb-4 flex items-center">
                        <User className="h-5 w-5 mr-2 text-green-600" />
                        Parent/Guardian Information
                      </h3>
                      <div className="space-y-4">
                        <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4">
                          <div className="space-y-2">
                            <Label htmlFor="father_name" className="text-green-800 dark:text-green-200">
                              Father's Name *
                            </Label>
                            <div className="relative">
                              <User className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-green-500" />
                              <Input
                                id="father_name"
                                value={primaryFormData.father_name}
                                onChange={(e) => setPrimaryFormData(prev => ({ ...prev, father_name: e.target.value }))}
                                placeholder="Enter father's name"
                                className="pl-10 rounded-xl border-green-200 dark:border-green-700 focus:border-green-500"
                                required
                              />
                            </div>
                          </div>
                          <div className="space-y-2">
                            <Label htmlFor="mother_name" className="text-green-800 dark:text-green-200">
                              Mother's Name *
                            </Label>
                            <Input
                              id="mother_name"
                              value={primaryFormData.mother_name}
                              onChange={(e) => setPrimaryFormData(prev => ({ ...prev, mother_name: e.target.value }))}
                              placeholder="Enter mother's name"
                              className="rounded-xl border-green-200 dark:border-green-700 focus:border-green-500"
                              required
                            />
                          </div>
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="parent_phone" className="text-green-800 dark:text-green-200">
                            Father's or Mother's Phone Number *
                          </Label>
                          <div className="relative">
                            <Phone className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-green-500" />
                            <Input
                              id="parent_phone"
                              type="tel"
                              value={primaryFormData.parent_phone}
                              onChange={(e) => setPrimaryFormData(prev => ({ ...prev, parent_phone: e.target.value }))}
                              placeholder="Enter phone number"
                              className="pl-10 rounded-xl border-green-200 dark:border-green-700 focus:border-green-500"
                              required
                            />
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Address Information */}
                  <div>
                    <h3 className="text-lg font-semibold text-green-800 dark:text-green-200 mb-4 flex items-center">
                      <MapPin className="h-5 w-5 mr-2 text-green-600" />
                      Address Information
                    </h3>
                    <div className="space-y-2">
                      <Label htmlFor="home_address" className="text-green-800 dark:text-green-200">
                        Home Address *
                      </Label>
                      <div className="relative">
                        <MapPin className="absolute left-3 top-3 h-4 w-4 text-green-500" />
                        <Textarea
                          id="home_address"
                          value={primaryFormData.home_address}
                          onChange={(e) => setPrimaryFormData(prev => ({ ...prev, home_address: e.target.value }))}
                          placeholder="Enter complete home address"
                          className="pl-10 rounded-xl border-green-200 dark:border-green-700 focus:border-green-500"
                          rows={3}
                          required
                        />
                      </div>
                    </div>
                  </div>

                  {/* School Information */}
                  <div>
                    <h3 className="text-lg font-semibold text-green-800 dark:text-green-200 mb-4 flex items-center">
                      <Building className="h-5 w-5 mr-2 text-green-600" />
                      School Information
                    </h3>
                    <div className="space-y-4">
                      <div className="space-y-2">
                        <Label htmlFor="school_name" className="text-green-800 dark:text-green-200">
                          Name of the School *
                        </Label>
                        <div className="relative">
                          <Building className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-green-500" />
                          <Input
                            id="school_name"
                            value={primaryFormData.school_name}
                            onChange={(e) => setPrimaryFormData(prev => ({ ...prev, school_name: e.target.value }))}
                            placeholder="Enter school name"
                            className="pl-10 rounded-xl border-green-200 dark:border-green-700 focus:border-green-500"
                            required
                          />
                        </div>
                      </div>

                      <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4">
                        <div className="space-y-2">
                          <Label htmlFor="headmaster_name" className="text-green-800 dark:text-green-200">
                            Name of Headmaster/Director *
                          </Label>
                          <Input
                            id="headmaster_name"
                            value={primaryFormData.headmaster_name}
                            onChange={(e) => setPrimaryFormData(prev => ({ ...prev, headmaster_name: e.target.value }))}
                            placeholder="Enter headmaster's name"
                            className="rounded-xl border-green-200 dark:border-green-700 focus:border-green-500"
                            required
                          />
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="school_account_number" className="text-green-800 dark:text-green-200">
                            School Account Number *
                          </Label>
                          <Input
                            id="school_account_number"
                            value={primaryFormData.school_account_number}
                            onChange={(e) => setPrimaryFormData(prev => ({ ...prev, school_account_number: e.target.value }))}
                            placeholder="Enter account number for scholarship payment"
                            className="rounded-xl border-green-200 dark:border-green-700 focus:border-green-500"
                            required
                          />
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Financial & Additional Information */}
                  <div>
                    <h3 className="text-lg font-semibold text-green-800 dark:text-green-200 mb-4 flex items-center">
                      <CreditCard className="h-5 w-5 mr-2 text-green-600" />
                      Financial & Additional Information
                    </h3>
                    <div className="space-y-4">
                      <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4">
                        <div className="space-y-2">
                          <Label htmlFor="current_school_fee" className="text-green-800 dark:text-green-200">
                            Current School Fee Amount *
                          </Label>
                          <div className="relative">
                            <CreditCard className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-green-500" />
                            <Input
                              id="current_school_fee"
                              type="number"
                              value={primaryFormData.current_school_fee}
                              onChange={(e) => setPrimaryFormData(prev => ({ ...prev, current_school_fee: e.target.value }))}
                              placeholder="Enter amount in Naira"
                              className="pl-10 rounded-xl border-green-200 dark:border-green-700 focus:border-green-500"
                              required
                            />
                          </div>
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="student_picture" className="text-green-800 dark:text-green-200">
                            Upload Student Picture *
                          </Label>
                          <Input
                            id="student_picture"
                            type="file"
                            accept="image/*"
                            onChange={(e) => handleFileChange('primary', 'student_picture', e.target.files?.[0] || null)}
                            className="rounded-xl border-green-200 dark:border-green-700 focus:border-green-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-green-50 file:text-green-700 hover:file:bg-green-100"
                            required
                          />
                        </div>
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="reason_for_scholarship" className="text-green-800 dark:text-green-200">
                          Reason for the Scholarship *
                        </Label>
                        <Textarea
                          id="reason_for_scholarship"
                          value={primaryFormData.reason_for_scholarship}
                          onChange={(e) => setPrimaryFormData(prev => ({ ...prev, reason_for_scholarship: e.target.value }))}
                          placeholder="Please explain why your child needs this scholarship"
                          className="rounded-xl border-green-200 dark:border-green-700 focus:border-green-500"
                          rows={4}
                          required
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="supporting_information" className="text-green-800 dark:text-green-200">
                          Any Other Supporting Information
                        </Label>
                        <Textarea
                          id="supporting_information"
                          value={primaryFormData.supporting_information}
                          onChange={(e) => setPrimaryFormData(prev => ({ ...prev, supporting_information: e.target.value }))}
                          placeholder="Any additional information that supports your application"
                          className="rounded-xl border-green-200 dark:border-green-700 focus:border-green-500"
                          rows={3}
                        />
                      </div>
                    </div>
                  </div>

                  <div className="flex justify-center pt-6 sm:pt-8">
                    <Button
                      type="submit"
                      disabled={submitting || !validateForm()}
                      size="lg"
                      className="w-full sm:w-auto bg-green-600 hover:bg-green-700 text-white px-6 sm:px-12 py-3 sm:py-4 text-base sm:text-lg font-semibold shadow-lg hover:shadow-xl transition-all duration-300"
                    >
                      {submitting ? (
                        <>
                          <Loader2 className="mr-2 sm:mr-3 h-5 w-5 sm:h-6 sm:w-6 animate-spin" />
                          <span className="text-sm sm:text-base">Submitting...</span>
                        </>
                      ) : (
                        <>
                          <CheckCircle className="mr-2 sm:mr-3 h-5 w-5 sm:h-6 sm:w-6" />
                          <span className="text-sm sm:text-base">Submit Primary School Application</span>
                        </>
                      )}
                    </Button>
                  </div>
                </form>
              </TabsContent>

              {/* Secondary School Form */}
              <TabsContent value="secondary" className="space-y-4 sm:space-y-6 md:space-y-8">
                <form onSubmit={handleSubmit} className="space-y-4 sm:space-y-6 md:space-y-8">
                  <div className="bg-green-50 p-3 sm:p-4 md:p-6 rounded-lg border border-green-200">
                    <div className="flex items-start sm:items-center">
                      <AlertTriangle className="h-4 w-4 sm:h-5 sm:w-5 text-green-600 mr-2 sm:mr-3 mt-0.5 sm:mt-0 flex-shrink-0" />
                      <p className="text-green-800 font-medium text-sm sm:text-base">
                        This form should be filled by the Student directly
                      </p>
                    </div>
                  </div>

                  {/* Student Information */}
                  <div className="space-y-6">
                    <div>
                      <h3 className="text-lg font-semibold text-green-800 dark:text-green-200 mb-4 flex items-center">
                        <User className="h-5 w-5 mr-2 text-green-600" />
                        Student Information
                      </h3>
                      <div className="space-y-4">
                        <div className="space-y-2">
                          <Label htmlFor="sec_student_full_name" className="text-green-800 dark:text-green-200">
                            Student Full Name *
                          </Label>
                          <div className="relative">
                            <User className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-green-500" />
                            <Input
                              id="sec_student_full_name"
                              value={secondaryFormData.student_full_name}
                              onChange={(e) => setSecondaryFormData(prev => ({ ...prev, student_full_name: e.target.value }))}
                              placeholder="Enter your full name"
                              className="pl-10 rounded-xl border-green-200 dark:border-green-700 focus:border-green-500"
                              required
                            />
                          </div>
                        </div>

                        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 sm:gap-4">
                          <div className="space-y-2">
                            <Label htmlFor="sec_age" className="text-green-800 dark:text-green-200">
                              Age *
                            </Label>
                            <Input
                              id="sec_age"
                              type="number"
                              value={secondaryFormData.age}
                              onChange={(e) => setSecondaryFormData(prev => ({ ...prev, age: e.target.value }))}
                              placeholder="Your age"
                              className="rounded-xl border-green-200 dark:border-green-700 focus:border-green-500"
                              required
                            />
                          </div>
                          <div className="space-y-2">
                            <Label htmlFor="sec_class" className="text-green-800 dark:text-green-200">
                              Class *
                            </Label>
                            <Select value={secondaryFormData.class} onValueChange={(value) => setSecondaryFormData(prev => ({ ...prev, class: value }))}>
                              <SelectTrigger className="rounded-xl border-green-200 dark:border-green-700 focus:border-green-500">
                                <SelectValue placeholder="Select class" />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="JSS 1">JSS 1</SelectItem>
                                <SelectItem value="JSS 2">JSS 2</SelectItem>
                                <SelectItem value="JSS 3">JSS 3</SelectItem>
                                <SelectItem value="SS 1">SS 1</SelectItem>
                                <SelectItem value="SS 2">SS 2</SelectItem>
                                <SelectItem value="SS 3">SS 3</SelectItem>
                              </SelectContent>
                            </Select>
                          </div>
                          <div className="space-y-2">
                            <Label htmlFor="sec_parent_phone" className="text-green-800 dark:text-green-200">
                              Parent's Phone Number *
                            </Label>
                            <div className="relative">
                              <Phone className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-green-500" />
                              <Input
                                id="sec_parent_phone"
                                type="tel"
                                value={secondaryFormData.parent_phone}
                                onChange={(e) => setSecondaryFormData(prev => ({ ...prev, parent_phone: e.target.value }))}
                                placeholder="Enter parent's phone number"
                                className="pl-10 rounded-xl border-green-200 dark:border-green-700 focus:border-green-500"
                                required
                              />
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Address and School Information */}
                    <div>
                      <h3 className="text-lg font-semibold text-green-800 dark:text-green-200 mb-4 flex items-center">
                        <MapPin className="h-5 w-5 mr-2 text-green-600" />
                        Address & School Information
                      </h3>
                      <div className="space-y-4">
                        <div className="space-y-2">
                          <Label htmlFor="sec_address" className="text-green-800 dark:text-green-200">
                            Address *
                          </Label>
                          <div className="relative">
                            <MapPin className="absolute left-3 top-3 h-4 w-4 text-green-500" />
                            <Textarea
                              id="sec_address"
                              value={secondaryFormData.address}
                              onChange={(e) => setSecondaryFormData(prev => ({ ...prev, address: e.target.value }))}
                              placeholder="Enter your complete address"
                              className="pl-10 rounded-xl border-green-200 dark:border-green-700 focus:border-green-500"
                              rows={3}
                              required
                            />
                          </div>
                        </div>

                        <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4">
                          <div className="space-y-2">
                            <Label htmlFor="sec_school_name" className="text-green-800 dark:text-green-200">
                              School Name *
                            </Label>
                            <div className="relative">
                              <Building className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-green-500" />
                              <Input
                                id="sec_school_name"
                                value={secondaryFormData.school_name}
                                onChange={(e) => setSecondaryFormData(prev => ({ ...prev, school_name: e.target.value }))}
                                placeholder="Enter your school name"
                                className="pl-10 rounded-xl border-green-200 dark:border-green-700 focus:border-green-500"
                                required
                              />
                            </div>
                          </div>

                          <div className="space-y-2">
                            <Label htmlFor="sec_principal_name" className="text-green-800 dark:text-green-200">
                              School Principal or Director Name *
                            </Label>
                            <Input
                              id="sec_principal_name"
                              value={secondaryFormData.principal_name}
                              onChange={(e) => setSecondaryFormData(prev => ({ ...prev, principal_name: e.target.value }))}
                              placeholder="Enter principal's name"
                              className="rounded-xl border-green-200 dark:border-green-700 focus:border-green-500"
                              required
                            />
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Financial Information */}
                  <div>
                    <h3 className="text-lg font-semibold text-green-800 dark:text-green-200 mb-4 flex items-center">
                      <CreditCard className="h-5 w-5 mr-2 text-green-600" />
                      Financial Information
                    </h3>
                    <div className="space-y-4">
                      <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4">
                        <div className="space-y-2">
                          <Label htmlFor="sec_principal_account_number" className="text-green-800 dark:text-green-200">
                            Principal or Financial Officer Account Number *
                          </Label>
                          <div className="relative">
                            <CreditCard className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-green-500" />
                            <Input
                              id="sec_principal_account_number"
                              value={secondaryFormData.principal_account_number}
                              onChange={(e) => setSecondaryFormData(prev => ({ ...prev, principal_account_number: e.target.value }))}
                              placeholder="Enter account number for scholarship payment"
                              className="pl-10 rounded-xl border-green-200 dark:border-green-700 focus:border-green-500"
                              required
                            />
                          </div>
                        </div>

                        <div className="space-y-2">
                          <Label htmlFor="sec_school_fee_amount" className="text-green-800 dark:text-green-200">
                            School Fee Amount *
                          </Label>
                          <Input
                            id="sec_school_fee_amount"
                            type="number"
                            value={secondaryFormData.school_fee_amount}
                            onChange={(e) => setSecondaryFormData(prev => ({ ...prev, school_fee_amount: e.target.value }))}
                            placeholder="Enter amount in Naira"
                            className="rounded-xl border-green-200 dark:border-green-700 focus:border-green-500"
                            required
                          />
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Picture and Application Reason */}
                  <div>
                    <h3 className="text-lg font-semibold text-green-800 dark:text-green-200 mb-4 flex items-center">
                      <FileUp className="h-5 w-5 mr-2 text-green-600" />
                      Picture & Application Reason
                    </h3>
                    <div className="space-y-4">
                      <div className="space-y-2">
                        <Label htmlFor="sec_student_picture" className="text-green-800 dark:text-green-200">
                          Upload Student Picture *
                        </Label>
                        <Input
                          id="sec_student_picture"
                          type="file"
                          accept="image/*"
                          onChange={(e) => handleFileChange('secondary', 'student_picture', e.target.files?.[0] || null)}
                          className="rounded-xl border-green-200 dark:border-green-700 focus:border-green-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-green-50 file:text-green-700 hover:file:bg-green-100"
                          required
                        />
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="sec_reason_for_scholarship" className="text-green-800 dark:text-green-200">
                          Reason for the Scholarship *
                        </Label>
                        <Textarea
                          id="sec_reason_for_scholarship"
                          value={secondaryFormData.reason_for_scholarship}
                          onChange={(e) => setSecondaryFormData(prev => ({ ...prev, reason_for_scholarship: e.target.value }))}
                          placeholder="Please explain why you need this scholarship"
                          className="rounded-xl border-green-200 dark:border-green-700 focus:border-green-500"
                          rows={4}
                          required
                        />
                      </div>
                    </div>
                  </div>

                  <div className="flex justify-center pt-6 sm:pt-8">
                    <Button
                      type="submit"
                      disabled={submitting || !validateForm()}
                      size="lg"
                      className="w-full sm:w-auto bg-gradient-to-r from-green-600 via-emerald-600 to-teal-600 hover:from-green-700 hover:via-emerald-700 hover:to-teal-700 text-white px-6 sm:px-12 py-3 sm:py-4 text-base sm:text-lg font-bold shadow-lg hover:shadow-xl transition-all duration-300"
                    >
                      {submitting ? (
                        <>
                          <Loader2 className="mr-2 sm:mr-3 h-5 w-5 sm:h-6 sm:w-6 animate-spin" />
                          <span className="text-sm sm:text-base">Submitting...</span>
                        </>
                      ) : (
                        <>
                          <CheckCircle className="mr-2 sm:mr-3 h-5 w-5 sm:h-6 sm:w-6" />
                          <span className="text-sm sm:text-base">Submit Secondary School Application</span>
                        </>
                      )}
                    </Button>
                  </div>
                </form>
              </TabsContent>

              {/* University Form */}
              <TabsContent value="university" className="space-y-4 sm:space-y-6 md:space-y-8">
                <form onSubmit={handleSubmit} className="space-y-4 sm:space-y-6 md:space-y-8">
                  <div className="bg-green-50 p-3 sm:p-4 md:p-6 rounded-lg border border-green-200">
                    <div className="flex items-start sm:items-center">
                      <AlertTriangle className="h-4 w-4 sm:h-5 sm:w-5 text-green-600 mr-2 sm:mr-3 mt-0.5 sm:mt-0 flex-shrink-0" />
                      <p className="text-green-800 font-medium text-sm sm:text-base">
                        This form should be filled by the University Student directly
                      </p>
                    </div>
                  </div>

                  {/* Personal Information */}
                  <div>
                    <h3 className="text-lg font-semibold text-green-800 dark:text-green-200 mb-4 flex items-center">
                      <User className="h-5 w-5 mr-2 text-green-600" />
                      Personal Information
                    </h3>
                    <div className="space-y-4">
                      <div className="space-y-2">
                        <Label htmlFor="uni_full_name" className="text-green-800 dark:text-green-200">
                          Full Name *
                        </Label>
                        <div className="relative">
                          <User className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-green-500" />
                          <Input
                            id="uni_full_name"
                            value={universityFormData.full_name}
                            onChange={(e) => setUniversityFormData(prev => ({ ...prev, full_name: e.target.value }))}
                            placeholder="Enter your full name"
                            className="pl-10 rounded-xl border-green-200 dark:border-green-700 focus:border-green-500"
                            required
                          />
                        </div>
                      </div>

                      <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4">
                        <div className="space-y-2">
                          <Label htmlFor="uni_age" className="text-green-800 dark:text-green-200">
                            Age *
                          </Label>
                          <Input
                            id="uni_age"
                            type="number"
                            value={universityFormData.age}
                            onChange={(e) => setUniversityFormData(prev => ({ ...prev, age: e.target.value }))}
                            placeholder="Your age"
                            className="rounded-xl border-green-200 dark:border-green-700 focus:border-green-500"
                            required
                          />
                        </div>

                        <div className="space-y-2">
                          <Label htmlFor="uni_phone_number" className="text-green-800 dark:text-green-200">
                            Phone Number *
                          </Label>
                          <div className="relative">
                            <Phone className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-green-500" />
                            <Input
                              id="uni_phone_number"
                              type="tel"
                              value={universityFormData.phone_number}
                              onChange={(e) => setUniversityFormData(prev => ({ ...prev, phone_number: e.target.value }))}
                              placeholder="Enter your phone number"
                              className="pl-10 rounded-xl border-green-200 dark:border-green-700 focus:border-green-500"
                              required
                            />
                          </div>
                        </div>
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="uni_email_address" className="text-green-800 dark:text-green-200">
                          Email Address *
                        </Label>
                        <div className="relative">
                          <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-green-500" />
                          <Input
                            id="uni_email_address"
                            type="email"
                            value={universityFormData.email_address}
                            onChange={(e) => setUniversityFormData(prev => ({ ...prev, email_address: e.target.value }))}
                            placeholder="Enter your email address"
                            className="pl-10 rounded-xl border-green-200 dark:border-green-700 focus:border-green-500"
                            required
                          />
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Academic Information */}
                  <div>
                    <h3 className="text-lg font-semibold text-green-800 dark:text-green-200 mb-4 flex items-center">
                      <GraduationCap className="h-5 w-5 mr-2 text-green-600" />
                      Academic Information
                    </h3>
                    <div className="space-y-4">
                      <div className="space-y-2">
                        <Label htmlFor="uni_course_of_study" className="text-green-800 dark:text-green-200">
                          Course of Study *
                        </Label>
                        <Input
                          id="uni_course_of_study"
                          value={universityFormData.course_of_study}
                          onChange={(e) => setUniversityFormData(prev => ({ ...prev, course_of_study: e.target.value }))}
                          placeholder="Enter your course of study"
                          className="rounded-xl border-green-200 dark:border-green-700 focus:border-green-500"
                          required
                        />
                      </div>

                      <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4">
                        <div className="space-y-2">
                          <Label htmlFor="uni_current_level" className="text-green-800 dark:text-green-200">
                            Current Level *
                          </Label>
                          <Select value={universityFormData.current_level} onValueChange={(value) => setUniversityFormData(prev => ({ ...prev, current_level: value }))}>
                            <SelectTrigger className="rounded-xl border-green-200 dark:border-green-700 focus:border-green-500">
                              <SelectValue placeholder="Select your current level" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="100L">100 Level</SelectItem>
                              <SelectItem value="200L">200 Level</SelectItem>
                              <SelectItem value="300L">300 Level</SelectItem>
                              <SelectItem value="400L">400 Level</SelectItem>
                              <SelectItem value="500L">500 Level</SelectItem>
                              <SelectItem value="600L">600 Level</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>

                        <div className="space-y-2">
                          <Label htmlFor="uni_matriculation_number" className="text-green-800 dark:text-green-200">
                            Matriculation Number *
                          </Label>
                          <Input
                            id="uni_matriculation_number"
                            value={universityFormData.matriculation_number}
                            onChange={(e) => setUniversityFormData(prev => ({ ...prev, matriculation_number: e.target.value }))}
                            placeholder="Enter your matriculation number"
                            className="rounded-xl border-green-200 dark:border-green-700 focus:border-green-500"
                            required
                          />
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Required Documents */}
                  <div>
                    <h3 className="text-lg font-semibold text-green-800 dark:text-green-200 mb-4 flex items-center">
                      <FileUp className="h-5 w-5 mr-2 text-green-600" />
                      Required Documents
                    </h3>
                    <div className="space-y-4">
                      <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4">
                        <div className="space-y-2">
                          <Label htmlFor="uni_student_id_card" className="text-green-800 dark:text-green-200">
                            Upload Student ID Card *
                          </Label>
                          <Input
                            id="uni_student_id_card"
                            type="file"
                            accept="image/*,.pdf"
                            onChange={(e) => handleFileChange('university', 'student_id_card', e.target.files?.[0] || null)}
                            className="rounded-xl border-green-200 dark:border-green-700 focus:border-green-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-green-50 file:text-green-700 hover:file:bg-green-100"
                            required
                          />
                        </div>

                        <div className="space-y-2">
                          <Label htmlFor="uni_payment_evidence" className="text-green-800 dark:text-green-200">
                            Upload Payment Evidence (Remita/Screenshot) *
                          </Label>
                          <Input
                            id="uni_payment_evidence"
                            type="file"
                            accept="image/*,.pdf"
                            onChange={(e) => handleFileChange('university', 'payment_evidence', e.target.files?.[0] || null)}
                            className="rounded-xl border-green-200 dark:border-green-700 focus:border-green-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-green-50 file:text-green-700 hover:file:bg-green-100"
                            required
                          />
                        </div>
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="uni_supporting_documents" className="text-green-800 dark:text-green-200">
                          Upload Supporting Documents (PDF, DOCX, JPG)
                        </Label>
                        <Input
                          id="uni_supporting_documents"
                          type="file"
                          accept=".pdf,.docx,.doc,.jpg,.jpeg,.png"
                          multiple
                          onChange={(e) => handleMultipleFileChange(e.target.files)}
                          className="rounded-xl border-green-200 dark:border-green-700 focus:border-green-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-green-50 file:text-green-700 hover:file:bg-green-100"
                        />
                        {universityFiles.supporting_documents.length > 0 && (
                          <div className="mt-2 space-y-2">
                            <p className="text-sm text-green-600">Selected files:</p>
                            {universityFiles.supporting_documents.map((file, index) => (
                              <div key={index} className="flex items-center justify-between bg-green-50 p-2 rounded-xl border border-green-200">
                                <span className="text-sm text-green-700">{file.name}</span>
                                <Button
                                  type="button"
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => removeFile(index)}
                                  className="text-red-600 hover:text-red-800"
                                >
                                  Remove
                                </Button>
                              </div>
                            ))}
                          </div>
                        )}
                      </div>
                    </div>
                  </div>

                  {/* Application Reason */}
                  <div>
                    <h3 className="text-lg font-semibold text-green-800 dark:text-green-200 mb-4 flex items-center">
                      <MessageSquare className="h-5 w-5 mr-2 text-green-600" />
                      Application Reason
                    </h3>
                    <div className="space-y-2">
                      <Label htmlFor="uni_reason_for_scholarship" className="text-green-800 dark:text-green-200">
                        Reason for the Scholarship *
                      </Label>
                      <Textarea
                        id="uni_reason_for_scholarship"
                        value={universityFormData.reason_for_scholarship}
                        onChange={(e) => setUniversityFormData(prev => ({ ...prev, reason_for_scholarship: e.target.value }))}
                        placeholder="Please explain why you need this scholarship"
                        className="rounded-xl border-green-200 dark:border-green-700 focus:border-green-500"
                        rows={4}
                        required
                      />
                    </div>
                  </div>

                  <div className="flex justify-center pt-6 sm:pt-8">
                    <Button
                      type="submit"
                      disabled={submitting || !validateForm()}
                      size="lg"
                      className="w-full sm:w-auto bg-gradient-to-r from-green-600 via-emerald-600 to-teal-600 hover:from-green-700 hover:via-emerald-700 hover:to-teal-700 text-white px-6 sm:px-12 py-3 sm:py-4 text-base sm:text-lg font-bold shadow-lg hover:shadow-xl transition-all duration-300"
                    >
                      {submitting ? (
                        <>
                          <Loader2 className="mr-2 sm:mr-3 h-5 w-5 sm:h-6 sm:w-6 animate-spin" />
                          <span className="text-sm sm:text-base">Submitting...</span>
                        </>
                      ) : (
                        <>
                          <CheckCircle className="mr-2 sm:mr-3 h-5 w-5 sm:h-6 sm:w-6" />
                          <span className="text-sm sm:text-base">Submit University Application</span>
                        </>
                      )}
                    </Button>
                  </div>
                </form>
              </TabsContent>
            </Tabs>
          </div>
        </div>
      </section>
    </div>
  )
}
