<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\User;
use App\Models\Scholarship;
use App\Models\ScholarshipApplication;
use App\Models\ScholarshipApplicationFile;
use Illuminate\Support\Facades\Storage;

class TestScholarshipApplicationsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $users = User::all();
        $scholarships = Scholarship::all();

        if ($users->isEmpty() || $scholarships->isEmpty()) {
            $this->command->error('No users or scholarships found. Please seed users and scholarships first.');
            return;
        }

        $statuses = ['pending', 'under_review', 'approved', 'rejected'];
        $categories = ['primary', 'secondary', 'university'];

        // Create test applications
        foreach ($scholarships->take(5) as $scholarship) {
            foreach ($users->take(3) as $user) {
                $formData = $this->generateFormData($scholarship->category ?? 'primary');

                $application = ScholarshipApplication::create([
                    'user_id' => $user->id,
                    'scholarship_id' => $scholarship->id,
                    'status' => $statuses[array_rand($statuses)],
                    'application_data' => $formData, // Required field
                    'form_data' => $formData, // New field for enhanced system
                    'submitted_at' => now()->subDays(rand(1, 30)),
                    'score' => rand(60, 100),
                    'review_notes' => 'Test review notes for application #' . rand(1000, 9999),
                    'internal_notes' => 'Internal notes for testing purposes',
                    'award_amount' => rand(500, 5000),
                    'reviewed_by' => $users->where('role', 'admin')->first()?->id,
                    'reviewed_at' => now()->subDays(rand(1, 15)),
                ]);

                // Create test files for some applications
                if (rand(0, 1)) {
                    $this->createTestFiles($application);
                }
            }
        }

        $this->command->info('Test scholarship applications created successfully!');
    }

    private function generateFormData($category): array
    {
        $baseData = [
            'first_name' => fake()->firstName(),
            'last_name' => fake()->lastName(),
            'email' => fake()->email(),
            'phone' => fake()->phoneNumber(),
            'date_of_birth' => fake()->date('Y-m-d', '-18 years'),
            'address' => fake()->address(),
            'city' => fake()->city(),
            'state' => fake()->state(),
            'postal_code' => fake()->postcode(),
            'emergency_contact_name' => fake()->name(),
            'emergency_contact_phone' => fake()->phoneNumber(),
        ];

        switch ($category) {
            case 'primary':
                return array_merge($baseData, [
                    'parent_guardian_name' => fake()->name(),
                    'parent_guardian_phone' => fake()->phoneNumber(),
                    'parent_guardian_email' => fake()->email(),
                    'school_name' => fake()->company() . ' Primary School',
                    'school_address' => fake()->address(),
                    'current_grade' => 'Grade ' . rand(1, 7),
                    'account_number' => fake()->bankAccountNumber(),
                    'bank_name' => fake()->company() . ' Bank',
                    'account_holder_name' => fake()->name(),
                ]);

            case 'secondary':
                return array_merge($baseData, [
                    'school_name' => fake()->company() . ' High School',
                    'school_address' => fake()->address(),
                    'current_grade' => 'Grade ' . rand(8, 12),
                    'principal_name' => fake()->name(),
                    'principal_phone' => fake()->phoneNumber(),
                    'principal_email' => fake()->email(),
                    'account_number' => fake()->bankAccountNumber(),
                    'bank_name' => fake()->company() . ' Bank',
                    'account_holder_name' => fake()->name(),
                ]);

            case 'university':
                return array_merge($baseData, [
                    'university_name' => fake()->company() . ' University',
                    'university_address' => fake()->address(),
                    'course_of_study' => fake()->randomElement(['Computer Science', 'Engineering', 'Medicine', 'Law', 'Business']),
                    'year_of_study' => rand(1, 4),
                    'matriculation_number' => 'MAT' . rand(100000, 999999),
                    'expected_graduation' => fake()->date('Y-m-d', '+4 years'),
                ]);

            default:
                return $baseData;
        }
    }

    private function createTestFiles(ScholarshipApplication $application): void
    {
        $fileTypes = [
            'academic_transcript' => 'Academic Transcript.pdf',
            'recommendation_letter' => 'Recommendation Letter.pdf',
            'personal_statement' => 'Personal Statement.pdf',
            'id_document' => 'ID Document.pdf',
            'proof_of_income' => 'Proof of Income.pdf',
        ];

        $selectedFiles = array_rand($fileTypes, rand(2, 4));
        if (!is_array($selectedFiles)) {
            $selectedFiles = [$selectedFiles];
        }

        foreach ($selectedFiles as $fileKey) {
            ScholarshipApplicationFile::create([
                'application_id' => $application->id,
                'field_name' => $fileKey,
                'original_name' => $fileTypes[$fileKey],
                'stored_name' => fake()->uuid() . '.pdf',
                'file_path' => 'scholarship-applications/' . date('Y/m') . '/' . fake()->uuid() . '.pdf',
                'file_size' => rand(100000, 5000000), // 100KB to 5MB
                'mime_type' => 'application/pdf',
                'file_hash' => hash('sha256', fake()->uuid()),
                'is_verified' => rand(0, 1),
                'admin_notes' => rand(0, 1) ? 'File verified and approved' : null,
            ]);
        }
    }
}
