"use strict";exports.id=348,exports.ids=[348],exports.modules={77348:(e,s,a)=>{a.r(s),a.d(s,{default:()=>g});var l=a(60687),t=a(43210);a(59556);var i=a(55192),c=a(24934),r=a(59821),n=a(85910),d=a(27351),x=a(96474),m=a(10022),h=a(86561),o=a(48730),j=a(28947),p=a(40228),u=a(85814),N=a.n(u);function g(){let[e,s]=(0,t.useState)(null),[a,u]=(0,t.useState)([]),[g,v]=(0,t.useState)([]),[f,y]=(0,t.useState)([]),[b,w]=(0,t.useState)(!0),A=e=>{switch(e.toLowerCase()){case"approved":return"bg-green-500";case"rejected":return"bg-red-500";case"under_review":return"bg-blue-500";case"pending":return"bg-yellow-500";default:return"bg-gray-500"}},_=e?.preferences?.student_data||{};return b?(0,l.jsx)("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:(0,l.jsxs)("div",{className:"text-center",children:[(0,l.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-green-600 mx-auto"}),(0,l.jsx)("p",{className:"mt-4 text-gray-600",children:"Loading dashboard..."})]})}):e?(0,l.jsx)("div",{className:"min-h-screen bg-gray-50 p-6",children:(0,l.jsxs)("div",{className:"max-w-7xl mx-auto space-y-6",children:[(0,l.jsx)("div",{className:"bg-white rounded-lg shadow-sm p-6",children:(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,l.jsx)("div",{className:"h-16 w-16 bg-green-600 rounded-full flex items-center justify-center",children:(0,l.jsx)(d.A,{className:"h-8 w-8 text-white"})}),(0,l.jsxs)("div",{children:[(0,l.jsxs)("h1",{className:"text-2xl font-bold text-gray-900",children:["Welcome back, ",e.first_name,"!"]}),(0,l.jsx)("p",{className:"text-gray-600",children:"Student Dashboard"}),_.current_level&&(0,l.jsxs)("p",{className:"text-sm text-green-600",children:[_.current_level," • ",_.institution]})]})]}),(0,l.jsx)(c.$,{asChild:!0,children:(0,l.jsxs)(N(),{href:"/scholarships",children:[(0,l.jsx)(x.A,{className:"h-4 w-4 mr-2"}),"Apply for Scholarships"]})})]})}),(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6",children:[(0,l.jsxs)(i.Zp,{children:[(0,l.jsxs)(i.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,l.jsx)(i.ZB,{className:"text-sm font-medium",children:"Applications"}),(0,l.jsx)(m.A,{className:"h-4 w-4 text-muted-foreground"})]}),(0,l.jsxs)(i.Wu,{children:[(0,l.jsx)("div",{className:"text-2xl font-bold",children:a.length}),(0,l.jsx)("p",{className:"text-xs text-muted-foreground",children:"Scholarship applications"})]})]}),(0,l.jsxs)(i.Zp,{children:[(0,l.jsxs)(i.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,l.jsx)(i.ZB,{className:"text-sm font-medium",children:"Approved"}),(0,l.jsx)(h.A,{className:"h-4 w-4 text-muted-foreground"})]}),(0,l.jsxs)(i.Wu,{children:[(0,l.jsx)("div",{className:"text-2xl font-bold text-green-600",children:a.filter(e=>"approved"===e.status).length}),(0,l.jsx)("p",{className:"text-xs text-muted-foreground",children:"Approved scholarships"})]})]}),(0,l.jsxs)(i.Zp,{children:[(0,l.jsxs)(i.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,l.jsx)(i.ZB,{className:"text-sm font-medium",children:"Pending"}),(0,l.jsx)(o.A,{className:"h-4 w-4 text-muted-foreground"})]}),(0,l.jsxs)(i.Wu,{children:[(0,l.jsx)("div",{className:"text-2xl font-bold text-yellow-600",children:a.filter(e=>"pending"===e.status||"under_review"===e.status).length}),(0,l.jsx)("p",{className:"text-xs text-muted-foreground",children:"Under review"})]})]}),(0,l.jsxs)(i.Zp,{children:[(0,l.jsxs)(i.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,l.jsx)(i.ZB,{className:"text-sm font-medium",children:"Available"}),(0,l.jsx)(j.A,{className:"h-4 w-4 text-muted-foreground"})]}),(0,l.jsxs)(i.Wu,{children:[(0,l.jsx)("div",{className:"text-2xl font-bold text-blue-600",children:f.length}),(0,l.jsx)("p",{className:"text-xs text-muted-foreground",children:"New opportunities"})]})]})]}),(0,l.jsxs)(n.tU,{defaultValue:"overview",className:"space-y-6",children:[(0,l.jsxs)(n.j7,{className:"grid w-full grid-cols-4",children:[(0,l.jsx)(n.Xi,{value:"overview",children:"Overview"}),(0,l.jsx)(n.Xi,{value:"scholarships",children:"My Applications"}),(0,l.jsx)(n.Xi,{value:"events",children:"Events"}),(0,l.jsx)(n.Xi,{value:"profile",children:"Profile"})]}),(0,l.jsxs)(n.av,{value:"overview",className:"space-y-6",children:[(0,l.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,l.jsxs)(i.Zp,{children:[(0,l.jsx)(i.aR,{children:(0,l.jsxs)(i.ZB,{className:"flex items-center",children:[(0,l.jsx)(m.A,{className:"h-5 w-5 mr-2"}),"Recent Applications"]})}),(0,l.jsxs)(i.Wu,{className:"space-y-4",children:[a.slice(0,3).map(e=>(0,l.jsxs)("div",{className:"flex items-center justify-between p-3 border rounded-lg",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)("p",{className:"font-medium",children:e.scholarship.title}),(0,l.jsxs)("p",{className:"text-sm text-gray-600",children:["₦",e.scholarship.amount.toLocaleString()]})]}),(0,l.jsx)(r.E,{className:A(e.status),children:e.status.replace("_"," ")})]},e.id)),0===a.length&&(0,l.jsx)("p",{className:"text-gray-500 text-center py-4",children:"No applications yet"})]})]}),(0,l.jsxs)(i.Zp,{children:[(0,l.jsx)(i.aR,{children:(0,l.jsxs)(i.ZB,{className:"flex items-center",children:[(0,l.jsx)(h.A,{className:"h-5 w-5 mr-2"}),"Available Scholarships"]})}),(0,l.jsxs)(i.Wu,{className:"space-y-4",children:[f.slice(0,3).map(e=>(0,l.jsxs)("div",{className:"p-3 border rounded-lg",children:[(0,l.jsx)("h4",{className:"font-medium",children:e.title}),(0,l.jsxs)("p",{className:"text-sm text-gray-600 mt-1",children:["Amount: ₦",e.amount?.toLocaleString()]}),(0,l.jsxs)("p",{className:"text-xs text-gray-500 mt-2",children:["Deadline: ",new Date(e.application_deadline).toLocaleDateString()]})]},e.id)),(0,l.jsx)(c.$,{variant:"outline",className:"w-full",asChild:!0,children:(0,l.jsx)(N(),{href:"/scholarships",children:"View All Scholarships"})})]})]})]}),(0,l.jsxs)(i.Zp,{children:[(0,l.jsx)(i.aR,{children:(0,l.jsxs)(i.ZB,{className:"flex items-center",children:[(0,l.jsx)(p.A,{className:"h-5 w-5 mr-2"}),"Upcoming Events"]})}),(0,l.jsx)(i.Wu,{children:(0,l.jsxs)("div",{className:"space-y-3",children:[g.map(e=>(0,l.jsxs)("div",{className:"flex items-center justify-between p-3 border rounded-lg",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)("p",{className:"font-medium",children:e.title}),(0,l.jsxs)("p",{className:"text-sm text-gray-600",children:[new Date(e.start_datetime).toLocaleDateString()," • ",e.event_type]})]}),(0,l.jsx)(c.$,{variant:"outline",size:"sm",children:"View Details"})]},e.id)),0===g.length&&(0,l.jsx)("p",{className:"text-gray-500 text-center py-4",children:"No upcoming events"})]})})]})]}),(0,l.jsx)(n.av,{value:"scholarships",className:"space-y-6",children:(0,l.jsxs)(i.Zp,{children:[(0,l.jsxs)(i.aR,{children:[(0,l.jsx)(i.ZB,{children:"My Scholarship Applications"}),(0,l.jsx)(i.BT,{children:"Track the status of your scholarship applications"})]}),(0,l.jsx)(i.Wu,{children:(0,l.jsxs)("div",{className:"space-y-4",children:[a.map(e=>(0,l.jsxs)("div",{className:"border rounded-lg p-4",children:[(0,l.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,l.jsx)("h4",{className:"font-semibold",children:e.scholarship.title}),(0,l.jsx)(r.E,{className:A(e.status),children:e.status.replace("_"," ")})]}),(0,l.jsxs)("div",{className:"grid grid-cols-2 gap-4 text-sm text-gray-600",children:[(0,l.jsxs)("p",{children:["Amount: ₦",e.scholarship.amount.toLocaleString()]}),(0,l.jsxs)("p",{children:["Applied: ",new Date(e.applied_at).toLocaleDateString()]})]})]},e.id)),0===a.length&&(0,l.jsxs)("div",{className:"text-center py-8",children:[(0,l.jsx)(h.A,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),(0,l.jsx)("p",{className:"text-gray-500 mb-4",children:"You haven't applied for any scholarships yet"}),(0,l.jsx)(c.$,{asChild:!0,children:(0,l.jsx)(N(),{href:"/scholarships",children:"Browse Scholarships"})})]})]})})]})}),(0,l.jsx)(n.av,{value:"events",className:"space-y-6",children:(0,l.jsxs)(i.Zp,{children:[(0,l.jsxs)(i.aR,{children:[(0,l.jsx)(i.ZB,{children:"Educational Events"}),(0,l.jsx)(i.BT,{children:"Join workshops, seminars, and networking events"})]}),(0,l.jsx)(i.Wu,{children:(0,l.jsxs)("div",{className:"space-y-4",children:[g.map(e=>(0,l.jsxs)("div",{className:"border rounded-lg p-4",children:[(0,l.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,l.jsx)("h4",{className:"font-semibold",children:e.title}),(0,l.jsx)(c.$,{variant:"outline",size:"sm",children:"Register"})]}),(0,l.jsxs)("div",{className:"text-sm text-gray-600",children:[(0,l.jsxs)("p",{children:["Date: ",new Date(e.start_datetime).toLocaleDateString()]}),(0,l.jsxs)("p",{children:["Type: ",e.event_type]})]})]},e.id)),0===g.length&&(0,l.jsx)("p",{className:"text-gray-500 text-center py-4",children:"No upcoming events"})]})})]})}),(0,l.jsx)(n.av,{value:"profile",className:"space-y-6",children:(0,l.jsxs)(i.Zp,{children:[(0,l.jsxs)(i.aR,{children:[(0,l.jsx)(i.ZB,{children:"Student Profile"}),(0,l.jsx)(i.BT,{children:"Manage your academic and personal information"})]}),(0,l.jsxs)(i.Wu,{className:"space-y-4",children:[(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)("label",{className:"text-sm font-medium",children:"Full Name"}),(0,l.jsxs)("p",{className:"text-sm text-gray-600",children:[e.first_name," ",e.last_name]})]}),(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)("label",{className:"text-sm font-medium",children:"Email"}),(0,l.jsx)("p",{className:"text-sm text-gray-600",children:e.email})]}),_.current_level&&(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)("label",{className:"text-sm font-medium",children:"Academic Level"}),(0,l.jsx)("p",{className:"text-sm text-gray-600",children:_.current_level})]}),_.institution&&(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)("label",{className:"text-sm font-medium",children:"Institution"}),(0,l.jsx)("p",{className:"text-sm text-gray-600",children:_.institution})]}),_.field_of_study&&(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)("label",{className:"text-sm font-medium",children:"Field of Study"}),(0,l.jsx)("p",{className:"text-sm text-gray-600",children:_.field_of_study})]}),_.gpa&&(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)("label",{className:"text-sm font-medium",children:"GPA"}),(0,l.jsx)("p",{className:"text-sm text-gray-600",children:_.gpa})]})]}),(0,l.jsx)(c.$,{variant:"outline",children:"Edit Profile"})]})]})})]})]})}):(0,l.jsx)("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:(0,l.jsx)("div",{className:"text-center",children:(0,l.jsx)("p",{className:"text-gray-600",children:"Unable to load user data"})})})}}};