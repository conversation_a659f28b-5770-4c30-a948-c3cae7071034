<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\NewsletterSubscriber;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Mail;
use Carbon\Carbon;

/**
 * @OA\Tag(
 *     name="Newsletter",
 *     description="Newsletter subscription and management endpoints"
 * )
 */
class NewsletterController extends Controller
{
    /**
     * @OA\Post(
     *     path="/api/v1/newsletter/subscribe",
     *     summary="Subscribe to newsletter",
     *     tags={"Newsletter"},
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\JsonContent(
     *             required={"email"},
     *             @OA\Property(property="email", type="string", format="email", example="<EMAIL>"),
     *             @OA\Property(property="first_name", type="string", example="John"),
     *             @OA\Property(property="last_name", type="string", example="Doe")
     *         )
     *     ),
     *     @OA\Response(
     *         response=201,
     *         description="Successfully subscribed",
     *         @OA\JsonContent(
     *             @OA\Property(property="message", type="string", example="Successfully subscribed to newsletter"),
     *             @OA\Property(property="success", type="boolean", example=true)
     *         )
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Validation error or already subscribed"
     *     )
     * )
     */
    public function subscribe(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'email' => 'required|email|max:255',
            'first_name' => 'nullable|string|max:255',
            'last_name' => 'nullable|string|max:255',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $validator->errors(),
                'success' => false
            ], 422);
        }

        // Check if already subscribed
        $existing = NewsletterSubscriber::where('email', $request->email)->first();
        
        if ($existing) {
            if ($existing->is_active) {
                return response()->json([
                    'message' => 'Email is already subscribed to our newsletter',
                    'success' => false
                ], 422);
            } else {
                // Reactivate subscription
                $existing->update([
                    'is_active' => true,
                    'first_name' => $request->first_name ?? $existing->first_name,
                    'last_name' => $request->last_name ?? $existing->last_name,
                    'subscribed_at' => now()
                ]);
                
                return response()->json([
                    'message' => 'Successfully resubscribed to newsletter',
                    'success' => true
                ], 200);
            }
        }

        // Create new subscription
        NewsletterSubscriber::create([
            'email' => $request->email,
            'first_name' => $request->first_name,
            'last_name' => $request->last_name,
            'is_active' => true,
            'subscribed_at' => now()
        ]);

        return response()->json([
            'message' => 'Successfully subscribed to newsletter',
            'success' => true
        ], 201);
    }

    /**
     * @OA\Post(
     *     path="/api/v1/newsletter/unsubscribe",
     *     summary="Unsubscribe from newsletter",
     *     tags={"Newsletter"},
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\JsonContent(
     *             required={"email"},
     *             @OA\Property(property="email", type="string", format="email", example="<EMAIL>")
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Successfully unsubscribed",
     *         @OA\JsonContent(
     *             @OA\Property(property="message", type="string", example="Successfully unsubscribed from newsletter"),
     *             @OA\Property(property="success", type="boolean", example=true)
     *         )
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="Email not found"
     *     )
     * )
     */
    public function unsubscribe(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'email' => 'required|email|max:255',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $validator->errors(),
                'success' => false
            ], 422);
        }

        $subscriber = NewsletterSubscriber::where('email', $request->email)->first();

        if (!$subscriber) {
            return response()->json([
                'message' => 'Email not found in our newsletter list',
                'success' => false
            ], 404);
        }

        $subscriber->update([
            'is_active' => false,
            'unsubscribed_at' => now()
        ]);

        return response()->json([
            'message' => 'Successfully unsubscribed from newsletter',
            'success' => true
        ], 200);
    }

    /**
     * Get newsletter subscribers (Admin only)
     */
    public function subscribers(Request $request)
    {
        $perPage = $request->get('per_page', 15);
        $search = $request->get('search');
        $status = $request->get('status'); // active, inactive, all

        $query = NewsletterSubscriber::query();

        if ($search) {
            $query->where(function($q) use ($search) {
                $q->where('email', 'like', "%{$search}%")
                  ->orWhere('first_name', 'like', "%{$search}%")
                  ->orWhere('last_name', 'like', "%{$search}%");
            });
        }

        if ($status && $status !== 'all') {
            $query->where('is_active', $status === 'active');
        }

        $subscribers = $query->orderBy('created_at', 'desc')->paginate($perPage);

        return response()->json([
            'subscribers' => $subscribers,
            'success' => true
        ]);
    }

    /**
     * Send newsletter (Admin only)
     */
    public function send(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'subject' => 'required|string|max:255',
            'content' => 'required|string',
            'send_to' => 'required|in:all,active,test',
            'test_email' => 'required_if:send_to,test|email'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $validator->errors(),
                'success' => false
            ], 422);
        }

        if ($request->send_to === 'test') {
            // Send test email
            try {
                Mail::send('emails.newsletter', [
                    'content' => $request->content
                ], function($message) use ($request) {
                    $message->to($request->test_email)
                           ->subject($request->subject);
                });

                return response()->json([
                    'message' => 'Test email sent successfully',
                    'success' => true
                ]);
            } catch (\Exception $e) {
                return response()->json([
                    'message' => 'Failed to send test email: ' . $e->getMessage(),
                    'success' => false
                ], 500);
            }
        }

        // Get subscribers based on send_to parameter
        $subscribers = NewsletterSubscriber::where('is_active', true)->get();

        if ($subscribers->isEmpty()) {
            return response()->json([
                'message' => 'No active subscribers found',
                'success' => false
            ], 404);
        }

        // Queue newsletter sending (in production, use job queues)
        $sentCount = 0;
        foreach ($subscribers as $subscriber) {
            try {
                Mail::send('emails.newsletter', [
                    'content' => $request->content,
                    'subscriber' => $subscriber
                ], function($message) use ($request, $subscriber) {
                    $message->to($subscriber->email)
                           ->subject($request->subject);
                });
                $sentCount++;
            } catch (\Exception $e) {
                // Log error but continue sending to other subscribers
                \Log::error("Failed to send newsletter to {$subscriber->email}: " . $e->getMessage());
            }
        }

        return response()->json([
            'message' => "Newsletter sent to {$sentCount} subscribers",
            'sent_count' => $sentCount,
            'total_subscribers' => $subscribers->count(),
            'success' => true
        ]);
    }

    /**
     * Create newsletter campaign (Admin only)
     */
    public function createCampaign(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'subject' => 'required|string|max:255',
            'content' => 'required|string',
            'scheduled_at' => 'nullable|date|after:now'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $validator->errors(),
                'success' => false
            ], 422);
        }

        // For now, just return success message
        // In a full implementation, you'd save this to a newsletter_campaigns table
        return response()->json([
            'message' => 'Newsletter campaign created successfully',
            'success' => true
        ]);
    }

    /**
     * Get newsletter campaigns (Admin only)
     */
    public function campaigns(Request $request)
    {
        // For now, return empty array
        // In a full implementation, you'd fetch from newsletter_campaigns table
        return response()->json([
            'campaigns' => [],
            'success' => true
        ]);
    }
} 