@extends('layouts.admin')

@section('title', 'Create Scholarship')

@section('content')
<div class="container-fluid">
    <!-- <PERSON> Header -->
    <div class="row">
        <div class="col-12">
            <div class="page-header d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 text-gray-800">Create New Scholarship</h1>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb bg-transparent p-0 m-0">
                        <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">Dashboard</a></li>
                        <li class="breadcrumb-item"><a href="{{ route('admin.scholarships.index') }}">Scholarships</a></li>
                        <li class="breadcrumb-item active">Create</li>
                    </ol>
                </nav>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-8">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Scholarship Information</h6>
                </div>
                <div class="card-body">
                    <form action="{{ route('admin.scholarships.store') }}" method="POST" enctype="multipart/form-data">
                        @csrf
                        
                        <div class="row">
                            <div class="col-md-12 mb-3">
                                <label for="title" class="form-label">Scholarship Title <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="title" name="title" required>
                                <div class="form-text">Enter a clear and descriptive title for the scholarship</div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="amount" class="form-label">Scholarship Amount (₦) <span class="text-danger">*</span></label>
                                <input type="number" class="form-control" id="amount" name="amount" step="0.01" min="0" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="category" class="form-label">Education Category <span class="text-danger">*</span></label>
                                <select class="form-select" id="category" name="category" required onchange="updateCategoryInfo()">
                                    <option value="">Select Education Level</option>
                                    <option value="primary">Primary School (Ages 5-13)</option>
                                    <option value="secondary">Secondary School (Ages 12-20)</option>
                                    <option value="university">University (Ages 16-35)</option>
                                </select>
                                <div class="form-text">Choose the education level this scholarship targets</div>
                            </div>
                        </div>

                        <!-- Category Information Alert -->
                        <div id="categoryInfo" class="alert alert-info d-none mb-3">
                            <div id="categoryInfoContent"></div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="application_deadline" class="form-label">Application Deadline <span class="text-danger">*</span></label>
                                <input type="datetime-local" class="form-control" id="application_deadline" name="application_deadline" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="application_start_date" class="form-label">Application Start Date</label>
                                <input type="datetime-local" class="form-control" id="application_start_date" name="application_start_date">
                                <div class="form-text">Leave empty to start immediately</div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="max_applicants" class="form-label">Maximum Applicants</label>
                                <input type="number" class="form-control" id="max_applicants" name="max_applicants" min="1">
                                <div class="form-text">Leave empty for unlimited applications</div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="contact_email" class="form-label">Contact Email</label>
                                <input type="email" class="form-control" id="contact_email" name="contact_email">
                                <div class="form-text">Email for scholarship inquiries</div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="description" class="form-label">Description <span class="text-danger">*</span></label>
                            <textarea class="form-control" id="description" name="description" rows="4" required></textarea>
                            <div class="form-text">Provide detailed information about the scholarship</div>
                        </div>

                        <div class="mb-3">
                            <label for="eligibility_criteria" class="form-label">Eligibility Criteria <span class="text-danger">*</span></label>
                            <textarea class="form-control" id="eligibility_criteria" name="eligibility_criteria" rows="4" required></textarea>
                            <div class="form-text">List the requirements and criteria for applicants</div>
                        </div>

                        <div class="mb-3">
                            <label for="required_documents" class="form-label">Required Documents</label>
                            <textarea class="form-control" id="required_documents" name="required_documents" rows="3"></textarea>
                            <div class="form-text">List documents that applicants need to submit</div>
                        </div>

                        <!-- Custom Fields Management -->
                        <div class="mb-4">
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <h6 class="mb-0">Custom Application Fields</h6>
                                <button type="button" class="btn btn-sm btn-outline-primary" onclick="addCustomField()">
                                    <i class="fas fa-plus me-1"></i>Add Field
                                </button>
                            </div>
                            <div id="customFieldsContainer" class="border rounded p-3 bg-light">
                                <p class="text-muted mb-0">No custom fields added yet. Click "Add Field" to create application-specific fields.</p>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="image" class="form-label">Scholarship Image</label>
                            <input type="file" class="form-control" id="image" name="image" accept="image/*">
                            <div class="form-text">Upload an image to represent this scholarship (optional)</div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="status" class="form-label">Status</label>
                                <select class="form-select" id="status" name="status">
                                    <option value="draft">Draft</option>
                                    <option value="active" selected>Active</option>
                                    <option value="inactive">Inactive</option>
                                </select>
                            </div>
                            <div class="col-md-6 mb-3">
                                <div class="form-check mt-4">
                                    <input class="form-check-input" type="checkbox" id="is_featured" name="is_featured" value="1">
                                    <label class="form-check-label" for="is_featured">
                                        Featured Scholarship
                                    </label>
                                    <div class="form-text">Featured scholarships appear prominently on the website</div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="is_open" name="is_open" value="1" checked>
                                    <label class="form-check-label" for="is_open">
                                        Open for Applications
                                    </label>
                                    <div class="form-text">Allow students to apply for this scholarship</div>
                                </div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="template_id" class="form-label">Use Template</label>
                                <select class="form-select" id="template_id" name="template_id">
                                    <option value="">No Template</option>
                                    <!-- Templates will be loaded via JavaScript -->
                                </select>
                                <div class="form-text">Load fields from an existing template</div>
                            </div>
                        </div>

                        <div class="d-flex justify-content-between">
                            <a href="{{ route('admin.scholarships.index') }}" class="btn btn-secondary">
                                <i class="fas fa-arrow-left me-2"></i>Back to Scholarships
                            </a>
                            <div>
                                <button type="submit" name="action" value="draft" class="btn btn-outline-primary me-2">
                                    <i class="fas fa-save me-2"></i>Save as Draft
                                </button>
                                <button type="submit" name="action" value="publish" class="btn btn-primary">
                                    <i class="fas fa-rocket me-2"></i>Create & Publish
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Scholarship Tips</h6>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <h6><i class="fas fa-lightbulb me-2"></i>Tips for Success</h6>
                        <ul class="mb-0 small">
                            <li>Choose the correct education category</li>
                            <li>Set realistic application deadlines</li>
                            <li>Define clear eligibility criteria</li>
                            <li>Add custom fields for specific requirements</li>
                            <li>Test the application flow before publishing</li>
                        </ul>
                    </div>
                    <div class="alert alert-warning mt-3">
                        <h6><i class="fas fa-exclamation-triangle me-2"></i>Category Guidelines</h6>
                        <ul class="mb-0 small">
                            <li><strong>Primary:</strong> Parent/guardian applies for child</li>
                            <li><strong>Secondary:</strong> Student applies with school details</li>
                            <li><strong>University:</strong> Student applies with matric number</li>
                        </ul>
                    </div>
                </div>
            </div>

            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Scholarship Statistics</h6>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-6">
                            <div class="border-end">
                                <h4 class="text-primary">8</h4>
                                <small class="text-muted">Active Scholarships</small>
                            </div>
                        </div>
                        <div class="col-6">
                            <h4 class="text-success">156</h4>
                            <small class="text-muted">Applications</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
let customFieldCounter = 0;

document.addEventListener('DOMContentLoaded', function() {
    // Set minimum date to today
    const today = new Date().toISOString().slice(0, 16);
    document.getElementById('application_deadline').setAttribute('min', today);
    if (document.getElementById('application_start_date')) {
        document.getElementById('application_start_date').setAttribute('min', today);
    }

    // Load templates
    loadTemplates();
});

function updateCategoryInfo() {
    const category = document.getElementById('category').value;
    const infoDiv = document.getElementById('categoryInfo');
    const contentDiv = document.getElementById('categoryInfoContent');

    if (category) {
        let content = '';
        switch(category) {
            case 'primary':
                content = `
                    <h6><i class="fas fa-school me-2"></i>Primary School Category</h6>
                    <ul class="mb-0">
                        <li><strong>Age Range:</strong> 5-13 years</li>
                        <li><strong>Form Filled By:</strong> Parent/Guardian</li>
                        <li><strong>Required Fields:</strong> Student details, Parent/Guardian info, Headmaster details, School account number</li>
                        <li><strong>Documents:</strong> School ID, Birth certificate, Academic records</li>
                    </ul>
                `;
                break;
            case 'secondary':
                content = `
                    <h6><i class="fas fa-graduation-cap me-2"></i>Secondary School Category</h6>
                    <ul class="mb-0">
                        <li><strong>Age Range:</strong> 12-20 years</li>
                        <li><strong>Form Filled By:</strong> Student</li>
                        <li><strong>Required Fields:</strong> Student details, Principal/Financial Officer info, School account number</li>
                        <li><strong>Documents:</strong> School ID, Academic transcripts, Recommendation letter</li>
                    </ul>
                `;
                break;
            case 'university':
                content = `
                    <h6><i class="fas fa-university me-2"></i>University Category</h6>
                    <ul class="mb-0">
                        <li><strong>Age Range:</strong> 16-35 years</li>
                        <li><strong>Form Filled By:</strong> Student</li>
                        <li><strong>Required Fields:</strong> Student details, Matriculation number, Student email</li>
                        <li><strong>Documents:</strong> Student ID, Transcripts, Admission letter</li>
                        <li><strong>Note:</strong> No school account number required</li>
                    </ul>
                `;
                break;
        }
        contentDiv.innerHTML = content;
        infoDiv.classList.remove('d-none');
    } else {
        infoDiv.classList.add('d-none');
    }
}

function addCustomField() {
    customFieldCounter++;
    const container = document.getElementById('customFieldsContainer');

    // Remove the "no fields" message if it exists
    if (container.querySelector('p.text-muted')) {
        container.innerHTML = '';
    }

    const fieldHtml = `
        <div class="custom-field-item border rounded p-3 mb-3 bg-white" data-field-id="${customFieldCounter}">
            <div class="d-flex justify-content-between align-items-center mb-2">
                <h6 class="mb-0">Custom Field #${customFieldCounter}</h6>
                <div>
                    <button type="button" class="btn btn-sm btn-outline-secondary me-1" onclick="moveFieldUp(${customFieldCounter})">
                        <i class="fas fa-arrow-up"></i>
                    </button>
                    <button type="button" class="btn btn-sm btn-outline-secondary me-1" onclick="moveFieldDown(${customFieldCounter})">
                        <i class="fas fa-arrow-down"></i>
                    </button>
                    <button type="button" class="btn btn-sm btn-outline-danger" onclick="removeCustomField(${customFieldCounter})">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </div>
            <div class="row">
                <div class="col-md-6">
                    <label class="form-label">Field Name</label>
                    <input type="text" class="form-control" name="custom_fields[${customFieldCounter}][field_name]" placeholder="e.g., gpa_score">
                </div>
                <div class="col-md-6">
                    <label class="form-label">Display Label</label>
                    <input type="text" class="form-control" name="custom_fields[${customFieldCounter}][field_label]" placeholder="e.g., GPA Score">
                </div>
            </div>
            <div class="row mt-2">
                <div class="col-md-4">
                    <label class="form-label">Field Type</label>
                    <select class="form-select" name="custom_fields[${customFieldCounter}][field_type]">
                        <option value="text">Text</option>
                        <option value="textarea">Textarea</option>
                        <option value="email">Email</option>
                        <option value="tel">Phone</option>
                        <option value="number">Number</option>
                        <option value="date">Date</option>
                        <option value="file">File Upload</option>
                        <option value="select">Dropdown</option>
                    </select>
                </div>
                <div class="col-md-4">
                    <label class="form-label">Validation</label>
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" name="custom_fields[${customFieldCounter}][is_required]" value="1">
                        <label class="form-check-label">Required</label>
                    </div>
                </div>
                <div class="col-md-4">
                    <label class="form-label">Display Order</label>
                    <input type="number" class="form-control" name="custom_fields[${customFieldCounter}][display_order]" value="${customFieldCounter}">
                </div>
            </div>
            <div class="mt-2">
                <label class="form-label">Help Text</label>
                <input type="text" class="form-control" name="custom_fields[${customFieldCounter}][help_text]" placeholder="Optional help text for applicants">
            </div>
        </div>
    `;

    container.insertAdjacentHTML('beforeend', fieldHtml);
}

function removeCustomField(fieldId) {
    if (confirm('Are you sure you want to remove this field?')) {
        const fieldElement = document.querySelector(`[data-field-id="${fieldId}"]`);
        fieldElement.remove();

        // Check if no fields remain
        const container = document.getElementById('customFieldsContainer');
        if (container.children.length === 0) {
            container.innerHTML = '<p class="text-muted mb-0">No custom fields added yet. Click "Add Field" to create application-specific fields.</p>';
        }
    }
}

function moveFieldUp(fieldId) {
    const fieldElement = document.querySelector(`[data-field-id="${fieldId}"]`);
    const previousElement = fieldElement.previousElementSibling;
    if (previousElement && previousElement.classList.contains('custom-field-item')) {
        fieldElement.parentNode.insertBefore(fieldElement, previousElement);
    }
}

function moveFieldDown(fieldId) {
    const fieldElement = document.querySelector(`[data-field-id="${fieldId}"]`);
    const nextElement = fieldElement.nextElementSibling;
    if (nextElement && nextElement.classList.contains('custom-field-item')) {
        fieldElement.parentNode.insertBefore(nextElement, fieldElement);
    }
}

function loadTemplates() {
    // Load scholarship templates via AJAX
    fetch('/api/v1/admin/scholarship-templates')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const select = document.getElementById('template_id');
                data.data.data.forEach(template => {
                    const option = document.createElement('option');
                    option.value = template.id;
                    option.textContent = `${template.name} (${template.category})`;
                    select.appendChild(option);
                });
            }
        })
        .catch(error => console.error('Error loading templates:', error));
}
</script>
@endsection
