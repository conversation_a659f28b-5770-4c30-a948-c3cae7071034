@extends('layouts.admin')

@section('title', 'Create Scholarship')

@section('content')
<div class="container-fluid">
    <!-- <PERSON> Header -->
    <div class="row">
        <div class="col-12">
            <div class="page-header d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 text-gray-800">Create New Scholarship</h1>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb bg-transparent p-0 m-0">
                        <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">Dashboard</a></li>
                        <li class="breadcrumb-item"><a href="{{ route('admin.scholarships.index') }}">Scholarships</a></li>
                        <li class="breadcrumb-item active">Create</li>
                    </ol>
                </nav>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-8">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Scholarship Information</h6>
                </div>
                <div class="card-body">
                    <form action="{{ route('admin.scholarships.store') }}" method="POST" enctype="multipart/form-data">
                        @csrf
                        
                        <div class="row">
                            <div class="col-md-12 mb-3">
                                <label for="title" class="form-label">Scholarship Title <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="title" name="title" required>
                                <div class="form-text">Enter a clear and descriptive title for the scholarship</div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="amount" class="form-label">Scholarship Amount (₦) <span class="text-danger">*</span></label>
                                <input type="number" class="form-control" id="amount" name="amount" step="0.01" min="0" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="category" class="form-label">Category</label>
                                <select class="form-select" id="category" name="category">
                                    <option value="">Select Category</option>
                                    <option value="academic">Academic Excellence</option>
                                    <option value="need-based">Need-Based</option>
                                    <option value="merit">Merit-Based</option>
                                    <option value="sports">Sports</option>
                                    <option value="arts">Arts & Culture</option>
                                    <option value="stem">STEM</option>
                                    <option value="community">Community Service</option>
                                    <option value="other">Other</option>
                                </select>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="application_deadline" class="form-label">Application Deadline <span class="text-danger">*</span></label>
                                <input type="date" class="form-control" id="application_deadline" name="application_deadline" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="award_date" class="form-label">Award Date</label>
                                <input type="date" class="form-control" id="award_date" name="award_date">
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="available_slots" class="form-label">Available Slots</label>
                                <input type="number" class="form-control" id="available_slots" name="available_slots" min="1" value="1">
                                <div class="form-text">Number of scholarships available</div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="education_level" class="form-label">Education Level</label>
                                <select class="form-select" id="education_level" name="education_level">
                                    <option value="">Select Level</option>
                                    <option value="primary">Primary School</option>
                                    <option value="secondary">Secondary School</option>
                                    <option value="undergraduate">Undergraduate</option>
                                    <option value="postgraduate">Postgraduate</option>
                                    <option value="vocational">Vocational Training</option>
                                </select>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="description" class="form-label">Description <span class="text-danger">*</span></label>
                            <textarea class="form-control" id="description" name="description" rows="4" required></textarea>
                            <div class="form-text">Provide detailed information about the scholarship</div>
                        </div>

                        <div class="mb-3">
                            <label for="eligibility_criteria" class="form-label">Eligibility Criteria <span class="text-danger">*</span></label>
                            <textarea class="form-control" id="eligibility_criteria" name="eligibility_criteria" rows="4" required></textarea>
                            <div class="form-text">List the requirements and criteria for applicants</div>
                        </div>

                        <div class="mb-3">
                            <label for="required_documents" class="form-label">Required Documents</label>
                            <textarea class="form-control" id="required_documents" name="required_documents" rows="3"></textarea>
                            <div class="form-text">List documents that applicants need to submit</div>
                        </div>

                        <div class="mb-3">
                            <label for="image" class="form-label">Scholarship Image</label>
                            <input type="file" class="form-control" id="image" name="image" accept="image/*">
                            <div class="form-text">Upload an image to represent this scholarship (optional)</div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="status" class="form-label">Status</label>
                                <select class="form-select" id="status" name="status">
                                    <option value="draft">Draft</option>
                                    <option value="open" selected>Open for Applications</option>
                                    <option value="closed">Closed</option>
                                    <option value="awarded">Awarded</option>
                                </select>
                            </div>
                            <div class="col-md-6 mb-3">
                                <div class="form-check mt-4">
                                    <input class="form-check-input" type="checkbox" id="featured" name="featured" value="1">
                                    <label class="form-check-label" for="featured">
                                        Featured Scholarship
                                    </label>
                                    <div class="form-text">Featured scholarships appear prominently on the website</div>
                                </div>
                            </div>
                        </div>

                        <div class="d-flex justify-content-between">
                            <a href="{{ route('admin.scholarships.index') }}" class="btn btn-secondary">
                                <i class="fas fa-arrow-left me-2"></i>Back to Scholarships
                            </a>
                            <div>
                                <button type="submit" name="action" value="draft" class="btn btn-outline-primary me-2">
                                    <i class="fas fa-save me-2"></i>Save as Draft
                                </button>
                                <button type="submit" name="action" value="publish" class="btn btn-primary">
                                    <i class="fas fa-rocket me-2"></i>Create & Publish
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Scholarship Tips</h6>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <h6><i class="fas fa-lightbulb me-2"></i>Tips for Success</h6>
                        <ul class="mb-0 small">
                            <li>Use clear, specific titles</li>
                            <li>Set realistic deadlines</li>
                            <li>Define clear eligibility criteria</li>
                            <li>Specify required documents</li>
                            <li>Include contact information</li>
                        </ul>
                    </div>
                </div>
            </div>

            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Scholarship Statistics</h6>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-6">
                            <div class="border-end">
                                <h4 class="text-primary">8</h4>
                                <small class="text-muted">Active Scholarships</small>
                            </div>
                        </div>
                        <div class="col-6">
                            <h4 class="text-success">156</h4>
                            <small class="text-muted">Applications</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Set minimum date to today
    const today = new Date().toISOString().split('T')[0];
    document.getElementById('application_deadline').setAttribute('min', today);
    document.getElementById('award_date').setAttribute('min', today);
    
    // Update award date minimum when deadline changes
    document.getElementById('application_deadline').addEventListener('change', function() {
        document.getElementById('award_date').setAttribute('min', this.value);
    });
});
</script>
@endsection
