@extends('layouts.admin')

@section('title', 'Edit Scholarship')

@section('content')
<div class="container-fluid">
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">Edit Scholarship</h1>
        <div>
            <a href="{{ route('admin.scholarships.show', $scholarship->id) }}" class="btn btn-secondary me-2">
                <i class="fas fa-arrow-left me-2"></i>Back to Details
            </a>
            <a href="{{ route('admin.scholarships.index') }}" class="btn btn-outline-secondary">
                <i class="fas fa-list me-2"></i>All Scholarships
            </a>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-8">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Scholarship Information</h6>
                </div>
                <div class="card-body">
                    <form action="{{ route('admin.scholarships.update', $scholarship->id) }}" method="POST">
                        @csrf
                        @method('PUT')
                        
                        <!-- Basic Information -->
                        <div class="mb-3">
                            <label for="title" class="form-label">Scholarship Title <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="title" name="title" value="{{ $scholarship->title }}" required>
                        </div>

                        <div class="mb-3">
                            <label for="description" class="form-label">Description <span class="text-danger">*</span></label>
                            <textarea class="form-control" id="description" name="description" rows="5" required>{{ $scholarship->description }}</textarea>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="amount" class="form-label">Scholarship Amount (₦) <span class="text-danger">*</span></label>
                                <input type="number" class="form-control" id="amount" name="amount" value="{{ $scholarship->amount }}" required>
                            </div>
                            <div class="col-md-6">
                                <label for="application_deadline" class="form-label">Application Deadline <span class="text-danger">*</span></label>
                                <input type="date" class="form-control" id="application_deadline" name="application_deadline" value="{{ $scholarship->application_deadline->format('Y-m-d') }}" required>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="status" class="form-label">Status <span class="text-danger">*</span></label>
                                <select class="form-control" id="status" name="status" required>
                                    <option value="draft" {{ $scholarship->status === 'draft' ? 'selected' : '' }}>Draft</option>
                                    <option value="open" {{ $scholarship->status === 'open' ? 'selected' : '' }}>Open for Applications</option>
                                    <option value="closed" {{ $scholarship->status === 'closed' ? 'selected' : '' }}>Closed</option>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label for="max_applicants" class="form-label">Maximum Applicants</label>
                                <input type="number" class="form-control" id="max_applicants" name="max_applicants" value="{{ $scholarship->max_applicants }}" placeholder="Leave empty for unlimited">
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="eligibility_criteria" class="form-label">Eligibility Criteria <span class="text-danger">*</span></label>
                            <textarea class="form-control" id="eligibility_criteria" name="eligibility_criteria" rows="6" required>{{ $scholarship->eligibility_criteria }}</textarea>
                            <small class="form-text text-muted">Describe who is eligible to apply for this scholarship.</small>
                        </div>

                        <div class="mb-3">
                            <label for="requirements" class="form-label">Application Requirements</label>
                            <div id="requirements-container">
                                @if($scholarship->requirements)
                                    @foreach($scholarship->requirements as $index => $requirement)
                                        <div class="input-group mb-2 requirement-item">
                                            <input type="text" class="form-control" name="requirements[]" value="{{ $requirement }}" placeholder="Enter requirement">
                                            <button type="button" class="btn btn-outline-danger remove-requirement">
                                                <i class="fas fa-times"></i>
                                            </button>
                                        </div>
                                    @endforeach
                                @else
                                    <div class="input-group mb-2 requirement-item">
                                        <input type="text" class="form-control" name="requirements[]" placeholder="Enter requirement">
                                        <button type="button" class="btn btn-outline-danger remove-requirement">
                                            <i class="fas fa-times"></i>
                                        </button>
                                    </div>
                                @endif
                            </div>
                            <button type="button" class="btn btn-outline-primary btn-sm" id="add-requirement">
                                <i class="fas fa-plus me-1"></i>Add Requirement
                            </button>
                        </div>

                        <div class="d-flex justify-content-between">
                            <a href="{{ route('admin.scholarships.show', $scholarship->id) }}" class="btn btn-secondary">
                                <i class="fas fa-times me-2"></i>Cancel
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>Update Scholarship
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-info">Quick Actions</h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="{{ route('scholarships.show', $scholarship->slug) }}" target="_blank" class="btn btn-outline-info">
                            <i class="fas fa-external-link-alt me-2"></i>Preview Public Page
                        </a>
                        <a href="{{ route('admin.scholarships.applications') }}" class="btn btn-outline-success">
                            <i class="fas fa-file-alt me-2"></i>View Applications
                        </a>
                        <button type="button" class="btn btn-outline-warning" onclick="duplicateScholarship()">
                            <i class="fas fa-copy me-2"></i>Duplicate Scholarship
                        </button>
                    </div>
                </div>
            </div>

            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-warning">Guidelines</h6>
                </div>
                <div class="card-body">
                    <ul class="small text-muted">
                        <li>Ensure the scholarship title is clear and descriptive</li>
                        <li>Set a realistic application deadline</li>
                        <li>Clearly define eligibility criteria</li>
                        <li>List all required documents and information</li>
                        <li>Review all information before publishing</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Add requirement functionality
    document.getElementById('add-requirement').addEventListener('click', function() {
        const container = document.getElementById('requirements-container');
        const newRequirement = document.createElement('div');
        newRequirement.className = 'input-group mb-2 requirement-item';
        newRequirement.innerHTML = `
            <input type="text" class="form-control" name="requirements[]" placeholder="Enter requirement">
            <button type="button" class="btn btn-outline-danger remove-requirement">
                <i class="fas fa-times"></i>
            </button>
        `;
        container.appendChild(newRequirement);
    });

    // Remove requirement functionality
    document.addEventListener('click', function(e) {
        if (e.target.closest('.remove-requirement')) {
            const item = e.target.closest('.requirement-item');
            if (document.querySelectorAll('.requirement-item').length > 1) {
                item.remove();
            }
        }
    });
});

function duplicateScholarship() {
    if (confirm('This will create a copy of this scholarship. Continue?')) {
        // Add duplication logic here
        console.log('Duplicate scholarship');
    }
}
</script>
@endsection 