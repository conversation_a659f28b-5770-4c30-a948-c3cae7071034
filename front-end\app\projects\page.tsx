"use client"


import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Tabs, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Progress } from "@/components/ui/progress"
import { Users, Heart, MapPin, Target, TrendingUp, Award, CheckCircle, Calendar, DollarSign } from "lucide-react"

const projects = [
  {
    id: 1,
    title: "Back-to-School Initiative 2024",
    description:
      "Providing school uniforms, textbooks, and supplies to 800+ primary school students across Kaduna, Kano, and Katsina states.",
    location: "Kaduna, Kano, Katsina States",
    category: "Education Support",
    status: "Active",
    beneficiaries: 800,
    budget: "₦2.5M",
    startDate: "2024-01-15",
    endDate: "2024-12-31",
    progress: 75,
    impact: [
      "800+ students received complete school supplies",
      "40% improvement in school attendance",
      "Reduced dropout rates by 25%",
    ],
  },
  {
    id: 2,
    title: "University Scholarship Program",
    description:
      "Full tuition scholarships for academically excellent but financially disadvantaged students pursuing higher education.",
    location: "ABU Zaria, Bayero University",
    category: "Higher Education",
    status: "Active",
    beneficiaries: 45,
    budget: "₦8.2M",
    startDate: "2023-09-01",
    endDate: "2027-06-30",
    progress: 60,
    impact: ["45 students currently on scholarship", "95% academic success rate", "12 graduates now employed"],
  },
  {
    id: 3,
    title: "Community Literacy Program",
    description:
      "Adult education and literacy classes for parents and community members to break the cycle of educational poverty.",
    location: "Rural Kaduna Communities",
    category: "Adult Education",
    status: "Active",
    beneficiaries: 200,
    budget: "₦1.8M",
    startDate: "2024-03-01",
    endDate: "2024-11-30",
    progress: 85,
    impact: [
      "200+ adults gained basic literacy",
      "60% can now help children with homework",
      "Improved community participation",
    ],
  },
  {
    id: 4,
    title: "School Library Development",
    description:
      "Establishing and stocking libraries in underserved schools with age-appropriate books and learning materials.",
    location: "15 Primary Schools",
    category: "Infrastructure",
    status: "Completed",
    beneficiaries: 1200,
    budget: "₦3.1M",
    startDate: "2023-06-01",
    endDate: "2024-02-28",
    progress: 100,
    impact: ["15 libraries established", "5,000+ books distributed", "40% increase in reading scores"],
  },
  {
    id: 5,
    title: "Teacher Training Workshop",
    description:
      "Professional development workshops for teachers to improve teaching methods and classroom management skills.",
    location: "Zaria Education Zone",
    category: "Capacity Building",
    status: "Planned",
    beneficiaries: 120,
    budget: "₦1.2M",
    startDate: "2024-07-01",
    endDate: "2024-09-30",
    progress: 0,
    impact: ["120 teachers to be trained", "Modern teaching methods", "Improved student outcomes"],
  },
  {
    id: 6,
    title: "Digital Learning Initiative",
    description:
      "Introducing computer literacy and digital skills training in selected schools to prepare students for the digital age.",
    location: "5 Secondary Schools",
    category: "Technology",
    status: "Planned",
    beneficiaries: 300,
    budget: "₦4.5M",
    startDate: "2024-09-01",
    endDate: "2025-06-30",
    progress: 0,
    impact: ["300 students to gain digital skills", "5 computer labs established", "Future-ready graduates"],
  },
]

export default function ProjectsPage() {
  const getStatusColor = (status: string) => {
    switch (status) {
      case "Active":
        return "bg-green-500"
      case "Completed":
        return "bg-blue-500"
      case "Planned":
        return "bg-amber-500"
      default:
        return "bg-gray-500"
    }
  }

  const filterProjectsByStatus = (status: string) => {
    if (status === "all") return projects
    return projects.filter((project) => project.status.toLowerCase() === status.toLowerCase())
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">

      <main>
        {/* Hero Section */}
        <section className="relative py-20 bg-gradient-to-br from-green-600 to-green-800 text-white">
          <div className="container mx-auto px-4 sm:px-6 lg:px-8">
            <div className="max-w-4xl mx-auto text-center space-y-6">
              <Badge className="bg-green-700 hover:bg-green-600 text-white rounded-full px-4 py-2">Our Projects</Badge>
              <h1 className="text-4xl font-bold tracking-tight sm:text-5xl md:text-6xl">
                Transforming Lives Through Education
              </h1>
              <p className="text-xl text-green-100 max-w-3xl mx-auto">
                Explore the diverse range of educational and support projects we undertake to uplift communities across Nigeria, assisting students, the underprivileged, and those in need.From providing essential school supplies to building modern learning centers and offering support programs, our projects are designed to create sustainable impact across Nigeria for students, underprivileged individuals, and those requiring assistance.
              </p>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-6 mt-12">
                <div className="text-center">
                  <div className="text-3xl font-bold text-amber-400">6</div>
                  <div className="text-green-200 text-sm">Active Projects</div>
                </div>
                <div className="text-center">
                  <div className="text-3xl font-bold text-amber-400">2,500+</div>
                  <div className="text-green-200 text-sm">Beneficiaries</div>
                </div>
                <div className="text-center">
                  <div className="text-3xl font-bold text-amber-400">₦21M</div>
                  <div className="text-green-200 text-sm">Total Investment</div>
                </div>
                <div className="text-center">
                  <div className="text-3xl font-bold text-amber-400">3</div>
                  <div className="text-green-200 text-sm">States Covered</div>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Projects Overview */}
        <section className="py-20 bg-white dark:bg-gray-900">
          <div className="container mx-auto px-4 sm:px-6 lg:px-8">
            <Tabs defaultValue="all" className="space-y-8">
              <div className="flex flex-col md:flex-row gap-4 items-center justify-between">
                <div>
                  <h2 className="text-3xl font-bold tracking-tight mb-2">Our Project Portfolio</h2>
                  <p className="text-gray-600 dark:text-gray-400">
                    Comprehensive educational initiatives across Northern Nigeria
                  </p>
                </div>
                <TabsList className="grid grid-cols-4 w-full md:w-auto">
                  <TabsTrigger value="all">All Projects</TabsTrigger>
                  <TabsTrigger value="active">Active</TabsTrigger>
                  <TabsTrigger value="completed">Completed</TabsTrigger>
                  <TabsTrigger value="planned">Planned</TabsTrigger>
                </TabsList>
              </div>

              <TabsContent value="all" className="space-y-8">
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {projects.map((project) => (
                    <Card key={project.id} className="overflow-hidden hover:shadow-lg transition-shadow duration-300">
                      <CardHeader className="pb-4">
                        <div className="flex items-start justify-between">
                          <Badge className={`${getStatusColor(project.status)} text-white rounded-full`}>
                            {project.status}
                          </Badge>
                          <Badge variant="outline" className="rounded-full">
                            {project.category}
                          </Badge>
                        </div>
                        <CardTitle className="text-xl text-green-800 dark:text-green-200">{project.title}</CardTitle>
                      </CardHeader>
                      <CardContent className="space-y-4">
                        <p className="text-gray-600 dark:text-gray-400 text-sm line-clamp-3">{project.description}</p>

                        <div className="space-y-2">
                          <div className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400">
                            <MapPin className="h-4 w-4 text-green-600" />
                            {project.location}
                          </div>
                          <div className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400">
                            <Users className="h-4 w-4 text-green-600" />
                            {project.beneficiaries} beneficiaries
                          </div>
                          <div className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400">
                            <DollarSign className="h-4 w-4 text-green-600" />
                            Budget: {project.budget}
                          </div>
                          <div className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400">
                            <Calendar className="h-4 w-4 text-green-600" />
                            {project.startDate} - {project.endDate}
                          </div>
                        </div>

                        {project.status !== "Planned" && (
                          <div className="space-y-2">
                            <div className="flex justify-between text-sm">
                              <span>Progress</span>
                              <span>{project.progress}%</span>
                            </div>
                            <Progress value={project.progress} className="h-2" />
                          </div>
                        )}

                        <Button className="w-full bg-green-600 hover:bg-green-700 text-white">View Details</Button>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </TabsContent>

              <TabsContent value="active" className="space-y-8">
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {filterProjectsByStatus("active").map((project) => (
                    <Card key={project.id} className="overflow-hidden hover:shadow-lg transition-shadow duration-300">
                      <CardHeader className="pb-4">
                        <div className="flex items-start justify-between">
                          <Badge className="bg-green-500 text-white rounded-full">Active</Badge>
                          <Badge variant="outline" className="rounded-full">
                            {project.category}
                          </Badge>
                        </div>
                        <CardTitle className="text-xl text-green-800 dark:text-green-200">{project.title}</CardTitle>
                      </CardHeader>
                      <CardContent className="space-y-4">
                        <p className="text-gray-600 dark:text-gray-400 text-sm">{project.description}</p>
                        <div className="space-y-2">
                          <div className="flex justify-between text-sm">
                            <span>Progress</span>
                            <span>{project.progress}%</span>
                          </div>
                          <Progress value={project.progress} className="h-2" />
                        </div>
                        <Button className="w-full bg-green-600 hover:bg-green-700 text-white">View Details</Button>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </TabsContent>

              <TabsContent value="completed" className="space-y-8">
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {filterProjectsByStatus("completed").map((project) => (
                    <Card key={project.id} className="overflow-hidden hover:shadow-lg transition-shadow duration-300">
                      <CardHeader className="pb-4">
                        <div className="flex items-start justify-between">
                          <Badge className="bg-blue-500 text-white rounded-full">Completed</Badge>
                          <Badge variant="outline" className="rounded-full">
                            {project.category}
                          </Badge>
                        </div>
                        <CardTitle className="text-xl text-green-800 dark:text-green-200">{project.title}</CardTitle>
                      </CardHeader>
                      <CardContent className="space-y-4">
                        <p className="text-gray-600 dark:text-gray-400 text-sm">{project.description}</p>
                        <div className="space-y-2">
                          <div className="flex justify-between text-sm">
                            <span>Progress</span>
                            <span>100%</span>
                          </div>
                          <Progress value={100} className="h-2" />
                        </div>
                        <Button className="w-full bg-green-600 hover:bg-green-700 text-white">View Results</Button>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </TabsContent>

              <TabsContent value="planned" className="space-y-8">
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {filterProjectsByStatus("planned").map((project) => (
                    <Card key={project.id} className="overflow-hidden hover:shadow-lg transition-shadow duration-300">
                      <CardHeader className="pb-4">
                        <div className="flex items-start justify-between">
                          <Badge className="bg-amber-500 text-white rounded-full">Planned</Badge>
                          <Badge variant="outline" className="rounded-full">
                            {project.category}
                          </Badge>
                        </div>
                        <CardTitle className="text-xl text-green-800 dark:text-green-200">{project.title}</CardTitle>
                      </CardHeader>
                      <CardContent className="space-y-4">
                        <p className="text-gray-600 dark:text-gray-400 text-sm">{project.description}</p>
                        <div className="space-y-2 text-sm text-gray-600 dark:text-gray-400">
                          <div className="flex items-center gap-2">
                            <Calendar className="h-4 w-4" />
                            Starts: {project.startDate}
                          </div>
                          <div className="flex items-center gap-2">
                            <Target className="h-4 w-4" />
                            Target: {project.beneficiaries} beneficiaries
                          </div>
                        </div>
                        <Button className="w-full bg-green-600 hover:bg-green-700 text-white">Learn More</Button>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </TabsContent>
            </Tabs>
          </div>
        </section>

        {/* Project Impact */}
        <section className="py-20 bg-green-50 dark:bg-green-950/20">
          <div className="container mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-16">
              <h2 className="text-3xl font-bold tracking-tight sm:text-4xl mb-4">Measuring Our Impact</h2>
              <p className="text-gray-600 dark:text-gray-400 text-lg max-w-2xl mx-auto">
                Every project we undertake is designed to create measurable, lasting change in the communities we serve.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <Card className="text-center border-green-100 dark:border-green-800">
                <CardContent className="p-6">
                  <div className="h-16 w-16 bg-green-100 dark:bg-green-900/20 rounded-full flex items-center justify-center mx-auto mb-4">
                    <TrendingUp className="h-8 w-8 text-green-600" />
                  </div>
                  <h3 className="text-xl font-semibold mb-2">Academic Performance</h3>
                  <p className="text-3xl font-bold text-green-600 mb-2">40%</p>
                  <p className="text-gray-600 dark:text-gray-400 text-sm">Average improvement in test scores</p>
                </CardContent>
              </Card>

              <Card className="text-center border-green-100 dark:border-green-800">
                <CardContent className="p-6">
                  <div className="h-16 w-16 bg-blue-100 dark:bg-blue-900/20 rounded-full flex items-center justify-center mx-auto mb-4">
                    <Users className="h-8 w-8 text-blue-600" />
                  </div>
                  <h3 className="text-xl font-semibold mb-2">School Attendance</h3>
                  <p className="text-3xl font-bold text-blue-600 mb-2">85%</p>
                  <p className="text-gray-600 dark:text-gray-400 text-sm">Increase in regular attendance</p>
                </CardContent>
              </Card>

              <Card className="text-center border-green-100 dark:border-green-800">
                <CardContent className="p-6">
                  <div className="h-16 w-16 bg-amber-100 dark:bg-amber-900/20 rounded-full flex items-center justify-center mx-auto mb-4">
                    <Award className="h-8 w-8 text-amber-600" />
                  </div>
                  <h3 className="text-xl font-semibold mb-2">Graduation Rate</h3>
                  <p className="text-3xl font-bold text-amber-600 mb-2">95%</p>
                  <p className="text-gray-600 dark:text-gray-400 text-sm">Of scholarship recipients graduate</p>
                </CardContent>
              </Card>

              <Card className="text-center border-green-100 dark:border-green-800">
                <CardContent className="p-6">
                  <div className="h-16 w-16 bg-purple-100 dark:bg-purple-900/20 rounded-full flex items-center justify-center mx-auto mb-4">
                    <CheckCircle className="h-8 w-8 text-purple-600" />
                  </div>
                  <h3 className="text-xl font-semibold mb-2">Project Success</h3>
                  <p className="text-3xl font-bold text-purple-600 mb-2">98%</p>
                  <p className="text-gray-600 dark:text-gray-400 text-sm">Projects completed on time</p>
                </CardContent>
              </Card>
            </div>
          </div>
        </section>

        {/* Call to Action */}
        <section className="py-20 bg-green-900 text-white">
          <div className="container mx-auto px-4 sm:px-6 lg:px-8">
            <div className="max-w-4xl mx-auto text-center space-y-8">
              <h2 className="text-3xl font-bold tracking-tight sm:text-4xl">Support Our Projects</h2>
              <p className="text-green-100 text-lg">
                Your contribution can help us expand our reach and create even more educational opportunities for
                underprivileged students across Northern Nigeria.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button size="lg" className="bg-amber-500 hover:bg-amber-600 text-green-950 font-semibold">
                  <Heart className="mr-2 h-5 w-5" />
                  Donate to Projects
                </Button>
                <Button size="lg" variant="outline" className="border-2 border-green-400 text-green-400 hover:bg-green-400 hover:text-white font-semibold">
                  Become a Partner
                </Button>
              </div>
            </div>
          </div>
        </section>
      </main>

      {/* Footer */}
      <footer className="bg-green-950 dark:bg-black text-white py-12">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h3 className="text-2xl font-bold mb-4">Building Tomorrow's Leaders</h3>
            <p className="text-green-200 max-w-2xl mx-auto">
              Every project we undertake is a step towards a more educated, empowered Northern Nigeria. Join us in this
              transformative journey.
            </p>
          </div>
        </div>
      </footer>
    </div>
  )
}
