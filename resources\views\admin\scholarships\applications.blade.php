@extends('layouts.admin')

@section('title', 'Scholarship Applications')

@section('content')
<div class="container-fluid">
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">Scholarship Applications</h1>
        <a href="{{ route('admin.scholarships.index') }}" class="btn btn-secondary">
            <i class="fas fa-arrow-left me-2"></i>Back to Scholarships
        </a>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6">
            <div class="card bg-primary text-white mb-4">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <div class="small text-white-50">Total Applications</div>
                            <div class="h4">{{ $applications->total() ?? 0 }}</div>
                        </div>
                        <div>
                            <i class="fas fa-file-alt fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="card bg-warning text-white mb-4">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <div class="small text-white-50">Pending Review</div>
                            <div class="h4">{{ $applications->where('status', 'pending')->count() ?? 0 }}</div>
                        </div>
                        <div>
                            <i class="fas fa-clock fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="card bg-success text-white mb-4">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <div class="small text-white-50">Approved</div>
                            <div class="h4">{{ $applications->where('status', 'approved')->count() ?? 0 }}</div>
                        </div>
                        <div>
                            <i class="fas fa-check fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="card bg-danger text-white mb-4">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <div class="small text-white-50">Rejected</div>
                            <div class="h4">{{ $applications->where('status', 'rejected')->count() ?? 0 }}</div>
                        </div>
                        <div>
                            <i class="fas fa-times fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Applications Table -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Applications Management</h6>
        </div>
        <div class="card-body">
            <!-- Filters -->
            <div class="row mb-3">
                <div class="col-md-4">
                    <div class="input-group">
                        <input type="text" class="form-control" placeholder="Search applications..." id="searchApplications">
                        <button class="btn btn-outline-secondary" type="button">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </div>
                <div class="col-md-3">
                    <select class="form-select" id="filterStatus">
                        <option value="">All Status</option>
                        <option value="pending">Pending</option>
                        <option value="approved">Approved</option>
                        <option value="rejected">Rejected</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <select class="form-select" id="filterScholarship">
                        <option value="">All Scholarships</option>
                        <!-- Scholarship options would be populated here -->
                    </select>
                </div>
                <div class="col-md-2">
                    <input type="date" class="form-control" id="filterDate" title="Filter by application date">
                </div>
            </div>

            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead class="table-dark">
                        <tr>
                            <th>Applicant</th>
                            <th>Scholarship</th>
                            <th>Application Date</th>
                            <th>Status</th>
                            <th>Score/Rating</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <!-- Sample data since we don't have real applications yet -->
                        <tr>
                            <td>
                                <div class="d-flex align-items-center">
                                    <div class="rounded-circle bg-primary text-white d-flex align-items-center justify-content-center me-3" style="width: 40px; height: 40px;">
                                        JD
                                    </div>
                                    <div>
                                        <div class="fw-bold">John Doe</div>
                                        <small class="text-muted"><EMAIL></small>
                                    </div>
                                </div>
                            </td>
                            <td>
                                <div>
                                    <strong>HLTKKQ Excellence Scholarship</strong>
                                    <br><small class="text-muted">₦500,000</small>
                                </div>
                            </td>
                            <td>
                                Dec 15, 2024
                                <br><small class="text-muted">2 days ago</small>
                            </td>
                            <td>
                                <span class="badge bg-warning">Pending</span>
                            </td>
                            <td>
                                <div class="text-center">
                                    <span class="text-muted">Not scored</span>
                                </div>
                            </td>
                            <td>
                                <div class="btn-group" role="group">
                                    <button class="btn btn-sm btn-outline-primary" onclick="viewApplication(1)" title="View">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="btn btn-sm btn-outline-success" onclick="approveApplication(1)" title="Approve">
                                        <i class="fas fa-check"></i>
                                    </button>
                                    <button class="btn btn-sm btn-outline-danger" onclick="rejectApplication(1)" title="Reject">
                                        <i class="fas fa-times"></i>
                                    </button>
                                    <button class="btn btn-sm btn-outline-info" onclick="downloadApplication(1)" title="Download">
                                        <i class="fas fa-download"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td>
                                <div class="d-flex align-items-center">
                                    <div class="rounded-circle bg-success text-white d-flex align-items-center justify-content-center me-3" style="width: 40px; height: 40px;">
                                        JS
                                    </div>
                                    <div>
                                        <div class="fw-bold">Jane Smith</div>
                                        <small class="text-muted"><EMAIL></small>
                                    </div>
                                </div>
                            </td>
                            <td>
                                <div>
                                    <strong>Women in STEM Scholarship</strong>
                                    <br><small class="text-muted">₦300,000</small>
                                </div>
                            </td>
                            <td>
                                Dec 12, 2024
                                <br><small class="text-muted">5 days ago</small>
                            </td>
                            <td>
                                <span class="badge bg-success">Approved</span>
                            </td>
                            <td>
                                <div class="text-center">
                                    <span class="text-success fw-bold">95/100</span>
                                    <br><small class="text-muted">Excellent</small>
                                </div>
                            </td>
                            <td>
                                <div class="btn-group" role="group">
                                    <button class="btn btn-sm btn-outline-primary" onclick="viewApplication(2)" title="View">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="btn btn-sm btn-outline-info" onclick="downloadApplication(2)" title="Download">
                                        <i class="fas fa-download"></i>
                                    </button>
                                    <button class="btn btn-sm btn-outline-secondary" onclick="sendNotification(2)" title="Send Notification">
                                        <i class="fas fa-envelope"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="6" class="text-center py-4 text-muted">
                                <i class="fas fa-file-alt fa-3x mb-3"></i>
                                <p>No more applications to show. Above are sample applications.</p>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- Application Details Modal -->
<div class="modal fade" id="applicationModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Application Details</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="applicationContent">
                <!-- Content will be loaded here -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <button type="button" class="btn btn-success" onclick="approveFromModal()">Approve</button>
                <button type="button" class="btn btn-danger" onclick="rejectFromModal()">Reject</button>
            </div>
        </div>
    </div>
</div>

<script>
function viewApplication(applicationId) {
    const modal = new bootstrap.Modal(document.getElementById('applicationModal'));
    
    // Mock application details
    const content = `
        <div class="row">
            <div class="col-md-6">
                <h6>Applicant Information</h6>
                <p><strong>Name:</strong> John Doe</p>
                <p><strong>Email:</strong> <EMAIL></p>
                <p><strong>Phone:</strong> +234 ************</p>
                <p><strong>Date of Birth:</strong> January 15, 1998</p>
            </div>
            <div class="col-md-6">
                <h6>Application Details</h6>
                <p><strong>Scholarship:</strong> HLTKKQ Excellence Scholarship</p>
                <p><strong>Applied:</strong> December 15, 2024</p>
                <p><strong>Status:</strong> <span class="badge bg-warning">Pending</span></p>
                <p><strong>Documents:</strong> Complete</p>
            </div>
        </div>
        <hr>
        <div>
            <h6>Personal Statement</h6>
            <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.</p>
        </div>
    `;
    
    document.getElementById('applicationContent').innerHTML = content;
    modal.show();
}

function approveApplication(applicationId) {
    if (confirm('Are you sure you want to approve this application?')) {
        console.log('Approve application:', applicationId);
        // Add approval logic here
    }
}

function rejectApplication(applicationId) {
    if (confirm('Are you sure you want to reject this application?')) {
        console.log('Reject application:', applicationId);
        // Add rejection logic here
    }
}

function downloadApplication(applicationId) {
    console.log('Download application:', applicationId);
    // Add download logic here
}

function sendNotification(applicationId) {
    console.log('Send notification for application:', applicationId);
    // Add notification logic here
}

function approveFromModal() {
    // Get current application ID and approve
    console.log('Approve from modal');
}

function rejectFromModal() {
    // Get current application ID and reject
    console.log('Reject from modal');
}
</script>
@endsection 