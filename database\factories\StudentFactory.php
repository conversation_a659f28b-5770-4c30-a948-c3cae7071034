<?php

namespace Database\Factories;

use App\Models\Student;
use App\Models\User;
use App\Models\PartnerOrganization;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Student>
 */
class StudentFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Student::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'user_id' => User::factory(),
            'school_id' => PartnerOrganization::factory()->school(),
            'student_id' => $this->faker->unique()->numerify('STU####'),
            'first_name' => $this->faker->firstName(),
            'last_name' => $this->faker->lastName(),
            'middle_name' => $this->faker->optional()->firstName(),
            'date_of_birth' => $this->faker->date('Y-m-d', '-10 years'),
            'gender' => $this->faker->randomElement(['male', 'female', 'other']),
            'phone' => $this->faker->optional()->phoneNumber(),
            'address' => $this->faker->address(),
            'city' => $this->faker->city(),
            'state' => $this->faker->state(),
            'postal_code' => $this->faker->postcode(),
            'country' => $this->faker->country(),
            'current_grade' => $this->faker->numberBetween(1, 12),
            'academic_year' => $this->faker->randomElement(['2023-2024', '2024-2025', '2025-2026']),
            'enrollment_date' => $this->faker->date('Y-m-d', '-2 years'),
            'status' => $this->faker->randomElement(['active', 'inactive', 'graduated', 'transferred']),
            'parent_guardian_name' => $this->faker->name(),
            'parent_guardian_phone' => $this->faker->phoneNumber(),
            'parent_guardian_email' => $this->faker->optional()->safeEmail(),
            'parent_guardian_relationship' => $this->faker->randomElement(['father', 'mother', 'guardian', 'uncle', 'aunt', 'grandparent']),
            'emergency_contact_name' => $this->faker->name(),
            'emergency_contact_phone' => $this->faker->phoneNumber(),
            'emergency_contact_relationship' => $this->faker->randomElement(['parent', 'sibling', 'relative', 'family_friend']),
            'medical_conditions' => $this->faker->optional()->sentence(),
            'allergies' => $this->faker->optional()->words(3, true),
            'special_needs' => $this->faker->optional()->sentence(),
            'previous_school' => $this->faker->optional()->company() . ' School',
            'transfer_reason' => $this->faker->optional()->sentence(),
            'gpa' => $this->faker->optional()->randomFloat(2, 2.0, 4.0),
            'attendance_rate' => $this->faker->optional()->randomFloat(2, 70.0, 100.0),
            'behavior_notes' => $this->faker->optional()->paragraph(),
            'extracurricular_activities' => $this->faker->optional()->randomElement([
                json_encode(['football', 'debate club']),
                json_encode(['music', 'art club', 'science club']),
                json_encode(['basketball']),
                null
            ]),
            'awards_achievements' => $this->faker->optional()->randomElement([
                json_encode(['Best Student 2023', 'Math Competition Winner']),
                json_encode(['Perfect Attendance']),
                null
            ]),
            'career_interests' => $this->faker->optional()->randomElement([
                json_encode(['medicine', 'engineering']),
                json_encode(['teaching', 'law']),
                json_encode(['business']),
                null
            ]),
            'financial_need_level' => $this->faker->randomElement(['low', 'medium', 'high', 'critical']),
            'household_income' => $this->faker->optional()->randomElement(['<50000', '************', '100000-200000', '>200000']),
            'family_size' => $this->faker->numberBetween(2, 10),
            'scholarship_history' => $this->faker->optional()->randomElement([
                json_encode([['scholarship' => 'Merit Award 2023', 'amount' => 25000]]),
                json_encode([['scholarship' => 'Need-based Grant 2022', 'amount' => 15000]]),
                null
            ]),
            'documents' => $this->faker->optional()->randomElement([
                json_encode(['birth_certificate.pdf', 'school_report.pdf']),
                json_encode(['transcript.pdf']),
                null
            ]),
            'photo' => $this->faker->optional()->imageUrl(150, 150, 'people'),
            'notes' => $this->faker->optional()->paragraph(),
            'created_at' => now(),
            'updated_at' => now(),
        ];
    }

    /**
     * Indicate that the student is in primary school.
     */
    public function primary(): static
    {
        return $this->state(fn (array $attributes) => [
            'current_grade' => $this->faker->numberBetween(1, 6),
            'school_id' => PartnerOrganization::factory()->school(),
        ]);
    }

    /**
     * Indicate that the student is in secondary school.
     */
    public function secondary(): static
    {
        return $this->state(fn (array $attributes) => [
            'current_grade' => $this->faker->numberBetween(7, 12),
            'school_id' => PartnerOrganization::factory()->school(),
        ]);
    }

    /**
     * Indicate that the student is in university.
     */
    public function university(): static
    {
        return $this->state(fn (array $attributes) => [
            'current_grade' => $this->faker->numberBetween(13, 16), // University years
            'school_id' => PartnerOrganization::factory()->university(),
            'academic_year' => $this->faker->randomElement(['Year 1', 'Year 2', 'Year 3', 'Year 4']),
        ]);
    }

    /**
     * Indicate that the student has high financial need.
     */
    public function highNeed(): static
    {
        return $this->state(fn (array $attributes) => [
            'financial_need_level' => 'critical',
            'household_income' => '<50000',
        ]);
    }

    /**
     * Indicate that the student is active.
     */
    public function active(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'active',
        ]);
    }
}
