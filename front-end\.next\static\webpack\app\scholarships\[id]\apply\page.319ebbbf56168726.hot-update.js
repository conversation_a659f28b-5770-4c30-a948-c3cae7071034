"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/scholarships/[id]/apply/page",{

/***/ "(app-pages-browser)/./lib/api.ts":
/*!********************!*\
  !*** ./lib/api.ts ***!
  \********************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ApiClient: () => (/* binding */ ApiClient),\n/* harmony export */   apiClient: () => (/* binding */ apiClient),\n/* harmony export */   extractArrayData: () => (/* binding */ extractArrayData),\n/* harmony export */   getCurrentUser: () => (/* binding */ getCurrentUser),\n/* harmony export */   isAuthenticated: () => (/* binding */ isAuthenticated),\n/* harmony export */   logout: () => (/* binding */ logout)\n/* harmony export */ });\nconst API_BASE_URL = \"http://localhost:8000/api/v1\" || 0;\n// Utility function to extract array data from paginated or direct responses\nconst extractArrayData = (response)=>{\n    if (!response || !response.data) return [];\n    // If data is already an array, return it\n    if (Array.isArray(response.data)) {\n        return response.data;\n    }\n    // If data has a data property (paginated response), return that array\n    if (response.data.data && Array.isArray(response.data.data)) {\n        return response.data.data;\n    }\n    // Default to empty array\n    return [];\n};\nclass ApiClient {\n    async request(endpoint) {\n        let options = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};\n        // Use the base URL directly if it already contains /api/v1, otherwise add it\n        const url = this.baseURL.includes('/api/v1') ? \"\".concat(this.baseURL).concat(endpoint) : \"\".concat(this.baseURL, \"/api/v1\").concat(endpoint);\n        const config = {\n            headers: {\n                'Content-Type': 'application/json',\n                'Accept': 'application/json',\n                ...options.headers\n            },\n            ...options\n        };\n        // Add authentication header if token exists\n        const token =  true ? localStorage.getItem('authToken') : 0;\n        if (token) {\n            config.headers = {\n                ...config.headers,\n                'Authorization': \"Bearer \".concat(token)\n            };\n        }\n        try {\n            console.log(\"API Request: \".concat(options.method || 'GET', \" \").concat(url)) // Debug logging\n            ;\n            const response = await fetch(url, config);\n            const data = await response.json();\n            console.log(\"API Response for \".concat(endpoint, \":\"), data) // Debug logging\n            ;\n            // Handle 401 Unauthorized - redirect to login\n            if (response.status === 401) {\n                if (true) {\n                    localStorage.removeItem('authToken');\n                    localStorage.removeItem('user');\n                    window.location.href = '/auth/login';\n                }\n            }\n            return data;\n        } catch (error) {\n            console.error('API request failed:', error);\n            throw error;\n        }\n    }\n    // Auth endpoints\n    async login(email, password) {\n        var _response_data;\n        const response = await this.request('/login', {\n            method: 'POST',\n            body: JSON.stringify({\n                email,\n                password\n            })\n        });\n        // Store token and user data if login successful\n        if (response.success && ((_response_data = response.data) === null || _response_data === void 0 ? void 0 : _response_data.access_token)) {\n            if (true) {\n                localStorage.setItem('authToken', response.data.access_token);\n                localStorage.setItem('user', JSON.stringify(response.data.user));\n            }\n        }\n        return response;\n    }\n    async register(userData) {\n        var _response_data;\n        // First, register the basic user\n        const response = await this.request('/register', {\n            method: 'POST',\n            body: JSON.stringify({\n                first_name: userData.first_name,\n                last_name: userData.last_name,\n                email: userData.email,\n                password: userData.password,\n                password_confirmation: userData.password_confirmation,\n                phone_number: userData.phone_number,\n                address: userData.address,\n                date_of_birth: userData.date_of_birth,\n                city: userData.city,\n                state: userData.state,\n                country: userData.country\n            })\n        });\n        // Store token and user data if registration successful\n        if (response.success && ((_response_data = response.data) === null || _response_data === void 0 ? void 0 : _response_data.token)) {\n            if (true) {\n                localStorage.setItem('authToken', response.data.token);\n                localStorage.setItem('user', JSON.stringify(response.data.user));\n            }\n        }\n        // If registration is successful, handle user type specific data\n        if (response.success && userData.user_type && userData.additional_data) {\n            try {\n                if (userData.user_type === 'volunteer') {\n                    // Submit volunteer application\n                    await this.applyAsVolunteer(userData.additional_data);\n                } else if (userData.user_type === 'student' || userData.user_type === 'partner') {\n                    // Store additional data in user preferences\n                    const preferences = {\n                        user_type: userData.user_type,\n                        profile_data: userData.additional_data,\n                        profile_completed: true\n                    };\n                    await this.updateUserPreferences(preferences);\n                }\n            } catch (error) {\n                console.error('Additional data submission failed:', error);\n                // Return the user registration success but note additional data failed\n                return {\n                    ...response,\n                    message: response.message + ' However, additional profile information could not be saved. You can complete your profile later.'\n                };\n            }\n        }\n        return response;\n    }\n    async logout() {\n        const response = await this.request('/logout', {\n            method: 'POST'\n        });\n        // Clear stored token and user data on logout\n        if (true) {\n            localStorage.removeItem('authToken');\n            localStorage.removeItem('user');\n        }\n        return response;\n    }\n    async getUser() {\n        return this.request('/user');\n    }\n    async forgotPassword(email) {\n        return this.request('/forgot-password', {\n            method: 'POST',\n            body: JSON.stringify({\n                email\n            })\n        });\n    }\n    // Profile endpoints\n    async getProfile() {\n        return this.request('/profile');\n    }\n    async updateProfile(profileData) {\n        return this.request('/profile', {\n            method: 'PUT',\n            body: JSON.stringify(profileData)\n        });\n    }\n    async updateUserPreferences(preferences) {\n        return this.request('/profile', {\n            method: 'PUT',\n            body: JSON.stringify({\n                preferences\n            })\n        });\n    }\n    async uploadAvatar(file) {\n        const formData = new FormData();\n        formData.append('avatar', file);\n        // Use the base URL directly if it already contains /api/v1, otherwise add it\n        const url = this.baseURL.includes('/api/v1') ? \"\".concat(this.baseURL, \"/profile/avatar\") : \"\".concat(this.baseURL, \"/api/v1/profile/avatar\");\n        const token =  true ? localStorage.getItem('authToken') : 0;\n        try {\n            const response = await fetch(url, {\n                method: 'POST',\n                headers: {\n                    'Authorization': token ? \"Bearer \".concat(token) : '',\n                    'Accept': 'application/json'\n                },\n                body: formData\n            });\n            const data = await response.json();\n            if (response.status === 401) {\n                if (true) {\n                    localStorage.removeItem('authToken');\n                    localStorage.removeItem('user');\n                    window.location.href = '/auth/login';\n                }\n            }\n            return data;\n        } catch (error) {\n            console.error('Avatar upload failed:', error);\n            throw error;\n        }\n    }\n    async changePassword(passwordData) {\n        return this.request('/profile/password', {\n            method: 'PUT',\n            body: JSON.stringify(passwordData)\n        });\n    }\n    async generateQrCode() {\n        return this.request('/profile/generate-qr', {\n            method: 'POST'\n        });\n    }\n    async getIdCard() {\n        return this.request('/profile/id-card');\n    }\n    // Dashboard endpoints\n    async getDashboardSummary() {\n        return this.request('/dashboard/summary');\n    }\n    // Volunteer endpoints\n    async getVolunteerApplication() {\n        return this.request('/volunteer/application');\n    }\n    async getVolunteerHours() {\n        return this.request('/volunteer/hours');\n    }\n    async getVolunteerOpportunities() {\n        return this.request('/volunteer/opportunities');\n    }\n    async logVolunteerHours(hoursData) {\n        return this.request('/volunteer/hours', {\n            method: 'POST',\n            body: JSON.stringify(hoursData)\n        });\n    }\n    // Scholarship endpoints\n    async getMyScholarshipApplications() {\n        return this.request('/scholarships/my-applications');\n    }\n    async getScholarships() {\n        return this.request('/scholarships');\n    }\n    async applyForScholarship(scholarshipId, applicationData) {\n        return this.request(\"/scholarships/\".concat(scholarshipId, \"/apply\"), {\n            method: 'POST',\n            body: JSON.stringify(applicationData)\n        });\n    }\n    // Event endpoints\n    async getMyEventRegistrations() {\n        return this.request('/events/my-registrations');\n    }\n    async getUpcomingEvents() {\n        return this.request('/events/upcoming');\n    }\n    // Helper method for building query strings\n    buildQueryString(params) {\n        const searchParams = new URLSearchParams();\n        Object.entries(params).forEach((param)=>{\n            let [key, value] = param;\n            if (value !== undefined && value !== null && value !== '') {\n                if (Array.isArray(value)) {\n                    value.forEach((item)=>searchParams.append(\"\".concat(key, \"[]\"), item));\n                } else {\n                    searchParams.append(key, value.toString());\n                }\n            }\n        });\n        return searchParams.toString();\n    }\n    // Admin Dashboard endpoints\n    async getAdminDashboard() {\n        return this.request('/admin/dashboard');\n    }\n    async getAdminAnalytics(params) {\n        const queryString = params ? \"?\".concat(this.buildQueryString(params)) : '';\n        return this.request(\"/admin/dashboard/analytics\".concat(queryString));\n    }\n    async getAdminStats(params) {\n        const queryString = params ? \"?\".concat(this.buildQueryString(params)) : '';\n        return this.request(\"/admin/dashboard/stats\".concat(queryString));\n    }\n    // Admin User Management\n    async getAdminUsers(params) {\n        const queryString = params ? \"?\".concat(this.buildQueryString(params)) : '';\n        return this.request(\"/admin/users\".concat(queryString));\n    }\n    async createAdminUser(userData) {\n        return this.request('/admin/users', {\n            method: 'POST',\n            body: JSON.stringify(userData)\n        });\n    }\n    async updateAdminUser(userId, userData) {\n        return this.request(\"/admin/users/\".concat(userId), {\n            method: 'PUT',\n            body: JSON.stringify(userData)\n        });\n    }\n    async deleteAdminUser(userId) {\n        return this.request(\"/admin/users/\".concat(userId), {\n            method: 'DELETE'\n        });\n    }\n    async bulkActionUsers(data) {\n        return this.request('/admin/users/bulk-action', {\n            method: 'POST',\n            body: JSON.stringify(data)\n        });\n    }\n    async exportUsers(params) {\n        const queryString = params ? \"?\".concat(this.buildQueryString(params)) : '';\n        return this.request(\"/admin/users/export\".concat(queryString));\n    }\n    // Admin Scholarship Management\n    async getScholarshipApplications(params) {\n        const queryString = params ? \"?\".concat(this.buildQueryString(params)) : '';\n        return this.request(\"/admin/scholarship-applications\".concat(queryString));\n    }\n    async reviewScholarshipApplication(applicationId, reviewData) {\n        return this.request(\"/admin/scholarship-applications/\".concat(applicationId, \"/review\"), {\n            method: 'PUT',\n            body: JSON.stringify(reviewData)\n        });\n    }\n    async bulkActionScholarshipApplications(data) {\n        return this.request('/admin/scholarship-applications/bulk-action', {\n            method: 'POST',\n            body: JSON.stringify(data)\n        });\n    }\n    async exportScholarshipApplications(params) {\n        const queryString = params ? \"?\".concat(this.buildQueryString(params)) : '';\n        return this.request(\"/admin/scholarship-applications/export\".concat(queryString));\n    }\n    async getScholarshipStatistics() {\n        return this.request('/admin/scholarships/statistics');\n    }\n    // Admin Event Management\n    async getAdminEvents(params) {\n        const queryString = params ? \"?\".concat(this.buildQueryString(params)) : '';\n        return this.request(\"/admin/events\".concat(queryString));\n    }\n    async createAdminEvent(eventData) {\n        return this.request('/admin/events', {\n            method: 'POST',\n            body: JSON.stringify(eventData)\n        });\n    }\n    async updateAdminEvent(eventId, eventData) {\n        return this.request(\"/admin/events/\".concat(eventId), {\n            method: 'PUT',\n            body: JSON.stringify(eventData)\n        });\n    }\n    async deleteAdminEvent(eventId) {\n        return this.request(\"/admin/events/\".concat(eventId), {\n            method: 'DELETE'\n        });\n    }\n    async bulkActionEvents(data) {\n        return this.request('/admin/events/bulk-action', {\n            method: 'POST',\n            body: JSON.stringify(data)\n        });\n    }\n    async exportEvents(params) {\n        const queryString = params ? \"?\".concat(this.buildQueryString(params)) : '';\n        return this.request(\"/admin/events/export\".concat(queryString));\n    }\n    async getEventStatistics() {\n        return this.request('/admin/events/statistics');\n    }\n    // Admin Program Management\n    async getAdminPrograms(params) {\n        const queryString = params ? \"?\".concat(this.buildQueryString(params)) : '';\n        return this.request(\"/admin/programs\".concat(queryString));\n    }\n    async createAdminProgram(programData) {\n        return this.request('/admin/programs', {\n            method: 'POST',\n            body: JSON.stringify(programData)\n        });\n    }\n    async updateAdminProgram(programId, programData) {\n        return this.request(\"/admin/programs/\".concat(programId), {\n            method: 'PUT',\n            body: JSON.stringify(programData)\n        });\n    }\n    async deleteAdminProgram(programId) {\n        return this.request(\"/admin/programs/\".concat(programId), {\n            method: 'DELETE'\n        });\n    }\n    // Admin Blog Management\n    async getAdminBlogPosts(params) {\n        const queryString = params ? \"?\".concat(this.buildQueryString(params)) : '';\n        return this.request(\"/admin/blog/posts\".concat(queryString));\n    }\n    async createAdminBlogPost(postData) {\n        return this.request('/admin/blog/posts', {\n            method: 'POST',\n            body: JSON.stringify(postData)\n        });\n    }\n    async updateAdminBlogPost(postId, postData) {\n        return this.request(\"/admin/blog/posts/\".concat(postId), {\n            method: 'PUT',\n            body: JSON.stringify(postData)\n        });\n    }\n    async deleteAdminBlogPost(postId) {\n        return this.request(\"/admin/blog/posts/\".concat(postId), {\n            method: 'DELETE'\n        });\n    }\n    async exportBlogPosts(params) {\n        const queryString = params ? \"?\".concat(this.buildQueryString(params)) : '';\n        return this.request(\"/admin/blog/posts/export\".concat(queryString));\n    }\n    // Donation endpoints\n    async getMyDonations() {\n        return this.request('/donations/my-donations');\n    }\n    async getDonationCampaigns() {\n        return this.request('/donations/campaigns');\n    }\n    // Blog endpoints\n    async getBlogPosts() {\n        let page = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 1;\n        return this.request(\"/blog/posts?page=\".concat(page));\n    }\n    async getBlogPost(slug) {\n        return this.request(\"/blog/posts/\".concat(slug));\n    }\n    // Events endpoints\n    async getEvents() {\n        let page = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 1;\n        return this.request(\"/events?page=\".concat(page));\n    }\n    async getEvent(id) {\n        return this.request(\"/events/\".concat(id));\n    }\n    async registerForEvent(eventId, additionalInfo) {\n        return this.request(\"/events/\".concat(eventId, \"/register\"), {\n            method: 'POST',\n            body: JSON.stringify({\n                additional_info: additionalInfo\n            })\n        });\n    }\n    // Programs endpoints\n    async getPrograms() {\n        let page = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 1;\n        return this.request(\"/programs?page=\".concat(page));\n    }\n    async getProgram(slug) {\n        return this.request(\"/programs/\".concat(slug));\n    }\n    // Additional scholarship endpoint\n    async getScholarship(id) {\n        return this.request(\"/scholarships/\".concat(id));\n    }\n    // Public scholarships endpoints (no authentication required)\n    async getPublicScholarships(params) {\n        const queryString = params ? \"?\".concat(this.buildQueryString(params)) : '';\n        return this.request(\"/public-scholarships\".concat(queryString));\n    }\n    async getPublicScholarship(id) {\n        return this.request(\"/public-scholarships/\".concat(id));\n    }\n    async submitScholarshipApplication(formData) {\n        const token = this.getToken();\n        const url = \"\".concat(this.baseURL, \"/api/v1/apply-scholarship\");\n        try {\n            const response = await fetch(url, {\n                method: 'POST',\n                headers: {\n                    'Authorization': token ? \"Bearer \".concat(token) : '',\n                    'Accept': 'application/json'\n                },\n                body: formData\n            });\n            const data = await response.json();\n            console.log(\"API Response for scholarship application:\", data);\n            if (!response.ok) {\n                throw new Error(data.message || \"HTTP error! status: \".concat(response.status));\n            }\n            return data;\n        } catch (error) {\n            console.error('Error submitting scholarship application:', error);\n            throw error;\n        }\n    }\n    async applyAsVolunteer(applicationData) {\n        return this.request('/volunteer/apply', {\n            method: 'POST',\n            body: JSON.stringify(applicationData)\n        });\n    }\n    // Contact endpoints\n    async submitContactForm(contactData) {\n        return this.request('/contact', {\n            method: 'POST',\n            body: JSON.stringify(contactData)\n        });\n    }\n    // Newsletter endpoints\n    async subscribeToNewsletter(email) {\n        return this.request('/newsletter/subscribe', {\n            method: 'POST',\n            body: JSON.stringify({\n                email\n            })\n        });\n    }\n    // Settings endpoint\n    async getSettings() {\n        return this.request('/settings');\n    }\n    constructor(baseURL = API_BASE_URL !== null && API_BASE_URL !== void 0 ? API_BASE_URL : \"\"){\n        this.baseURL = baseURL;\n    }\n}\n// Create and export a default instance\nconst apiClient = new ApiClient();\n// Export the class for custom instances\n\n// Export the extractArrayData utility function\n\n// Helper function to check if user is authenticated\nconst isAuthenticated = ()=>{\n    if (false) {}\n    return !!localStorage.getItem('authToken');\n};\n// Helper function to get current user\nconst getCurrentUser = ()=>{\n    if (false) {}\n    const userStr = localStorage.getItem('user');\n    return userStr ? JSON.parse(userStr) : null;\n};\n// Helper function to logout\nconst logout = async ()=>{\n    try {\n        await apiClient.logout();\n    } catch (error) {\n        console.error('Logout error:', error);\n    } finally{\n        if (true) {\n            localStorage.removeItem('authToken');\n            localStorage.removeItem('user');\n            window.location.href = '/auth/login';\n        }\n    }\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/api.ts\n"));

/***/ })

});