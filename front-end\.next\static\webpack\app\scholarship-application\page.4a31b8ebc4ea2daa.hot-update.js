"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/scholarship-application/page",{

/***/ "(app-pages-browser)/./components/scholarship/ScholarshipApplicationPage.tsx":
/*!***************************************************************!*\
  !*** ./components/scholarship/ScholarshipApplicationPage.tsx ***!
  \***************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ScholarshipApplicationPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./components/ui/label.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/textarea */ \"(app-pages-browser)/./components/ui/textarea.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./components/ui/select.tsx\");\n/* harmony import */ var _components_ui_tabs__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/tabs */ \"(app-pages-browser)/./components/ui/tabs.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BookOpen_Building_CheckCircle_CreditCard_FileUp_GraduationCap_Loader2_MapPin_MessageSquare_Phone_School_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BookOpen,Building,CheckCircle,CreditCard,FileUp,GraduationCap,Loader2,MapPin,MessageSquare,Phone,School,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/book-open.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BookOpen_Building_CheckCircle_CreditCard_FileUp_GraduationCap_Loader2_MapPin_MessageSquare_Phone_School_User_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BookOpen,Building,CheckCircle,CreditCard,FileUp,GraduationCap,Loader2,MapPin,MessageSquare,Phone,School,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/school.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BookOpen_Building_CheckCircle_CreditCard_FileUp_GraduationCap_Loader2_MapPin_MessageSquare_Phone_School_User_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BookOpen,Building,CheckCircle,CreditCard,FileUp,GraduationCap,Loader2,MapPin,MessageSquare,Phone,School,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/graduation-cap.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BookOpen_Building_CheckCircle_CreditCard_FileUp_GraduationCap_Loader2_MapPin_MessageSquare_Phone_School_User_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BookOpen,Building,CheckCircle,CreditCard,FileUp,GraduationCap,Loader2,MapPin,MessageSquare,Phone,School,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/triangle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BookOpen_Building_CheckCircle_CreditCard_FileUp_GraduationCap_Loader2_MapPin_MessageSquare_Phone_School_User_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BookOpen,Building,CheckCircle,CreditCard,FileUp,GraduationCap,Loader2,MapPin,MessageSquare,Phone,School,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BookOpen_Building_CheckCircle_CreditCard_FileUp_GraduationCap_Loader2_MapPin_MessageSquare_Phone_School_User_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BookOpen,Building,CheckCircle,CreditCard,FileUp,GraduationCap,Loader2,MapPin,MessageSquare,Phone,School,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/phone.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BookOpen_Building_CheckCircle_CreditCard_FileUp_GraduationCap_Loader2_MapPin_MessageSquare_Phone_School_User_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BookOpen,Building,CheckCircle,CreditCard,FileUp,GraduationCap,Loader2,MapPin,MessageSquare,Phone,School,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BookOpen_Building_CheckCircle_CreditCard_FileUp_GraduationCap_Loader2_MapPin_MessageSquare_Phone_School_User_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BookOpen,Building,CheckCircle,CreditCard,FileUp,GraduationCap,Loader2,MapPin,MessageSquare,Phone,School,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/building.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BookOpen_Building_CheckCircle_CreditCard_FileUp_GraduationCap_Loader2_MapPin_MessageSquare_Phone_School_User_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BookOpen,Building,CheckCircle,CreditCard,FileUp,GraduationCap,Loader2,MapPin,MessageSquare,Phone,School,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/credit-card.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BookOpen_Building_CheckCircle_CreditCard_FileUp_GraduationCap_Loader2_MapPin_MessageSquare_Phone_School_User_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BookOpen,Building,CheckCircle,CreditCard,FileUp,GraduationCap,Loader2,MapPin,MessageSquare,Phone,School,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BookOpen_Building_CheckCircle_CreditCard_FileUp_GraduationCap_Loader2_MapPin_MessageSquare_Phone_School_User_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BookOpen,Building,CheckCircle,CreditCard,FileUp,GraduationCap,Loader2,MapPin,MessageSquare,Phone,School,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BookOpen_Building_CheckCircle_CreditCard_FileUp_GraduationCap_Loader2_MapPin_MessageSquare_Phone_School_User_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BookOpen,Building,CheckCircle,CreditCard,FileUp,GraduationCap,Loader2,MapPin,MessageSquare,Phone,School,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-up.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BookOpen_Building_CheckCircle_CreditCard_FileUp_GraduationCap_Loader2_MapPin_MessageSquare_Phone_School_User_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BookOpen,Building,CheckCircle,CreditCard,FileUp,GraduationCap,Loader2,MapPin,MessageSquare,Phone,School,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/message-square.js\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./hooks/use-toast.ts\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./lib/api.ts\");\n/* harmony import */ var _SuccessMessage__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./SuccessMessage */ \"(app-pages-browser)/./components/scholarship/SuccessMessage.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\nfunction ScholarshipApplicationPage() {\n    _s();\n    const [selectedCategory, setSelectedCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('primary');\n    const [submitting, setSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showSuccess, setShowSuccess] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_9__.useToast)();\n    // Form data for different categories\n    const [primaryFormData, setPrimaryFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        student_full_name: '',\n        age: '',\n        current_class: '',\n        father_name: '',\n        mother_name: '',\n        parent_phone: '',\n        home_address: '',\n        school_name: '',\n        headmaster_name: '',\n        school_account_number: '',\n        reason_for_scholarship: '',\n        current_school_fee: '',\n        supporting_information: ''\n    });\n    const [secondaryFormData, setSecondaryFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        student_full_name: '',\n        age: '',\n        class: '',\n        parent_phone: '',\n        address: '',\n        school_name: '',\n        principal_name: '',\n        principal_account_number: '',\n        reason_for_scholarship: '',\n        school_fee_amount: ''\n    });\n    const [universityFormData, setUniversityFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        full_name: '',\n        age: '',\n        course_of_study: '',\n        current_level: '',\n        phone_number: '',\n        email_address: '',\n        matriculation_number: '',\n        reason_for_scholarship: ''\n    });\n    // File uploads for different categories\n    const [primaryFiles, setPrimaryFiles] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        student_picture: null\n    });\n    const [secondaryFiles, setSecondaryFiles] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        student_picture: null\n    });\n    const [universityFiles, setUniversityFiles] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        student_id_card: null,\n        payment_evidence: null,\n        supporting_documents: []\n    });\n    // Category configuration - Updated to match platform design system\n    const categoryConfig = {\n        primary: {\n            icon: _barrel_optimize_names_AlertTriangle_BookOpen_Building_CheckCircle_CreditCard_FileUp_GraduationCap_Loader2_MapPin_MessageSquare_Phone_School_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n            title: \"Primary School Scholarship\",\n            description: \"For Primary 1-6 students (Filled by Parent/Guardian)\",\n            color: \"bg-blue-500\",\n            bgColor: \"bg-blue-50\",\n            textColor: \"text-blue-700\",\n            borderColor: \"border-blue-200\"\n        },\n        secondary: {\n            icon: _barrel_optimize_names_AlertTriangle_BookOpen_Building_CheckCircle_CreditCard_FileUp_GraduationCap_Loader2_MapPin_MessageSquare_Phone_School_User_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n            title: \"Secondary School Scholarship\",\n            description: \"For Secondary school students (Filled by Student)\",\n            color: \"bg-green-500\",\n            bgColor: \"bg-green-50\",\n            textColor: \"text-green-700\",\n            borderColor: \"border-green-200\"\n        },\n        university: {\n            icon: _barrel_optimize_names_AlertTriangle_BookOpen_Building_CheckCircle_CreditCard_FileUp_GraduationCap_Loader2_MapPin_MessageSquare_Phone_School_User_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"],\n            title: \"University Scholarship\",\n            description: \"For University students (Filled by Student)\",\n            color: \"bg-purple-500\",\n            bgColor: \"bg-purple-50\",\n            textColor: \"text-purple-700\",\n            borderColor: \"border-purple-200\"\n        }\n    };\n    const handleFileChange = (category, field, file)=>{\n        if (category === 'primary') {\n            setPrimaryFiles((prev)=>({\n                    ...prev,\n                    [field]: file\n                }));\n        } else if (category === 'secondary') {\n            setSecondaryFiles((prev)=>({\n                    ...prev,\n                    [field]: file\n                }));\n        } else if (category === 'university') {\n            if (field === 'supporting_documents' && file) {\n                setUniversityFiles((prev)=>({\n                        ...prev,\n                        supporting_documents: [\n                            ...prev.supporting_documents,\n                            file\n                        ]\n                    }));\n            } else {\n                setUniversityFiles((prev)=>({\n                        ...prev,\n                        [field]: file\n                    }));\n            }\n        }\n    };\n    const handleMultipleFileChange = (files)=>{\n        if (files && selectedCategory === 'university') {\n            const fileArray = Array.from(files);\n            setUniversityFiles((prev)=>({\n                    ...prev,\n                    supporting_documents: [\n                        ...prev.supporting_documents,\n                        ...fileArray\n                    ]\n                }));\n        }\n    };\n    const removeFile = (index)=>{\n        setUniversityFiles((prev)=>({\n                ...prev,\n                supporting_documents: prev.supporting_documents.filter((_, i)=>i !== index)\n            }));\n    };\n    const validateForm = ()=>{\n        if (selectedCategory === 'primary') {\n            const required = [\n                'student_full_name',\n                'age',\n                'current_class',\n                'father_name',\n                'mother_name',\n                'parent_phone',\n                'home_address',\n                'school_name',\n                'headmaster_name',\n                'school_account_number',\n                'reason_for_scholarship',\n                'current_school_fee'\n            ];\n            return required.every((field)=>primaryFormData[field].trim() !== '') && primaryFiles.student_picture;\n        } else if (selectedCategory === 'secondary') {\n            const required = [\n                'student_full_name',\n                'age',\n                'class',\n                'parent_phone',\n                'address',\n                'school_name',\n                'principal_name',\n                'principal_account_number',\n                'reason_for_scholarship',\n                'school_fee_amount'\n            ];\n            return required.every((field)=>secondaryFormData[field].trim() !== '') && secondaryFiles.student_picture;\n        } else if (selectedCategory === 'university') {\n            const required = [\n                'full_name',\n                'age',\n                'course_of_study',\n                'current_level',\n                'phone_number',\n                'email_address',\n                'matriculation_number',\n                'reason_for_scholarship'\n            ];\n            return required.every((field)=>universityFormData[field].trim() !== '') && universityFiles.student_id_card && universityFiles.payment_evidence;\n        }\n        return false;\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        if (!validateForm()) {\n            toast({\n                title: \"Validation Error\",\n                description: \"Please fill in all required fields and upload required documents\",\n                variant: \"destructive\"\n            });\n            return;\n        }\n        try {\n            setSubmitting(true);\n            const formData = new FormData();\n            formData.append('category', selectedCategory);\n            // Add form data based on category\n            if (selectedCategory === 'primary') {\n                formData.append('form_data', JSON.stringify(primaryFormData));\n                if (primaryFiles.student_picture) {\n                    formData.append('student_picture', primaryFiles.student_picture);\n                }\n            } else if (selectedCategory === 'secondary') {\n                formData.append('form_data', JSON.stringify(secondaryFormData));\n                if (secondaryFiles.student_picture) {\n                    formData.append('student_picture', secondaryFiles.student_picture);\n                }\n            } else if (selectedCategory === 'university') {\n                formData.append('form_data', JSON.stringify(universityFormData));\n                if (universityFiles.student_id_card) {\n                    formData.append('student_id_card', universityFiles.student_id_card);\n                }\n                if (universityFiles.payment_evidence) {\n                    formData.append('payment_evidence', universityFiles.payment_evidence);\n                }\n                universityFiles.supporting_documents.forEach((file, index)=>{\n                    formData.append(\"supporting_documents[\".concat(index, \"]\"), file);\n                });\n            }\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_10__.apiClient.post('/scholarships/apply', formData, {\n                headers: {\n                    'Content-Type': 'multipart/form-data'\n                }\n            });\n            if (response.success) {\n                // Show success message\n                setShowSuccess(true);\n                // Reset form\n                if (selectedCategory === 'primary') {\n                    setPrimaryFormData({\n                        student_full_name: '',\n                        age: '',\n                        current_class: '',\n                        father_name: '',\n                        mother_name: '',\n                        parent_phone: '',\n                        home_address: '',\n                        school_name: '',\n                        headmaster_name: '',\n                        school_account_number: '',\n                        reason_for_scholarship: '',\n                        current_school_fee: '',\n                        supporting_information: ''\n                    });\n                    setPrimaryFiles({\n                        student_picture: null\n                    });\n                } else if (selectedCategory === 'secondary') {\n                    setSecondaryFormData({\n                        student_full_name: '',\n                        age: '',\n                        class: '',\n                        parent_phone: '',\n                        address: '',\n                        school_name: '',\n                        principal_name: '',\n                        principal_account_number: '',\n                        reason_for_scholarship: '',\n                        school_fee_amount: ''\n                    });\n                    setSecondaryFiles({\n                        student_picture: null\n                    });\n                } else if (selectedCategory === 'university') {\n                    setUniversityFormData({\n                        full_name: '',\n                        age: '',\n                        course_of_study: '',\n                        current_level: '',\n                        phone_number: '',\n                        email_address: '',\n                        matriculation_number: '',\n                        reason_for_scholarship: ''\n                    });\n                    setUniversityFiles({\n                        student_id_card: null,\n                        payment_evidence: null,\n                        supporting_documents: []\n                    });\n                }\n            } else {\n                toast({\n                    title: \"Error\",\n                    description: response.message || \"Failed to submit application\",\n                    variant: \"destructive\"\n                });\n            }\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error('Error submitting application:', error);\n            toast({\n                title: \"Error\",\n                description: ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || \"Failed to submit application\",\n                variant: \"destructive\"\n            });\n        } finally{\n            setSubmitting(false);\n        }\n    };\n    const handleBackToForm = ()=>{\n        setShowSuccess(false);\n    };\n    const handleGoHome = ()=>{\n        // Navigate to dashboard or home page\n        window.location.href = '/dashboard';\n    };\n    if (showSuccess) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_SuccessMessage__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n            onBackToForm: handleBackToForm,\n            onGoHome: handleGoHome\n        }, void 0, false, {\n            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n            lineNumber: 321,\n            columnNumber: 12\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50 dark:bg-gray-900\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"relative py-20 bg-gradient-to-br from-green-600 to-green-800 text-white\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-w-4xl mx-auto text-center space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-4xl font-bold tracking-tight sm:text-5xl md:text-6xl\",\n                                children: \"Scholarship Application\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                lineNumber: 330,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xl text-green-100 max-w-3xl mx-auto\",\n                                children: \"Choose your scholarship category and complete the application form\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                lineNumber: 333,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                        lineNumber: 329,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                    lineNumber: 328,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                lineNumber: 327,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-12 bg-white dark:bg-gray-900\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-w-4xl mx-auto\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-3xl font-bold tracking-tight mb-2\",\n                                        children: \"Apply for Scholarship\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                        lineNumber: 345,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600 dark:text-gray-400\",\n                                        children: \"Select your education level and fill out the appropriate form\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                        lineNumber: 346,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                lineNumber: 344,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_8__.Tabs, {\n                                value: selectedCategory,\n                                onValueChange: (value)=>setSelectedCategory(value),\n                                className: \"w-full\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_8__.TabsList, {\n                                        className: \"grid w-full grid-cols-1 md:grid-cols-3 mb-8 h-auto p-2 gap-2 bg-gray-100 rounded-lg\",\n                                        children: Object.entries(categoryConfig).map((param)=>{\n                                            let [key, config] = param;\n                                            const IconComponent = config.icon;\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_8__.TabsTrigger, {\n                                                value: key,\n                                                className: \"flex flex-col items-center p-4 space-y-3 rounded-lg transition-all duration-300 data-[state=active]:bg-white data-[state=active]:shadow-lg\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"p-3 rounded-full bg-green-600 text-white shadow-lg\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(IconComponent, {\n                                                            className: \"h-6 w-6\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                            lineNumber: 362,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                        lineNumber: 361,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"font-bold text-sm text-gray-900 leading-tight\",\n                                                                children: config.title\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                lineNumber: 365,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs text-gray-600 mt-1\",\n                                                                children: config.description\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                lineNumber: 366,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                        lineNumber: 364,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, key, true, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                lineNumber: 356,\n                                                columnNumber: 21\n                                            }, this);\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                        lineNumber: 352,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_8__.TabsContent, {\n                                        value: \"primary\",\n                                        className: \"space-y-8\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                            onSubmit: handleSubmit,\n                                            className: \"space-y-8\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-green-50 p-6 rounded-lg border border-green-200\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BookOpen_Building_CheckCircle_CreditCard_FileUp_GraduationCap_Loader2_MapPin_MessageSquare_Phone_School_User_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                className: \"h-5 w-5 text-green-600 mr-3\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                lineNumber: 378,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-green-800 font-medium\",\n                                                                children: \"This form should be filled by a Parent or Guardian on behalf of the student\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                lineNumber: 379,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                        lineNumber: 377,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                    lineNumber: 376,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"text-lg font-semibold text-green-800 dark:text-green-200 mb-4 flex items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BookOpen_Building_CheckCircle_CreditCard_FileUp_GraduationCap_Loader2_MapPin_MessageSquare_Phone_School_User_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                            className: \"h-5 w-5 mr-2 text-green-600\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                            lineNumber: 389,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        \"Student Information\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                    lineNumber: 388,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"space-y-4\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"space-y-2\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                                                    htmlFor: \"student_full_name\",\n                                                                                    className: \"text-green-800 dark:text-green-200\",\n                                                                                    children: \"Student Full Name *\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                    lineNumber: 394,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"relative\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BookOpen_Building_CheckCircle_CreditCard_FileUp_GraduationCap_Loader2_MapPin_MessageSquare_Phone_School_User_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                                            className: \"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-green-500\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                            lineNumber: 398,\n                                                                                            columnNumber: 29\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                                            id: \"student_full_name\",\n                                                                                            value: primaryFormData.student_full_name,\n                                                                                            onChange: (e)=>setPrimaryFormData((prev)=>({\n                                                                                                        ...prev,\n                                                                                                        student_full_name: e.target.value\n                                                                                                    })),\n                                                                                            placeholder: \"Enter student's full name\",\n                                                                                            className: \"pl-10 rounded-xl border-green-200 dark:border-green-700 focus:border-green-500\",\n                                                                                            required: true\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                            lineNumber: 399,\n                                                                                            columnNumber: 29\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                    lineNumber: 397,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                            lineNumber: 393,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"space-y-2\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                                                            htmlFor: \"age\",\n                                                                                            className: \"text-green-800 dark:text-green-200\",\n                                                                                            children: \"Age *\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                            lineNumber: 412,\n                                                                                            columnNumber: 29\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                                            id: \"age\",\n                                                                                            type: \"number\",\n                                                                                            value: primaryFormData.age,\n                                                                                            onChange: (e)=>setPrimaryFormData((prev)=>({\n                                                                                                        ...prev,\n                                                                                                        age: e.target.value\n                                                                                                    })),\n                                                                                            placeholder: \"Age\",\n                                                                                            className: \"rounded-xl border-green-200 dark:border-green-700 focus:border-green-500\",\n                                                                                            required: true\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                            lineNumber: 415,\n                                                                                            columnNumber: 29\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                    lineNumber: 411,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"space-y-2\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                                                            htmlFor: \"current_class\",\n                                                                                            className: \"text-green-800 dark:text-green-200\",\n                                                                                            children: \"Current Class *\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                            lineNumber: 426,\n                                                                                            columnNumber: 29\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.Select, {\n                                                                                            value: primaryFormData.current_class,\n                                                                                            onValueChange: (value)=>setPrimaryFormData((prev)=>({\n                                                                                                        ...prev,\n                                                                                                        current_class: value\n                                                                                                    })),\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectTrigger, {\n                                                                                                    className: \"rounded-xl border-green-200 dark:border-green-700 focus:border-green-500\",\n                                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectValue, {\n                                                                                                        placeholder: \"Select class\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                                        lineNumber: 431,\n                                                                                                        columnNumber: 33\n                                                                                                    }, this)\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                                    lineNumber: 430,\n                                                                                                    columnNumber: 31\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectContent, {\n                                                                                                    children: [\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                                                            value: \"Primary 1\",\n                                                                                                            children: \"Primary 1\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                                            lineNumber: 434,\n                                                                                                            columnNumber: 33\n                                                                                                        }, this),\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                                                            value: \"Primary 2\",\n                                                                                                            children: \"Primary 2\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                                            lineNumber: 435,\n                                                                                                            columnNumber: 33\n                                                                                                        }, this),\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                                                            value: \"Primary 3\",\n                                                                                                            children: \"Primary 3\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                                            lineNumber: 436,\n                                                                                                            columnNumber: 33\n                                                                                                        }, this),\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                                                            value: \"Primary 4\",\n                                                                                                            children: \"Primary 4\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                                            lineNumber: 437,\n                                                                                                            columnNumber: 33\n                                                                                                        }, this),\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                                                            value: \"Primary 5\",\n                                                                                                            children: \"Primary 5\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                                            lineNumber: 438,\n                                                                                                            columnNumber: 33\n                                                                                                        }, this),\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                                                            value: \"Primary 6\",\n                                                                                                            children: \"Primary 6\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                                            lineNumber: 439,\n                                                                                                            columnNumber: 33\n                                                                                                        }, this)\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                                    lineNumber: 433,\n                                                                                                    columnNumber: 31\n                                                                                                }, this)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                            lineNumber: 429,\n                                                                                            columnNumber: 29\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                    lineNumber: 425,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                            lineNumber: 410,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                    lineNumber: 392,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                            lineNumber: 387,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"text-lg font-semibold text-green-800 dark:text-green-200 mb-4 flex items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BookOpen_Building_CheckCircle_CreditCard_FileUp_GraduationCap_Loader2_MapPin_MessageSquare_Phone_School_User_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                            className: \"h-5 w-5 mr-2 text-green-600\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                            lineNumber: 450,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        \"Parent/Guardian Information\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                    lineNumber: 449,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"space-y-4\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"space-y-2\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                                                            htmlFor: \"father_name\",\n                                                                                            className: \"text-green-800 dark:text-green-200\",\n                                                                                            children: \"Father's Name *\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                            lineNumber: 456,\n                                                                                            columnNumber: 29\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"relative\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BookOpen_Building_CheckCircle_CreditCard_FileUp_GraduationCap_Loader2_MapPin_MessageSquare_Phone_School_User_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                                                    className: \"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-green-500\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                                    lineNumber: 460,\n                                                                                                    columnNumber: 31\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                                                    id: \"father_name\",\n                                                                                                    value: primaryFormData.father_name,\n                                                                                                    onChange: (e)=>setPrimaryFormData((prev)=>({\n                                                                                                                ...prev,\n                                                                                                                father_name: e.target.value\n                                                                                                            })),\n                                                                                                    placeholder: \"Enter father's name\",\n                                                                                                    className: \"pl-10 rounded-xl border-green-200 dark:border-green-700 focus:border-green-500\",\n                                                                                                    required: true\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                                    lineNumber: 461,\n                                                                                                    columnNumber: 31\n                                                                                                }, this)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                            lineNumber: 459,\n                                                                                            columnNumber: 29\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                    lineNumber: 455,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"space-y-2\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                                                            htmlFor: \"mother_name\",\n                                                                                            className: \"text-green-800 dark:text-green-200\",\n                                                                                            children: \"Mother's Name *\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                            lineNumber: 472,\n                                                                                            columnNumber: 29\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                                            id: \"mother_name\",\n                                                                                            value: primaryFormData.mother_name,\n                                                                                            onChange: (e)=>setPrimaryFormData((prev)=>({\n                                                                                                        ...prev,\n                                                                                                        mother_name: e.target.value\n                                                                                                    })),\n                                                                                            placeholder: \"Enter mother's name\",\n                                                                                            className: \"rounded-xl border-green-200 dark:border-green-700 focus:border-green-500\",\n                                                                                            required: true\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                            lineNumber: 475,\n                                                                                            columnNumber: 29\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                    lineNumber: 471,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                            lineNumber: 454,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"space-y-2\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                                                    htmlFor: \"parent_phone\",\n                                                                                    className: \"text-green-800 dark:text-green-200\",\n                                                                                    children: \"Father's or Mother's Phone Number *\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                    lineNumber: 486,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"relative\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BookOpen_Building_CheckCircle_CreditCard_FileUp_GraduationCap_Loader2_MapPin_MessageSquare_Phone_School_User_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                                            className: \"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-green-500\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                            lineNumber: 490,\n                                                                                            columnNumber: 29\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                                            id: \"parent_phone\",\n                                                                                            type: \"tel\",\n                                                                                            value: primaryFormData.parent_phone,\n                                                                                            onChange: (e)=>setPrimaryFormData((prev)=>({\n                                                                                                        ...prev,\n                                                                                                        parent_phone: e.target.value\n                                                                                                    })),\n                                                                                            placeholder: \"Enter phone number\",\n                                                                                            className: \"pl-10 rounded-xl border-green-200 dark:border-green-700 focus:border-green-500\",\n                                                                                            required: true\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                            lineNumber: 491,\n                                                                                            columnNumber: 29\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                    lineNumber: 489,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                            lineNumber: 485,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                    lineNumber: 453,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                            lineNumber: 448,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                    lineNumber: 386,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-lg font-semibold text-green-800 dark:text-green-200 mb-4 flex items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BookOpen_Building_CheckCircle_CreditCard_FileUp_GraduationCap_Loader2_MapPin_MessageSquare_Phone_School_User_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                    className: \"h-5 w-5 mr-2 text-green-600\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                    lineNumber: 509,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                \"Address Information\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                            lineNumber: 508,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                                    htmlFor: \"home_address\",\n                                                                    className: \"text-green-800 dark:text-green-200\",\n                                                                    children: \"Home Address *\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                    lineNumber: 513,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"relative\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BookOpen_Building_CheckCircle_CreditCard_FileUp_GraduationCap_Loader2_MapPin_MessageSquare_Phone_School_User_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                            className: \"absolute left-3 top-3 h-4 w-4 text-green-500\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                            lineNumber: 517,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_6__.Textarea, {\n                                                                            id: \"home_address\",\n                                                                            value: primaryFormData.home_address,\n                                                                            onChange: (e)=>setPrimaryFormData((prev)=>({\n                                                                                        ...prev,\n                                                                                        home_address: e.target.value\n                                                                                    })),\n                                                                            placeholder: \"Enter complete home address\",\n                                                                            className: \"pl-10 rounded-xl border-green-200 dark:border-green-700 focus:border-green-500\",\n                                                                            rows: 3,\n                                                                            required: true\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                            lineNumber: 518,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                    lineNumber: 516,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                            lineNumber: 512,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                    lineNumber: 507,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-lg font-semibold text-green-800 dark:text-green-200 mb-4 flex items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BookOpen_Building_CheckCircle_CreditCard_FileUp_GraduationCap_Loader2_MapPin_MessageSquare_Phone_School_User_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                    className: \"h-5 w-5 mr-2 text-green-600\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                    lineNumber: 534,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                \"School Information\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                            lineNumber: 533,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"space-y-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                                            htmlFor: \"school_name\",\n                                                                            className: \"text-green-800 dark:text-green-200\",\n                                                                            children: \"Name of the School *\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                            lineNumber: 539,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"relative\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BookOpen_Building_CheckCircle_CreditCard_FileUp_GraduationCap_Loader2_MapPin_MessageSquare_Phone_School_User_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                                    className: \"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-green-500\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                    lineNumber: 543,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                                    id: \"school_name\",\n                                                                                    value: primaryFormData.school_name,\n                                                                                    onChange: (e)=>setPrimaryFormData((prev)=>({\n                                                                                                ...prev,\n                                                                                                school_name: e.target.value\n                                                                                            })),\n                                                                                    placeholder: \"Enter school name\",\n                                                                                    className: \"pl-10 rounded-xl border-green-200 dark:border-green-700 focus:border-green-500\",\n                                                                                    required: true\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                    lineNumber: 544,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                            lineNumber: 542,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                    lineNumber: 538,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"space-y-2\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                                                    htmlFor: \"headmaster_name\",\n                                                                                    className: \"text-green-800 dark:text-green-200\",\n                                                                                    children: \"Name of Headmaster/Director *\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                    lineNumber: 557,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                                    id: \"headmaster_name\",\n                                                                                    value: primaryFormData.headmaster_name,\n                                                                                    onChange: (e)=>setPrimaryFormData((prev)=>({\n                                                                                                ...prev,\n                                                                                                headmaster_name: e.target.value\n                                                                                            })),\n                                                                                    placeholder: \"Enter headmaster's name\",\n                                                                                    className: \"rounded-xl border-green-200 dark:border-green-700 focus:border-green-500\",\n                                                                                    required: true\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                    lineNumber: 560,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                            lineNumber: 556,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"space-y-2\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                                                    htmlFor: \"school_account_number\",\n                                                                                    className: \"text-green-800 dark:text-green-200\",\n                                                                                    children: \"School Account Number *\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                    lineNumber: 570,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                                    id: \"school_account_number\",\n                                                                                    value: primaryFormData.school_account_number,\n                                                                                    onChange: (e)=>setPrimaryFormData((prev)=>({\n                                                                                                ...prev,\n                                                                                                school_account_number: e.target.value\n                                                                                            })),\n                                                                                    placeholder: \"Enter account number for scholarship payment\",\n                                                                                    className: \"rounded-xl border-green-200 dark:border-green-700 focus:border-green-500\",\n                                                                                    required: true\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                    lineNumber: 573,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                            lineNumber: 569,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                    lineNumber: 555,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                            lineNumber: 537,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                    lineNumber: 532,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-lg font-semibold text-green-800 dark:text-green-200 mb-4 flex items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BookOpen_Building_CheckCircle_CreditCard_FileUp_GraduationCap_Loader2_MapPin_MessageSquare_Phone_School_User_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                    className: \"h-5 w-5 mr-2 text-green-600\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                    lineNumber: 589,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                \"Financial & Additional Information\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                            lineNumber: 588,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"space-y-2\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                                                    htmlFor: \"current_school_fee\",\n                                                                                    className: \"text-green-800 dark:text-green-200\",\n                                                                                    children: \"Current School Fee Amount *\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                    lineNumber: 595,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"relative\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BookOpen_Building_CheckCircle_CreditCard_FileUp_GraduationCap_Loader2_MapPin_MessageSquare_Phone_School_User_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                                            className: \"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-green-500\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                            lineNumber: 599,\n                                                                                            columnNumber: 29\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                                            id: \"current_school_fee\",\n                                                                                            type: \"number\",\n                                                                                            value: primaryFormData.current_school_fee,\n                                                                                            onChange: (e)=>setPrimaryFormData((prev)=>({\n                                                                                                        ...prev,\n                                                                                                        current_school_fee: e.target.value\n                                                                                                    })),\n                                                                                            placeholder: \"Enter amount in Naira\",\n                                                                                            className: \"pl-10 rounded-xl border-green-200 dark:border-green-700 focus:border-green-500\",\n                                                                                            required: true\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                            lineNumber: 600,\n                                                                                            columnNumber: 29\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                    lineNumber: 598,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                            lineNumber: 594,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"space-y-2\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                                                    htmlFor: \"student_picture\",\n                                                                                    className: \"text-green-800 dark:text-green-200\",\n                                                                                    children: \"Upload Student Picture *\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                    lineNumber: 612,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                                    id: \"student_picture\",\n                                                                                    type: \"file\",\n                                                                                    accept: \"image/*\",\n                                                                                    onChange: (e)=>{\n                                                                                        var _e_target_files;\n                                                                                        return handleFileChange('primary', 'student_picture', ((_e_target_files = e.target.files) === null || _e_target_files === void 0 ? void 0 : _e_target_files[0]) || null);\n                                                                                    },\n                                                                                    className: \"rounded-xl border-green-200 dark:border-green-700 focus:border-green-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-green-50 file:text-green-700 hover:file:bg-green-100\",\n                                                                                    required: true\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                    lineNumber: 615,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                            lineNumber: 611,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                    lineNumber: 593,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"space-y-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                                            htmlFor: \"reason_for_scholarship\",\n                                                                            className: \"text-green-800 dark:text-green-200\",\n                                                                            children: \"Reason for the Scholarship *\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                            lineNumber: 626,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_6__.Textarea, {\n                                                                            id: \"reason_for_scholarship\",\n                                                                            value: primaryFormData.reason_for_scholarship,\n                                                                            onChange: (e)=>setPrimaryFormData((prev)=>({\n                                                                                        ...prev,\n                                                                                        reason_for_scholarship: e.target.value\n                                                                                    })),\n                                                                            placeholder: \"Please explain why your child needs this scholarship\",\n                                                                            className: \"rounded-xl border-green-200 dark:border-green-700 focus:border-green-500\",\n                                                                            rows: 4,\n                                                                            required: true\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                            lineNumber: 629,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                    lineNumber: 625,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"space-y-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                                            htmlFor: \"supporting_information\",\n                                                                            className: \"text-green-800 dark:text-green-200\",\n                                                                            children: \"Any Other Supporting Information\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                            lineNumber: 640,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_6__.Textarea, {\n                                                                            id: \"supporting_information\",\n                                                                            value: primaryFormData.supporting_information,\n                                                                            onChange: (e)=>setPrimaryFormData((prev)=>({\n                                                                                        ...prev,\n                                                                                        supporting_information: e.target.value\n                                                                                    })),\n                                                                            placeholder: \"Any additional information that supports your application\",\n                                                                            className: \"rounded-xl border-green-200 dark:border-green-700 focus:border-green-500\",\n                                                                            rows: 3\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                            lineNumber: 643,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                    lineNumber: 639,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                            lineNumber: 592,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                    lineNumber: 587,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-center pt-8\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                        type: \"submit\",\n                                                        disabled: submitting || !validateForm(),\n                                                        size: \"lg\",\n                                                        className: \"bg-green-600 hover:bg-green-700 text-white px-12 py-4 text-lg font-semibold shadow-lg hover:shadow-xl transition-all duration-300\",\n                                                        children: submitting ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BookOpen_Building_CheckCircle_CreditCard_FileUp_GraduationCap_Loader2_MapPin_MessageSquare_Phone_School_User_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                    className: \"mr-3 h-6 w-6 animate-spin\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                    lineNumber: 664,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                \"Submitting Application...\"\n                                                            ]\n                                                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BookOpen_Building_CheckCircle_CreditCard_FileUp_GraduationCap_Loader2_MapPin_MessageSquare_Phone_School_User_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                    className: \"mr-3 h-6 w-6\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                    lineNumber: 669,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                \"Submit Primary School Application\"\n                                                            ]\n                                                        }, void 0, true)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                        lineNumber: 656,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                    lineNumber: 655,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                            lineNumber: 375,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                        lineNumber: 374,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_8__.TabsContent, {\n                                        value: \"secondary\",\n                                        className: \"space-y-8\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                            onSubmit: handleSubmit,\n                                            className: \"space-y-8\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-green-50 p-6 rounded-lg border border-green-200\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BookOpen_Building_CheckCircle_CreditCard_FileUp_GraduationCap_Loader2_MapPin_MessageSquare_Phone_School_User_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                className: \"h-5 w-5 text-green-600 mr-3\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                lineNumber: 683,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-green-800 font-medium\",\n                                                                children: \"This form should be filled by the Student directly\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                lineNumber: 684,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                        lineNumber: 682,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                    lineNumber: 681,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"text-lg font-semibold text-gray-900 mb-4 flex items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BookOpen_Building_CheckCircle_CreditCard_FileUp_GraduationCap_Loader2_MapPin_MessageSquare_Phone_School_User_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                            className: \"h-5 w-5 mr-2 text-green-600\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                            lineNumber: 694,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        \"Student Information\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                    lineNumber: 693,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                                                    htmlFor: \"sec_student_full_name\",\n                                                                                    children: \"Student Full Name *\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                    lineNumber: 699,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                                    id: \"sec_student_full_name\",\n                                                                                    value: secondaryFormData.student_full_name,\n                                                                                    onChange: (e)=>setSecondaryFormData((prev)=>({\n                                                                                                ...prev,\n                                                                                                student_full_name: e.target.value\n                                                                                            })),\n                                                                                    placeholder: \"Enter your full name\",\n                                                                                    className: \"mt-1\",\n                                                                                    required: true\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                    lineNumber: 700,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                            lineNumber: 698,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                                                    htmlFor: \"sec_age\",\n                                                                                    children: \"Age *\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                    lineNumber: 710,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                                    id: \"sec_age\",\n                                                                                    type: \"number\",\n                                                                                    value: secondaryFormData.age,\n                                                                                    onChange: (e)=>setSecondaryFormData((prev)=>({\n                                                                                                ...prev,\n                                                                                                age: e.target.value\n                                                                                            })),\n                                                                                    placeholder: \"Your age\",\n                                                                                    className: \"mt-1\",\n                                                                                    required: true\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                    lineNumber: 711,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                            lineNumber: 709,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                                                    htmlFor: \"sec_class\",\n                                                                                    children: \"Class *\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                    lineNumber: 722,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.Select, {\n                                                                                    value: secondaryFormData.class,\n                                                                                    onValueChange: (value)=>setSecondaryFormData((prev)=>({\n                                                                                                ...prev,\n                                                                                                class: value\n                                                                                            })),\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectTrigger, {\n                                                                                            className: \"mt-1\",\n                                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectValue, {\n                                                                                                placeholder: \"Select class\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                                lineNumber: 725,\n                                                                                                columnNumber: 31\n                                                                                            }, this)\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                            lineNumber: 724,\n                                                                                            columnNumber: 29\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectContent, {\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                                                    value: \"JSS 1\",\n                                                                                                    children: \"JSS 1\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                                    lineNumber: 728,\n                                                                                                    columnNumber: 31\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                                                    value: \"JSS 2\",\n                                                                                                    children: \"JSS 2\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                                    lineNumber: 729,\n                                                                                                    columnNumber: 31\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                                                    value: \"JSS 3\",\n                                                                                                    children: \"JSS 3\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                                    lineNumber: 730,\n                                                                                                    columnNumber: 31\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                                                    value: \"SS 1\",\n                                                                                                    children: \"SS 1\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                                    lineNumber: 731,\n                                                                                                    columnNumber: 31\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                                                    value: \"SS 2\",\n                                                                                                    children: \"SS 2\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                                    lineNumber: 732,\n                                                                                                    columnNumber: 31\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                                                    value: \"SS 3\",\n                                                                                                    children: \"SS 3\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                                    lineNumber: 733,\n                                                                                                    columnNumber: 31\n                                                                                                }, this)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                            lineNumber: 727,\n                                                                                            columnNumber: 29\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                    lineNumber: 723,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                            lineNumber: 721,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                                                    htmlFor: \"sec_parent_phone\",\n                                                                                    children: \"Parent's Phone Number *\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                    lineNumber: 738,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                                    id: \"sec_parent_phone\",\n                                                                                    type: \"tel\",\n                                                                                    value: secondaryFormData.parent_phone,\n                                                                                    onChange: (e)=>setSecondaryFormData((prev)=>({\n                                                                                                ...prev,\n                                                                                                parent_phone: e.target.value\n                                                                                            })),\n                                                                                    placeholder: \"Enter parent's phone number\",\n                                                                                    className: \"mt-1\",\n                                                                                    required: true\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                    lineNumber: 739,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                            lineNumber: 737,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                    lineNumber: 697,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                            lineNumber: 692,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                                            className: \"border-2 border-red-200 bg-red-50/50 shadow-sm hover:shadow-md transition-shadow\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                                                    className: \"pb-4\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                                        className: \"text-xl font-bold text-gray-900 flex items-center\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BookOpen_Building_CheckCircle_CreditCard_FileUp_GraduationCap_Loader2_MapPin_MessageSquare_Phone_School_User_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                                className: \"h-6 w-6 mr-3 text-red-600\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                lineNumber: 756,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            \"Address & School Information\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                        lineNumber: 755,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                    lineNumber: 754,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                                                    className: \"space-y-4\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                                                    htmlFor: \"sec_address\",\n                                                                                    children: \"Address *\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                    lineNumber: 763,\n                                                                                    columnNumber: 25\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_6__.Textarea, {\n                                                                                    id: \"sec_address\",\n                                                                                    value: secondaryFormData.address,\n                                                                                    onChange: (e)=>setSecondaryFormData((prev)=>({\n                                                                                                ...prev,\n                                                                                                address: e.target.value\n                                                                                            })),\n                                                                                    placeholder: \"Enter your complete address\",\n                                                                                    className: \"mt-1\",\n                                                                                    rows: 3,\n                                                                                    required: true\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                    lineNumber: 764,\n                                                                                    columnNumber: 25\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                            lineNumber: 762,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                                                    htmlFor: \"sec_school_name\",\n                                                                                    children: \"School Name *\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                    lineNumber: 776,\n                                                                                    columnNumber: 25\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                                    id: \"sec_school_name\",\n                                                                                    value: secondaryFormData.school_name,\n                                                                                    onChange: (e)=>setSecondaryFormData((prev)=>({\n                                                                                                ...prev,\n                                                                                                school_name: e.target.value\n                                                                                            })),\n                                                                                    placeholder: \"Enter your school name\",\n                                                                                    className: \"mt-1\",\n                                                                                    required: true\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                    lineNumber: 777,\n                                                                                    columnNumber: 25\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                            lineNumber: 775,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                                                    htmlFor: \"sec_principal_name\",\n                                                                                    children: \"School Principal or Director Name *\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                    lineNumber: 788,\n                                                                                    columnNumber: 25\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                                    id: \"sec_principal_name\",\n                                                                                    value: secondaryFormData.principal_name,\n                                                                                    onChange: (e)=>setSecondaryFormData((prev)=>({\n                                                                                                ...prev,\n                                                                                                principal_name: e.target.value\n                                                                                            })),\n                                                                                    placeholder: \"Enter principal's name\",\n                                                                                    className: \"mt-1\",\n                                                                                    required: true\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                    lineNumber: 789,\n                                                                                    columnNumber: 25\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                            lineNumber: 787,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                    lineNumber: 760,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                            lineNumber: 753,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                    lineNumber: 691,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                                    className: \"border-2 border-orange-200 bg-orange-50/50 shadow-sm hover:shadow-md transition-shadow\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                                            className: \"pb-4\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                                className: \"text-xl font-bold text-gray-900 flex items-center\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BookOpen_Building_CheckCircle_CreditCard_FileUp_GraduationCap_Loader2_MapPin_MessageSquare_Phone_School_User_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                        className: \"h-6 w-6 mr-3 text-orange-600\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                        lineNumber: 806,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    \"Financial Information\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                lineNumber: 805,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                            lineNumber: 804,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                                            className: \"space-y-6\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                                                htmlFor: \"sec_principal_account_number\",\n                                                                                children: \"Principal or Financial Officer Account Number *\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                lineNumber: 814,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                                id: \"sec_principal_account_number\",\n                                                                                value: secondaryFormData.principal_account_number,\n                                                                                onChange: (e)=>setSecondaryFormData((prev)=>({\n                                                                                            ...prev,\n                                                                                            principal_account_number: e.target.value\n                                                                                        })),\n                                                                                placeholder: \"Enter account number for scholarship payment\",\n                                                                                className: \"mt-1\",\n                                                                                required: true\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                lineNumber: 815,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                        lineNumber: 813,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                                                htmlFor: \"sec_school_fee_amount\",\n                                                                                children: \"School Fee Amount *\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                lineNumber: 826,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                                id: \"sec_school_fee_amount\",\n                                                                                type: \"number\",\n                                                                                value: secondaryFormData.school_fee_amount,\n                                                                                onChange: (e)=>setSecondaryFormData((prev)=>({\n                                                                                            ...prev,\n                                                                                            school_fee_amount: e.target.value\n                                                                                        })),\n                                                                                placeholder: \"Enter amount in Naira\",\n                                                                                className: \"mt-1\",\n                                                                                required: true\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                lineNumber: 827,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                        lineNumber: 825,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                lineNumber: 812,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                            lineNumber: 810,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                    lineNumber: 803,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                                    className: \"border-2 border-indigo-200 bg-indigo-50/50 shadow-sm hover:shadow-md transition-shadow\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                                            className: \"pb-4\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                                className: \"text-xl font-bold text-gray-900 flex items-center\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BookOpen_Building_CheckCircle_CreditCard_FileUp_GraduationCap_Loader2_MapPin_MessageSquare_Phone_School_User_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                        className: \"h-6 w-6 mr-3 text-indigo-600\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                        lineNumber: 845,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    \"Picture & Application Reason\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                lineNumber: 844,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                            lineNumber: 843,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                                            className: \"space-y-6\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                                                htmlFor: \"sec_student_picture\",\n                                                                                children: \"Upload Student Picture *\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                lineNumber: 852,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"mt-1\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                                    id: \"sec_student_picture\",\n                                                                                    type: \"file\",\n                                                                                    accept: \"image/*\",\n                                                                                    onChange: (e)=>{\n                                                                                        var _e_target_files;\n                                                                                        return handleFileChange('secondary', 'student_picture', ((_e_target_files = e.target.files) === null || _e_target_files === void 0 ? void 0 : _e_target_files[0]) || null);\n                                                                                    },\n                                                                                    className: \"file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-green-50 file:text-green-700 hover:file:bg-green-100\",\n                                                                                    required: true\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                    lineNumber: 854,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                lineNumber: 853,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                        lineNumber: 851,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                    lineNumber: 850,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                                            htmlFor: \"sec_reason_for_scholarship\",\n                                                                            children: \"Reason for the Scholarship *\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                            lineNumber: 867,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_6__.Textarea, {\n                                                                            id: \"sec_reason_for_scholarship\",\n                                                                            value: secondaryFormData.reason_for_scholarship,\n                                                                            onChange: (e)=>setSecondaryFormData((prev)=>({\n                                                                                        ...prev,\n                                                                                        reason_for_scholarship: e.target.value\n                                                                                    })),\n                                                                            placeholder: \"Please explain why you need this scholarship\",\n                                                                            className: \"mt-1\",\n                                                                            rows: 4,\n                                                                            required: true\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                            lineNumber: 868,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                    lineNumber: 866,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                            lineNumber: 849,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                    lineNumber: 842,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-center pt-8\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                        type: \"submit\",\n                                                        disabled: submitting || !validateForm(),\n                                                        size: \"lg\",\n                                                        className: \"bg-gradient-to-r from-green-600 via-emerald-600 to-teal-600 hover:from-green-700 hover:via-emerald-700 hover:to-teal-700 text-white px-12 py-4 text-lg font-bold shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105\",\n                                                        children: submitting ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BookOpen_Building_CheckCircle_CreditCard_FileUp_GraduationCap_Loader2_MapPin_MessageSquare_Phone_School_User_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                    className: \"mr-3 h-6 w-6 animate-spin\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                    lineNumber: 890,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                \"Submitting Application...\"\n                                                            ]\n                                                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BookOpen_Building_CheckCircle_CreditCard_FileUp_GraduationCap_Loader2_MapPin_MessageSquare_Phone_School_User_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                    className: \"mr-3 h-6 w-6\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                    lineNumber: 895,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                \"Submit Secondary School Application\"\n                                                            ]\n                                                        }, void 0, true)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                        lineNumber: 882,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                    lineNumber: 881,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                            lineNumber: 680,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                        lineNumber: 679,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_8__.TabsContent, {\n                                        value: \"university\",\n                                        className: \"space-y-8\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                            onSubmit: handleSubmit,\n                                            className: \"space-y-8\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-purple-50 p-6 rounded-lg border-2 border-purple-200 shadow-sm\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BookOpen_Building_CheckCircle_CreditCard_FileUp_GraduationCap_Loader2_MapPin_MessageSquare_Phone_School_User_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                className: \"h-6 w-6 text-purple-600 mr-3\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                lineNumber: 909,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-purple-800 font-semibold text-lg\",\n                                                                children: \"This form should be filled by the University Student directly\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                lineNumber: 910,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                        lineNumber: 908,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                    lineNumber: 907,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-8\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                                            className: \"border-2 border-purple-200 bg-purple-50/50 shadow-sm hover:shadow-md transition-shadow\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                                                    className: \"pb-4\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                                        className: \"text-xl font-bold text-gray-900 flex items-center\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BookOpen_Building_CheckCircle_CreditCard_FileUp_GraduationCap_Loader2_MapPin_MessageSquare_Phone_School_User_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                                className: \"h-6 w-6 mr-3 text-purple-600\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                lineNumber: 921,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            \"Personal Information\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                        lineNumber: 920,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                    lineNumber: 919,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                                                    className: \"space-y-4\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                                                    htmlFor: \"uni_full_name\",\n                                                                                    children: \"Full Name *\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                    lineNumber: 928,\n                                                                                    columnNumber: 25\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                                    id: \"uni_full_name\",\n                                                                                    value: universityFormData.full_name,\n                                                                                    onChange: (e)=>setUniversityFormData((prev)=>({\n                                                                                                ...prev,\n                                                                                                full_name: e.target.value\n                                                                                            })),\n                                                                                    placeholder: \"Enter your full name\",\n                                                                                    className: \"mt-1\",\n                                                                                    required: true\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                    lineNumber: 929,\n                                                                                    columnNumber: 25\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                            lineNumber: 927,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                                                    htmlFor: \"uni_age\",\n                                                                                    children: \"Age *\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                    lineNumber: 940,\n                                                                                    columnNumber: 25\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                                    id: \"uni_age\",\n                                                                                    type: \"number\",\n                                                                                    value: universityFormData.age,\n                                                                                    onChange: (e)=>setUniversityFormData((prev)=>({\n                                                                                                ...prev,\n                                                                                                age: e.target.value\n                                                                                            })),\n                                                                                    placeholder: \"Your age\",\n                                                                                    className: \"mt-1\",\n                                                                                    required: true\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                    lineNumber: 941,\n                                                                                    columnNumber: 25\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                            lineNumber: 939,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                                                    htmlFor: \"uni_phone_number\",\n                                                                                    children: \"Phone Number *\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                    lineNumber: 953,\n                                                                                    columnNumber: 25\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                                    id: \"uni_phone_number\",\n                                                                                    type: \"tel\",\n                                                                                    value: universityFormData.phone_number,\n                                                                                    onChange: (e)=>setUniversityFormData((prev)=>({\n                                                                                                ...prev,\n                                                                                                phone_number: e.target.value\n                                                                                            })),\n                                                                                    placeholder: \"Enter your phone number\",\n                                                                                    className: \"mt-1\",\n                                                                                    required: true\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                    lineNumber: 954,\n                                                                                    columnNumber: 25\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                            lineNumber: 952,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                                                    htmlFor: \"uni_email_address\",\n                                                                                    children: \"Email Address *\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                    lineNumber: 966,\n                                                                                    columnNumber: 25\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                                    id: \"uni_email_address\",\n                                                                                    type: \"email\",\n                                                                                    value: universityFormData.email_address,\n                                                                                    onChange: (e)=>setUniversityFormData((prev)=>({\n                                                                                                ...prev,\n                                                                                                email_address: e.target.value\n                                                                                            })),\n                                                                                    placeholder: \"Enter your email address\",\n                                                                                    className: \"mt-1\",\n                                                                                    required: true\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                    lineNumber: 967,\n                                                                                    columnNumber: 25\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                            lineNumber: 965,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                    lineNumber: 925,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                            lineNumber: 918,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                                            className: \"border-2 border-blue-200 bg-blue-50/50 shadow-sm hover:shadow-md transition-shadow\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                                                    className: \"pb-4\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                                        className: \"text-xl font-bold text-gray-900 flex items-center\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BookOpen_Building_CheckCircle_CreditCard_FileUp_GraduationCap_Loader2_MapPin_MessageSquare_Phone_School_User_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                                className: \"h-6 w-6 mr-3 text-blue-600\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                lineNumber: 984,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            \"Academic Information\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                        lineNumber: 983,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                    lineNumber: 982,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                                                    className: \"space-y-4\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                                                    htmlFor: \"uni_course_of_study\",\n                                                                                    children: \"Course of Study *\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                    lineNumber: 991,\n                                                                                    columnNumber: 25\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                                    id: \"uni_course_of_study\",\n                                                                                    value: universityFormData.course_of_study,\n                                                                                    onChange: (e)=>setUniversityFormData((prev)=>({\n                                                                                                ...prev,\n                                                                                                course_of_study: e.target.value\n                                                                                            })),\n                                                                                    placeholder: \"Enter your course of study\",\n                                                                                    className: \"mt-1\",\n                                                                                    required: true\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                    lineNumber: 992,\n                                                                                    columnNumber: 25\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                            lineNumber: 990,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                                                    htmlFor: \"uni_current_level\",\n                                                                                    children: \"Current Level *\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                    lineNumber: 1003,\n                                                                                    columnNumber: 25\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.Select, {\n                                                                                    value: universityFormData.current_level,\n                                                                                    onValueChange: (value)=>setUniversityFormData((prev)=>({\n                                                                                                ...prev,\n                                                                                                current_level: value\n                                                                                            })),\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectTrigger, {\n                                                                                            className: \"mt-1\",\n                                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectValue, {\n                                                                                                placeholder: \"Select your current level\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                                lineNumber: 1006,\n                                                                                                columnNumber: 29\n                                                                                            }, this)\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                            lineNumber: 1005,\n                                                                                            columnNumber: 27\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectContent, {\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                                                    value: \"100L\",\n                                                                                                    children: \"100 Level\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                                    lineNumber: 1009,\n                                                                                                    columnNumber: 29\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                                                    value: \"200L\",\n                                                                                                    children: \"200 Level\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                                    lineNumber: 1010,\n                                                                                                    columnNumber: 29\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                                                    value: \"300L\",\n                                                                                                    children: \"300 Level\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                                    lineNumber: 1011,\n                                                                                                    columnNumber: 29\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                                                    value: \"400L\",\n                                                                                                    children: \"400 Level\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                                    lineNumber: 1012,\n                                                                                                    columnNumber: 29\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                                                    value: \"500L\",\n                                                                                                    children: \"500 Level\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                                    lineNumber: 1013,\n                                                                                                    columnNumber: 29\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                                                    value: \"600L\",\n                                                                                                    children: \"600 Level\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                                    lineNumber: 1014,\n                                                                                                    columnNumber: 29\n                                                                                                }, this)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                            lineNumber: 1008,\n                                                                                            columnNumber: 27\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                    lineNumber: 1004,\n                                                                                    columnNumber: 25\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                            lineNumber: 1002,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                                                    htmlFor: \"uni_matriculation_number\",\n                                                                                    children: \"Matriculation Number *\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                    lineNumber: 1020,\n                                                                                    columnNumber: 25\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                                    id: \"uni_matriculation_number\",\n                                                                                    value: universityFormData.matriculation_number,\n                                                                                    onChange: (e)=>setUniversityFormData((prev)=>({\n                                                                                                ...prev,\n                                                                                                matriculation_number: e.target.value\n                                                                                            })),\n                                                                                    placeholder: \"Enter your matriculation number\",\n                                                                                    className: \"mt-1\",\n                                                                                    required: true\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                    lineNumber: 1021,\n                                                                                    columnNumber: 25\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                            lineNumber: 1019,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                    lineNumber: 988,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                            lineNumber: 981,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                    lineNumber: 916,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                                    className: \"border-2 border-orange-200 bg-orange-50/50 shadow-sm hover:shadow-md transition-shadow\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                                            className: \"pb-4\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                                className: \"text-xl font-bold text-gray-900 flex items-center\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BookOpen_Building_CheckCircle_CreditCard_FileUp_GraduationCap_Loader2_MapPin_MessageSquare_Phone_School_User_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                        className: \"h-6 w-6 mr-3 text-orange-600\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                        lineNumber: 1038,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    \"Required Documents\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                lineNumber: 1037,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                            lineNumber: 1036,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                                            className: \"space-y-6\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                                                    htmlFor: \"uni_student_id_card\",\n                                                                                    children: \"Upload Student ID Card *\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                    lineNumber: 1046,\n                                                                                    columnNumber: 25\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"mt-1\",\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                                        id: \"uni_student_id_card\",\n                                                                                        type: \"file\",\n                                                                                        accept: \"image/*,.pdf\",\n                                                                                        onChange: (e)=>{\n                                                                                            var _e_target_files;\n                                                                                            return handleFileChange('university', 'student_id_card', ((_e_target_files = e.target.files) === null || _e_target_files === void 0 ? void 0 : _e_target_files[0]) || null);\n                                                                                        },\n                                                                                        className: \"file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-purple-50 file:text-purple-700 hover:file:bg-purple-100\",\n                                                                                        required: true\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                        lineNumber: 1048,\n                                                                                        columnNumber: 27\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                    lineNumber: 1047,\n                                                                                    columnNumber: 25\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                            lineNumber: 1045,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                                                    htmlFor: \"uni_payment_evidence\",\n                                                                                    children: \"Upload Payment Evidence (Remita/Screenshot) *\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                    lineNumber: 1060,\n                                                                                    columnNumber: 25\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"mt-1\",\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                                        id: \"uni_payment_evidence\",\n                                                                                        type: \"file\",\n                                                                                        accept: \"image/*,.pdf\",\n                                                                                        onChange: (e)=>{\n                                                                                            var _e_target_files;\n                                                                                            return handleFileChange('university', 'payment_evidence', ((_e_target_files = e.target.files) === null || _e_target_files === void 0 ? void 0 : _e_target_files[0]) || null);\n                                                                                        },\n                                                                                        className: \"file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-purple-50 file:text-purple-700 hover:file:bg-purple-100\",\n                                                                                        required: true\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                        lineNumber: 1062,\n                                                                                        columnNumber: 27\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                    lineNumber: 1061,\n                                                                                    columnNumber: 25\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                            lineNumber: 1059,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                    lineNumber: 1044,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                                            htmlFor: \"uni_supporting_documents\",\n                                                                            children: \"Upload Supporting Documents (PDF, DOCX, JPG)\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                            lineNumber: 1075,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"mt-1\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                                id: \"uni_supporting_documents\",\n                                                                                type: \"file\",\n                                                                                accept: \".pdf,.docx,.doc,.jpg,.jpeg,.png\",\n                                                                                multiple: true,\n                                                                                onChange: (e)=>handleMultipleFileChange(e.target.files),\n                                                                                className: \"file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-purple-50 file:text-purple-700 hover:file:bg-purple-100\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                lineNumber: 1077,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                            lineNumber: 1076,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        universityFiles.supporting_documents.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"mt-2 space-y-2\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"text-sm text-gray-600\",\n                                                                                    children: \"Selected files:\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                    lineNumber: 1088,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                universityFiles.supporting_documents.map((file, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"flex items-center justify-between bg-gray-50 p-2 rounded\",\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                className: \"text-sm text-gray-700\",\n                                                                                                children: file.name\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                                lineNumber: 1091,\n                                                                                                columnNumber: 31\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                                                type: \"button\",\n                                                                                                variant: \"ghost\",\n                                                                                                size: \"sm\",\n                                                                                                onClick: ()=>removeFile(index),\n                                                                                                className: \"text-red-600 hover:text-red-800\",\n                                                                                                children: \"Remove\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                                lineNumber: 1092,\n                                                                                                columnNumber: 31\n                                                                                            }, this)\n                                                                                        ]\n                                                                                    }, index, true, {\n                                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                        lineNumber: 1090,\n                                                                                        columnNumber: 29\n                                                                                    }, this))\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                            lineNumber: 1087,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                    lineNumber: 1074,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                            lineNumber: 1042,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                    lineNumber: 1035,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                                    className: \"border-2 border-indigo-200 bg-indigo-50/50 shadow-sm hover:shadow-md transition-shadow\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                                            className: \"pb-4\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                                className: \"text-xl font-bold text-gray-900 flex items-center\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BookOpen_Building_CheckCircle_CreditCard_FileUp_GraduationCap_Loader2_MapPin_MessageSquare_Phone_School_User_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                        className: \"h-6 w-6 mr-3 text-indigo-600\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                        lineNumber: 1113,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    \"Application Reason\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                lineNumber: 1112,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                            lineNumber: 1111,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                                        htmlFor: \"uni_reason_for_scholarship\",\n                                                                        children: \"Reason for the Scholarship *\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                        lineNumber: 1119,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_6__.Textarea, {\n                                                                        id: \"uni_reason_for_scholarship\",\n                                                                        value: universityFormData.reason_for_scholarship,\n                                                                        onChange: (e)=>setUniversityFormData((prev)=>({\n                                                                                    ...prev,\n                                                                                    reason_for_scholarship: e.target.value\n                                                                                })),\n                                                                        placeholder: \"Please explain why you need this scholarship\",\n                                                                        className: \"mt-1\",\n                                                                        rows: 4,\n                                                                        required: true\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                        lineNumber: 1120,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                lineNumber: 1118,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                            lineNumber: 1117,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                    lineNumber: 1110,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-center pt-8\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                        type: \"submit\",\n                                                        disabled: submitting || !validateForm(),\n                                                        size: \"lg\",\n                                                        className: \"bg-gradient-to-r from-purple-600 via-violet-600 to-indigo-600 hover:from-purple-700 hover:via-violet-700 hover:to-indigo-700 text-white px-12 py-4 text-lg font-bold shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105\",\n                                                        children: submitting ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BookOpen_Building_CheckCircle_CreditCard_FileUp_GraduationCap_Loader2_MapPin_MessageSquare_Phone_School_User_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                    className: \"mr-3 h-6 w-6 animate-spin\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                    lineNumber: 1142,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                \"Submitting Application...\"\n                                                            ]\n                                                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BookOpen_Building_CheckCircle_CreditCard_FileUp_GraduationCap_Loader2_MapPin_MessageSquare_Phone_School_User_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                    className: \"mr-3 h-6 w-6\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                    lineNumber: 1147,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                \"Submit University Application\"\n                                                            ]\n                                                        }, void 0, true)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                        lineNumber: 1134,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                    lineNumber: 1133,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                            lineNumber: 906,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                        lineNumber: 905,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                lineNumber: 351,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                        lineNumber: 343,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                    lineNumber: 342,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                lineNumber: 341,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n        lineNumber: 325,\n        columnNumber: 5\n    }, this);\n}\n_s(ScholarshipApplicationPage, \"Sti8wAfvn58MX1lRHs4cA5vIto0=\", false, function() {\n    return [\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_9__.useToast\n    ];\n});\n_c = ScholarshipApplicationPage;\nvar _c;\n$RefreshReg$(_c, \"ScholarshipApplicationPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/scholarship/ScholarshipApplicationPage.tsx\n"));

/***/ })

});