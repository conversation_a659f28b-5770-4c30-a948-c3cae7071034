"use client"

import { useEffect, useState } from "react"
import { apiClient, extractArrayData } from "@/lib/api"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Ta<PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { 
  Building, 
  Users, 
  Calendar, 
  FileText, 
  Award, 
  Target,
  Handshake,
  Mail,
  Phone,
  Globe,
  MapPin,
  Plus,
  CheckCircle,
  Clock
} from "lucide-react"
import Link from "next/link"
import ProposalManager from "@/components/dashboard/partner/ProposalManager"
import ResourceSharing from "@/components/dashboard/partner/ResourceSharing"

interface User {
  id: number
  first_name: string
  last_name: string
  email: string
  phone_number?: string
  preferences?: {
    partner_data?: {
      organization_name?: string
    }
  }
}

interface Event {
  id: number
  title: string
  date: string
  status: string
}

interface Program {
  id: number
  name: string
  status: string
}

export default function PartnerDashboard() {
  const [user, setUser] = useState<User | null>(null)
  const [partnershipApplications, setPartnershipApplications] = useState<any[]>([])
  const [collaborationOpportunities, setCollaborationOpportunities] = useState<any[]>([])
  const [upcomingEvents, setUpcomingEvents] = useState<Event[]>([])
  const [programs, setPrograms] = useState<Program[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const fetchDashboardData = async () => {
      try {
        // Fetch user data first
        const userData = await apiClient.getProfile()
        if (userData.success) {
          setUser(userData.data)
        }

        // Fetch partner-specific data
        const [eventsResponse, programsResponse] = await Promise.all([
          apiClient.getUpcomingEvents(),
          apiClient.getPrograms()
        ])

        const eventsData = extractArrayData(eventsResponse)
        const programsData = extractArrayData(programsResponse)

        setUpcomingEvents(eventsData.slice(0, 5))
        setPrograms(programsData.slice(0, 3))
        setLoading(false)
      } catch (error) {
        console.error('Error fetching dashboard data:', error)
        setLoading(false)
      }
    }

    fetchDashboardData()
  }, [])

  if (loading || !user) {
    return <div>Loading...</div>
  }

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'approved': return 'bg-green-500'
      case 'rejected': return 'bg-red-500'
      case 'pending': return 'bg-yellow-500'
      case 'under_review': return 'bg-blue-500'
      default: return 'bg-gray-500'
    }
  }

  const partnerInfo = (user?.preferences?.partner_data || {}) as {
    organization_name?: string
    organization_type?: string
    sector?: string
    [key: string]: any
  }

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-7xl mx-auto space-y-6">
        {/* Header */}
        <div className="bg-white rounded-lg shadow-sm p-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="h-16 w-16 bg-blue-600 rounded-full flex items-center justify-center">
                <Building className="h-8 w-8 text-white" />
              </div>
              <div>
                <h1 className="text-2xl font-bold text-gray-900">
                  Welcome, {partnerInfo.organization_name || user?.first_name}!
                </h1>
                <p className="text-gray-600">Partnership Dashboard</p>
                {partnerInfo.organization_type && (
                  <p className="text-sm text-blue-600">
                    {partnerInfo.organization_type} • {partnerInfo.sector}
                  </p>
                )}
              </div>
            </div>
            <div className="flex space-x-3">
              <Button variant="outline" asChild>
                <Link href="/partnerships/opportunities">
                  <Plus className="h-4 w-4 mr-2" />
                  View Opportunities
                </Link>
              </Button>
              <Button asChild>
                <Link href="/partnerships/apply">
                  Apply for Partnership
                </Link>
              </Button>
            </div>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Applications</CardTitle>
              <FileText className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{partnershipApplications.length}</div>
              <p className="text-xs text-muted-foreground">
                Partnership applications
              </p>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Opportunities</CardTitle>
              <Target className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-blue-600">
                {collaborationOpportunities.length}
              </div>
              <p className="text-xs text-muted-foreground">
                Available collaborations
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Programs</CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">
                {programs.length}
              </div>
              <p className="text-xs text-muted-foreground">
                Available programs
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Events</CardTitle>
              <Calendar className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-purple-600">
                {upcomingEvents.length}
              </div>
              <p className="text-xs text-muted-foreground">
                Upcoming events
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Main Content */}
        <Tabs defaultValue="overview" className="space-y-6">
          <TabsList className="grid w-full grid-cols-6">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="opportunities">Opportunities</TabsTrigger>
            <TabsTrigger value="proposals">Proposals</TabsTrigger>
            <TabsTrigger value="resources">Resources</TabsTrigger>
            <TabsTrigger value="applications">Applications</TabsTrigger>
            <TabsTrigger value="profile">Profile</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Collaboration Opportunities */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <Handshake className="h-5 w-5 mr-2" />
                    Collaboration Opportunities
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  {collaborationOpportunities.slice(0, 3).map((opportunity: any) => (
                    <div key={opportunity.id} className="p-3 border rounded-lg">
                      <h4 className="font-medium">{opportunity.title}</h4>
                      <p className="text-sm text-gray-600 mt-1">{opportunity.description}</p>
                      <div className="flex items-center gap-4 mt-2 text-xs text-gray-500">
                        <span><MapPin className="h-3 w-3 inline mr-1" />{opportunity.location}</span>
                        <span><Clock className="h-3 w-3 inline mr-1" />{opportunity.timeline}</span>
                      </div>
                      <div className="mt-2">
                        <Badge variant="secondary">{opportunity.type}</Badge>
                        <span className="text-xs text-gray-500 ml-2">{opportunity.budget_range}</span>
                      </div>
                    </div>
                  ))}
                  <Button variant="outline" className="w-full" asChild>
                    <Link href="/partnerships/opportunities">View All Opportunities</Link>
                  </Button>
                </CardContent>
              </Card>

              {/* Current Programs */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <Users className="h-5 w-5 mr-2" />
                    Available Programs
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  {programs.map((program: any) => (
                    <div key={program.id} className="p-3 border rounded-lg">
                      <h4 className="font-medium">{program.title}</h4>
                      <p className="text-sm text-gray-600 mt-1">{program.description}</p>
                      <div className="flex items-center justify-between mt-2">
                        <Badge variant="outline">{program.category}</Badge>
                        <Button variant="outline" size="sm">Learn More</Button>
                      </div>
                    </div>
                  ))}
                  {programs.length === 0 && (
                    <p className="text-gray-500 text-center py-4">No programs available</p>
                  )}
                </CardContent>
              </Card>
            </div>

            {/* Partnership Applications Status */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <FileText className="h-5 w-5 mr-2" />
                  Partnership Applications
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {partnershipApplications.map((application: any) => (
                    <div key={application.id} className="flex items-center justify-between p-3 border rounded-lg">
                      <div>
                        <p className="font-medium">{application.program}</p>
                        <p className="text-sm text-gray-600">
                          Submitted: {new Date(application.submitted_at).toLocaleDateString()}
                        </p>
                      </div>
                      <Badge className={getStatusColor(application.status)}>
                        {application.status}
                      </Badge>
                    </div>
                  ))}
                  {partnershipApplications.length === 0 && (
                    <div className="text-center py-8">
                      <Handshake className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                      <p className="text-gray-500 mb-4">No partnership applications yet</p>
                      <Button asChild>
                        <Link href="/partnerships/apply">Submit Application</Link>
                      </Button>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="opportunities" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Partnership Opportunities</CardTitle>
                <CardDescription>
                  Explore collaboration opportunities that align with your organization's mission
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {collaborationOpportunities.map((opportunity: any) => (
                    <div key={opportunity.id} className="border rounded-lg p-4">
                      <div className="flex items-center justify-between mb-2">
                        <h4 className="font-semibold">{opportunity.title}</h4>
                        <Button variant="outline" size="sm">Apply</Button>
                      </div>
                      <p className="text-sm text-gray-600 mb-3">{opportunity.description}</p>
                      <div className="grid grid-cols-2 gap-4 text-sm text-gray-600">
                        <p><MapPin className="h-4 w-4 inline mr-1" />Location: {opportunity.location}</p>
                        <p><Clock className="h-4 w-4 inline mr-1" />Timeline: {opportunity.timeline}</p>
                        <p><Target className="h-4 w-4 inline mr-1" />Type: {opportunity.type}</p>
                        <p><Award className="h-4 w-4 inline mr-1" />Budget: {opportunity.budget_range}</p>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="proposals" className="space-y-6">
            <ProposalManager />
          </TabsContent>

          <TabsContent value="resources" className="space-y-6">
            <ResourceSharing />
          </TabsContent>

          <TabsContent value="applications" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>My Partnership Applications</CardTitle>
                <CardDescription>
                  Track the status of your partnership applications
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {partnershipApplications.map((application: any) => (
                    <div key={application.id} className="border rounded-lg p-4">
                      <div className="flex items-center justify-between mb-2">
                        <h4 className="font-semibold">{application.program}</h4>
                        <Badge className={getStatusColor(application.status)}>
                          {application.status}
                        </Badge>
                      </div>
                      <div className="grid grid-cols-2 gap-4 text-sm text-gray-600">
                        <p>Organization: {application.organization}</p>
                        <p>Submitted: {new Date(application.submitted_at).toLocaleDateString()}</p>
                      </div>
                      <div className="mt-3 flex space-x-2">
                        <Button variant="outline" size="sm">View Details</Button>
                        {application.status === 'pending' && (
                          <Button variant="outline" size="sm">Edit Application</Button>
                        )}
                      </div>
                    </div>
                  ))}
                  {partnershipApplications.length === 0 && (
                    <div className="text-center py-8">
                      <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                      <p className="text-gray-500 mb-4">No applications submitted yet</p>
                      <Button asChild>
                        <Link href="/partnerships/apply">Submit Your First Application</Link>
                      </Button>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="profile" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Organization Profile</CardTitle>
                <CardDescription>
                  Manage your organization's information and partnership preferences
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Organization Name</label>
                    <p className="text-sm text-gray-600">{partnerInfo.organization_name || 'Not specified'}</p>
                  </div>
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Organization Type</label>
                    <p className="text-sm text-gray-600">{partnerInfo.organization_type || 'Not specified'}</p>
                  </div>
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Contact Person</label>
                    <p className="text-sm text-gray-600">{user?.first_name} {user?.last_name}</p>
                  </div>
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Email</label>
                    <p className="text-sm text-gray-600">{user?.email}</p>
                  </div>
                  {partnerInfo.website && (
                    <div className="space-y-2">
                      <label className="text-sm font-medium">Website</label>
                      <p className="text-sm text-gray-600">{partnerInfo.website}</p>
                    </div>
                  )}
                  {partnerInfo.sector && (
                    <div className="space-y-2">
                      <label className="text-sm font-medium">Sector</label>
                      <p className="text-sm text-gray-600">{partnerInfo.sector}</p>
                    </div>
                  )}
                  {partnerInfo.size && (
                    <div className="space-y-2">
                      <label className="text-sm font-medium">Organization Size</label>
                      <p className="text-sm text-gray-600">{partnerInfo.size}</p>
                    </div>
                  )}
                  {partnerInfo.annual_budget && (
                    <div className="space-y-2">
                      <label className="text-sm font-medium">Annual Budget</label>
                      <p className="text-sm text-gray-600">{partnerInfo.annual_budget}</p>
                    </div>
                  )}
                </div>
                
                {partnerInfo.partnership_interests && (
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Partnership Interests</label>
                    <p className="text-sm text-gray-600">{partnerInfo.partnership_interests}</p>
                  </div>
                )}
                
                <Button variant="outline">Edit Organization Profile</Button>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  )
}