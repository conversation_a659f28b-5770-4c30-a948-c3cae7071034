<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Scholarship;
use App\Models\ScholarshipApplication;
use App\Models\ScholarshipApplicationFile;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\DB;

class StudentScholarshipController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth:sanctum');
        $this->middleware('student.auth');
    }

    /**
     * Get available scholarships for individual students (University only)
     */
    public function getAvailableScholarships(): JsonResponse
    {
        try {
            $scholarships = Scholarship::where('status', 'active')
                ->where('is_open', true)
                ->where('category', 'university')
                ->where('application_deadline', '>', now())
                ->with(['fields' => function($query) {
                    $query->where('is_active', true);
                }])
                ->orderBy('application_deadline', 'asc')
                ->get();

            return response()->json([
                'success' => true,
                'data' => $scholarships,
                'message' => 'Available university scholarships retrieved successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve scholarships',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get scholarship details for students
     */
    public function getScholarshipDetails($id): JsonResponse
    {
        try {
            $scholarship = Scholarship::where('status', 'active')
                ->where('is_open', true)
                ->where('category', 'university')
                ->with(['fields' => function($query) {
                    $query->where('is_active', true);
                }])
                ->findOrFail($id);

            return response()->json([
                'success' => true,
                'data' => $scholarship,
                'message' => 'Scholarship details retrieved successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Scholarship not found or not available for individual students',
                'error' => $e->getMessage()
            ], 404);
        }
    }

    /**
     * Apply for university scholarship as individual student
     */
    public function applyForScholarship(Request $request, $scholarshipId): JsonResponse
    {
        try {
            DB::beginTransaction();

            $user = Auth::user();

            // Get scholarship and validate
            $scholarship = Scholarship::where('status', 'active')
                ->where('is_open', true)
                ->where('category', 'university')
                ->findOrFail($scholarshipId);

            // Check if application deadline has passed
            if ($scholarship->application_deadline <= now()) {
                return response()->json([
                    'success' => false,
                    'message' => 'This scholarship application deadline has passed'
                ], 410);
            }

            // Check if user already applied
            $existingApplication = ScholarshipApplication::where('scholarship_id', $scholarshipId)
                ->where('user_id', $user->id)
                ->first();

            if ($existingApplication) {
                return response()->json([
                    'success' => false,
                    'message' => 'You have already applied for this scholarship'
                ], 400);
            }

            // Validate request data
            $validator = $this->validateStudentApplication($request, $scholarship);
            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation error',
                    'errors' => $validator->errors()
                ], 422);
            }

            // Handle file uploads
            $uploadedFiles = $this->handleFileUploads($request, $scholarship);

            // Create application
            $application = $this->createStudentApplication(
                $scholarship,
                $user,
                $request,
                $uploadedFiles
            );

            DB::commit();

            return response()->json([
                'success' => true,
                'data' => [
                    'application' => $application->load(['scholarship', 'user']),
                ],
                'message' => 'University scholarship application submitted successfully'
            ], 201);

        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => 'Failed to submit application',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get applications submitted by this student
     */
    public function getMyApplications(Request $request): JsonResponse
    {
        try {
            $user = Auth::user();

            $query = ScholarshipApplication::where('user_id', $user->id)
                ->whereHas('scholarship', function ($q) {
                    $q->where('category', 'university');
                })
                ->with(['scholarship', 'files']);

            // Filter by status
            if ($request->has('status')) {
                $query->where('status', $request->status);
            }

            $applications = $query->orderBy('created_at', 'desc')
                                ->paginate($request->get('per_page', 15));

            return response()->json([
                'success' => true,
                'data' => $applications,
                'message' => 'Applications retrieved successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve applications',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Validate student application data for university scholarships
     */
    private function validateStudentApplication(Request $request, Scholarship $scholarship)
    {
        $rules = [
            // Student information
            'full_name' => 'required|string|max:255',
            'age' => 'required|integer|min:16|max:35',
            'phone_number' => 'required|string|regex:/^[\+]?[0-9\s\-\(\)]+$/',
            'email_address' => 'required|email|max:255',
            'home_address' => 'nullable|string|max:500',

            // University information
            'matriculation_number' => 'required|string|max:50',
            'university_name' => 'required|string|max:255',
            'course_of_study' => 'required|string|max:255',
            'current_level' => 'required|string|max:50',
            'year_of_study' => 'required|integer|min:1|max:7',
            'expected_graduation_year' => 'required|integer|min:2024|max:2030',
            'current_cgpa' => 'nullable|numeric|min:0|max:5.0',

            // Application details
            'reason_for_scholarship' => 'required|string|min:50|max:1000',
            'career_goals' => 'nullable|string|max:1000',
            'financial_need_explanation' => 'nullable|string|max:1000',

            // File uploads
            'student_id_card' => 'required|file|mimes:jpeg,png,jpg,pdf|max:2048',
            'remita_payment' => 'required|file|mimes:jpeg,png,jpg,pdf|max:2048',
            'academic_transcript' => 'nullable|file|mimes:jpeg,png,jpg,pdf|max:5120',
            'supporting_documents' => 'nullable|file|mimes:jpeg,png,jpg,pdf|max:5120',
        ];

        return Validator::make($request->all(), $rules);
    }

    /**
     * Handle file uploads for student application
     */
    private function handleFileUploads(Request $request, Scholarship $scholarship): array
    {
        $uploadedFiles = [];
        $fileFields = ['student_id_card', 'remita_payment', 'academic_transcript', 'supporting_documents'];

        foreach ($fileFields as $field) {
            if ($request->hasFile($field)) {
                $file = $request->file($field);
                if ($file && $file->isValid()) {
                    $filename = time() . '_' . $field . '_' . $file->getClientOriginalName();
                    $path = $file->storeAs('student_applications', $filename, 'public');

                    $uploadedFiles[] = [
                        'field_name' => $field,
                        'original_name' => $file->getClientOriginalName(),
                        'file_path' => $path,
                        'file_size' => $file->getSize(),
                        'file_type' => $file->getMimeType(),
                    ];
                }
            }
        }

        return $uploadedFiles;
    }

    /**
     * Create scholarship application for individual student
     */
    private function createStudentApplication(
        Scholarship $scholarship,
        $user,
        Request $request,
        array $uploadedFiles
    ): ScholarshipApplication {
        // Prepare form data
        $formData = [
            'full_name' => $request->full_name,
            'age' => $request->age,
            'phone_number' => $request->phone_number,
            'email_address' => $request->email_address,
            'home_address' => $request->home_address,
            'matriculation_number' => $request->matriculation_number,
            'university_name' => $request->university_name,
            'course_of_study' => $request->course_of_study,
            'current_level' => $request->current_level,
            'year_of_study' => $request->year_of_study,
            'expected_graduation_year' => $request->expected_graduation_year,
            'current_cgpa' => $request->current_cgpa,
            'reason_for_scholarship' => $request->reason_for_scholarship,
            'career_goals' => $request->career_goals,
            'financial_need_explanation' => $request->financial_need_explanation,
        ];

        // Create application
        $application = ScholarshipApplication::create([
            'scholarship_id' => $scholarship->id,
            'user_id' => $user->id,
            'application_type' => 'individual',
            'matriculation_number' => $request->matriculation_number,
            'institution_name' => $request->university_name,
            'course_of_study' => $request->course_of_study,
            'year_of_study' => $request->year_of_study,
            'expected_graduation_year' => $request->expected_graduation_year,
            'form_data' => $formData,
            'status' => 'pending',
            'submitted_at' => now(),
        ]);

        // Create file records
        foreach ($uploadedFiles as $fileData) {
            ScholarshipApplicationFile::create([
                'application_id' => $application->id,
                'field_name' => $fileData['field_name'],
                'original_name' => $fileData['original_name'],
                'file_path' => $fileData['file_path'],
                'file_size' => $fileData['file_size'],
                'file_type' => $fileData['file_type'],
                'upload_date' => now(),
            ]);
        }

        return $application;
    }
}
