<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Support\Str;

class PartnerOrganization extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'organization_id',
        'name',
        'slug',
        'type',
        'description',
        'email',
        'phone',
        'alternative_phone',
        'address',
        'city',
        'state',
        'country',
        'postal_code',
        'registration_number',
        'license_number',
        'established_date',
        'student_capacity',
        'current_enrollment',
        'education_levels',
        'grade_levels',
        'contact_person_name',
        'contact_person_title',
        'contact_person_phone',
        'contact_person_email',
        'principal_name',
        'principal_phone',
        'principal_email',
        'principal_qualification',
        'principal_experience_years',
        'verification_status',
        'verified_at',
        'verified_by',
        'verification_notes',
        'status',
        'partnership_start_date',
        'partnership_end_date',
        'partnership_status',
        'partnership_terms',
        'documents',
        'credentials',
        'certifications',
        'success_rate',
        'total_applications',
        'approved_applications',
        'average_student_performance',
        'facilities',
        'programs_offered',
        'extracurricular_activities',
        'mission_statement',
        'vision_statement',
        'additional_info',
        'created_by',
        'updated_by',
        'last_activity_at',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'established_date' => 'date',
        'partnership_start_date' => 'date',
        'partnership_end_date' => 'date',
        'verified_at' => 'datetime',
        'last_activity_at' => 'datetime',
        'education_levels' => 'array',
        'grade_levels' => 'array',
        'documents' => 'array',
        'credentials' => 'array',
        'certifications' => 'array',
        'facilities' => 'array',
        'programs_offered' => 'array',
        'extracurricular_activities' => 'array',
        'additional_info' => 'array',
        'student_capacity' => 'integer',
        'current_enrollment' => 'integer',
        'principal_experience_years' => 'integer',
        'total_applications' => 'integer',
        'approved_applications' => 'integer',
        'success_rate' => 'decimal:2',
        'average_student_performance' => 'decimal:2',
    ];

    /**
     * The accessors to append to the model's array form.
     *
     * @var array<int, string>
     */
    protected $appends = ['type_display', 'verification_status_display', 'partnership_status_display'];

    /**
     * Boot method to auto-generate organization_id and slug
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($model) {
            if (empty($model->organization_id)) {
                $model->organization_id = static::generateOrganizationId();
            }
            if (empty($model->slug)) {
                $model->slug = static::generateSlug($model->name);
            }
        });

        static::updating(function ($model) {
            if ($model->isDirty('name') && empty($model->slug)) {
                $model->slug = static::generateSlug($model->name);
            }
        });
    }

    /**
     * Generate a unique organization ID
     */
    public static function generateOrganizationId(): string
    {
        do {
            $orgId = 'HLTKKQ-ORG-' . str_pad(random_int(1, 9999), 4, '0', STR_PAD_LEFT);
        } while (self::where('organization_id', $orgId)->exists());

        return $orgId;
    }

    /**
     * Generate a unique slug from name
     */
    public static function generateSlug(string $name): string
    {
        $baseSlug = Str::slug($name);
        $slug = $baseSlug;
        $counter = 1;

        while (self::where('slug', $slug)->exists()) {
            $slug = $baseSlug . '-' . $counter;
            $counter++;
        }

        return $slug;
    }

    /**
     * Get the route key for the model.
     */
    public function getRouteKeyName(): string
    {
        return 'slug';
    }

    /**
     * Get the display format for organization type.
     */
    public function getTypeDisplayAttribute(): string
    {
        return match($this->type) {
            'primary_school' => 'Primary School',
            'secondary_school' => 'Secondary School',
            'university' => 'University',
            'ngo' => 'NGO',
            'government' => 'Government Agency',
            'private_sector' => 'Private Sector',
            'other' => 'Other',
            default => ucfirst(str_replace('_', ' ', $this->type))
        };
    }

    /**
     * Get the display format for verification status.
     */
    public function getVerificationStatusDisplayAttribute(): string
    {
        return match($this->verification_status) {
            'pending' => 'Pending Verification',
            'verified' => 'Verified',
            'rejected' => 'Verification Rejected',
            'suspended' => 'Verification Suspended',
            default => ucfirst($this->verification_status)
        };
    }

    /**
     * Get the display format for partnership status.
     */
    public function getPartnershipStatusDisplayAttribute(): string
    {
        return match($this->partnership_status) {
            'active' => 'Active Partnership',
            'inactive' => 'Inactive Partnership',
            'terminated' => 'Partnership Terminated',
            'pending' => 'Partnership Pending',
            default => ucfirst($this->partnership_status)
        };
    }

    /**
     * Check if organization is verified.
     */
    public function isVerified(): bool
    {
        return $this->verification_status === 'verified';
    }

    /**
     * Check if organization has active partnership.
     */
    public function hasActivePartnership(): bool
    {
        return $this->partnership_status === 'active';
    }

    /**
     * Check if organization is a school.
     */
    public function isSchool(): bool
    {
        return in_array($this->type, ['primary_school', 'secondary_school']);
    }

    /**
     * Check if organization is a university.
     */
    public function isUniversity(): bool
    {
        return $this->type === 'university';
    }

    /**
     * Get the user who created this organization.
     */
    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Get the user who last updated this organization.
     */
    public function updater(): BelongsTo
    {
        return $this->belongsTo(User::class, 'updated_by');
    }

    /**
     * Get the user who verified this organization.
     */
    public function verifier(): BelongsTo
    {
        return $this->belongsTo(User::class, 'verified_by');
    }

    /**
     * Get all students enrolled in this organization.
     */
    public function students(): HasMany
    {
        return $this->hasMany(Student::class, 'school_id');
    }

    /**
     * Get all scholarship applications from this organization.
     */
    public function scholarshipApplications(): HasMany
    {
        return $this->hasMany(ScholarshipApplication::class, 'school_id');
    }

    /**
     * Get users associated with this organization.
     */
    public function users(): BelongsToMany
    {
        return $this->belongsToMany(User::class, 'partner_organization_users')
                    ->withPivot(['role', 'status', 'start_date', 'end_date', 'permissions', 'notes'])
                    ->withTimestamps();
    }

    /**
     * Get active users for this organization.
     */
    public function activeUsers()
    {
        return $this->users()->wherePivot('status', 'active');
    }

    /**
     * Get the primary user (main contact) for this organization.
     */
    public function primaryUser()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Scope for verified organizations.
     */
    public function scopeVerified($query)
    {
        return $query->where('verification_status', 'verified');
    }

    /**
     * Scope for active organizations.
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    /**
     * Scope for organizations with active partnerships.
     */
    public function scopeActivePartnership($query)
    {
        return $query->where('partnership_status', 'active');
    }

    /**
     * Scope for schools (primary and secondary).
     */
    public function scopeSchools($query)
    {
        return $query->whereIn('type', ['primary_school', 'secondary_school']);
    }

    /**
     * Scope for universities.
     */
    public function scopeUniversities($query)
    {
        return $query->where('type', 'university');
    }

    /**
     * Scope by organization type.
     */
    public function scopeByType($query, $type)
    {
        return $query->where('type', $type);
    }

    /**
     * Scope by location.
     */
    public function scopeByLocation($query, $state = null, $city = null)
    {
        if ($state) {
            $query->where('state', $state);
        }
        if ($city) {
            $query->where('city', $city);
        }
        return $query;
    }
}
