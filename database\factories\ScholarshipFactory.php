<?php

namespace Database\Factories;

use App\Models\Scholarship;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Scholarship>
 */
class ScholarshipFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Scholarship::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $categories = ['primary', 'secondary', 'university'];
        $category = $this->faker->randomElement($categories);
        
        return [
            'title' => $this->faker->sentence(4) . ' Scholarship',
            'description' => $this->faker->paragraph(3),
            'category' => $category,
            'amount' => $this->faker->randomElement([25000, 50000, 75000, 100000, 150000, 200000]),
            'currency' => 'NGN',
            'max_recipients' => $this->faker->numberBetween(5, 50),
            'application_deadline' => $this->faker->dateTimeBetween('now', '+6 months'),
            'academic_year' => $this->faker->randomElement(['2024-2025', '2025-2026']),
            'eligibility_criteria' => $this->faker->paragraph(2),
            'required_documents' => json_encode([
                'Academic transcript',
                'Letter of recommendation',
                'Personal statement',
                'Financial need documentation'
            ]),
            'application_instructions' => $this->faker->paragraph(3),
            'selection_criteria' => $this->faker->paragraph(2),
            'renewable' => $this->faker->boolean(60),
            'renewal_criteria' => $this->faker->optional()->paragraph(),
            'contact_email' => $this->faker->safeEmail(),
            'contact_phone' => $this->faker->optional()->phoneNumber(),
            'website_url' => $this->faker->optional()->url(),
            'sponsor_name' => $this->faker->optional()->company(),
            'sponsor_logo' => $this->faker->optional()->imageUrl(200, 100, 'business'),
            'terms_conditions' => $this->faker->paragraph(4),
            'privacy_policy' => $this->faker->paragraph(3),
            'status' => $this->faker->randomElement(['draft', 'active', 'closed', 'cancelled']),
            'featured' => $this->faker->boolean(20),
            'priority' => $this->faker->numberBetween(1, 10),
            'tags' => json_encode($this->faker->randomElements([
                'merit-based', 'need-based', 'stem', 'arts', 'sports', 
                'leadership', 'community-service', 'minority', 'women'
            ], $this->faker->numberBetween(1, 4))),
            'metadata' => json_encode([
                'created_by' => 'system',
                'source' => 'internal',
                'version' => '1.0'
            ]),
            'created_at' => now(),
            'updated_at' => now(),
        ];
    }

    /**
     * Indicate that the scholarship is for primary students.
     */
    public function primary(): static
    {
        return $this->state(fn (array $attributes) => [
            'category' => 'primary',
            'title' => 'Primary Education ' . $this->faker->word() . ' Scholarship',
            'amount' => $this->faker->randomElement([25000, 50000, 75000]),
        ]);
    }

    /**
     * Indicate that the scholarship is for secondary students.
     */
    public function secondary(): static
    {
        return $this->state(fn (array $attributes) => [
            'category' => 'secondary',
            'title' => 'Secondary Education ' . $this->faker->word() . ' Scholarship',
            'amount' => $this->faker->randomElement([50000, 75000, 100000]),
        ]);
    }

    /**
     * Indicate that the scholarship is for university students.
     */
    public function university(): static
    {
        return $this->state(fn (array $attributes) => [
            'category' => 'university',
            'title' => 'University ' . $this->faker->word() . ' Scholarship',
            'amount' => $this->faker->randomElement([100000, 150000, 200000, 250000]),
        ]);
    }

    /**
     * Indicate that the scholarship is active.
     */
    public function active(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'active',
            'application_deadline' => $this->faker->dateTimeBetween('+1 month', '+6 months'),
        ]);
    }

    /**
     * Indicate that the scholarship is closed.
     */
    public function closed(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'closed',
            'application_deadline' => $this->faker->dateTimeBetween('-6 months', '-1 month'),
        ]);
    }

    /**
     * Indicate that the scholarship is featured.
     */
    public function featured(): static
    {
        return $this->state(fn (array $attributes) => [
            'featured' => true,
            'priority' => $this->faker->numberBetween(8, 10),
        ]);
    }

    /**
     * Indicate that the scholarship is renewable.
     */
    public function renewable(): static
    {
        return $this->state(fn (array $attributes) => [
            'renewable' => true,
            'renewal_criteria' => 'Maintain minimum GPA of 3.0 and demonstrate continued financial need.',
        ]);
    }
}
