(()=>{var e={};e.id=915,e.ids=[915],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5336:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(62688).A)("CircleCheckBig",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},7430:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(62688).A)("School",[["path",{d:"M14 22v-4a2 2 0 1 0-4 0v4",key:"hhkicm"}],["path",{d:"m18 10 4 2v8a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2v-8l4-2",key:"1vwozw"}],["path",{d:"M18 5v17",key:"1sw6gf"}],["path",{d:"m4 6 8-4 8 4",key:"1q0ilc"}],["path",{d:"M6 5v17",key:"1xfsm0"}],["circle",{cx:"12",cy:"9",r:"2",key:"1092wv"}]])},8068:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>r});let r=(0,a(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\scholarships\\\\[id]\\\\apply\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\laragon\\www\\laravel-api-ngo\\front-end\\app\\scholarships\\[id]\\apply\\page.tsx","default")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},15616:(e,t,a)=>{"use strict";a.d(t,{T:()=>l});var r=a(60687),s=a(43210),i=a(96241);let l=s.forwardRef(({className:e,...t},a)=>(0,r.jsx)("textarea",{className:(0,i.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",e),ref:a,...t}));l.displayName="Textarea"},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},27900:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(62688).A)("Send",[["path",{d:"M14.536 21.686a.5.5 0 0 0 .937-.024l6.5-19a.496.496 0 0 0-.635-.635l-19 6.5a.5.5 0 0 0-.024.937l7.93 3.18a2 2 0 0 1 1.112 1.11z",key:"1ffxy3"}],["path",{d:"m21.854 2.147-10.94 10.939",key:"12cjpa"}]])},28559:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(62688).A)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},36866:(e,t,a)=>{Promise.resolve().then(a.bind(a,67662))},39390:(e,t,a)=>{"use strict";a.d(t,{J:()=>d});var r=a(60687),s=a(43210),i=a(78148),l=a(24224),n=a(96241);let o=(0,l.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),d=s.forwardRef(({className:e,...t},a)=>(0,r.jsx)(i.b,{ref:a,className:(0,n.cn)(o(),e),...t}));d.displayName=i.b.displayName},41862:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(62688).A)("LoaderCircle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},55192:(e,t,a)=>{"use strict";a.d(t,{BT:()=>d,Wu:()=>c,ZB:()=>o,Zp:()=>l,aR:()=>n,wL:()=>p});var r=a(60687),s=a(43210),i=a(96241);let l=s.forwardRef(({className:e,...t},a)=>(0,r.jsx)("div",{ref:a,className:(0,i.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",e),...t}));l.displayName="Card";let n=s.forwardRef(({className:e,...t},a)=>(0,r.jsx)("div",{ref:a,className:(0,i.cn)("flex flex-col space-y-1.5 p-6",e),...t}));n.displayName="CardHeader";let o=s.forwardRef(({className:e,...t},a)=>(0,r.jsx)("div",{ref:a,className:(0,i.cn)("text-2xl font-semibold leading-none tracking-tight",e),...t}));o.displayName="CardTitle";let d=s.forwardRef(({className:e,...t},a)=>(0,r.jsx)("div",{ref:a,className:(0,i.cn)("text-sm text-muted-foreground",e),...t}));d.displayName="CardDescription";let c=s.forwardRef(({className:e,...t},a)=>(0,r.jsx)("div",{ref:a,className:(0,i.cn)("p-6 pt-0",e),...t}));c.displayName="CardContent";let p=s.forwardRef(({className:e,...t},a)=>(0,r.jsx)("div",{ref:a,className:(0,i.cn)("flex items-center p-6 pt-0",e),...t}));p.displayName="CardFooter"},59821:(e,t,a)=>{"use strict";a.d(t,{E:()=>n});var r=a(60687);a(43210);var s=a(24224),i=a(96241);let l=(0,s.F)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function n({className:e,variant:t,...a}){return(0,r.jsx)("div",{className:(0,i.cn)(l({variant:t}),e),...a})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63974:(e,t,a)=>{"use strict";a.d(t,{bq:()=>u,eb:()=>h,gC:()=>f,l6:()=>c,yv:()=>p});var r=a(60687),s=a(43210),i=a(97822),l=a(78272),n=a(3589),o=a(13964),d=a(96241);let c=i.bL;i.YJ;let p=i.WT,u=s.forwardRef(({className:e,children:t,...a},s)=>(0,r.jsxs)(i.l9,{ref:s,className:(0,d.cn)("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",e),...a,children:[t,(0,r.jsx)(i.In,{asChild:!0,children:(0,r.jsx)(l.A,{className:"h-4 w-4 opacity-50"})})]}));u.displayName=i.l9.displayName;let m=s.forwardRef(({className:e,...t},a)=>(0,r.jsx)(i.PP,{ref:a,className:(0,d.cn)("flex cursor-default items-center justify-center py-1",e),...t,children:(0,r.jsx)(n.A,{className:"h-4 w-4"})}));m.displayName=i.PP.displayName;let x=s.forwardRef(({className:e,...t},a)=>(0,r.jsx)(i.wn,{ref:a,className:(0,d.cn)("flex cursor-default items-center justify-center py-1",e),...t,children:(0,r.jsx)(l.A,{className:"h-4 w-4"})}));x.displayName=i.wn.displayName;let f=s.forwardRef(({className:e,children:t,position:a="popper",...s},l)=>(0,r.jsx)(i.ZL,{children:(0,r.jsxs)(i.UC,{ref:l,className:(0,d.cn)("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===a&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:a,...s,children:[(0,r.jsx)(m,{}),(0,r.jsx)(i.LM,{className:(0,d.cn)("p-1","popper"===a&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:t}),(0,r.jsx)(x,{})]})}));f.displayName=i.UC.displayName,s.forwardRef(({className:e,...t},a)=>(0,r.jsx)(i.JU,{ref:a,className:(0,d.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",e),...t})).displayName=i.JU.displayName;let h=s.forwardRef(({className:e,children:t,...a},s)=>(0,r.jsxs)(i.q7,{ref:s,className:(0,d.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),...a,children:[(0,r.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,r.jsx)(i.VF,{children:(0,r.jsx)(o.A,{className:"h-4 w-4"})})}),(0,r.jsx)(i.p4,{children:t})]}));h.displayName=i.q7.displayName,s.forwardRef(({className:e,...t},a)=>(0,r.jsx)(i.wv,{ref:a,className:(0,d.cn)("-mx-1 my-1 h-px bg-muted",e),...t})).displayName=i.wv.displayName},67662:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>C});var r=a(60687),s=a(43210),i=a(16189),l=a(85814),n=a.n(l),o=a(55192),d=a(24934),c=a(68988),p=a(39390),u=a(15616),m=a(63974),x=a(59821),f=a(82080),h=a(7430),g=a(27351),y=a(93613),b=a(28559),j=a(41862),v=a(27900),N=a(5336),w=a(71702),_=a(59556);let A={primary:{icon:f.A,title:"Primary School",color:"bg-blue-500",bgColor:"bg-blue-50",textColor:"text-blue-700"},secondary:{icon:h.A,title:"Secondary School",color:"bg-green-500",bgColor:"bg-green-50",textColor:"text-green-700"},university:{icon:g.A,title:"University",color:"bg-purple-500",bgColor:"bg-purple-50",textColor:"text-purple-700"}};function C(){let e=(0,i.useParams)(),t=(0,i.useRouter)(),{toast:a}=(0,w.dj)(),[l,f]=(0,s.useState)(null),[h,g]=(0,s.useState)(!0),[C,S]=(0,s.useState)(!1),[k,P]=(0,s.useState)(null),[E,T]=(0,s.useState)({}),[R,q]=(0,s.useState)({}),D=async e=>{try{g(!0),P(null);let t=await _.uE.getPublicScholarship(e);if(t.success&&t.data){f(t.data);let e={};t.data.custom_fields?.forEach(t=>{e[t.field_name]="checkbox"===t.field_type?[]:""}),T(e)}else throw Error(t.message||"Scholarship not found")}catch(e){console.error("Error fetching scholarship:",e),P("Failed to load scholarship details. Please try again later.")}finally{g(!1)}},M=(e,t)=>{T(a=>({...a,[e]:t}))},O=(e,t)=>{t?q(a=>({...a,[e]:t})):q(t=>{let a={...t};return delete a[e],a})},I=()=>{if(!l?.custom_fields)return!0;for(let e of l.custom_fields)if(e.is_required){let t=E[e.field_name];if(!t||Array.isArray(t)&&0===t.length)return a({title:"Validation Error",description:`${e.field_name} is required`,variant:"destructive"}),!1}return!0},L=async e=>{if(e.preventDefault(),I())try{S(!0);let e=new FormData;e.append("scholarship_id",l.id.toString()),Object.entries(E).forEach(([t,a])=>{Array.isArray(a)?e.append(t,JSON.stringify(a)):e.append(t,a.toString())}),Object.entries(R).forEach(([t,a])=>{e.append(t,a)}),await new Promise(e=>setTimeout(e,2e3)),a({title:"Application Submitted!",description:"Your scholarship application has been submitted successfully.",variant:"default"}),t.push("/scholarships?submitted=true")}catch(e){console.error("Error submitting application:",e),a({title:"Submission Failed",description:"Failed to submit your application. Please try again.",variant:"destructive"})}finally{S(!1)}},F=e=>{let t=E[e.field_name]||"";switch(e.field_type){case"text":case"email":case"phone":case"number":return(0,r.jsx)(c.p,{type:"number"===e.field_type?"number":"email"===e.field_type?"email":"text",value:t,onChange:t=>M(e.field_name,t.target.value),placeholder:`Enter ${e.field_name.toLowerCase()}`,required:e.is_required});case"textarea":return(0,r.jsx)(u.T,{value:t,onChange:t=>M(e.field_name,t.target.value),placeholder:`Enter ${e.field_name.toLowerCase()}`,required:e.is_required,rows:4});case"select":return(0,r.jsxs)(m.l6,{value:t,onValueChange:t=>M(e.field_name,t),children:[(0,r.jsx)(m.bq,{children:(0,r.jsx)(m.yv,{placeholder:`Select ${e.field_name.toLowerCase()}`})}),(0,r.jsx)(m.gC,{children:e.field_options?.map(e=>r.jsx(m.eb,{value:e,children:e},e))})]});case"file":return(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(c.p,{type:"file",onChange:t=>O(e.field_name,t.target.files?.[0]||null),accept:".pdf,.doc,.docx,.jpg,.jpeg,.png",required:e.is_required}),(0,r.jsx)("p",{className:"text-xs text-gray-500",children:"Accepted formats: PDF, DOC, DOCX, JPG, PNG (Max 5MB)"})]});case"checkbox":return(0,r.jsx)("div",{className:"space-y-2",children:e.field_options?.map(a=>r.jsxs("label",{className:"flex items-center space-x-2",children:[r.jsx("input",{type:"checkbox",checked:Array.isArray(t)&&t.includes(a),onChange:r=>{let s=Array.isArray(t)?t:[];r.target.checked?M(e.field_name,[...s,a]):M(e.field_name,s.filter(e=>e!==a))},className:"rounded"}),r.jsx("span",{className:"text-sm",children:a})]},a))});default:return(0,r.jsx)(c.p,{value:t,onChange:t=>M(e.field_name,t.target.value),placeholder:`Enter ${e.field_name.toLowerCase()}`,required:e.is_required})}};if(h)return(0,r.jsx)("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900 py-8",children:(0,r.jsx)("div",{className:"container mx-auto px-4",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-green-600 mx-auto"}),(0,r.jsx)("p",{className:"mt-4 text-gray-600",children:"Loading application form..."})]})})});if(k||!l)return(0,r.jsx)("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900 py-8",children:(0,r.jsx)("div",{className:"container mx-auto px-4",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)(y.A,{className:"h-12 w-12 text-red-500 mx-auto mb-4"}),(0,r.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-2",children:"Error Loading Application"}),(0,r.jsx)("p",{className:"text-gray-600 mb-6",children:k||"Scholarship not found"}),(0,r.jsxs)("div",{className:"flex gap-4 justify-center",children:[(0,r.jsxs)(d.$,{onClick:()=>t.back(),variant:"outline",children:[(0,r.jsx)(b.A,{className:"mr-2 h-4 w-4"}),"Go Back"]}),(0,r.jsx)(d.$,{onClick:()=>D(e.id),children:"Try Again"})]})]})})});let $=A[l.category],z=$.icon;return(0,r.jsx)("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900 py-8",children:(0,r.jsxs)("div",{className:"container mx-auto px-4 sm:px-6 lg:px-8 max-w-4xl",children:[(0,r.jsx)("div",{className:"flex items-center gap-4 mb-8",children:(0,r.jsxs)(d.$,{variant:"outline",onClick:()=>t.back(),className:"flex items-center gap-2",children:[(0,r.jsx)(b.A,{className:"h-4 w-4"}),"Back to Scholarship"]})}),(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[(0,r.jsx)("div",{className:"lg:col-span-2",children:(0,r.jsxs)(o.Zp,{children:[(0,r.jsxs)(o.aR,{children:[(0,r.jsxs)("div",{className:"flex items-center gap-3 mb-4",children:[(0,r.jsx)("div",{className:`p-2 rounded-lg ${$.bgColor}`,children:(0,r.jsx)(z,{className:`h-6 w-6 ${$.textColor}`})}),(0,r.jsxs)("div",{children:[(0,r.jsx)(o.ZB,{className:"text-xl",children:"Apply for Scholarship"}),(0,r.jsx)(o.BT,{children:l.title})]})]}),l.category_instructions&&(0,r.jsxs)("div",{className:"bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg",children:[(0,r.jsx)("h4",{className:"font-semibold text-blue-900 dark:text-blue-100 mb-2",children:"Important Instructions"}),(0,r.jsxs)("p",{className:"text-blue-800 dark:text-blue-200 text-sm mb-2",children:[(0,r.jsx)("strong",{children:"Who fills this form:"})," ",l.category_instructions.filled_by]}),(0,r.jsx)("p",{className:"text-blue-700 dark:text-blue-300 text-sm",children:l.category_instructions.instruction})]})]}),(0,r.jsx)(o.Wu,{children:(0,r.jsxs)("form",{onSubmit:L,className:"space-y-6",children:[l.custom_fields?.map(e=>r.jsxs("div",{className:"space-y-2",children:[r.jsxs(p.J,{htmlFor:e.field_name,className:"flex items-center gap-2",children:[e.field_name.replace(/_/g," ").replace(/\b\w/g,e=>e.toUpperCase()),e.is_required&&r.jsx(x.E,{variant:"outline",className:"text-xs",children:"Required"})]}),F(e)]},e.id)),(0,r.jsx)("div",{className:"pt-6 border-t",children:(0,r.jsx)(d.$,{type:"submit",disabled:C,className:"w-full bg-green-600 hover:bg-green-700 text-white",size:"lg",children:C?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(j.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Submitting Application..."]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(v.A,{className:"mr-2 h-4 w-4"}),"Submit Application"]})})})]})})]})}),(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)(o.Zp,{children:[(0,r.jsx)(o.aR,{children:(0,r.jsx)(o.ZB,{className:"text-lg",children:"Scholarship Details"})}),(0,r.jsxs)(o.Wu,{className:"space-y-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"Award Amount"}),(0,r.jsxs)("p",{className:"font-semibold text-green-600",children:["₦",l.amount.toLocaleString()]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"Category"}),(0,r.jsx)(x.E,{variant:"outline",children:$.title})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"Application Deadline"}),(0,r.jsx)("p",{className:"font-semibold",children:new Date(l.application_deadline).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"})})]})]})]}),l.category_instructions?.required_info&&(0,r.jsxs)(o.Zp,{children:[(0,r.jsx)(o.aR,{children:(0,r.jsx)(o.ZB,{className:"text-lg",children:"Required Information"})}),(0,r.jsx)(o.Wu,{children:(0,r.jsx)("ul",{className:"space-y-2",children:l.category_instructions.required_info.map((e,t)=>(0,r.jsxs)("li",{className:"flex items-center gap-2 text-sm",children:[(0,r.jsx)(N.A,{className:"h-4 w-4 text-green-600"}),e]},t))})})]}),(0,r.jsxs)(o.Zp,{children:[(0,r.jsx)(o.aR,{children:(0,r.jsx)(o.ZB,{className:"text-lg",children:"Need Help?"})}),(0,r.jsxs)(o.Wu,{children:[(0,r.jsx)("p",{className:"text-sm text-gray-600 mb-4",children:"Having trouble with your application?"}),(0,r.jsx)(n(),{href:"/contact",children:(0,r.jsx)(d.$,{variant:"outline",className:"w-full",children:"Contact Support"})})]})]})]})]})]})})}},71702:(e,t,a)=>{"use strict";a.d(t,{dj:()=>u});var r=a(43210);let s=0,i=new Map,l=e=>{if(i.has(e))return;let t=setTimeout(()=>{i.delete(e),c({type:"REMOVE_TOAST",toastId:e})},1e6);i.set(e,t)},n=(e,t)=>{switch(t.type){case"ADD_TOAST":return{...e,toasts:[t.toast,...e.toasts].slice(0,1)};case"UPDATE_TOAST":return{...e,toasts:e.toasts.map(e=>e.id===t.toast.id?{...e,...t.toast}:e)};case"DISMISS_TOAST":{let{toastId:a}=t;return a?l(a):e.toasts.forEach(e=>{l(e.id)}),{...e,toasts:e.toasts.map(e=>e.id===a||void 0===a?{...e,open:!1}:e)}}case"REMOVE_TOAST":if(void 0===t.toastId)return{...e,toasts:[]};return{...e,toasts:e.toasts.filter(e=>e.id!==t.toastId)}}},o=[],d={toasts:[]};function c(e){d=n(d,e),o.forEach(e=>{e(d)})}function p({...e}){let t=(s=(s+1)%Number.MAX_SAFE_INTEGER).toString(),a=()=>c({type:"DISMISS_TOAST",toastId:t});return c({type:"ADD_TOAST",toast:{...e,id:t,open:!0,onOpenChange:e=>{e||a()}}}),{id:t,dismiss:a,update:e=>c({type:"UPDATE_TOAST",toast:{...e,id:t}})}}function u(){let[e,t]=r.useState(d);return r.useEffect(()=>(o.push(t),()=>{let e=o.indexOf(t);e>-1&&o.splice(e,1)}),[e]),{...e,toast:p,dismiss:e=>c({type:"DISMISS_TOAST",toastId:e})}}},78148:(e,t,a)=>{"use strict";a.d(t,{b:()=>n});var r=a(43210),s=a(14163),i=a(60687),l=r.forwardRef((e,t)=>(0,i.jsx)(s.sG.label,{...e,ref:t,onMouseDown:t=>{t.target.closest("button, input, select, textarea")||(e.onMouseDown?.(t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));l.displayName="Label";var n=l},86639:(e,t,a)=>{"use strict";a.r(t),a.d(t,{GlobalError:()=>l.a,__next_app__:()=>p,pages:()=>c,routeModule:()=>u,tree:()=>d});var r=a(65239),s=a(48088),i=a(88170),l=a.n(i),n=a(30893),o={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>n[e]);a.d(t,o);let d={children:["",{children:["scholarships",{children:["[id]",{children:["apply",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,8068)),"C:\\laragon\\www\\laravel-api-ngo\\front-end\\app\\scholarships\\[id]\\apply\\page.tsx"]}]},{}]},{}]},{}]},{layout:[()=>Promise.resolve().then(a.bind(a,58014)),"C:\\laragon\\www\\laravel-api-ngo\\front-end\\app\\layout.tsx"],error:[()=>Promise.resolve().then(a.bind(a,52608)),"C:\\laragon\\www\\laravel-api-ngo\\front-end\\app\\error.tsx"],loading:[()=>Promise.resolve().then(a.bind(a,99766)),"C:\\laragon\\www\\laravel-api-ngo\\front-end\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(a.bind(a,82366)),"C:\\laragon\\www\\laravel-api-ngo\\front-end\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(a.t.bind(a,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(a.t.bind(a,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["C:\\laragon\\www\\laravel-api-ngo\\front-end\\app\\scholarships\\[id]\\apply\\page.tsx"],p={require:a,loadChunk:()=>Promise.resolve()},u=new r.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/scholarships/[id]/apply/page",pathname:"/scholarships/[id]/apply",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},93613:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(62688).A)("CircleAlert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},96698:(e,t,a)=>{Promise.resolve().then(a.bind(a,8068))}};var t=require("../../../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),r=t.X(0,[555,394,702],()=>a(86639));module.exports=r})();