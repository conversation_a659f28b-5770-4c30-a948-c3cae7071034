"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2504],{5040:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("BookOpen",[["path",{d:"M12 7v14",key:"1akyts"}],["path",{d:"M3 18a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1h5a4 4 0 0 1 4 4 4 4 0 0 1 4-4h5a1 1 0 0 1 1 1v13a1 1 0 0 1-1 1h-6a3 3 0 0 0-3 3 3 3 0 0 0-3-3z",key:"ruj8y"}]])},5845:(e,t,r)=>{r.d(t,{i:()=>a});var n=r(12115),l=r(39033);function a({prop:e,defaultProp:t,onChange:r=()=>{}}){let[a,o]=function({defaultProp:e,onChange:t}){let r=n.useState(e),[a]=r,o=n.useRef(a),u=(0,l.c)(t);return n.useEffect(()=>{o.current!==a&&(u(a),o.current=a)},[a,o,u]),r}({defaultProp:t,onChange:r}),u=void 0!==e,i=u?e:a,c=(0,l.c)(r);return[i,n.useCallback(t=>{if(u){let r="function"==typeof t?t(e):t;r!==e&&c(r)}else o(t)},[u,e,o,c])]}},6101:(e,t,r)=>{r.d(t,{s:()=>o,t:()=>a});var n=r(12115);function l(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function a(...e){return t=>{let r=!1,n=e.map(e=>{let n=l(e,t);return r||"function"!=typeof n||(r=!0),n});if(r)return()=>{for(let t=0;t<n.length;t++){let r=n[t];"function"==typeof r?r():l(e[t],null)}}}}function o(...e){return n.useCallback(a(...e),e)}},12486:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("Send",[["path",{d:"M14.536 21.686a.5.5 0 0 0 .937-.024l6.5-19a.496.496 0 0 0-.635-.635l-19 6.5a.5.5 0 0 0-.024.937l7.93 3.18a2 2 0 0 1 1.112 1.11z",key:"1ffxy3"}],["path",{d:"m21.854 2.147-10.94 10.939",key:"12cjpa"}]])},35169:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},35695:(e,t,r)=>{var n=r(18999);r.o(n,"notFound")&&r.d(t,{notFound:function(){return n.notFound}}),r.o(n,"useParams")&&r.d(t,{useParams:function(){return n.useParams}}),r.o(n,"usePathname")&&r.d(t,{usePathname:function(){return n.usePathname}}),r.o(n,"useRouter")&&r.d(t,{useRouter:function(){return n.useRouter}}),r.o(n,"useSearchParams")&&r.d(t,{useSearchParams:function(){return n.useSearchParams}})},39033:(e,t,r)=>{r.d(t,{c:()=>l});var n=r(12115);function l(e){let t=n.useRef(e);return n.useEffect(()=>{t.current=e}),n.useMemo(()=>(...e)=>t.current?.(...e),[])}},40646:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("CircleCheckBig",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},40968:(e,t,r)=>{r.d(t,{b:()=>u});var n=r(12115),l=r(63655),a=r(95155),o=n.forwardRef((e,t)=>(0,a.jsx)(l.sG.label,{...e,ref:t,onMouseDown:t=>{var r;t.target.closest("button, input, select, textarea")||(null===(r=e.onMouseDown)||void 0===r||r.call(e,t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));o.displayName="Label";var u=o},46081:(e,t,r)=>{r.d(t,{A:()=>o,q:()=>a});var n=r(12115),l=r(95155);function a(e,t){let r=n.createContext(t),a=e=>{let{children:t,...a}=e,o=n.useMemo(()=>a,Object.values(a));return(0,l.jsx)(r.Provider,{value:o,children:t})};return a.displayName=e+"Provider",[a,function(l){let a=n.useContext(r);if(a)return a;if(void 0!==t)return t;throw Error(`\`${l}\` must be used within \`${e}\``)}]}function o(e,t=[]){let r=[],a=()=>{let t=r.map(e=>n.createContext(e));return function(r){let l=r?.[e]||t;return n.useMemo(()=>({[`__scope${e}`]:{...r,[e]:l}}),[r,l])}};return a.scopeName=e,[function(t,a){let o=n.createContext(a),u=r.length;r=[...r,a];let i=t=>{let{scope:r,children:a,...i}=t,c=r?.[e]?.[u]||o,s=n.useMemo(()=>i,Object.values(i));return(0,l.jsx)(c.Provider,{value:s,children:a})};return i.displayName=t+"Provider",[i,function(r,l){let i=l?.[e]?.[u]||o,c=n.useContext(i);if(c)return c;if(void 0!==a)return a;throw Error(`\`${r}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let r=()=>{let r=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let l=r.reduce((t,{useScope:r,scopeName:n})=>{let l=r(e)[`__scope${n}`];return{...t,...l}},{});return n.useMemo(()=>({[`__scope${t.scopeName}`]:l}),[l])}};return r.scopeName=t.scopeName,r}(a,...t)]}},51154:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("LoaderCircle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},52712:(e,t,r)=>{r.d(t,{N:()=>l});var n=r(12115),l=globalThis?.document?n.useLayoutEffect:()=>{}},53896:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("School",[["path",{d:"M14 22v-4a2 2 0 1 0-4 0v4",key:"hhkicm"}],["path",{d:"m18 10 4 2v8a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2v-8l4-2",key:"1vwozw"}],["path",{d:"M18 5v17",key:"1sw6gf"}],["path",{d:"m4 6 8-4 8 4",key:"1q0ilc"}],["path",{d:"M6 5v17",key:"1xfsm0"}],["circle",{cx:"12",cy:"9",r:"2",key:"1092wv"}]])},61285:(e,t,r)=>{r.d(t,{B:()=>i});var n,l=r(12115),a=r(52712),o=(n||(n=r.t(l,2)))["useId".toString()]||(()=>void 0),u=0;function i(e){let[t,r]=l.useState(o());return(0,a.N)(()=>{e||r(e=>e??String(u++))},[e]),e||(t?`radix-${t}`:"")}},63655:(e,t,r)=>{r.d(t,{hO:()=>i,sG:()=>u});var n=r(12115),l=r(47650),a=r(99708),o=r(95155),u=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"].reduce((e,t)=>{let r=n.forwardRef((e,r)=>{let{asChild:n,...l}=e,u=n?a.DX:t;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,o.jsx)(u,{...l,ref:r})});return r.displayName=`Primitive.${t}`,{...e,[t]:r}},{});function i(e,t){e&&l.flushSync(()=>e.dispatchEvent(t))}},82284:(e,t,r)=>{r.d(t,{N:()=>i});var n=r(12115),l=r(46081),a=r(6101),o=r(99708),u=r(95155);function i(e){let t=e+"CollectionProvider",[r,i]=(0,l.A)(t),[c,s]=r(t,{collectionRef:{current:null},itemMap:new Map}),f=e=>{let{scope:t,children:r}=e,l=n.useRef(null),a=n.useRef(new Map).current;return(0,u.jsx)(c,{scope:t,itemMap:a,collectionRef:l,children:r})};f.displayName=t;let d=e+"CollectionSlot",p=n.forwardRef((e,t)=>{let{scope:r,children:n}=e,l=s(d,r),i=(0,a.s)(t,l.collectionRef);return(0,u.jsx)(o.DX,{ref:i,children:n})});p.displayName=d;let m=e+"CollectionItemSlot",y="data-radix-collection-item",h=n.forwardRef((e,t)=>{let{scope:r,children:l,...i}=e,c=n.useRef(null),f=(0,a.s)(t,c),d=s(m,r);return n.useEffect(()=>(d.itemMap.set(c,{ref:c,...i}),()=>void d.itemMap.delete(c))),(0,u.jsx)(o.DX,{[y]:"",ref:f,children:l})});return h.displayName=m,[{Provider:f,Slot:p,ItemSlot:h},function(t){let r=s(e+"CollectionConsumer",t);return n.useCallback(()=>{let e=r.collectionRef.current;if(!e)return[];let t=Array.from(e.querySelectorAll("[".concat(y,"]")));return Array.from(r.itemMap.values()).sort((e,r)=>t.indexOf(e.ref.current)-t.indexOf(r.ref.current))},[r.collectionRef,r.itemMap])},i]}},85185:(e,t,r)=>{r.d(t,{m:()=>n});function n(e,t,{checkForDefaultPrevented:r=!0}={}){return function(n){if(e?.(n),!1===r||!n.defaultPrevented)return t?.(n)}}},85339:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("CircleAlert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},87949:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("GraduationCap",[["path",{d:"M21.42 10.922a1 1 0 0 0-.019-1.838L12.83 5.18a2 2 0 0 0-1.66 0L2.6 9.08a1 1 0 0 0 0 1.832l8.57 3.908a2 2 0 0 0 1.66 0z",key:"j76jl0"}],["path",{d:"M22 10v6",key:"1lu8f3"}],["path",{d:"M6 12.5V16a6 3 0 0 0 12 0v-3.5",key:"1r8lef"}]])},94315:(e,t,r)=>{r.d(t,{jH:()=>a});var n=r(12115);r(95155);var l=n.createContext(void 0);function a(e){let t=n.useContext(l);return e||t||"ltr"}},99708:(e,t,r)=>{r.d(t,{DX:()=>o,xV:()=>i});var n=r(12115),l=r(6101),a=r(95155),o=n.forwardRef((e,t)=>{let{children:r,...l}=e,o=n.Children.toArray(r),i=o.find(c);if(i){let e=i.props.children,r=o.map(t=>t!==i?t:n.Children.count(e)>1?n.Children.only(null):n.isValidElement(e)?e.props.children:null);return(0,a.jsx)(u,{...l,ref:t,children:n.isValidElement(e)?n.cloneElement(e,void 0,r):null})}return(0,a.jsx)(u,{...l,ref:t,children:r})});o.displayName="Slot";var u=n.forwardRef((e,t)=>{let{children:r,...a}=e;if(n.isValidElement(r)){let e=function(e){let t=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,r=t&&"isReactWarning"in t&&t.isReactWarning;return r?e.ref:(r=(t=Object.getOwnPropertyDescriptor(e,"ref")?.get)&&"isReactWarning"in t&&t.isReactWarning)?e.props.ref:e.props.ref||e.ref}(r);return n.cloneElement(r,{...function(e,t){let r={...t};for(let n in t){let l=e[n],a=t[n];/^on[A-Z]/.test(n)?l&&a?r[n]=(...e)=>{a(...e),l(...e)}:l&&(r[n]=l):"style"===n?r[n]={...l,...a}:"className"===n&&(r[n]=[l,a].filter(Boolean).join(" "))}return{...e,...r}}(a,r.props),ref:t?(0,l.t)(t,e):e})}return n.Children.count(r)>1?n.Children.only(null):null});u.displayName="SlotClone";var i=({children:e})=>(0,a.jsx)(a.Fragment,{children:e});function c(e){return n.isValidElement(e)&&e.type===i}}}]);