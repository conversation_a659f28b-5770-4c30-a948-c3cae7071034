"use client"

import { useEffect, useState } from "react"
import { apiClient, extractArrayData } from "@/lib/api"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { 
  Heart, 
  Clock, 
  Calendar, 
  Users, 
  Award, 
  Target,
  CheckCircle,
  AlertCircle,
  Plus,
  TrendingUp,
  BookOpen,
  MapPin
} from "lucide-react"
import Link from "next/link"

interface User {
  id: number
  first_name: string
  last_name: string
  email: string
  phone_number?: string
  preferences?: any
}

interface VolunteerDashboardProps {
  user: User
}

interface VolunteerApplication {
  id: number
  application_status: string
  hours_logged: number
  skills: string[]
  interests: string[]
  motivation: string
  approved_at?: string
}

interface VolunteerOpportunity {
  id: number
  title: string
  description: string
  location: string
  time_commitment: string
  skills_required: string[]
}

export default function VolunteerDashboard({ user }: VolunteerDashboardProps) {
  const [volunteerApplication, setVolunteerApplication] = useState<VolunteerApplication | null>(null)
  const [volunteerHours, setVolunteerHours] = useState<any>(null)
  const [opportunities, setOpportunities] = useState<VolunteerOpportunity[]>([])
  const [upcomingEvents, setUpcomingEvents] = useState([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const fetchDashboardData = async () => {
      try {
        // Fetch volunteer-specific data
        const [applicationResponse, hoursResponse, opportunitiesResponse, eventsResponse] = await Promise.all([
          apiClient.getVolunteerApplication(),
          apiClient.getVolunteerHours(),
          apiClient.getVolunteerOpportunities(),
          apiClient.getUpcomingEvents()
        ])

        if (applicationResponse.success) {
          setVolunteerApplication(applicationResponse.data)
        }

        if (hoursResponse.success) {
          setVolunteerHours(hoursResponse.data)
        }

        if (opportunitiesResponse.success) {
          const opportunitiesData = extractArrayData(opportunitiesResponse)
          setOpportunities(opportunitiesData.slice(0, 5))
        }

        if (eventsResponse.success) {
          const eventsData = extractArrayData(eventsResponse)
          setUpcomingEvents(eventsData.slice(0, 3))
        }
      } catch (error) {
        console.error('Failed to fetch dashboard data:', error)
      } finally {
        setLoading(false)
      }
    }

    fetchDashboardData()
  }, [])

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'approved': return 'bg-green-500'
      case 'rejected': return 'bg-red-500'
      case 'pending': return 'bg-yellow-500'
      default: return 'bg-gray-500'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status.toLowerCase()) {
      case 'approved': return <CheckCircle className="h-4 w-4" />
      case 'rejected': return <AlertCircle className="h-4 w-4" />
      case 'pending': return <Clock className="h-4 w-4" />
      default: return <Clock className="h-4 w-4" />
    }
  }

  const volunteerInfo = user.preferences?.volunteer_data || {}
  const hoursGoal = 100 // Default goal, could be customizable
  const hoursProgress = volunteerApplication ? (volunteerApplication.hours_logged / hoursGoal) * 100 : 0

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto">
        {/* Header - Mobile Responsive */}
        <div className="bg-white rounded-none sm:rounded-lg shadow-sm p-4 sm:p-6 mx-0 sm:mx-6 mt-0 sm:mt-6">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0">
            <div className="flex items-center space-x-3 sm:space-x-4">
              <div className="h-12 w-12 sm:h-16 sm:w-16 bg-green-600 rounded-full flex items-center justify-center">
                <Heart className="h-6 w-6 sm:h-8 sm:w-8 text-white" />
              </div>
              <div>
                <h1 className="text-xl sm:text-2xl font-bold text-gray-900">
                  Welcome back, {user.first_name}!
                </h1>
                <p className="text-sm sm:text-base text-gray-600">Volunteer Dashboard</p>
                {volunteerApplication && (
                  <div className="flex items-center mt-2">
                    {getStatusIcon(volunteerApplication.application_status)}
                    <span className="ml-2 text-xs sm:text-sm font-medium">
                      Status: {volunteerApplication.application_status.replace('_', ' ')}
                    </span>
                  </div>
                )}
              </div>
            </div>
            <div className="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-3">
              <Button variant="outline" size="sm" asChild className="w-full sm:w-auto">
                <Link href="/volunteer/opportunities">
                  <Plus className="h-4 w-4 mr-2" />
                  Find Opportunities
                </Link>
              </Button>
              {!volunteerApplication && (
                <Button size="sm" asChild className="w-full sm:w-auto">
                  <Link href="/volunteer/apply">
                    Apply as Volunteer
                  </Link>
                </Button>
              )}
            </div>
          </div>
        </div>

        {/* Stats Cards - Mobile Responsive Grid */}
        <div className="grid grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-6 px-4 sm:px-6 mt-4 sm:mt-6">
          <Card className="p-3 sm:p-4">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2 px-0">
              <CardTitle className="text-xs sm:text-sm font-medium">Hours Logged</CardTitle>
              <Clock className="h-3 w-3 sm:h-4 sm:w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent className="px-0">
              <div className="text-lg sm:text-2xl font-bold">
                {volunteerApplication?.hours_logged || 0}h
              </div>
              <p className="text-xs text-muted-foreground">
                Total volunteer hours
              </p>
            </CardContent>
          </Card>
          
          <Card className="p-3 sm:p-4">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2 px-0">
              <CardTitle className="text-xs sm:text-sm font-medium">Progress</CardTitle>
              <Target className="h-3 w-3 sm:h-4 sm:w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent className="px-0">
              <div className="text-lg sm:text-2xl font-bold">
                {Math.round(hoursProgress)}%
              </div>
              <p className="text-xs text-muted-foreground">
                Of {hoursGoal}h goal
              </p>
            </CardContent>
          </Card>

          <Card className="p-3 sm:p-4">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2 px-0">
              <CardTitle className="text-xs sm:text-sm font-medium">Opportunities</CardTitle>
              <Users className="h-3 w-3 sm:h-4 sm:w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent className="px-0">
              <div className="text-lg sm:text-2xl font-bold">
                {opportunities.length}
              </div>
              <p className="text-xs text-muted-foreground">
                Available to you
              </p>
            </CardContent>
          </Card>

          <Card className="p-3 sm:p-4">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2 px-0">
              <CardTitle className="text-xs sm:text-sm font-medium">Badges</CardTitle>
              <Award className="h-3 w-3 sm:h-4 sm:w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent className="px-0">
              <div className="text-lg sm:text-2xl font-bold">
                {volunteerHours?.badges_earned?.length || 0}
              </div>
              <p className="text-xs text-muted-foreground">
                Earned badges
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Main Content */}
        <Tabs defaultValue="overview" className="space-y-6">
          <TabsList className="grid w-full grid-cols-5">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="hours">Hours</TabsTrigger>
            <TabsTrigger value="opportunities">Opportunities</TabsTrigger>
            <TabsTrigger value="events">Events</TabsTrigger>
            <TabsTrigger value="profile">Profile</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Volunteer Progress */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <TrendingUp className="h-5 w-5 mr-2" />
                    Volunteer Progress
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <div className="flex justify-between text-sm mb-2">
                      <span>Hours Goal Progress</span>
                      <span>{volunteerApplication?.hours_logged || 0}h / {hoursGoal}h</span>
                    </div>
                    <Progress value={hoursProgress} className="h-2" />
                  </div>
                  
                  {volunteerApplication?.approved_at && (
                    <div className="bg-green-50 p-3 rounded-lg">
                      <p className="text-sm text-green-700">
                        <CheckCircle className="h-4 w-4 inline mr-2" />
                        Volunteer since {new Date(volunteerApplication.approved_at).toLocaleDateString()}
                      </p>
                    </div>
                  )}

                  {volunteerApplication?.application_status === 'pending' && (
                    <div className="bg-yellow-50 p-3 rounded-lg">
                      <p className="text-sm text-yellow-700">
                        <Clock className="h-4 w-4 inline mr-2" />
                        Your application is being reviewed. We'll notify you once it's processed.
                      </p>
                    </div>
                  )}
                </CardContent>
              </Card>

              {/* Recent Opportunities */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <Users className="h-5 w-5 mr-2" />
                    Available Opportunities
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  {opportunities.slice(0, 3).map((opportunity) => (
                    <div key={opportunity.id} className="p-3 border rounded-lg">
                      <h4 className="font-medium">{opportunity.title}</h4>
                      <p className="text-sm text-gray-600 mt-1">{opportunity.description}</p>
                      <div className="flex items-center gap-4 mt-2 text-xs text-gray-500">
                        <span><MapPin className="h-3 w-3 inline mr-1" />{opportunity.location}</span>
                        <span><Clock className="h-3 w-3 inline mr-1" />{opportunity.time_commitment}</span>
                      </div>
                    </div>
                  ))}
                  {opportunities.length === 0 && (
                    <p className="text-gray-500 text-center py-4">No opportunities available</p>
                  )}
                  <Button variant="outline" className="w-full" asChild>
                    <Link href="/volunteer/opportunities">View All Opportunities</Link>
                  </Button>
                </CardContent>
              </Card>
            </div>

            {/* Skills & Interests */}
            {volunteerApplication && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <BookOpen className="h-5 w-5 mr-2" />
                    Your Skills & Interests
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <h4 className="font-medium mb-3">Skills</h4>
                      <div className="flex flex-wrap gap-2">
                        {volunteerApplication.skills?.map((skill, index) => (
                          <Badge key={index} variant="secondary">{skill}</Badge>
                        ))}
                        {(!volunteerApplication.skills || volunteerApplication.skills.length === 0) && (
                          <p className="text-gray-500">No skills listed</p>
                        )}
                      </div>
                    </div>
                    <div>
                      <h4 className="font-medium mb-3">Interests</h4>
                      <div className="flex flex-wrap gap-2">
                        {volunteerApplication.interests?.map((interest, index) => (
                          <Badge key={index} variant="outline">{interest}</Badge>
                        ))}
                        {(!volunteerApplication.interests || volunteerApplication.interests.length === 0) && (
                          <p className="text-gray-500">No interests listed</p>
                        )}
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}
          </TabsContent>

          <TabsContent value="hours" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Volunteer Hours</CardTitle>
                <CardDescription>
                  Track and log your volunteer hours
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div className="text-center p-4 bg-green-50 rounded-lg">
                      <div className="text-2xl font-bold text-green-600">
                        {volunteerApplication?.hours_logged || 0}
                      </div>
                      <p className="text-sm text-green-700">Total Hours</p>
                    </div>
                    <div className="text-center p-4 bg-blue-50 rounded-lg">
                      <div className="text-2xl font-bold text-blue-600">
                        {Math.round(hoursProgress)}%
                      </div>
                      <p className="text-sm text-blue-700">Goal Progress</p>
                    </div>
                    <div className="text-center p-4 bg-purple-50 rounded-lg">
                      <div className="text-2xl font-bold text-purple-600">
                        {volunteerHours?.badges_earned?.length || 0}
                      </div>
                      <p className="text-sm text-purple-700">Badges Earned</p>
                    </div>
                  </div>

                  <div className="flex justify-between items-center">
                    <h4 className="font-medium">Recent Hours</h4>
                    <Button size="sm">Log New Hours</Button>
                  </div>

                  <div className="text-center py-8 text-gray-500">
                    <Clock className="h-12 w-12 mx-auto mb-4 opacity-50" />
                    <p>No logged hours yet</p>
                    <Button className="mt-4">Log Your First Hours</Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="opportunities" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Volunteer Opportunities</CardTitle>
                <CardDescription>
                  Find opportunities that match your skills and interests
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {opportunities.map((opportunity) => (
                    <div key={opportunity.id} className="border rounded-lg p-4">
                      <div className="flex items-center justify-between mb-2">
                        <h4 className="font-semibold">{opportunity.title}</h4>
                        <Button variant="outline" size="sm">Learn More</Button>
                      </div>
                      <p className="text-sm text-gray-600 mb-3">{opportunity.description}</p>
                      <div className="flex items-center gap-4 text-sm text-gray-500">
                        <span><MapPin className="h-4 w-4 inline mr-1" />{opportunity.location}</span>
                        <span><Clock className="h-4 w-4 inline mr-1" />{opportunity.time_commitment}</span>
                      </div>
                      <div className="flex flex-wrap gap-2 mt-3">
                        {opportunity.skills_required?.map((skill, index) => (
                          <Badge key={index} variant="secondary" className="text-xs">{skill}</Badge>
                        ))}
                      </div>
                    </div>
                  ))}
                  {opportunities.length === 0 && (
                    <div className="text-center py-8">
                      <Users className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                      <p className="text-gray-500 mb-4">No opportunities available right now</p>
                      <Button asChild>
                        <Link href="/volunteer/opportunities">Browse All Opportunities</Link>
                      </Button>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="events" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Volunteer Events</CardTitle>
                <CardDescription>
                  Join volunteer events and training sessions
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {upcomingEvents.map((event: any) => (
                    <div key={event.id} className="border rounded-lg p-4">
                      <div className="flex items-center justify-between mb-2">
                        <h4 className="font-semibold">{event.title}</h4>
                        <Button variant="outline" size="sm">Register</Button>
                      </div>
                      <div className="text-sm text-gray-600">
                        <p>Date: {new Date(event.start_datetime).toLocaleDateString()}</p>
                        <p>Type: {event.event_type}</p>
                      </div>
                    </div>
                  ))}
                  {upcomingEvents.length === 0 && (
                    <p className="text-gray-500 text-center py-4">No upcoming events</p>
                  )}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="profile" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Volunteer Profile</CardTitle>
                <CardDescription>
                  Manage your volunteer information and preferences
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Full Name</label>
                    <p className="text-sm text-gray-600">{user.first_name} {user.last_name}</p>
                  </div>
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Email</label>
                    <p className="text-sm text-gray-600">{user.email}</p>
                  </div>
                  {volunteerInfo.availability && (
                    <div className="space-y-2">
                      <label className="text-sm font-medium">Availability</label>
                      <p className="text-sm text-gray-600">{volunteerInfo.availability}</p>
                    </div>
                  )}
                  {volunteerInfo.experience && (
                    <div className="space-y-2">
                      <label className="text-sm font-medium">Experience</label>
                      <p className="text-sm text-gray-600">{volunteerInfo.experience}</p>
                    </div>
                  )}
                </div>
                
                {volunteerApplication?.motivation && (
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Motivation</label>
                    <p className="text-sm text-gray-600">{volunteerApplication.motivation}</p>
                  </div>
                )}
                
                <Button variant="outline">Edit Profile</Button>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  )
} 