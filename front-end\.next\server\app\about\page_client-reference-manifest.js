globalThis.__RSC_MANIFEST=(globalThis.__RSC_MANIFEST||{});globalThis.__RSC_MANIFEST["/about/page"]={"moduleLoading":{"prefix":"/_next/","crossOrigin":null},"ssrModuleMapping":{"(app-pages-browser)/./components/footer.tsx":{"*":{"id":"(ssr)/./components/footer.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/navigation.tsx":{"*":{"id":"(ssr)/./components/navigation.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/theme-provider.tsx":{"*":{"id":"(ssr)/./components/theme-provider.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/error.tsx":{"*":{"id":"(ssr)/./app/error.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/app-dir/link.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/image-component.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/image-component.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/about/page.tsx":{"*":{"id":"(ssr)/./app/about/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/blog/page.tsx":{"*":{"id":"(ssr)/./app/blog/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/blog/[slug]/page.tsx":{"*":{"id":"(ssr)/./app/blog/[slug]/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/scholarship/ScholarshipApplicationPage.tsx":{"*":{"id":"(ssr)/./components/scholarship/ScholarshipApplicationPage.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/projects/page.tsx":{"*":{"id":"(ssr)/./app/projects/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/impact/page.tsx":{"*":{"id":"(ssr)/./app/impact/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/team/page.tsx":{"*":{"id":"(ssr)/./app/team/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/auth/register/page.tsx":{"*":{"id":"(ssr)/./app/auth/register/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/auth/login/page.tsx":{"*":{"id":"(ssr)/./app/auth/login/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/contact/page.tsx":{"*":{"id":"(ssr)/./app/contact/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/scholarships/[id]/page.tsx":{"*":{"id":"(ssr)/./app/scholarships/[id]/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/scholarships/[id]/apply/page.tsx":{"*":{"id":"(ssr)/./app/scholarships/[id]/apply/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/scholarships/page.tsx":{"*":{"id":"(ssr)/./app/scholarships/page.tsx","name":"*","chunks":[],"async":false}}},"edgeSSRModuleMapping":{},"clientModules":{"C:\\laragon\\www\\laravel-api-ngo\\front-end\\app\\globals.css":{"id":"(app-pages-browser)/./app/globals.css","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\laragon\\www\\laravel-api-ngo\\front-end\\components\\footer.tsx":{"id":"(app-pages-browser)/./components/footer.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\laragon\\www\\laravel-api-ngo\\front-end\\components\\navigation.tsx":{"id":"(app-pages-browser)/./components/navigation.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\laragon\\www\\laravel-api-ngo\\front-end\\components\\theme-provider.tsx":{"id":"(app-pages-browser)/./components/theme-provider.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\laragon\\www\\laravel-api-ngo\\front-end\\node_modules\\next\\font\\google\\target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"],\"display\":\"swap\",\"variable\":\"--font-inter\"}],\"variableName\":\"inter\"}":{"id":"(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"],\"display\":\"swap\",\"variable\":\"--font-inter\"}],\"variableName\":\"inter\"}","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\laragon\\www\\laravel-api-ngo\\front-end\\app\\error.tsx":{"id":"(app-pages-browser)/./app/error.tsx","name":"*","chunks":["app/error","static/chunks/app/error.js"],"async":false},"C:\\laragon\\www\\laravel-api-ngo\\front-end\\node_modules\\next\\dist\\client\\app-dir\\link.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"C:\\laragon\\www\\laravel-api-ngo\\front-end\\node_modules\\next\\dist\\esm\\client\\app-dir\\link.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"C:\\laragon\\www\\laravel-api-ngo\\front-end\\node_modules\\next\\dist\\client\\image-component.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/image-component.js","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"C:\\laragon\\www\\laravel-api-ngo\\front-end\\node_modules\\next\\dist\\esm\\client\\image-component.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/image-component.js","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"C:\\laragon\\www\\laravel-api-ngo\\front-end\\node_modules\\next\\dist\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\laragon\\www\\laravel-api-ngo\\front-end\\node_modules\\next\\dist\\esm\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\laragon\\www\\laravel-api-ngo\\front-end\\node_modules\\next\\dist\\client\\components\\client-segment.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\laragon\\www\\laravel-api-ngo\\front-end\\node_modules\\next\\dist\\esm\\client\\components\\client-segment.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\laragon\\www\\laravel-api-ngo\\front-end\\node_modules\\next\\dist\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\laragon\\www\\laravel-api-ngo\\front-end\\node_modules\\next\\dist\\esm\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\laragon\\www\\laravel-api-ngo\\front-end\\node_modules\\next\\dist\\client\\components\\http-access-fallback\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\laragon\\www\\laravel-api-ngo\\front-end\\node_modules\\next\\dist\\esm\\client\\components\\http-access-fallback\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\laragon\\www\\laravel-api-ngo\\front-end\\node_modules\\next\\dist\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\laragon\\www\\laravel-api-ngo\\front-end\\node_modules\\next\\dist\\esm\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\laragon\\www\\laravel-api-ngo\\front-end\\node_modules\\next\\dist\\client\\components\\metadata\\async-metadata.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\laragon\\www\\laravel-api-ngo\\front-end\\node_modules\\next\\dist\\esm\\client\\components\\metadata\\async-metadata.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\laragon\\www\\laravel-api-ngo\\front-end\\node_modules\\next\\dist\\client\\components\\metadata\\metadata-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\laragon\\www\\laravel-api-ngo\\front-end\\node_modules\\next\\dist\\esm\\client\\components\\metadata\\metadata-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\laragon\\www\\laravel-api-ngo\\front-end\\node_modules\\next\\dist\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\laragon\\www\\laravel-api-ngo\\front-end\\node_modules\\next\\dist\\esm\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\laragon\\www\\laravel-api-ngo\\front-end\\app\\about\\page.tsx":{"id":"(app-pages-browser)/./app/about/page.tsx","name":"*","chunks":["app/about/page","static/chunks/app/about/page.js"],"async":false},"C:\\laragon\\www\\laravel-api-ngo\\front-end\\app\\blog\\page.tsx":{"id":"(app-pages-browser)/./app/blog/page.tsx","name":"*","chunks":[],"async":false},"C:\\laragon\\www\\laravel-api-ngo\\front-end\\app\\blog\\[slug]\\page.tsx":{"id":"(app-pages-browser)/./app/blog/[slug]/page.tsx","name":"*","chunks":[],"async":false},"C:\\laragon\\www\\laravel-api-ngo\\front-end\\components\\scholarship\\ScholarshipApplicationPage.tsx":{"id":"(app-pages-browser)/./components/scholarship/ScholarshipApplicationPage.tsx","name":"*","chunks":[],"async":false},"C:\\laragon\\www\\laravel-api-ngo\\front-end\\app\\projects\\page.tsx":{"id":"(app-pages-browser)/./app/projects/page.tsx","name":"*","chunks":[],"async":false},"C:\\laragon\\www\\laravel-api-ngo\\front-end\\app\\impact\\page.tsx":{"id":"(app-pages-browser)/./app/impact/page.tsx","name":"*","chunks":[],"async":false},"C:\\laragon\\www\\laravel-api-ngo\\front-end\\app\\team\\page.tsx":{"id":"(app-pages-browser)/./app/team/page.tsx","name":"*","chunks":[],"async":false},"C:\\laragon\\www\\laravel-api-ngo\\front-end\\app\\auth\\register\\page.tsx":{"id":"(app-pages-browser)/./app/auth/register/page.tsx","name":"*","chunks":[],"async":false},"C:\\laragon\\www\\laravel-api-ngo\\front-end\\app\\auth\\login\\page.tsx":{"id":"(app-pages-browser)/./app/auth/login/page.tsx","name":"*","chunks":[],"async":false},"C:\\laragon\\www\\laravel-api-ngo\\front-end\\app\\contact\\page.tsx":{"id":"(app-pages-browser)/./app/contact/page.tsx","name":"*","chunks":[],"async":false},"C:\\laragon\\www\\laravel-api-ngo\\front-end\\app\\scholarships\\[id]\\page.tsx":{"id":"(app-pages-browser)/./app/scholarships/[id]/page.tsx","name":"*","chunks":[],"async":false},"C:\\laragon\\www\\laravel-api-ngo\\front-end\\app\\scholarships\\[id]\\apply\\page.tsx":{"id":"(app-pages-browser)/./app/scholarships/[id]/apply/page.tsx","name":"*","chunks":[],"async":false},"C:\\laragon\\www\\laravel-api-ngo\\front-end\\app\\scholarships\\page.tsx":{"id":"(app-pages-browser)/./app/scholarships/page.tsx","name":"*","chunks":[],"async":false}},"entryCSSFiles":{"C:\\laragon\\www\\laravel-api-ngo\\front-end\\":[],"C:\\laragon\\www\\laravel-api-ngo\\front-end\\app\\layout":[{"inlined":false,"path":"static/css/app/layout.css"}],"C:\\laragon\\www\\laravel-api-ngo\\front-end\\app\\error":[],"C:\\laragon\\www\\laravel-api-ngo\\front-end\\app\\loading":[],"C:\\laragon\\www\\laravel-api-ngo\\front-end\\app\\not-found":[],"C:\\laragon\\www\\laravel-api-ngo\\front-end\\app\\page":[],"C:\\laragon\\www\\laravel-api-ngo\\front-end\\app\\about\\page":[]},"rscModuleMapping":{"(app-pages-browser)/./app/globals.css":{"*":{"id":"(rsc)/./app/globals.css","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/footer.tsx":{"*":{"id":"(rsc)/./components/footer.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/navigation.tsx":{"*":{"id":"(rsc)/./components/navigation.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/theme-provider.tsx":{"*":{"id":"(rsc)/./components/theme-provider.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/error.tsx":{"*":{"id":"(rsc)/./app/error.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/app-dir/link.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/image-component.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/image-component.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/about/page.tsx":{"*":{"id":"(rsc)/./app/about/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/blog/page.tsx":{"*":{"id":"(rsc)/./app/blog/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/blog/[slug]/page.tsx":{"*":{"id":"(rsc)/./app/blog/[slug]/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/scholarship/ScholarshipApplicationPage.tsx":{"*":{"id":"(rsc)/./components/scholarship/ScholarshipApplicationPage.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/projects/page.tsx":{"*":{"id":"(rsc)/./app/projects/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/impact/page.tsx":{"*":{"id":"(rsc)/./app/impact/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/team/page.tsx":{"*":{"id":"(rsc)/./app/team/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/auth/register/page.tsx":{"*":{"id":"(rsc)/./app/auth/register/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/auth/login/page.tsx":{"*":{"id":"(rsc)/./app/auth/login/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/contact/page.tsx":{"*":{"id":"(rsc)/./app/contact/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/scholarships/[id]/page.tsx":{"*":{"id":"(rsc)/./app/scholarships/[id]/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/scholarships/[id]/apply/page.tsx":{"*":{"id":"(rsc)/./app/scholarships/[id]/apply/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/scholarships/page.tsx":{"*":{"id":"(rsc)/./app/scholarships/page.tsx","name":"*","chunks":[],"async":false}}},"edgeRscModuleMapping":{}}