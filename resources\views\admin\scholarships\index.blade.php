@extends('layouts.admin')

@section('title', 'Scholarships Management')

@push('styles')
<style>
.chart-container {
    position: relative;
    height: 300px;
    width: 100%;
}
</style>
@endpush

@section('content')
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0">Scholarships Management</h1>
        <div>
            <a href="{{ route('admin.scholarships.applications') }}" class="btn btn-warning me-2">
                <i class="fas fa-graduation-cap me-2"></i>Applications
            </a>
            <a href="{{ route('admin.scholarships.create') }}" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i>Create Scholarship
            </a>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6">
            <div class="card bg-primary text-white mb-4">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <div class="small text-white-50">Total Scholarships</div>
                            <div class="h4">{{ $statistics['total_scholarships'] ?? 0 }}</div>
                        </div>
                        <div>
                            <i class="fas fa-graduation-cap fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="card bg-success text-white mb-4">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <div class="small text-white-50">Active Scholarships</div>
                            <div class="h4">{{ $statistics['active_scholarships'] ?? 0 }}</div>
                        </div>
                        <div>
                            <i class="fas fa-door-open fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="card bg-info text-white mb-4">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <div class="small text-white-50">Total Value</div>
                            <div class="h4">₦{{ number_format($statistics['total_value'] ?? 0, 0) }}</div>
                        </div>
                        <div>
                            <i class="fas fa-money-bill-wave fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="card bg-warning text-white mb-4">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <div class="small text-white-50">Total Applications</div>
                            <div class="h4">{{ $statistics['total_applications'] ?? 0 }}</div>
                        </div>
                        <div>
                            <i class="fas fa-users fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Category Statistics and Charts -->
    <div class="row mb-4">
        <div class="col-lg-8">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Scholarship Analytics</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <canvas id="categoryChart" width="400" height="200"></canvas>
                        </div>
                        <div class="col-md-6">
                            <canvas id="statusChart" width="400" height="200"></canvas>
                        </div>
                    </div>
                    <hr>
                    <div class="row">
                        <div class="col-md-12">
                            <canvas id="applicationTrendChart" width="800" height="300"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-4">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Category Breakdown</h6>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <div class="d-flex justify-content-between align-items-center">
                            <span><i class="fas fa-school text-primary me-2"></i>Primary School</span>
                            <span class="badge bg-primary">{{ $statistics['category_stats']['primary'] ?? 0 }}</span>
                        </div>
                        <div class="progress mt-1" style="height: 6px;">
                            <div class="progress-bar bg-primary" style="width: {{ $statistics['total_scholarships'] > 0 ? (($statistics['category_stats']['primary'] ?? 0) / $statistics['total_scholarships']) * 100 : 0 }}%"></div>
                        </div>
                    </div>
                    <div class="mb-3">
                        <div class="d-flex justify-content-between align-items-center">
                            <span><i class="fas fa-graduation-cap text-success me-2"></i>Secondary School</span>
                            <span class="badge bg-success">{{ $statistics['category_stats']['secondary'] ?? 0 }}</span>
                        </div>
                        <div class="progress mt-1" style="height: 6px;">
                            <div class="progress-bar bg-success" style="width: {{ $statistics['total_scholarships'] > 0 ? (($statistics['category_stats']['secondary'] ?? 0) / $statistics['total_scholarships']) * 100 : 0 }}%"></div>
                        </div>
                    </div>
                    <div class="mb-3">
                        <div class="d-flex justify-content-between align-items-center">
                            <span><i class="fas fa-university text-info me-2"></i>University</span>
                            <span class="badge bg-info">{{ $statistics['category_stats']['university'] ?? 0 }}</span>
                        </div>
                        <div class="progress mt-1" style="height: 6px;">
                            <div class="progress-bar bg-info" style="width: {{ $statistics['total_scholarships'] > 0 ? (($statistics['category_stats']['university'] ?? 0) / $statistics['total_scholarships']) * 100 : 0 }}%"></div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Quick Actions</h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <button class="btn btn-outline-primary" onclick="bulkActions()">
                            <i class="fas fa-tasks me-2"></i>Bulk Actions
                        </button>
                        <button class="btn btn-outline-success" onclick="exportData()">
                            <i class="fas fa-download me-2"></i>Export Data
                        </button>
                        <button class="btn btn-outline-info" onclick="generateReport()">
                            <i class="fas fa-chart-bar me-2"></i>Generate Report
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Scholarships Table -->
    <div class="card">
        <div class="card-header">
            <h5 class="card-title mb-0">All Scholarships</h5>
        </div>
        <div class="card-body">
            <!-- Search and Filter -->
            <div class="row mb-3">
                <div class="col-md-4">
                    <div class="input-group">
                        <input type="text" class="form-control" placeholder="Search scholarships..." id="searchScholarships">
                        <button class="btn btn-outline-secondary" type="button">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </div>
                <div class="col-md-2">
                    <select class="form-select" id="filterCategory">
                        <option value="">All Categories</option>
                        <option value="primary">Primary School</option>
                        <option value="secondary">Secondary School</option>
                        <option value="university">University</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <select class="form-select" id="filterStatus">
                        <option value="">All Status</option>
                        <option value="draft">Draft</option>
                        <option value="active">Active</option>
                        <option value="inactive">Inactive</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <select class="form-select" id="filterAmount">
                        <option value="">All Amounts</option>
                        <option value="0-50000">₦0 - ₦50,000</option>
                        <option value="50000-100000">₦50,000 - ₦100,000</option>
                        <option value="100000-500000">₦100,000 - ₦500,000</option>
                        <option value="500000+">₦500,000+</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <div class="dropdown">
                        <button class="btn btn-outline-secondary dropdown-toggle w-100" type="button" data-bs-toggle="dropdown">
                            <i class="fas fa-cog me-1"></i>Actions
                        </button>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#" onclick="bulkActions()"><i class="fas fa-tasks me-2"></i>Bulk Actions</a></li>
                            <li><a class="dropdown-item" href="#" onclick="exportData()"><i class="fas fa-download me-2"></i>Export Data</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="#" onclick="refreshData()"><i class="fas fa-sync me-2"></i>Refresh</a></li>
                        </ul>
                    </div>
                </div>
            </div>

            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead class="table-dark">
                        <tr>
                            <th>
                                <input type="checkbox" id="selectAll" class="form-check-input">
                            </th>
                            <th>Scholarship</th>
                            <th>Category</th>
                            <th>Amount</th>
                            <th>Deadline</th>
                            <th>Applications</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse($scholarships as $scholarship)
                        <tr>
                            <td>
                                <input type="checkbox" class="form-check-input scholarship-checkbox" value="{{ $scholarship->id }}">
                            </td>
                            <td>
                                <div>
                                    <div class="fw-bold">{{ $scholarship->title }}</div>
                                    <small class="text-muted">{{ Str::limit($scholarship->description, 80) }}</small>
                                    @if($scholarship->eligibility_criteria)
                                        <br><small class="text-info">
                                            <i class="fas fa-info-circle"></i>
                                            {{ Str::limit($scholarship->eligibility_criteria, 60) }}
                                        </small>
                                    @endif
                                </div>
                            </td>
                            <td>
                                @php
                                    $categoryInfo = [
                                        'primary' => ['icon' => 'fas fa-school', 'color' => 'primary', 'label' => 'Primary'],
                                        'secondary' => ['icon' => 'fas fa-graduation-cap', 'color' => 'success', 'label' => 'Secondary'],
                                        'university' => ['icon' => 'fas fa-university', 'color' => 'info', 'label' => 'University']
                                    ];
                                    $category = $categoryInfo[$scholarship->category] ?? ['icon' => 'fas fa-question', 'color' => 'secondary', 'label' => 'Unknown'];
                                @endphp
                                <span class="badge bg-{{ $category['color'] }}">
                                    <i class="{{ $category['icon'] }} me-1"></i>{{ $category['label'] }}
                                </span>
                                <br><small class="text-muted">
                                    @if($scholarship->category === 'primary')
                                        Ages 5-13
                                    @elseif($scholarship->category === 'secondary')
                                        Ages 12-20
                                    @elseif($scholarship->category === 'university')
                                        Ages 16-35
                                    @endif
                                </small>
                            </td>
                            <td>
                                <div class="text-center">
                                    <strong class="text-success h5">₦{{ number_format($scholarship->amount, 0) }}</strong>
                                    @if($scholarship->max_applicants)
                                        <br><small class="text-muted">Max {{ $scholarship->max_applicants }} recipients</small>
                                    @endif
                                </div>
                            </td>
                            <td>
                                <div class="text-center">
                                    <strong>{{ $scholarship->application_deadline->format('M d, Y') }}</strong>
                                    <br>
                                    @if($scholarship->application_deadline->isFuture())
                                        @php
                                            $daysLeft = now()->diffInDays($scholarship->application_deadline);
                                        @endphp
                                        <span class="badge bg-{{ $daysLeft > 30 ? 'success' : ($daysLeft > 7 ? 'warning' : 'danger') }}">
                                            {{ $daysLeft }} days left
                                        </span>
                                    @else
                                        <span class="badge bg-secondary">Expired</span>
                                    @endif
                                </div>
                            </td>
                            <td>
                                @php
                                    $applications = $scholarship->applications ? $scholarship->applications->count() : 0;
                                    $percentage = $scholarship->max_applicants ? ($applications / $scholarship->max_applicants) * 100 : 0;
                                @endphp
                                <div class="text-center">
                                    <strong class="text-{{ $percentage > 80 ? 'danger' : ($percentage > 50 ? 'warning' : 'success') }}">
                                        {{ $applications }}
                                    </strong>
                                    @if($scholarship->max_applicants)
                                        / {{ $scholarship->max_applicants }}
                                        <div class="progress mt-1" style="height: 4px;">
                                            <div class="progress-bar bg-{{ $percentage > 80 ? 'danger' : ($percentage > 50 ? 'warning' : 'success') }}" 
                                                 style="width: {{ min(100, $percentage) }}%"></div>
                                        </div>
                                        <small class="text-muted">{{ round($percentage) }}% filled</small>
                                    @else
                                        <br><small class="text-muted">Unlimited</small>
                                    @endif
                                </div>
                            </td>
                            <td>
                                <span class="badge bg-{{ 
                                    $scholarship->status === 'open' ? 'success' : 
                                    ($scholarship->status === 'closed' ? 'danger' : 'warning') 
                                }}">
                                    {{ ucfirst($scholarship->status) }}
                                </span>
                                @if($scholarship->status === 'open' && $scholarship->application_deadline->isPast())
                                    <br><small class="text-danger">Auto-close pending</small>
                                @endif
                            </td>
                            <td>
                                {{ $scholarship->created_at->format('M d, Y') }}
                                <br><small class="text-muted">{{ $scholarship->created_at->diffForHumans() }}</small>
                            </td>
                            <td>
                                <div class="btn-group" role="group">
                                    <a href="{{ route('scholarships.show', $scholarship->slug) }}" target="_blank" class="btn btn-sm btn-outline-info" title="View Public">
                                        <i class="fas fa-external-link-alt"></i>
                                    </a>
                                    <a href="{{ route('admin.scholarships.show', $scholarship->id) }}" class="btn btn-sm btn-outline-primary" title="View Details">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="{{ route('admin.scholarships.applications', $scholarship->id) }}" class="btn btn-sm btn-outline-success" title="Applications">
                                        <i class="fas fa-graduation-cap"></i>
                                    </a>
                                    <a href="{{ route('admin.scholarships.edit', $scholarship->id) }}" class="btn btn-sm btn-outline-secondary" title="Edit">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    @if($scholarship->status === 'draft')
                                        <button class="btn btn-sm btn-outline-success" onclick="openScholarship({{ $scholarship->id }})" title="Open Applications">
                                            <i class="fas fa-play"></i>
                                        </button>
                                    @elseif($scholarship->status === 'open')
                                        <button class="btn btn-sm btn-outline-warning" onclick="closeScholarship({{ $scholarship->id }})" title="Close Applications">
                                            <i class="fas fa-stop"></i>
                                        </button>
                                    @endif
                                    <button class="btn btn-sm btn-outline-danger" onclick="deleteScholarship({{ $scholarship->id }})" title="Delete">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        @empty
                        <tr>
                            <td colspan="8" class="text-center py-4">
                                <i class="fas fa-graduation-cap fa-3x text-muted mb-3"></i>
                                <p class="text-muted">No scholarships found</p>
                                <a href="{{ route('admin.scholarships.create') }}" class="btn btn-primary">Create Your First Scholarship</a>
                            </td>
                        </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <div class="d-flex justify-content-between align-items-center mt-3">
                <div>
                    Showing {{ $scholarships->firstItem() }} to {{ $scholarships->lastItem() }} of {{ $scholarships->total() }} results
                </div>
                {{ $scholarships->links() }}
            </div>
        </div>
    </div>
</div>

<!-- Status Change Confirmation Modal -->
<div class="modal fade" id="statusChangeModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="statusChangeTitle">Change Scholarship Status</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="statusChangeBody">
                <!-- Content will be set by JavaScript -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" id="confirmStatusChange">Confirm</button>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteScholarshipModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Delete Scholarship</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                Are you sure you want to delete this scholarship? This action cannot be undone and will affect all applications.
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-danger" id="confirmDeleteScholarship">Delete Scholarship</button>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// Initialize charts when page loads
document.addEventListener('DOMContentLoaded', function() {
    initializeCharts();
    initializeEventListeners();
});

function initializeCharts() {
    // Category Distribution Chart
    const categoryCtx = document.getElementById('categoryChart').getContext('2d');
    new Chart(categoryCtx, {
        type: 'doughnut',
        data: {
            labels: ['Primary School', 'Secondary School', 'University'],
            datasets: [{
                data: [
                    {{ $statistics['category_stats']['primary'] ?? 0 }},
                    {{ $statistics['category_stats']['secondary'] ?? 0 }},
                    {{ $statistics['category_stats']['university'] ?? 0 }}
                ],
                backgroundColor: ['#007bff', '#28a745', '#17a2b8'],
                borderWidth: 2
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                title: {
                    display: true,
                    text: 'Scholarships by Category'
                },
                legend: {
                    position: 'bottom'
                }
            }
        }
    });

    // Status Distribution Chart
    const statusCtx = document.getElementById('statusChart').getContext('2d');
    new Chart(statusCtx, {
        type: 'pie',
        data: {
            labels: ['Active', 'Draft', 'Inactive'],
            datasets: [{
                data: [
                    {{ $statistics['status_stats']['active'] ?? 0 }},
                    {{ $statistics['status_stats']['draft'] ?? 0 }},
                    {{ $statistics['status_stats']['inactive'] ?? 0 }}
                ],
                backgroundColor: ['#28a745', '#ffc107', '#dc3545'],
                borderWidth: 2
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                title: {
                    display: true,
                    text: 'Scholarships by Status'
                },
                legend: {
                    position: 'bottom'
                }
            }
        }
    });

    // Application Trend Chart
    const trendCtx = document.getElementById('applicationTrendChart').getContext('2d');
    new Chart(trendCtx, {
        type: 'line',
        data: {
            labels: {!! json_encode($statistics['trend_labels'] ?? []) !!},
            datasets: [{
                label: 'Applications',
                data: {!! json_encode($statistics['trend_data'] ?? []) !!},
                borderColor: '#007bff',
                backgroundColor: 'rgba(0, 123, 255, 0.1)',
                tension: 0.4,
                fill: true
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                title: {
                    display: true,
                    text: 'Application Trends (Last 30 Days)'
                }
            },
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });
}

function initializeEventListeners() {
    // Select all checkbox
    document.getElementById('selectAll').addEventListener('change', function() {
        const checkboxes = document.querySelectorAll('.scholarship-checkbox');
        checkboxes.forEach(checkbox => {
            checkbox.checked = this.checked;
        });
        updateBulkActionButtons();
    });

    // Individual checkboxes
    document.querySelectorAll('.scholarship-checkbox').forEach(checkbox => {
        checkbox.addEventListener('change', updateBulkActionButtons);
    });

    // Search functionality
    document.getElementById('searchScholarships').addEventListener('input', debounce(function() {
        filterScholarships();
    }, 300));

    // Filter functionality
    document.getElementById('filterCategory').addEventListener('change', filterScholarships);
    document.getElementById('filterStatus').addEventListener('change', filterScholarships);
    document.getElementById('filterAmount').addEventListener('change', filterScholarships);
}

function updateBulkActionButtons() {
    const selectedCount = document.querySelectorAll('.scholarship-checkbox:checked').length;
    const bulkActionBtn = document.querySelector('[onclick="bulkActions()"]');
    if (bulkActionBtn) {
        bulkActionBtn.textContent = selectedCount > 0 ? `Bulk Actions (${selectedCount})` : 'Bulk Actions';
        bulkActionBtn.disabled = selectedCount === 0;
    }
}

function filterScholarships() {
    const search = document.getElementById('searchScholarships').value.toLowerCase();
    const category = document.getElementById('filterCategory').value;
    const status = document.getElementById('filterStatus').value;
    const amount = document.getElementById('filterAmount').value;

    // Build query parameters
    const params = new URLSearchParams();
    if (search) params.append('search', search);
    if (category) params.append('category', category);
    if (status) params.append('status', status);
    if (amount) params.append('amount', amount);

    // Update URL and reload
    const newUrl = window.location.pathname + (params.toString() ? '?' + params.toString() : '');
    window.location.href = newUrl;
}

function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

function openScholarship(scholarshipId) {
    const modal = new bootstrap.Modal(document.getElementById('statusChangeModal'));
    document.getElementById('statusChangeTitle').textContent = 'Open Scholarship Applications';
    document.getElementById('statusChangeBody').innerHTML = 'Are you sure you want to open applications for this scholarship? Students will be able to apply once opened.';

    modal.show();

    document.getElementById('confirmStatusChange').onclick = function() {
        updateScholarshipStatus(scholarshipId, 'active');
        modal.hide();
    };
}

function closeScholarship(scholarshipId) {
    const modal = new bootstrap.Modal(document.getElementById('statusChangeModal'));
    document.getElementById('statusChangeTitle').textContent = 'Close Scholarship Applications';
    document.getElementById('statusChangeBody').innerHTML = 'Are you sure you want to close applications for this scholarship? No new applications will be accepted.';

    modal.show();

    document.getElementById('confirmStatusChange').onclick = function() {
        updateScholarshipStatus(scholarshipId, 'inactive');
        modal.hide();
    };
}

function updateScholarshipStatus(scholarshipId, status) {
    fetch(`/api/v1/admin/scholarships/${scholarshipId}/status`, {
        method: 'PATCH',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        },
        body: JSON.stringify({ status: status })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert('Error updating scholarship status: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Error updating scholarship status');
    });
}

function deleteScholarship(scholarshipId) {
    const modal = new bootstrap.Modal(document.getElementById('deleteScholarshipModal'));
    modal.show();

    document.getElementById('confirmDeleteScholarship').onclick = function() {
        fetch(`/api/v1/admin/scholarships/${scholarshipId}`, {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Error deleting scholarship: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error deleting scholarship');
        });
        modal.hide();
    };
}

function bulkActions() {
    const selectedIds = Array.from(document.querySelectorAll('.scholarship-checkbox:checked')).map(cb => cb.value);
    if (selectedIds.length === 0) {
        alert('Please select scholarships to perform bulk actions');
        return;
    }

    const actions = [
        { value: 'activate', text: 'Activate Selected' },
        { value: 'deactivate', text: 'Deactivate Selected' },
        { value: 'delete', text: 'Delete Selected' }
    ];

    let actionHtml = '<select class="form-select" id="bulkActionSelect">';
    actionHtml += '<option value="">Choose Action...</option>';
    actions.forEach(action => {
        actionHtml += `<option value="${action.value}">${action.text}</option>`;
    });
    actionHtml += '</select>';

    const modal = new bootstrap.Modal(document.getElementById('statusChangeModal'));
    document.getElementById('statusChangeTitle').textContent = `Bulk Actions (${selectedIds.length} selected)`;
    document.getElementById('statusChangeBody').innerHTML = actionHtml;

    modal.show();

    document.getElementById('confirmStatusChange').onclick = function() {
        const action = document.getElementById('bulkActionSelect').value;
        if (!action) {
            alert('Please select an action');
            return;
        }

        performBulkAction(selectedIds, action);
        modal.hide();
    };
}

function performBulkAction(ids, action) {
    fetch('/api/v1/admin/scholarships/bulk-action', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        },
        body: JSON.stringify({ ids: ids, action: action })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert('Error performing bulk action: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Error performing bulk action');
    });
}

function exportData() {
    const params = new URLSearchParams(window.location.search);
    params.append('export', 'true');
    window.open('/api/v1/admin/scholarships/export?' + params.toString(), '_blank');
}

function generateReport() {
    window.open('/api/v1/admin/scholarships/report', '_blank');
}

function refreshData() {
    location.reload();
}
</script>
@endpush
@endsection 