@extends('layouts.admin')

@section('title', 'Scholarships Management')

@section('content')
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0">Scholarships Management</h1>
        <div>
            <a href="{{ route('admin.scholarships.applications') }}" class="btn btn-warning me-2">
                <i class="fas fa-graduation-cap me-2"></i>Applications
            </a>
            <a href="{{ route('admin.scholarships.create') }}" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i>Create Scholarship
            </a>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6">
            <div class="card bg-primary text-white mb-4">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <div class="small text-white-50">Total Scholarships</div>
                            <div class="h4">{{ $scholarships->total() }}</div>
                        </div>
                        <div>
                            <i class="fas fa-graduation-cap fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="card bg-success text-white mb-4">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <div class="small text-white-50">Open Applications</div>
                            <div class="h4">{{ $scholarships->where('status', 'open')->count() }}</div>
                        </div>
                        <div>
                            <i class="fas fa-door-open fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="card bg-info text-white mb-4">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <div class="small text-white-50">Total Value</div>
                            <div class="h4">₦{{ number_format($scholarships->sum('amount'), 0) }}</div>
                        </div>
                        <div>
                            <i class="fas fa-money-bill-wave fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="card bg-warning text-white mb-4">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <div class="small text-white-50">Applications</div>
                            <div class="h4">156</div>
                        </div>
                        <div>
                            <i class="fas fa-users fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Scholarships Table -->
    <div class="card">
        <div class="card-header">
            <h5 class="card-title mb-0">All Scholarships</h5>
        </div>
        <div class="card-body">
            <!-- Search and Filter -->
            <div class="row mb-3">
                <div class="col-md-6">
                    <div class="input-group">
                        <input type="text" class="form-control" placeholder="Search scholarships..." id="searchScholarships">
                        <button class="btn btn-outline-secondary" type="button">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </div>
                <div class="col-md-3">
                    <select class="form-select" id="filterStatus">
                        <option value="">All Status</option>
                        <option value="draft">Draft</option>
                        <option value="open">Open</option>
                        <option value="closed">Closed</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <select class="form-select" id="filterAmount">
                        <option value="">All Amounts</option>
                        <option value="0-50000">₦0 - ₦50,000</option>
                        <option value="50000-100000">₦50,000 - ₦100,000</option>
                        <option value="100000-500000">₦100,000 - ₦500,000</option>
                        <option value="500000+">₦500,000+</option>
                    </select>
                </div>
            </div>

            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead class="table-dark">
                        <tr>
                            <th>Scholarship</th>
                            <th>Amount</th>
                            <th>Deadline</th>
                            <th>Applications</th>
                            <th>Status</th>
                            <th>Created</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse($scholarships as $scholarship)
                        <tr>
                            <td>
                                <div>
                                    <div class="fw-bold">{{ $scholarship->title }}</div>
                                    <small class="text-muted">{{ Str::limit($scholarship->description, 80) }}</small>
                                    @if($scholarship->eligibility_criteria)
                                        <br><small class="text-info">
                                            <i class="fas fa-info-circle"></i> 
                                            {{ Str::limit($scholarship->eligibility_criteria, 60) }}
                                        </small>
                                    @endif
                                </div>
                            </td>
                            <td>
                                <div class="text-center">
                                    <strong class="text-success h5">₦{{ number_format($scholarship->amount, 0) }}</strong>
                                    @if($scholarship->max_applicants)
                                        <br><small class="text-muted">Max {{ $scholarship->max_applicants }} recipients</small>
                                    @endif
                                </div>
                            </td>
                            <td>
                                <div class="text-center">
                                    <strong>{{ $scholarship->application_deadline->format('M d, Y') }}</strong>
                                    <br>
                                    @if($scholarship->application_deadline->isFuture())
                                        @php
                                            $daysLeft = now()->diffInDays($scholarship->application_deadline);
                                        @endphp
                                        <span class="badge bg-{{ $daysLeft > 30 ? 'success' : ($daysLeft > 7 ? 'warning' : 'danger') }}">
                                            {{ $daysLeft }} days left
                                        </span>
                                    @else
                                        <span class="badge bg-secondary">Expired</span>
                                    @endif
                                </div>
                            </td>
                            <td>
                                @php
                                    $applications = $scholarship->applications ? $scholarship->applications->count() : 0;
                                    $percentage = $scholarship->max_applicants ? ($applications / $scholarship->max_applicants) * 100 : 0;
                                @endphp
                                <div class="text-center">
                                    <strong class="text-{{ $percentage > 80 ? 'danger' : ($percentage > 50 ? 'warning' : 'success') }}">
                                        {{ $applications }}
                                    </strong>
                                    @if($scholarship->max_applicants)
                                        / {{ $scholarship->max_applicants }}
                                        <div class="progress mt-1" style="height: 4px;">
                                            <div class="progress-bar bg-{{ $percentage > 80 ? 'danger' : ($percentage > 50 ? 'warning' : 'success') }}" 
                                                 style="width: {{ min(100, $percentage) }}%"></div>
                                        </div>
                                        <small class="text-muted">{{ round($percentage) }}% filled</small>
                                    @else
                                        <br><small class="text-muted">Unlimited</small>
                                    @endif
                                </div>
                            </td>
                            <td>
                                <span class="badge bg-{{ 
                                    $scholarship->status === 'open' ? 'success' : 
                                    ($scholarship->status === 'closed' ? 'danger' : 'warning') 
                                }}">
                                    {{ ucfirst($scholarship->status) }}
                                </span>
                                @if($scholarship->status === 'open' && $scholarship->application_deadline->isPast())
                                    <br><small class="text-danger">Auto-close pending</small>
                                @endif
                            </td>
                            <td>
                                {{ $scholarship->created_at->format('M d, Y') }}
                                <br><small class="text-muted">{{ $scholarship->created_at->diffForHumans() }}</small>
                            </td>
                            <td>
                                <div class="btn-group" role="group">
                                    <a href="{{ route('scholarships.show', $scholarship->slug) }}" target="_blank" class="btn btn-sm btn-outline-info" title="View Public">
                                        <i class="fas fa-external-link-alt"></i>
                                    </a>
                                    <a href="{{ route('admin.scholarships.show', $scholarship->id) }}" class="btn btn-sm btn-outline-primary" title="View Details">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="{{ route('admin.scholarships.applications', $scholarship->id) }}" class="btn btn-sm btn-outline-success" title="Applications">
                                        <i class="fas fa-graduation-cap"></i>
                                    </a>
                                    <a href="{{ route('admin.scholarships.edit', $scholarship->id) }}" class="btn btn-sm btn-outline-secondary" title="Edit">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    @if($scholarship->status === 'draft')
                                        <button class="btn btn-sm btn-outline-success" onclick="openScholarship({{ $scholarship->id }})" title="Open Applications">
                                            <i class="fas fa-play"></i>
                                        </button>
                                    @elseif($scholarship->status === 'open')
                                        <button class="btn btn-sm btn-outline-warning" onclick="closeScholarship({{ $scholarship->id }})" title="Close Applications">
                                            <i class="fas fa-stop"></i>
                                        </button>
                                    @endif
                                    <button class="btn btn-sm btn-outline-danger" onclick="deleteScholarship({{ $scholarship->id }})" title="Delete">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        @empty
                        <tr>
                            <td colspan="7" class="text-center py-4">
                                <i class="fas fa-graduation-cap fa-3x text-muted mb-3"></i>
                                <p class="text-muted">No scholarships found</p>
                                <a href="{{ route('admin.scholarships.create') }}" class="btn btn-primary">Create Your First Scholarship</a>
                            </td>
                        </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <div class="d-flex justify-content-between align-items-center mt-3">
                <div>
                    Showing {{ $scholarships->firstItem() }} to {{ $scholarships->lastItem() }} of {{ $scholarships->total() }} results
                </div>
                {{ $scholarships->links() }}
            </div>
        </div>
    </div>
</div>

<!-- Status Change Confirmation Modal -->
<div class="modal fade" id="statusChangeModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="statusChangeTitle">Change Scholarship Status</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="statusChangeBody">
                <!-- Content will be set by JavaScript -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" id="confirmStatusChange">Confirm</button>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteScholarshipModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Delete Scholarship</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                Are you sure you want to delete this scholarship? This action cannot be undone and will affect all applications.
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-danger" id="confirmDeleteScholarship">Delete Scholarship</button>
            </div>
        </div>
    </div>
</div>

<script>
function openScholarship(scholarshipId) {
    const modal = new bootstrap.Modal(document.getElementById('statusChangeModal'));
    document.getElementById('statusChangeTitle').textContent = 'Open Scholarship Applications';
    document.getElementById('statusChangeBody').innerHTML = 'Are you sure you want to open applications for this scholarship? Students will be able to apply once opened.';
    
    modal.show();
    
    document.getElementById('confirmStatusChange').onclick = function() {
        console.log('Open scholarship:', scholarshipId);
        modal.hide();
        // Add AJAX call to open scholarship
    };
}

function closeScholarship(scholarshipId) {
    const modal = new bootstrap.Modal(document.getElementById('statusChangeModal'));
    document.getElementById('statusChangeTitle').textContent = 'Close Scholarship Applications';
    document.getElementById('statusChangeBody').innerHTML = 'Are you sure you want to close applications for this scholarship? No new applications will be accepted.';
    
    modal.show();
    
    document.getElementById('confirmStatusChange').onclick = function() {
        console.log('Close scholarship:', scholarshipId);
        modal.hide();
        // Add AJAX call to close scholarship
    };
}

function deleteScholarship(scholarshipId) {
    const modal = new bootstrap.Modal(document.getElementById('deleteScholarshipModal'));
    modal.show();
    
    document.getElementById('confirmDeleteScholarship').onclick = function() {
        console.log('Delete scholarship:', scholarshipId);
        modal.hide();
        // Add AJAX call to delete scholarship
    };
}

// Search functionality
document.getElementById('searchScholarships').addEventListener('input', function() {
    console.log('Search:', this.value);
});

// Filter functionality
document.getElementById('filterStatus').addEventListener('change', function() {
    console.log('Filter status:', this.value);
});

document.getElementById('filterAmount').addEventListener('change', function() {
    console.log('Filter amount:', this.value);
});
</script>
@endsection 