(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7915],{53580:(e,t,r)=>{"use strict";r.d(t,{dj:()=>m});var s=r(12115);let a=0,i=new Map,l=e=>{if(i.has(e))return;let t=setTimeout(()=>{i.delete(e),c({type:"REMOVE_TOAST",toastId:e})},1e6);i.set(e,t)},n=(e,t)=>{switch(t.type){case"ADD_TOAST":return{...e,toasts:[t.toast,...e.toasts].slice(0,1)};case"UPDATE_TOAST":return{...e,toasts:e.toasts.map(e=>e.id===t.toast.id?{...e,...t.toast}:e)};case"DISMISS_TOAST":{let{toastId:r}=t;return r?l(r):e.toasts.forEach(e=>{l(e.id)}),{...e,toasts:e.toasts.map(e=>e.id===r||void 0===r?{...e,open:!1}:e)}}case"REMOVE_TOAST":if(void 0===t.toastId)return{...e,toasts:[]};return{...e,toasts:e.toasts.filter(e=>e.id!==t.toastId)}}},d=[],o={toasts:[]};function c(e){o=n(o,e),d.forEach(e=>{e(o)})}function u(e){let{...t}=e,r=(a=(a+1)%Number.MAX_SAFE_INTEGER).toString(),s=()=>c({type:"DISMISS_TOAST",toastId:r});return c({type:"ADD_TOAST",toast:{...t,id:r,open:!0,onOpenChange:e=>{e||s()}}}),{id:r,dismiss:s,update:e=>c({type:"UPDATE_TOAST",toast:{...e,id:r}})}}function m(){let[e,t]=s.useState(o);return s.useEffect(()=>(d.push(t),()=>{let e=d.indexOf(t);e>-1&&d.splice(e,1)}),[e]),{...e,toast:u,dismiss:e=>c({type:"DISMISS_TOAST",toastId:e})}}},53999:(e,t,r)=>{"use strict";r.d(t,{cn:()=>i});var s=r(52596),a=r(39688);function i(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,a.QP)((0,s.$)(t))}},61854:(e,t,r)=>{Promise.resolve().then(r.bind(r,94156))},82714:(e,t,r)=>{"use strict";r.d(t,{J:()=>o});var s=r(95155),a=r(12115),i=r(40968),l=r(74466),n=r(53999);let d=(0,l.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),o=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)(i.b,{ref:t,className:(0,n.cn)(d(),r),...a})});o.displayName=i.b.displayName},88145:(e,t,r)=>{"use strict";r.d(t,{E:()=>n});var s=r(95155);r(12115);var a=r(74466),i=r(53999);let l=(0,a.F)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function n(e){let{className:t,variant:r,...a}=e;return(0,s.jsx)("div",{className:(0,i.cn)(l({variant:r}),t),...a})}},88482:(e,t,r)=>{"use strict";r.d(t,{BT:()=>o,Wu:()=>c,ZB:()=>d,Zp:()=>l,aR:()=>n,wL:()=>u});var s=r(95155),a=r(12115),i=r(53999);let l=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("div",{ref:t,className:(0,i.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",r),...a})});l.displayName="Card";let n=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("div",{ref:t,className:(0,i.cn)("flex flex-col space-y-1.5 p-6",r),...a})});n.displayName="CardHeader";let d=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("div",{ref:t,className:(0,i.cn)("text-2xl font-semibold leading-none tracking-tight",r),...a})});d.displayName="CardTitle";let o=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("div",{ref:t,className:(0,i.cn)("text-sm text-muted-foreground",r),...a})});o.displayName="CardDescription";let c=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("div",{ref:t,className:(0,i.cn)("p-6 pt-0",r),...a})});c.displayName="CardContent";let u=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("div",{ref:t,className:(0,i.cn)("flex items-center p-6 pt-0",r),...a})});u.displayName="CardFooter"},89852:(e,t,r)=>{"use strict";r.d(t,{p:()=>l});var s=r(95155),a=r(12115),i=r(53999);let l=a.forwardRef((e,t)=>{let{className:r,type:a,...l}=e;return(0,s.jsx)("input",{type:a,className:(0,i.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",r),ref:t,suppressHydrationWarning:!0,...l})});l.displayName="Input"},94156:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>A});var s=r(95155),a=r(12115),i=r(35695),l=r(6874),n=r.n(l),d=r(88482),o=r(97168),c=r(89852),u=r(82714),m=r(99474),p=r(95784),f=r(88145),x=r(5040),h=r(53896),g=r(87949),b=r(85339),y=r(35169),v=r(51154),j=r(12486),N=r(40646),w=r(53580),_=r(31886);let S={primary:{icon:x.A,title:"Primary School",color:"bg-blue-500",bgColor:"bg-blue-50",textColor:"text-blue-700"},secondary:{icon:h.A,title:"Secondary School",color:"bg-green-500",bgColor:"bg-green-50",textColor:"text-green-700"},university:{icon:g.A,title:"University",color:"bg-purple-500",bgColor:"bg-purple-50",textColor:"text-purple-700"}};function A(){var e,t;let r=(0,i.useParams)(),l=(0,i.useRouter)(),{toast:x}=(0,w.dj)(),[h,g]=(0,a.useState)(null),[A,C]=(0,a.useState)(!0),[k,E]=(0,a.useState)(!1),[T,R]=(0,a.useState)(null),[D,O]=(0,a.useState)({}),[q,P]=(0,a.useState)({});(0,a.useEffect)(()=>{r.id&&I(r.id)},[r.id]);let I=async e=>{try{C(!0),R(null);let r=await _.uE.getPublicScholarship(e);if(r.success&&r.data){var t;g(r.data);let e={};null===(t=r.data.custom_fields)||void 0===t||t.forEach(t=>{e[t.field_name]="checkbox"===t.field_type?[]:""}),O(e)}else throw Error(r.message||"Scholarship not found")}catch(e){console.error("Error fetching scholarship:",e),R("Failed to load scholarship details. Please try again later.")}finally{C(!1)}},F=(e,t)=>{O(r=>({...r,[e]:t}))},L=(e,t)=>{t?P(r=>({...r,[e]:t})):P(t=>{let r={...t};return delete r[e],r})},B=()=>{if(!(null==h?void 0:h.custom_fields))return!0;for(let e of h.custom_fields)if(e.is_required){let t=D[e.field_name];if(!t||Array.isArray(t)&&0===t.length)return x({title:"Validation Error",description:"".concat(e.field_name," is required"),variant:"destructive"}),!1}return!0},Z=async e=>{if(e.preventDefault(),B())try{E(!0);let e=new FormData;e.append("scholarship_id",h.id.toString()),Object.entries(D).forEach(t=>{let[r,s]=t;Array.isArray(s)?e.append(r,JSON.stringify(s)):e.append(r,s.toString())}),Object.entries(q).forEach(t=>{let[r,s]=t;e.append(r,s)}),await new Promise(e=>setTimeout(e,2e3)),x({title:"Application Submitted!",description:"Your scholarship application has been submitted successfully.",variant:"default"}),l.push("/scholarships?submitted=true")}catch(e){console.error("Error submitting application:",e),x({title:"Submission Failed",description:"Failed to submit your application. Please try again.",variant:"destructive"})}finally{E(!1)}},M=e=>{var t,r;let a=D[e.field_name]||"";switch(e.field_type){case"text":case"email":case"phone":case"number":return(0,s.jsx)(c.p,{type:"number"===e.field_type?"number":"email"===e.field_type?"email":"text",value:a,onChange:t=>F(e.field_name,t.target.value),placeholder:"Enter ".concat(e.field_name.toLowerCase()),required:e.is_required});case"textarea":return(0,s.jsx)(m.T,{value:a,onChange:t=>F(e.field_name,t.target.value),placeholder:"Enter ".concat(e.field_name.toLowerCase()),required:e.is_required,rows:4});case"select":return(0,s.jsxs)(p.l6,{value:a,onValueChange:t=>F(e.field_name,t),children:[(0,s.jsx)(p.bq,{children:(0,s.jsx)(p.yv,{placeholder:"Select ".concat(e.field_name.toLowerCase())})}),(0,s.jsx)(p.gC,{children:null===(t=e.field_options)||void 0===t?void 0:t.map(e=>(0,s.jsx)(p.eb,{value:e,children:e},e))})]});case"file":return(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(c.p,{type:"file",onChange:t=>{var r;return L(e.field_name,(null===(r=t.target.files)||void 0===r?void 0:r[0])||null)},accept:".pdf,.doc,.docx,.jpg,.jpeg,.png",required:e.is_required}),(0,s.jsx)("p",{className:"text-xs text-gray-500",children:"Accepted formats: PDF, DOC, DOCX, JPG, PNG (Max 5MB)"})]});case"checkbox":return(0,s.jsx)("div",{className:"space-y-2",children:null===(r=e.field_options)||void 0===r?void 0:r.map(t=>(0,s.jsxs)("label",{className:"flex items-center space-x-2",children:[(0,s.jsx)("input",{type:"checkbox",checked:Array.isArray(a)&&a.includes(t),onChange:r=>{let s=Array.isArray(a)?a:[];r.target.checked?F(e.field_name,[...s,t]):F(e.field_name,s.filter(e=>e!==t))},className:"rounded"}),(0,s.jsx)("span",{className:"text-sm",children:t})]},t))});default:return(0,s.jsx)(c.p,{value:a,onChange:t=>F(e.field_name,t.target.value),placeholder:"Enter ".concat(e.field_name.toLowerCase()),required:e.is_required})}};if(A)return(0,s.jsx)("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900 py-8",children:(0,s.jsx)("div",{className:"container mx-auto px-4",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-green-600 mx-auto"}),(0,s.jsx)("p",{className:"mt-4 text-gray-600",children:"Loading application form..."})]})})});if(T||!h)return(0,s.jsx)("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900 py-8",children:(0,s.jsx)("div",{className:"container mx-auto px-4",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)(b.A,{className:"h-12 w-12 text-red-500 mx-auto mb-4"}),(0,s.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-2",children:"Error Loading Application"}),(0,s.jsx)("p",{className:"text-gray-600 mb-6",children:T||"Scholarship not found"}),(0,s.jsxs)("div",{className:"flex gap-4 justify-center",children:[(0,s.jsxs)(o.$,{onClick:()=>l.back(),variant:"outline",children:[(0,s.jsx)(y.A,{className:"mr-2 h-4 w-4"}),"Go Back"]}),(0,s.jsx)(o.$,{onClick:()=>I(r.id),children:"Try Again"})]})]})})});let U=S[h.category],W=U.icon;return(0,s.jsx)("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900 py-8",children:(0,s.jsxs)("div",{className:"container mx-auto px-4 sm:px-6 lg:px-8 max-w-4xl",children:[(0,s.jsx)("div",{className:"flex items-center gap-4 mb-8",children:(0,s.jsxs)(o.$,{variant:"outline",onClick:()=>l.back(),className:"flex items-center gap-2",children:[(0,s.jsx)(y.A,{className:"h-4 w-4"}),"Back to Scholarship"]})}),(0,s.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[(0,s.jsx)("div",{className:"lg:col-span-2",children:(0,s.jsxs)(d.Zp,{children:[(0,s.jsxs)(d.aR,{children:[(0,s.jsxs)("div",{className:"flex items-center gap-3 mb-4",children:[(0,s.jsx)("div",{className:"p-2 rounded-lg ".concat(U.bgColor),children:(0,s.jsx)(W,{className:"h-6 w-6 ".concat(U.textColor)})}),(0,s.jsxs)("div",{children:[(0,s.jsx)(d.ZB,{className:"text-xl",children:"Apply for Scholarship"}),(0,s.jsx)(d.BT,{children:h.title})]})]}),h.category_instructions&&(0,s.jsxs)("div",{className:"bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg",children:[(0,s.jsx)("h4",{className:"font-semibold text-blue-900 dark:text-blue-100 mb-2",children:"Important Instructions"}),(0,s.jsxs)("p",{className:"text-blue-800 dark:text-blue-200 text-sm mb-2",children:[(0,s.jsx)("strong",{children:"Who fills this form:"})," ",h.category_instructions.filled_by]}),(0,s.jsx)("p",{className:"text-blue-700 dark:text-blue-300 text-sm",children:h.category_instructions.instruction})]})]}),(0,s.jsx)(d.Wu,{children:(0,s.jsxs)("form",{onSubmit:Z,className:"space-y-6",children:[null===(e=h.custom_fields)||void 0===e?void 0:e.map(e=>(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsxs)(u.J,{htmlFor:e.field_name,className:"flex items-center gap-2",children:[e.field_name.replace(/_/g," ").replace(/\b\w/g,e=>e.toUpperCase()),e.is_required&&(0,s.jsx)(f.E,{variant:"outline",className:"text-xs",children:"Required"})]}),M(e)]},e.id)),(0,s.jsx)("div",{className:"pt-6 border-t",children:(0,s.jsx)(o.$,{type:"submit",disabled:k,className:"w-full bg-green-600 hover:bg-green-700 text-white",size:"lg",children:k?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(v.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Submitting Application..."]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(j.A,{className:"mr-2 h-4 w-4"}),"Submit Application"]})})})]})})]})}),(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)(d.Zp,{children:[(0,s.jsx)(d.aR,{children:(0,s.jsx)(d.ZB,{className:"text-lg",children:"Scholarship Details"})}),(0,s.jsxs)(d.Wu,{className:"space-y-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm text-gray-600",children:"Award Amount"}),(0,s.jsxs)("p",{className:"font-semibold text-green-600",children:["₦",h.amount.toLocaleString()]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm text-gray-600",children:"Category"}),(0,s.jsx)(f.E,{variant:"outline",children:U.title})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm text-gray-600",children:"Application Deadline"}),(0,s.jsx)("p",{className:"font-semibold",children:new Date(h.application_deadline).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"})})]})]})]}),(null===(t=h.category_instructions)||void 0===t?void 0:t.required_info)&&(0,s.jsxs)(d.Zp,{children:[(0,s.jsx)(d.aR,{children:(0,s.jsx)(d.ZB,{className:"text-lg",children:"Required Information"})}),(0,s.jsx)(d.Wu,{children:(0,s.jsx)("ul",{className:"space-y-2",children:h.category_instructions.required_info.map((e,t)=>(0,s.jsxs)("li",{className:"flex items-center gap-2 text-sm",children:[(0,s.jsx)(N.A,{className:"h-4 w-4 text-green-600"}),e]},t))})})]}),(0,s.jsxs)(d.Zp,{children:[(0,s.jsx)(d.aR,{children:(0,s.jsx)(d.ZB,{className:"text-lg",children:"Need Help?"})}),(0,s.jsxs)(d.Wu,{children:[(0,s.jsx)("p",{className:"text-sm text-gray-600 mb-4",children:"Having trouble with your application?"}),(0,s.jsx)(n(),{href:"/contact",children:(0,s.jsx)(o.$,{variant:"outline",className:"w-full",children:"Contact Support"})})]})]})]})]})]})})}},95784:(e,t,r)=>{"use strict";r.d(t,{bq:()=>m,eb:()=>h,gC:()=>x,l6:()=>c,yv:()=>u});var s=r(95155),a=r(12115),i=r(38715),l=r(66474),n=r(47863),d=r(5196),o=r(53999);let c=i.bL;i.YJ;let u=i.WT,m=a.forwardRef((e,t)=>{let{className:r,children:a,...n}=e;return(0,s.jsxs)(i.l9,{ref:t,className:(0,o.cn)("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",r),...n,children:[a,(0,s.jsx)(i.In,{asChild:!0,children:(0,s.jsx)(l.A,{className:"h-4 w-4 opacity-50"})})]})});m.displayName=i.l9.displayName;let p=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)(i.PP,{ref:t,className:(0,o.cn)("flex cursor-default items-center justify-center py-1",r),...a,children:(0,s.jsx)(n.A,{className:"h-4 w-4"})})});p.displayName=i.PP.displayName;let f=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)(i.wn,{ref:t,className:(0,o.cn)("flex cursor-default items-center justify-center py-1",r),...a,children:(0,s.jsx)(l.A,{className:"h-4 w-4"})})});f.displayName=i.wn.displayName;let x=a.forwardRef((e,t)=>{let{className:r,children:a,position:l="popper",...n}=e;return(0,s.jsx)(i.ZL,{children:(0,s.jsxs)(i.UC,{ref:t,className:(0,o.cn)("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===l&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",r),position:l,...n,children:[(0,s.jsx)(p,{}),(0,s.jsx)(i.LM,{className:(0,o.cn)("p-1","popper"===l&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:a}),(0,s.jsx)(f,{})]})})});x.displayName=i.UC.displayName,a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)(i.JU,{ref:t,className:(0,o.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",r),...a})}).displayName=i.JU.displayName;let h=a.forwardRef((e,t)=>{let{className:r,children:a,...l}=e;return(0,s.jsxs)(i.q7,{ref:t,className:(0,o.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",r),...l,children:[(0,s.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,s.jsx)(i.VF,{children:(0,s.jsx)(d.A,{className:"h-4 w-4"})})}),(0,s.jsx)(i.p4,{children:a})]})});h.displayName=i.q7.displayName,a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)(i.wv,{ref:t,className:(0,o.cn)("-mx-1 my-1 h-px bg-muted",r),...a})}).displayName=i.wv.displayName},97168:(e,t,r)=>{"use strict";r.d(t,{$:()=>o,r:()=>d});var s=r(95155),a=r(12115),i=r(99708),l=r(74466),n=r(53999);let d=(0,l.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-all duration-200 ease-in-out focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 transform hover:scale-105",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90 hover:brightness-110",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90 hover:brightness-110",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground hover:brightness-105",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80 hover:brightness-105",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),o=a.forwardRef((e,t)=>{let{className:r,variant:a,size:l,asChild:o=!1,...c}=e,u=o?i.DX:"button";return(0,s.jsx)(u,{className:(0,n.cn)(d({variant:a,size:l,className:r})),ref:t,suppressHydrationWarning:!0,...c})});o.displayName="Button"},99474:(e,t,r)=>{"use strict";r.d(t,{T:()=>l});var s=r(95155),a=r(12115),i=r(53999);let l=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("textarea",{className:(0,i.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",r),ref:t,...a})});l.displayName="Textarea"}},e=>{var t=t=>e(e.s=t);e.O(0,[1778,6874,4057,461,2504,1886,8441,1684,7358],()=>t(61854)),_N_E=e.O()}]);