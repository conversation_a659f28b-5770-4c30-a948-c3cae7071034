<?php

namespace Tests\Feature\Api;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;
use App\Models\User;
use App\Models\Student;
use App\Models\PartnerOrganization;
use App\Models\Scholarship;
use App\Models\ScholarshipApplication;
use App\Models\StudentProgression;
use Laravel\Sanctum\Sanctum;

class RoleBasedAuthorizationTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected $adminUser;
    protected $partnerUser;
    protected $studentUser;
    protected $partnerOrganization;
    protected $student;
    protected $scholarship;

    protected function setUp(): void
    {
        parent::setUp();

        // Create test users
        $this->adminUser = User::factory()->create([
            'role' => 'admin',
            'email' => '<EMAIL>'
        ]);

        $this->partnerOrganization = PartnerOrganization::factory()->create([
            'name' => 'Test School',
            'type' => 'school'
        ]);

        $this->partnerUser = User::factory()->create([
            'role' => 'partner_organization',
            'email' => '<EMAIL>',
            'partner_organization_id' => $this->partnerOrganization->id
        ]);

        $this->studentUser = User::factory()->create([
            'role' => 'user',
            'email' => '<EMAIL>'
        ]);

        // Create test data
        $this->student = Student::factory()->create([
            'school_id' => $this->partnerOrganization->id,
            'user_id' => $this->studentUser->id,
            'current_grade' => 10
        ]);

        $this->scholarship = Scholarship::factory()->create([
            'category' => 'primary',
            'title' => 'Test Primary Scholarship',
            'amount' => 50000
        ]);
    }

    /** @test */
    public function admin_can_access_all_endpoints()
    {
        Sanctum::actingAs($this->adminUser);

        // Test admin access to partner endpoints
        $response = $this->getJson('/api/v1/partner-dashboard');
        $response->assertStatus(200);

        // Test admin access to student endpoints
        $response = $this->getJson('/api/v1/student-progression/analytics');
        $response->assertStatus(200);

        // Test admin access to multi-year endpoints
        $response = $this->getJson('/api/v1/multi-year-applications/analytics');
        $response->assertStatus(200);
    }

    /** @test */
    public function partner_organization_can_access_appropriate_endpoints()
    {
        Sanctum::actingAs($this->partnerUser);

        // Test partner access to their dashboard
        $response = $this->getJson('/api/v1/partner-dashboard');
        $response->assertStatus(200);

        // Test partner access to student progression
        $response = $this->getJson('/api/v1/student-progression/analytics');
        $response->assertStatus(200);

        // Test partner access to primary/secondary scholarships
        $response = $this->getJson('/api/v1/partner-scholarships/available');
        $response->assertStatus(200);
    }

    /** @test */
    public function partner_organization_cannot_access_university_scholarships()
    {
        Sanctum::actingAs($this->partnerUser);

        // Create university scholarship
        $universityScholarship = Scholarship::factory()->create([
            'category' => 'university',
            'title' => 'Test University Scholarship'
        ]);

        // Test partner cannot access university scholarship details
        $response = $this->getJson("/api/v1/student-scholarships/{$universityScholarship->id}/details");
        $response->assertStatus(403);
    }

    /** @test */
    public function student_can_access_university_scholarships_only()
    {
        Sanctum::actingAs($this->studentUser);

        // Create university scholarship
        $universityScholarship = Scholarship::factory()->create([
            'category' => 'university',
            'title' => 'Test University Scholarship'
        ]);

        // Test student can access university scholarships
        $response = $this->getJson('/api/v1/student-scholarships/available');
        $response->assertStatus(200);

        // Test student cannot access partner dashboard
        $response = $this->getJson('/api/v1/partner-dashboard');
        $response->assertStatus(403);
    }

    /** @test */
    public function student_cannot_access_primary_secondary_scholarships()
    {
        Sanctum::actingAs($this->studentUser);

        // Test student cannot access partner scholarship endpoints
        $response = $this->getJson('/api/v1/partner-scholarships/available');
        $response->assertStatus(403);
    }

    /** @test */
    public function unauthenticated_users_cannot_access_protected_endpoints()
    {
        // Test without authentication
        $response = $this->getJson('/api/v1/partner-dashboard');
        $response->assertStatus(401);

        $response = $this->getJson('/api/v1/student-progression/analytics');
        $response->assertStatus(401);

        $response = $this->getJson('/api/v1/multi-year-applications/analytics');
        $response->assertStatus(401);
    }

    /** @test */
    public function partner_can_only_access_their_own_students()
    {
        Sanctum::actingAs($this->partnerUser);

        // Create another partner organization and student
        $otherPartner = PartnerOrganization::factory()->create();
        $otherStudent = Student::factory()->create([
            'school_id' => $otherPartner->id
        ]);

        // Test partner can access their own student
        $response = $this->getJson("/api/v1/student-progression/student/{$this->student->id}/history");
        $response->assertStatus(200);

        // Test partner cannot access other partner's student
        $response = $this->getJson("/api/v1/student-progression/student/{$otherStudent->id}/history");
        $response->assertStatus(403);
    }

    /** @test */
    public function student_can_only_access_their_own_data()
    {
        Sanctum::actingAs($this->studentUser);

        // Create another student
        $otherStudent = Student::factory()->create();

        // Test student can access their own data
        $response = $this->getJson("/api/v1/multi-year-applications/student/{$this->student->id}/history");
        $response->assertStatus(200);

        // Test student cannot access other student's data
        $response = $this->getJson("/api/v1/multi-year-applications/student/{$otherStudent->id}/history");
        $response->assertStatus(403);
    }

    /** @test */
    public function bulk_operations_require_appropriate_permissions()
    {
        // Test admin can perform bulk operations
        Sanctum::actingAs($this->adminUser);
        $response = $this->postJson('/api/v1/student-progression/bulk-advance', [
            'student_ids' => [$this->student->id],
            'academic_year' => '2024-2025'
        ]);
        $response->assertStatus(200);

        // Test partner can perform bulk operations for their students
        Sanctum::actingAs($this->partnerUser);
        $response = $this->postJson('/api/v1/student-progression/bulk-advance', [
            'student_ids' => [$this->student->id],
            'academic_year' => '2024-2025'
        ]);
        $response->assertStatus(200);

        // Test student cannot perform bulk operations
        Sanctum::actingAs($this->studentUser);
        $response = $this->postJson('/api/v1/student-progression/bulk-advance', [
            'student_ids' => [$this->student->id],
            'academic_year' => '2024-2025'
        ]);
        $response->assertStatus(403);
    }

    /** @test */
    public function scholarship_category_middleware_enforces_access_control()
    {
        // Create scholarships of different categories
        $primaryScholarship = Scholarship::factory()->create(['category' => 'primary']);
        $secondaryScholarship = Scholarship::factory()->create(['category' => 'secondary']);
        $universityScholarship = Scholarship::factory()->create(['category' => 'university']);

        // Test partner can access primary/secondary
        Sanctum::actingAs($this->partnerUser);
        $response = $this->postJson("/api/v1/multi-year-applications/create", [
            'scholarship_id' => $primaryScholarship->id,
            'student_id' => $this->student->id
        ]);
        $response->assertStatus(200);

        // Test partner cannot access university
        $response = $this->postJson("/api/v1/multi-year-applications/create", [
            'scholarship_id' => $universityScholarship->id,
            'student_id' => $this->student->id
        ]);
        $response->assertStatus(403);

        // Test student can access university only
        Sanctum::actingAs($this->studentUser);
        $response = $this->postJson("/api/v1/multi-year-applications/create", [
            'scholarship_id' => $universityScholarship->id,
            'student_id' => $this->student->id
        ]);
        $response->assertStatus(200);

        // Test student cannot access primary
        $response = $this->postJson("/api/v1/multi-year-applications/create", [
            'scholarship_id' => $primaryScholarship->id,
            'student_id' => $this->student->id
        ]);
        $response->assertStatus(403);
    }

    /** @test */
    public function middleware_provides_proper_error_messages()
    {
        // Test unauthenticated access
        $response = $this->getJson('/api/v1/partner-dashboard');
        $response->assertStatus(401)
                ->assertJson([
                    'success' => false,
                    'message' => 'Authentication required'
                ]);

        // Test unauthorized role access
        Sanctum::actingAs($this->studentUser);
        $response = $this->getJson('/api/v1/partner-dashboard');
        $response->assertStatus(403)
                ->assertJson([
                    'success' => false,
                    'message' => 'Insufficient permissions'
                ]);
    }
}
