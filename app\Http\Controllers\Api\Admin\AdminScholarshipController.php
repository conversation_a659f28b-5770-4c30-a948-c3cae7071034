<?php

namespace App\Http\Controllers\Api\Admin;

use App\Http\Controllers\Controller;
use App\Models\Scholarship;
use App\Models\ScholarshipApplication;
use App\Models\ScholarshipApplicationFile;
use App\Models\ScholarshipCategory;
use App\Models\ScholarshipTemplate;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;
use Carbon\Carbon;

/**
 * @OA\Tag(
 *     name="Admin Scholarships",
 *     description="Admin API Endpoints for Scholarship Management"
 * )
 */
class AdminScholarshipController extends Controller
{
    /**
     * @OA\Get(
     *     path="/api/v1/admin/scholarships",
     *     summary="Get all scholarships for admin",
     *     tags={"Admin Scholarships"},
     *     security={{"sanctum": {}}},
     *     @OA\Response(
     *         response=200,
     *         description="Scholarships retrieved successfully"
     *     )
     * )
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $perPage = $request->get('per_page', 15);
            $category = $request->get('category');
            $status = $request->get('status');
            $search = $request->get('search');
            $sortBy = $request->get('sort_by', 'created_at');
            $sortOrder = $request->get('sort_order', 'desc');

            $query = Scholarship::with(['creator:id,name,email', 'updater:id,name,email', 'categoryDetails'])
                ->withCount(['applications', 'applications as pending_applications_count' => function($q) {
                    $q->where('status', 'pending');
                }, 'applications as approved_applications_count' => function($q) {
                    $q->where('status', 'approved');
                }]);

            // Filter by category
            if ($category && $category !== 'all') {
                $query->where('category', $category);
            }

            // Filter by status
            if ($status && $status !== 'all') {
                if ($status === 'open') {
                    $query->where('is_open', true)->where('application_deadline', '>', now());
                } elseif ($status === 'closed') {
                    $query->where(function($q) {
                        $q->where('is_open', false)->orWhere('application_deadline', '<=', now());
                    });
                } elseif ($status === 'active') {
                    $query->where('status', 'active');
                } elseif ($status === 'inactive') {
                    $query->where('status', 'inactive');
                }
            }

            // Search functionality
            if ($search) {
                $query->where(function($q) use ($search) {
                    $q->where('title', 'like', "%{$search}%")
                      ->orWhere('description', 'like', "%{$search}%")
                      ->orWhere('eligibility_criteria', 'like', "%{$search}%");
                });
            }

            // Sorting
            if (in_array($sortBy, ['title', 'amount', 'application_deadline', 'created_at', 'applications_count'])) {
                $query->orderBy($sortBy, $sortOrder);
            } else {
                $query->orderBy('created_at', 'desc');
            }

            $scholarships = $query->paginate($perPage);

            // Add summary statistics
            $stats = [
                'total_scholarships' => Scholarship::count(),
                'active_scholarships' => Scholarship::where('status', 'active')->count(),
                'open_scholarships' => Scholarship::where('is_open', true)->where('application_deadline', '>', now())->count(),
                'categories' => [
                    'primary' => Scholarship::where('category', 'primary')->count(),
                    'secondary' => Scholarship::where('category', 'secondary')->count(),
                    'university' => Scholarship::where('category', 'university')->count(),
                ]
            ];

            return response()->json([
                'success' => true,
                'message' => 'Scholarships retrieved successfully',
                'data' => $scholarships,
                'stats' => $stats
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve scholarships',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * @OA\Post(
     *     path="/api/v1/admin/scholarships",
     *     summary="Create a new scholarship",
     *     tags={"Admin Scholarships"},
     *     security={{"sanctum": {}}},
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\JsonContent(
     *             required={"title", "description", "amount", "application_deadline"},
     *             @OA\Property(property="title", type="string"),
     *             @OA\Property(property="description", type="string"),
     *             @OA\Property(property="amount", type="number"),
     *             @OA\Property(property="application_deadline", type="string", format="date"),
     *             @OA\Property(property="eligibility_criteria", type="string"),
     *             @OA\Property(property="required_documents", type="string"),
     *             @OA\Property(property="max_recipients", type="integer"),
     *             @OA\Property(property="is_active", type="boolean")
     *         )
     *     ),
     *     @OA\Response(
     *         response=201,
     *         description="Scholarship created successfully"
     *     )
     * )
     */
    public function store(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'title' => 'required|string|max:255',
                'category' => 'required|string|in:primary,secondary,university',
                'description' => 'required|string',
                'eligibility_criteria' => 'nullable|string',
                'application_instructions' => 'nullable|string',
                'terms_conditions' => 'nullable|string',
                'amount' => 'required|numeric|min:1',
                'application_deadline' => 'required|date|after:today',
                'application_start_date' => 'nullable|date|before:application_deadline',
                'max_applicants' => 'nullable|integer|min:1',
                'contact_email' => 'nullable|email',
                'documents_required' => 'nullable|array',
                'custom_fields' => 'nullable|array',
                'notification_settings' => 'nullable|array',
                'is_open' => 'boolean',
                'is_featured' => 'boolean',
                'status' => 'nullable|string|in:active,inactive',
                'template_id' => 'nullable|integer|exists:scholarship_templates,id'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $data = $validator->validated();

            // Generate slug from title
            $data['slug'] = Str::slug($data['title']);

            // Ensure unique slug
            $originalSlug = $data['slug'];
            $counter = 1;
            while (Scholarship::where('slug', $data['slug'])->exists()) {
                $data['slug'] = $originalSlug . '-' . $counter;
                $counter++;
            }

            // Set default values
            $data['status'] = $data['status'] ?? 'active';
            $data['is_open'] = $data['is_open'] ?? true;
            $data['is_featured'] = $data['is_featured'] ?? false;
            $data['current_applicants'] = 0;
            $data['created_by'] = Auth::id();

            // If template is provided, merge template fields
            if (isset($data['template_id'])) {
                $template = ScholarshipTemplate::findOrFail($data['template_id']);
                if ($template->category === $data['category']) {
                    $data['custom_fields'] = array_merge($template->default_fields, $data['custom_fields'] ?? []);
                }
                unset($data['template_id']);
            }

            $scholarship = Scholarship::create($data);

            return response()->json([
                'success' => true,
                'message' => 'Scholarship created successfully',
                'data' => $scholarship->load(['creator:id,name,email', 'categoryDetails'])
            ], 201);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to create scholarship',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * @OA\Get(
     *     path="/api/v1/admin/scholarships/{id}",
     *     summary="Get specific scholarship details",
     *     tags={"Admin Scholarships"},
     *     security={{"sanctum": {}}},
     *     @OA\Parameter(
     *         name="id",
     *         in="path",
     *         required=true,
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Scholarship retrieved successfully"
     *     )
     * )
     */
    public function show($id): JsonResponse
    {
        try {
            $scholarship = Scholarship::with(['applications.user'])
                ->withCount('applications')
                ->findOrFail($id);

            return response()->json([
                'success' => true,
                'message' => 'Scholarship retrieved successfully',
                'data' => $scholarship
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Scholarship not found',
                'error' => $e->getMessage()
            ], 404);
        }
    }

    /**
     * @OA\Put(
     *     path="/api/v1/admin/scholarships/{id}",
     *     summary="Update a scholarship",
     *     tags={"Admin Scholarships"},
     *     security={{"sanctum": {}}},
     *     @OA\Parameter(
     *         name="id",
     *         in="path",
     *         required=true,
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Scholarship updated successfully"
     *     )
     * )
     */
    public function update(Request $request, $id): JsonResponse
    {
        try {
            $scholarship = Scholarship::findOrFail($id);

            $validator = Validator::make($request->all(), [
                'title' => 'string|max:255',
                'description' => 'string',
                'amount' => 'numeric|min:1',
                'application_deadline' => 'date',
                'eligibility_criteria' => 'nullable|string',
                'required_documents' => 'nullable|string',
                'max_recipients' => 'nullable|integer|min:1',
                'is_active' => 'boolean'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $scholarship->update($validator->validated());

            return response()->json([
                'success' => true,
                'message' => 'Scholarship updated successfully',
                'data' => $scholarship
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update scholarship',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * @OA\Delete(
     *     path="/api/v1/admin/scholarships/{id}",
     *     summary="Delete a scholarship",
     *     tags={"Admin Scholarships"},
     *     security={{"sanctum": {}}},
     *     @OA\Parameter(
     *         name="id",
     *         in="path",
     *         required=true,
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Scholarship deleted successfully"
     *     )
     * )
     */
    public function destroy($id): JsonResponse
    {
        try {
            $scholarship = Scholarship::findOrFail($id);
            
            // Check if scholarship has applications
            if ($scholarship->applications()->count() > 0) {
                return response()->json([
                    'success' => false,
                    'message' => 'Cannot delete scholarship with existing applications'
                ], 400);
            }

            $scholarship->delete();

            return response()->json([
                'success' => true,
                'message' => 'Scholarship deleted successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to delete scholarship',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get scholarship applications with enhanced filtering and search
     */
    public function applications(Request $request): JsonResponse
    {
        try {
            $perPage = $request->get('per_page', 15);
            $status = $request->get('status');
            $scholarshipId = $request->get('scholarship_id');
            $search = $request->get('search');
            $dateRange = $request->get('date_range');
            $sortBy = $request->get('sort_by', 'submitted_at');
            $sortOrder = $request->get('sort_order', 'desc');

            $query = ScholarshipApplication::with(['user', 'scholarship', 'reviewer'])
                ->select('scholarship_applications.*');

            // Apply status filter
            if ($status && $status !== 'all') {
                $query->where('status', $status);
            }

            // Apply scholarship filter
            if ($scholarshipId) {
                $query->where('scholarship_id', $scholarshipId);
            }

            // Apply search filter
            if ($search) {
                $query->where(function($q) use ($search) {
                    $q->whereHas('user', function($userQuery) use ($search) {
                        $userQuery->where('first_name', 'like', "%{$search}%")
                                  ->orWhere('last_name', 'like', "%{$search}%")
                                  ->orWhere('email', 'like', "%{$search}%");
                    })->orWhereHas('scholarship', function($scholarshipQuery) use ($search) {
                        $scholarshipQuery->where('title', 'like', "%{$search}%");
                    });
                });
            }

            // Apply date range filter
            if ($dateRange && $dateRange !== 'all') {
                $startDate = match($dateRange) {
                    'today' => Carbon::today(),
                    'week' => Carbon::now()->startOfWeek(),
                    'month' => Carbon::now()->startOfMonth(),
                    'quarter' => Carbon::now()->startOfQuarter(),
                    default => null
                };

                if ($startDate) {
                    $query->where('submitted_at', '>=', $startDate);
                }
            }

            // Apply sorting
            if (in_array($sortBy, ['submitted_at', 'reviewed_at', 'status'])) {
                $query->orderBy($sortBy, $sortOrder);
            } else {
                $query->orderBy('submitted_at', 'desc');
            }

            $applications = $query->paginate($perPage);

            // Calculate summary statistics
            $stats = [
                'total_applications' => ScholarshipApplication::count(),
                'submitted_applications' => ScholarshipApplication::where('status', 'pending')->count(),
                'under_review_applications' => ScholarshipApplication::where('status', 'under_review')->count(),
                'approved_applications' => ScholarshipApplication::where('status', 'approved')->count(),
                'rejected_applications' => ScholarshipApplication::where('status', 'rejected')->count(),
                'applications_this_month' => ScholarshipApplication::whereMonth('submitted_at', Carbon::now()->month)
                    ->whereYear('submitted_at', Carbon::now()->year)
                    ->count()
            ];

            return response()->json([
                'success' => true,
                'message' => 'Scholarship applications retrieved successfully',
                'data' => [
                    'data' => $applications->items(),
                    'meta' => [
                        'total' => $applications->total(),
                        'per_page' => $applications->perPage(),
                        'current_page' => $applications->currentPage(),
                        'last_page' => $applications->lastPage(),
                        'from' => $applications->firstItem(),
                        'to' => $applications->lastItem(),
                    ],
                    'stats' => $stats
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve applications',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get single scholarship application with full details
     */
    public function getApplication($id): JsonResponse
    {
        try {
            $application = ScholarshipApplication::with([
                'user:id,first_name,last_name,email,phone',
                'scholarship:id,title,amount,category,description',
                'reviewer:id,first_name,last_name,email',
                'files'
            ])->findOrFail($id);

            return response()->json([
                'success' => true,
                'message' => 'Application retrieved successfully',
                'data' => $application
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve application',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Review scholarship application with enhanced features
     */
    public function reviewApplication(Request $request, $id): JsonResponse
    {
        try {
            $application = ScholarshipApplication::findOrFail($id);

            $validator = Validator::make($request->all(), [
                'status' => 'nullable|in:pending,under_review,approved,rejected',
                'score' => 'nullable|numeric|min:0|max:100',
                'review_notes' => 'nullable|string|max:1000',
                'internal_notes' => 'nullable|string|max:1000',
                'award_amount' => 'nullable|numeric|min:0'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $data = $validator->validated();

            // Only update fields that were provided
            if (isset($data['status']) || isset($data['score']) || isset($data['review_notes']) ||
                isset($data['internal_notes']) || isset($data['award_amount'])) {
                $data['reviewed_by'] = auth()->id();
                $data['reviewed_at'] = now();
            }

            $application->update(array_filter($data, function($value) {
                return $value !== null;
            }));

            return response()->json([
                'success' => true,
                'message' => 'Application reviewed successfully',
                'data' => $application->load(['user', 'scholarship', 'reviewer'])
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to review application',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Bulk operations on scholarship applications
     */
    public function bulkAction(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'action' => 'required|in:approve,reject,under_review,delete,export',
                'application_ids' => 'required|array|min:1',
                'application_ids.*' => 'integer|exists:scholarship_applications,id',
                'review_notes' => 'nullable|string|max:1000',
                'award_amount' => 'nullable|numeric|min:0'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $action = $request->action;
            $applicationIds = $request->application_ids;
            $reviewNotes = $request->review_notes;
            $awardAmount = $request->award_amount;

            $applications = ScholarshipApplication::whereIn('id', $applicationIds)->get();

            if ($applications->count() !== count($applicationIds)) {
                return response()->json([
                    'success' => false,
                    'message' => 'Some applications not found'
                ], 404);
            }

            $successCount = 0;
            $errors = [];

            foreach ($applications as $application) {
                try {
                    switch ($action) {
                        case 'approve':
                            $application->update([
                                'status' => 'approved',
                                'review_notes' => $reviewNotes,
                                'award_amount' => $awardAmount,
                                'reviewed_by' => auth()->id(),
                                'reviewed_at' => now()
                            ]);
                            break;
                        case 'reject':
                            $application->update([
                                'status' => 'rejected',
                                'review_notes' => $reviewNotes,
                                'reviewed_by' => auth()->id(),
                                'reviewed_at' => now()
                            ]);
                            break;
                        case 'under_review':
                            $application->update([
                                'status' => 'under_review',
                                'review_notes' => $reviewNotes,
                                'reviewed_by' => auth()->id(),
                                'reviewed_at' => now()
                            ]);
                            break;
                        case 'delete':
                            $application->delete();
                            break;
                    }
                    $successCount++;
                } catch (\Exception $e) {
                    $errors[] = "Failed to process application ID {$application->id}: " . $e->getMessage();
                }
            }

            $message = "Successfully processed {$successCount} out of " . count($applicationIds) . " applications";
            if (!empty($errors)) {
                $message .= ". Errors: " . implode(', ', $errors);
            }

            return response()->json([
                'success' => true,
                'message' => $message,
                'data' => [
                    'processed_count' => $successCount,
                    'total_count' => count($applicationIds),
                    'errors' => $errors
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to perform bulk action',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Bulk review applications (separate endpoint for review modal)
     */
    public function bulkReview(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'application_ids' => 'required|array|min:1',
                'application_ids.*' => 'integer|exists:scholarship_applications,id',
                'action' => 'required|in:approved,rejected,under_review',
                'notes' => 'nullable|string|max:1000'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $applicationIds = $request->input('application_ids');
            $action = $request->input('action');
            $notes = $request->input('notes');

            $updated = ScholarshipApplication::whereIn('id', $applicationIds)->update([
                'status' => $action,
                'review_notes' => $notes,
                'reviewed_by' => auth()->id(),
                'reviewed_at' => now()
            ]);

            return response()->json([
                'success' => true,
                'message' => "Successfully updated {$updated} applications to {$action}",
                'data' => ['updated_count' => $updated]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to perform bulk review',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Bulk status update (separate endpoint for quick status changes)
     */
    public function bulkStatus(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'application_ids' => 'required|array|min:1',
                'application_ids.*' => 'integer|exists:scholarship_applications,id',
                'status' => 'required|in:pending,under_review,approved,rejected'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $applicationIds = $request->input('application_ids');
            $status = $request->input('status');

            $updated = ScholarshipApplication::whereIn('id', $applicationIds)->update([
                'status' => $status,
                'reviewed_by' => auth()->id(),
                'reviewed_at' => now()
            ]);

            return response()->json([
                'success' => true,
                'message' => "Successfully updated {$updated} applications to {$status}",
                'data' => ['updated_count' => $updated]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update application status',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Compare multiple applications
     */
    public function compareApplications(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'application_ids' => 'required|array|min:2|max:5',
                'application_ids.*' => 'integer|exists:scholarship_applications,id'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $applicationIds = $request->input('application_ids');

            $applications = ScholarshipApplication::with([
                'user:id,first_name,last_name,email,phone',
                'scholarship:id,title,amount,category',
                'reviewer:id,first_name,last_name,email',
                'files'
            ])->whereIn('id', $applicationIds)->get();

            return response()->json([
                'success' => true,
                'message' => 'Applications comparison data retrieved successfully',
                'data' => $applications
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve comparison data',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get file details for preview
     */
    public function getFile($fileId): JsonResponse
    {
        try {
            $file = ScholarshipApplicationFile::findOrFail($fileId);

            return response()->json([
                'success' => true,
                'message' => 'File details retrieved successfully',
                'data' => $file
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve file details',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Toggle file verification status
     */
    public function toggleFileVerification($fileId): JsonResponse
    {
        try {
            $file = ScholarshipApplicationFile::findOrFail($fileId);

            $file->update([
                'is_verified' => !$file->is_verified,
                'verified_by' => auth()->id(),
                'verified_at' => now()
            ]);

            return response()->json([
                'success' => true,
                'message' => 'File verification status updated successfully',
                'data' => $file
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update file verification',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Send notification to applicant
     */
    public function sendNotification($applicationId): JsonResponse
    {
        try {
            $application = ScholarshipApplication::with(['user', 'scholarship'])->findOrFail($applicationId);

            // Here you would integrate with your notification system
            // For now, we'll just return a success response

            return response()->json([
                'success' => true,
                'message' => 'Notification sent successfully to ' . $application->user->email,
                'data' => [
                    'recipient' => $application->user->email,
                    'status' => $application->status,
                    'scholarship' => $application->scholarship->title
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to send notification',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Download application files as ZIP
     */
    public function downloadApplicationFiles($applicationId): JsonResponse
    {
        try {
            $application = ScholarshipApplication::with('files')->findOrFail($applicationId);

            if ($application->files->isEmpty()) {
                return response()->json([
                    'success' => false,
                    'message' => 'No files found for this application'
                ], 404);
            }

            // Here you would create a ZIP file with all application files
            // For now, we'll return the file URLs

            return response()->json([
                'success' => true,
                'message' => 'Application files ready for download',
                'data' => [
                    'application_id' => $applicationId,
                    'files' => $application->files->map(function($file) {
                        return [
                            'id' => $file->id,
                            'name' => $file->original_name,
                            'url' => $file->file_url,
                            'size' => $file->file_size
                        ];
                    })
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to prepare files for download',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Export scholarship applications
     */
    public function exportApplications(Request $request): JsonResponse
    {
        try {
            $format = $request->get('format', 'csv');
            $status = $request->get('status');
            $scholarshipId = $request->get('scholarship_id');

            $query = ScholarshipApplication::with(['user', 'scholarship']);

            if ($status && $status !== 'all') {
                $query->where('status', $status);
            }

            if ($scholarshipId) {
                $query->where('scholarship_id', $scholarshipId);
            }

            $applications = $query->get();

            // Prepare data for export
            $exportData = $applications->map(function ($application) {
                return [
                    'Application ID' => $application->id,
                    'Applicant Name' => $application->user->first_name . ' ' . $application->user->last_name,
                    'Email' => $application->user->email,
                    'Scholarship' => $application->scholarship->title,
                    'Amount' => $application->scholarship->amount,
                    'Status' => $application->status,
                    'Submitted At' => $application->submitted_at->format('Y-m-d H:i:s'),
                    'Reviewed At' => $application->reviewed_at ? $application->reviewed_at->format('Y-m-d H:i:s') : '',
                    'Award Amount' => $application->award_amount ?? '',
                    'Review Notes' => $application->review_notes ?? ''
                ];
            });

            return response()->json([
                'success' => true,
                'message' => 'Export data prepared successfully',
                'data' => [
                    'format' => $format,
                    'count' => $exportData->count(),
                    'data' => $exportData
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to export applications',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get scholarship statistics
     */
    public function statistics(): JsonResponse
    {
        try {
            $stats = [
                'total_scholarships' => Scholarship::count(),
                'active_scholarships' => Scholarship::where('status', 'active')->count(),
                'open_scholarships' => Scholarship::where('is_open', true)->where('application_deadline', '>', now())->count(),
                'total_applications' => ScholarshipApplication::count(),
                'pending_applications' => ScholarshipApplication::where('status', 'pending')->count(),
                'under_review_applications' => ScholarshipApplication::where('status', 'under_review')->count(),
                'approved_applications' => ScholarshipApplication::where('status', 'approved')->count(),
                'rejected_applications' => ScholarshipApplication::where('status', 'rejected')->count(),
                'total_awarded_amount' => ScholarshipApplication::where('status', 'approved')
                    ->sum('award_amount'),
                'scholarships_expiring_soon' => Scholarship::where('application_deadline', '<=', Carbon::now()->addDays(30))
                    ->where('application_deadline', '>', Carbon::now())
                    ->where('is_open', true)
                    ->count(),
                'categories' => [
                    'primary' => [
                        'total' => Scholarship::where('category', 'primary')->count(),
                        'applications' => ScholarshipApplication::whereHas('scholarship', function($q) {
                            $q->where('category', 'primary');
                        })->count()
                    ],
                    'secondary' => [
                        'total' => Scholarship::where('category', 'secondary')->count(),
                        'applications' => ScholarshipApplication::whereHas('scholarship', function($q) {
                            $q->where('category', 'secondary');
                        })->count()
                    ],
                    'university' => [
                        'total' => Scholarship::where('category', 'university')->count(),
                        'applications' => ScholarshipApplication::whereHas('scholarship', function($q) {
                            $q->where('category', 'university');
                        })->count()
                    ]
                ]
            ];

            return response()->json([
                'success' => true,
                'message' => 'Scholarship statistics retrieved successfully',
                'data' => $stats
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve statistics',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Close a scholarship (update status to closed)
     */
    public function closeScholarship($id): JsonResponse
    {
        try {
            $scholarship = Scholarship::findOrFail($id);

            $scholarship->update([
                'is_open' => false,
                'status' => 'inactive',
                'updated_by' => Auth::id()
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Scholarship closed successfully',
                'data' => $scholarship->fresh()
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to close scholarship',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get scholarship applications for a specific scholarship
     */
    public function getScholarshipApplications($id, Request $request): JsonResponse
    {
        try {
            $scholarship = Scholarship::findOrFail($id);
            $perPage = $request->get('per_page', 15);
            $status = $request->get('status');
            $search = $request->get('search');

            $query = $scholarship->applications()
                ->with(['user:id,name,email,phone', 'reviewer:id,name,email', 'files']);

            // Filter by status
            if ($status && $status !== 'all') {
                $query->where('status', $status);
            }

            // Search functionality
            if ($search) {
                $query->whereHas('user', function($q) use ($search) {
                    $q->where('name', 'like', "%{$search}%")
                      ->orWhere('email', 'like', "%{$search}%");
                });
            }

            $applications = $query->orderBy('submitted_at', 'desc')->paginate($perPage);

            return response()->json([
                'success' => true,
                'message' => 'Scholarship applications retrieved successfully',
                'data' => [
                    'scholarship' => $scholarship->only(['id', 'title', 'category', 'amount']),
                    'applications' => $applications
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve scholarship applications',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Download application file
     */
    public function downloadFile($applicationId, $fileId): JsonResponse
    {
        try {
            $application = ScholarshipApplication::findOrFail($applicationId);
            $file = ScholarshipApplicationFile::where('application_id', $applicationId)
                ->where('id', $fileId)
                ->firstOrFail();

            if (!Storage::exists($file->file_path)) {
                return response()->json([
                    'success' => false,
                    'message' => 'File not found on storage'
                ], 404);
            }

            // Update download count and last downloaded info
            $file->increment('download_count');
            $file->update([
                'last_downloaded_at' => now(),
                'last_downloaded_by' => Auth::id()
            ]);

            $fileContent = Storage::get($file->file_path);
            $mimeType = Storage::mimeType($file->file_path);

            return response()->json([
                'success' => true,
                'message' => 'File retrieved successfully',
                'data' => [
                    'file_name' => $file->original_name,
                    'file_size' => $file->file_size,
                    'mime_type' => $mimeType,
                    'download_url' => Storage::url($file->file_path),
                    'file_info' => $file->only(['id', 'field_name', 'original_name', 'file_size', 'upload_date'])
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to download file',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get all files for an application
     */
    public function getApplicationFiles($applicationId): JsonResponse
    {
        try {
            $application = ScholarshipApplication::with(['files', 'user:id,name,email'])->findOrFail($applicationId);

            return response()->json([
                'success' => true,
                'message' => 'Application files retrieved successfully',
                'data' => [
                    'application' => $application->only(['id', 'application_id', 'status', 'submitted_at']),
                    'applicant' => $application->user,
                    'files' => $application->files->map(function($file) {
                        return [
                            'id' => $file->id,
                            'field_name' => $file->field_name,
                            'original_name' => $file->original_name,
                            'file_size' => $file->file_size,
                            'file_size_formatted' => $file->getFormattedFileSize(),
                            'file_type' => $file->file_type,
                            'is_image' => $file->isImage(),
                            'is_pdf' => $file->isPdf(),
                            'upload_date' => $file->upload_date,
                            'is_verified' => $file->is_verified,
                            'verification_notes' => $file->verification_notes,
                            'download_count' => $file->download_count,
                            'download_url' => route('admin.scholarships.applications.files.download', [
                                'applicationId' => $applicationId,
                                'fileId' => $file->id
                            ])
                        ];
                    })
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve application files',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Verify/unverify an application file
     */
    public function verifyFile(Request $request, $applicationId, $fileId): JsonResponse
    {
        try {
            $file = ScholarshipApplicationFile::where('application_id', $applicationId)
                ->where('id', $fileId)
                ->firstOrFail();

            $validator = Validator::make($request->all(), [
                'is_verified' => 'required|boolean',
                'verification_notes' => 'nullable|string|max:500',
                'admin_notes' => 'nullable|string|max:1000'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $data = $validator->validated();
            $data['verified_by'] = Auth::id();
            $data['verified_at'] = now();

            $file->update($data);

            return response()->json([
                'success' => true,
                'message' => 'File verification status updated successfully',
                'data' => $file->fresh()
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update file verification',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
