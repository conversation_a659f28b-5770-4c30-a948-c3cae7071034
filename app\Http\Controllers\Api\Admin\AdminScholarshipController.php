<?php

namespace App\Http\Controllers\Api\Admin;

use App\Http\Controllers\Controller;
use App\Models\Scholarship;
use App\Models\ScholarshipApplication;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Validator;
use Carbon\Carbon;

/**
 * @OA\Tag(
 *     name="Admin Scholarships",
 *     description="Admin API Endpoints for Scholarship Management"
 * )
 */
class AdminScholarshipController extends Controller
{
    /**
     * @OA\Get(
     *     path="/api/v1/admin/scholarships",
     *     summary="Get all scholarships for admin",
     *     tags={"Admin Scholarships"},
     *     security={{"sanctum": {}}},
     *     @OA\Response(
     *         response=200,
     *         description="Scholarships retrieved successfully"
     *     )
     * )
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $perPage = $request->get('per_page', 15);
            $scholarships = Scholarship::withCount('applications')
                ->orderBy('created_at', 'desc')
                ->paginate($perPage);

            return response()->json([
                'success' => true,
                'message' => 'Scholarships retrieved successfully',
                'data' => $scholarships
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve scholarships',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * @OA\Post(
     *     path="/api/v1/admin/scholarships",
     *     summary="Create a new scholarship",
     *     tags={"Admin Scholarships"},
     *     security={{"sanctum": {}}},
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\JsonContent(
     *             required={"title", "description", "amount", "application_deadline"},
     *             @OA\Property(property="title", type="string"),
     *             @OA\Property(property="description", type="string"),
     *             @OA\Property(property="amount", type="number"),
     *             @OA\Property(property="application_deadline", type="string", format="date"),
     *             @OA\Property(property="eligibility_criteria", type="string"),
     *             @OA\Property(property="required_documents", type="string"),
     *             @OA\Property(property="max_recipients", type="integer"),
     *             @OA\Property(property="is_active", type="boolean")
     *         )
     *     ),
     *     @OA\Response(
     *         response=201,
     *         description="Scholarship created successfully"
     *     )
     * )
     */
    public function store(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'title' => 'required|string|max:255',
                'description' => 'required|string',
                'amount' => 'required|numeric|min:1',
                'application_deadline' => 'required|date|after:today',
                'eligibility_criteria' => 'nullable|string',
                'required_documents' => 'nullable|string',
                'max_recipients' => 'nullable|integer|min:1',
                'is_active' => 'boolean'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $scholarship = Scholarship::create($validator->validated());

            return response()->json([
                'success' => true,
                'message' => 'Scholarship created successfully',
                'data' => $scholarship
            ], 201);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to create scholarship',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * @OA\Get(
     *     path="/api/v1/admin/scholarships/{id}",
     *     summary="Get specific scholarship details",
     *     tags={"Admin Scholarships"},
     *     security={{"sanctum": {}}},
     *     @OA\Parameter(
     *         name="id",
     *         in="path",
     *         required=true,
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Scholarship retrieved successfully"
     *     )
     * )
     */
    public function show($id): JsonResponse
    {
        try {
            $scholarship = Scholarship::with(['applications.user'])
                ->withCount('applications')
                ->findOrFail($id);

            return response()->json([
                'success' => true,
                'message' => 'Scholarship retrieved successfully',
                'data' => $scholarship
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Scholarship not found',
                'error' => $e->getMessage()
            ], 404);
        }
    }

    /**
     * @OA\Put(
     *     path="/api/v1/admin/scholarships/{id}",
     *     summary="Update a scholarship",
     *     tags={"Admin Scholarships"},
     *     security={{"sanctum": {}}},
     *     @OA\Parameter(
     *         name="id",
     *         in="path",
     *         required=true,
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Scholarship updated successfully"
     *     )
     * )
     */
    public function update(Request $request, $id): JsonResponse
    {
        try {
            $scholarship = Scholarship::findOrFail($id);

            $validator = Validator::make($request->all(), [
                'title' => 'string|max:255',
                'description' => 'string',
                'amount' => 'numeric|min:1',
                'application_deadline' => 'date',
                'eligibility_criteria' => 'nullable|string',
                'required_documents' => 'nullable|string',
                'max_recipients' => 'nullable|integer|min:1',
                'is_active' => 'boolean'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $scholarship->update($validator->validated());

            return response()->json([
                'success' => true,
                'message' => 'Scholarship updated successfully',
                'data' => $scholarship
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update scholarship',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * @OA\Delete(
     *     path="/api/v1/admin/scholarships/{id}",
     *     summary="Delete a scholarship",
     *     tags={"Admin Scholarships"},
     *     security={{"sanctum": {}}},
     *     @OA\Parameter(
     *         name="id",
     *         in="path",
     *         required=true,
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Scholarship deleted successfully"
     *     )
     * )
     */
    public function destroy($id): JsonResponse
    {
        try {
            $scholarship = Scholarship::findOrFail($id);
            
            // Check if scholarship has applications
            if ($scholarship->applications()->count() > 0) {
                return response()->json([
                    'success' => false,
                    'message' => 'Cannot delete scholarship with existing applications'
                ], 400);
            }

            $scholarship->delete();

            return response()->json([
                'success' => true,
                'message' => 'Scholarship deleted successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to delete scholarship',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get scholarship applications with enhanced filtering and search
     */
    public function applications(Request $request): JsonResponse
    {
        try {
            $perPage = $request->get('per_page', 15);
            $status = $request->get('status');
            $scholarshipId = $request->get('scholarship_id');
            $search = $request->get('search');
            $dateRange = $request->get('date_range');
            $sortBy = $request->get('sort_by', 'submitted_at');
            $sortOrder = $request->get('sort_order', 'desc');

            $query = ScholarshipApplication::with(['user', 'scholarship', 'reviewer'])
                ->select('scholarship_applications.*');

            // Apply status filter
            if ($status && $status !== 'all') {
                $query->where('status', $status);
            }

            // Apply scholarship filter
            if ($scholarshipId) {
                $query->where('scholarship_id', $scholarshipId);
            }

            // Apply search filter
            if ($search) {
                $query->where(function($q) use ($search) {
                    $q->whereHas('user', function($userQuery) use ($search) {
                        $userQuery->where('first_name', 'like', "%{$search}%")
                                  ->orWhere('last_name', 'like', "%{$search}%")
                                  ->orWhere('email', 'like', "%{$search}%");
                    })->orWhereHas('scholarship', function($scholarshipQuery) use ($search) {
                        $scholarshipQuery->where('title', 'like', "%{$search}%");
                    });
                });
            }

            // Apply date range filter
            if ($dateRange && $dateRange !== 'all') {
                $startDate = match($dateRange) {
                    'today' => Carbon::today(),
                    'week' => Carbon::now()->startOfWeek(),
                    'month' => Carbon::now()->startOfMonth(),
                    'quarter' => Carbon::now()->startOfQuarter(),
                    default => null
                };

                if ($startDate) {
                    $query->where('submitted_at', '>=', $startDate);
                }
            }

            // Apply sorting
            if (in_array($sortBy, ['submitted_at', 'reviewed_at', 'status'])) {
                $query->orderBy($sortBy, $sortOrder);
            } else {
                $query->orderBy('submitted_at', 'desc');
            }

            $applications = $query->paginate($perPage);

            // Calculate summary statistics
            $stats = [
                'total_applications' => ScholarshipApplication::count(),
                'submitted_applications' => ScholarshipApplication::where('status', 'pending')->count(),
                'under_review_applications' => ScholarshipApplication::where('status', 'under_review')->count(),
                'approved_applications' => ScholarshipApplication::where('status', 'approved')->count(),
                'rejected_applications' => ScholarshipApplication::where('status', 'rejected')->count(),
                'applications_this_month' => ScholarshipApplication::whereMonth('submitted_at', Carbon::now()->month)
                    ->whereYear('submitted_at', Carbon::now()->year)
                    ->count()
            ];

            return response()->json([
                'success' => true,
                'message' => 'Scholarship applications retrieved successfully',
                'data' => [
                    'data' => $applications->items(),
                    'meta' => [
                        'total' => $applications->total(),
                        'per_page' => $applications->perPage(),
                        'current_page' => $applications->currentPage(),
                        'last_page' => $applications->lastPage(),
                        'from' => $applications->firstItem(),
                        'to' => $applications->lastItem(),
                    ],
                    'stats' => $stats
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve applications',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Review scholarship application
     */
    public function reviewApplication(Request $request, $id): JsonResponse
    {
        try {
            $application = ScholarshipApplication::findOrFail($id);
            
            $validator = Validator::make($request->all(), [
                'status' => 'required|in:under_review,approved,rejected',
                'review_notes' => 'nullable|string|max:1000',
                'award_amount' => 'nullable|numeric|min:0'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $data = $validator->validated();
            $data['reviewed_by'] = auth()->id();
            $data['reviewed_at'] = now();

            $application->update($data);

            return response()->json([
                'success' => true,
                'message' => 'Application reviewed successfully',
                'data' => $application->load(['user', 'scholarship'])
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to review application',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Bulk operations on scholarship applications
     */
    public function bulkAction(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'action' => 'required|in:approve,reject,under_review,delete,export',
                'application_ids' => 'required|array|min:1',
                'application_ids.*' => 'integer|exists:scholarship_applications,id',
                'review_notes' => 'nullable|string|max:1000',
                'award_amount' => 'nullable|numeric|min:0'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $action = $request->action;
            $applicationIds = $request->application_ids;
            $reviewNotes = $request->review_notes;
            $awardAmount = $request->award_amount;

            $applications = ScholarshipApplication::whereIn('id', $applicationIds)->get();

            if ($applications->count() !== count($applicationIds)) {
                return response()->json([
                    'success' => false,
                    'message' => 'Some applications not found'
                ], 404);
            }

            $successCount = 0;
            $errors = [];

            foreach ($applications as $application) {
                try {
                    switch ($action) {
                        case 'approve':
                            $application->update([
                                'status' => 'approved',
                                'review_notes' => $reviewNotes,
                                'award_amount' => $awardAmount,
                                'reviewed_by' => auth()->id(),
                                'reviewed_at' => now()
                            ]);
                            break;
                        case 'reject':
                            $application->update([
                                'status' => 'rejected',
                                'review_notes' => $reviewNotes,
                                'reviewed_by' => auth()->id(),
                                'reviewed_at' => now()
                            ]);
                            break;
                        case 'under_review':
                            $application->update([
                                'status' => 'under_review',
                                'review_notes' => $reviewNotes,
                                'reviewed_by' => auth()->id(),
                                'reviewed_at' => now()
                            ]);
                            break;
                        case 'delete':
                            $application->delete();
                            break;
                    }
                    $successCount++;
                } catch (\Exception $e) {
                    $errors[] = "Failed to process application ID {$application->id}: " . $e->getMessage();
                }
            }

            $message = "Successfully processed {$successCount} out of " . count($applicationIds) . " applications";
            if (!empty($errors)) {
                $message .= ". Errors: " . implode(', ', $errors);
            }

            return response()->json([
                'success' => true,
                'message' => $message,
                'data' => [
                    'processed_count' => $successCount,
                    'total_count' => count($applicationIds),
                    'errors' => $errors
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to perform bulk action',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Export scholarship applications
     */
    public function exportApplications(Request $request): JsonResponse
    {
        try {
            $format = $request->get('format', 'csv');
            $status = $request->get('status');
            $scholarshipId = $request->get('scholarship_id');

            $query = ScholarshipApplication::with(['user', 'scholarship']);

            if ($status && $status !== 'all') {
                $query->where('status', $status);
            }

            if ($scholarshipId) {
                $query->where('scholarship_id', $scholarshipId);
            }

            $applications = $query->get();

            // Prepare data for export
            $exportData = $applications->map(function ($application) {
                return [
                    'Application ID' => $application->id,
                    'Applicant Name' => $application->user->first_name . ' ' . $application->user->last_name,
                    'Email' => $application->user->email,
                    'Scholarship' => $application->scholarship->title,
                    'Amount' => $application->scholarship->amount,
                    'Status' => $application->status,
                    'Submitted At' => $application->submitted_at->format('Y-m-d H:i:s'),
                    'Reviewed At' => $application->reviewed_at ? $application->reviewed_at->format('Y-m-d H:i:s') : '',
                    'Award Amount' => $application->award_amount ?? '',
                    'Review Notes' => $application->review_notes ?? ''
                ];
            });

            return response()->json([
                'success' => true,
                'message' => 'Export data prepared successfully',
                'data' => [
                    'format' => $format,
                    'count' => $exportData->count(),
                    'data' => $exportData
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to export applications',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get scholarship statistics
     */
    public function statistics(): JsonResponse
    {
        try {
            $stats = [
                'total_scholarships' => Scholarship::count(),
                'active_scholarships' => Scholarship::where('is_active', true)->count(),
                'total_applications' => ScholarshipApplication::count(),
                'pending_applications' => ScholarshipApplication::where('status', 'pending')->count(),
                'approved_applications' => ScholarshipApplication::where('status', 'approved')->count(),
                'total_awarded_amount' => ScholarshipApplication::where('status', 'approved')
                    ->sum('award_amount'),
                'scholarships_expiring_soon' => Scholarship::where('application_deadline', '<=', Carbon::now()->addDays(30))
                    ->where('application_deadline', '>', Carbon::now())
                    ->where('is_active', true)
                    ->count()
            ];

            return response()->json([
                'success' => true,
                'message' => 'Scholarship statistics retrieved successfully',
                'data' => $stats
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve statistics',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
