"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/scholarship-application/page",{

/***/ "(app-pages-browser)/./components/scholarship/ScholarshipApplicationPage.tsx":
/*!***************************************************************!*\
  !*** ./components/scholarship/ScholarshipApplicationPage.tsx ***!
  \***************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ScholarshipApplicationPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./components/ui/label.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/textarea */ \"(app-pages-browser)/./components/ui/textarea.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./components/ui/select.tsx\");\n/* harmony import */ var _components_ui_tabs__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/tabs */ \"(app-pages-browser)/./components/ui/tabs.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BookOpen_Building_CheckCircle_CreditCard_FileUp_GraduationCap_Loader2_MapPin_School_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BookOpen,Building,CheckCircle,CreditCard,FileUp,GraduationCap,Loader2,MapPin,School,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/book-open.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BookOpen_Building_CheckCircle_CreditCard_FileUp_GraduationCap_Loader2_MapPin_School_User_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BookOpen,Building,CheckCircle,CreditCard,FileUp,GraduationCap,Loader2,MapPin,School,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/school.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BookOpen_Building_CheckCircle_CreditCard_FileUp_GraduationCap_Loader2_MapPin_School_User_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BookOpen,Building,CheckCircle,CreditCard,FileUp,GraduationCap,Loader2,MapPin,School,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/graduation-cap.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BookOpen_Building_CheckCircle_CreditCard_FileUp_GraduationCap_Loader2_MapPin_School_User_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BookOpen,Building,CheckCircle,CreditCard,FileUp,GraduationCap,Loader2,MapPin,School,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/triangle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BookOpen_Building_CheckCircle_CreditCard_FileUp_GraduationCap_Loader2_MapPin_School_User_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BookOpen,Building,CheckCircle,CreditCard,FileUp,GraduationCap,Loader2,MapPin,School,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BookOpen_Building_CheckCircle_CreditCard_FileUp_GraduationCap_Loader2_MapPin_School_User_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BookOpen,Building,CheckCircle,CreditCard,FileUp,GraduationCap,Loader2,MapPin,School,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BookOpen_Building_CheckCircle_CreditCard_FileUp_GraduationCap_Loader2_MapPin_School_User_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BookOpen,Building,CheckCircle,CreditCard,FileUp,GraduationCap,Loader2,MapPin,School,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/building.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BookOpen_Building_CheckCircle_CreditCard_FileUp_GraduationCap_Loader2_MapPin_School_User_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BookOpen,Building,CheckCircle,CreditCard,FileUp,GraduationCap,Loader2,MapPin,School,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/credit-card.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BookOpen_Building_CheckCircle_CreditCard_FileUp_GraduationCap_Loader2_MapPin_School_User_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BookOpen,Building,CheckCircle,CreditCard,FileUp,GraduationCap,Loader2,MapPin,School,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BookOpen_Building_CheckCircle_CreditCard_FileUp_GraduationCap_Loader2_MapPin_School_User_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BookOpen,Building,CheckCircle,CreditCard,FileUp,GraduationCap,Loader2,MapPin,School,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BookOpen_Building_CheckCircle_CreditCard_FileUp_GraduationCap_Loader2_MapPin_School_User_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BookOpen,Building,CheckCircle,CreditCard,FileUp,GraduationCap,Loader2,MapPin,School,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-up.js\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./hooks/use-toast.ts\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./lib/api.ts\");\n/* harmony import */ var _SuccessMessage__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./SuccessMessage */ \"(app-pages-browser)/./components/scholarship/SuccessMessage.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\nfunction ScholarshipApplicationPage() {\n    _s();\n    const [selectedCategory, setSelectedCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('primary');\n    const [submitting, setSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showSuccess, setShowSuccess] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_9__.useToast)();\n    // Form data for different categories\n    const [primaryFormData, setPrimaryFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        student_full_name: '',\n        age: '',\n        current_class: '',\n        father_name: '',\n        mother_name: '',\n        parent_phone: '',\n        home_address: '',\n        school_name: '',\n        headmaster_name: '',\n        school_account_number: '',\n        reason_for_scholarship: '',\n        current_school_fee: '',\n        supporting_information: ''\n    });\n    const [secondaryFormData, setSecondaryFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        student_full_name: '',\n        age: '',\n        class: '',\n        parent_phone: '',\n        address: '',\n        school_name: '',\n        principal_name: '',\n        principal_account_number: '',\n        reason_for_scholarship: '',\n        school_fee_amount: ''\n    });\n    const [universityFormData, setUniversityFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        full_name: '',\n        age: '',\n        course_of_study: '',\n        current_level: '',\n        phone_number: '',\n        email_address: '',\n        matriculation_number: '',\n        reason_for_scholarship: ''\n    });\n    // File uploads for different categories\n    const [primaryFiles, setPrimaryFiles] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        student_picture: null\n    });\n    const [secondaryFiles, setSecondaryFiles] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        student_picture: null\n    });\n    const [universityFiles, setUniversityFiles] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        student_id_card: null,\n        payment_evidence: null,\n        supporting_documents: []\n    });\n    // Category configuration - Updated to match platform design system\n    const categoryConfig = {\n        primary: {\n            icon: _barrel_optimize_names_AlertTriangle_BookOpen_Building_CheckCircle_CreditCard_FileUp_GraduationCap_Loader2_MapPin_School_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n            title: \"Primary School Scholarship\",\n            description: \"For Primary 1-6 students (Filled by Parent/Guardian)\",\n            color: \"bg-blue-500\",\n            bgColor: \"bg-blue-50\",\n            textColor: \"text-blue-700\",\n            borderColor: \"border-blue-200\"\n        },\n        secondary: {\n            icon: _barrel_optimize_names_AlertTriangle_BookOpen_Building_CheckCircle_CreditCard_FileUp_GraduationCap_Loader2_MapPin_School_User_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n            title: \"Secondary School Scholarship\",\n            description: \"For Secondary school students (Filled by Student)\",\n            color: \"bg-green-500\",\n            bgColor: \"bg-green-50\",\n            textColor: \"text-green-700\",\n            borderColor: \"border-green-200\"\n        },\n        university: {\n            icon: _barrel_optimize_names_AlertTriangle_BookOpen_Building_CheckCircle_CreditCard_FileUp_GraduationCap_Loader2_MapPin_School_User_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"],\n            title: \"University Scholarship\",\n            description: \"For University students (Filled by Student)\",\n            color: \"bg-purple-500\",\n            bgColor: \"bg-purple-50\",\n            textColor: \"text-purple-700\",\n            borderColor: \"border-purple-200\"\n        }\n    };\n    const handleFileChange = (category, field, file)=>{\n        if (category === 'primary') {\n            setPrimaryFiles((prev)=>({\n                    ...prev,\n                    [field]: file\n                }));\n        } else if (category === 'secondary') {\n            setSecondaryFiles((prev)=>({\n                    ...prev,\n                    [field]: file\n                }));\n        } else if (category === 'university') {\n            if (field === 'supporting_documents' && file) {\n                setUniversityFiles((prev)=>({\n                        ...prev,\n                        supporting_documents: [\n                            ...prev.supporting_documents,\n                            file\n                        ]\n                    }));\n            } else {\n                setUniversityFiles((prev)=>({\n                        ...prev,\n                        [field]: file\n                    }));\n            }\n        }\n    };\n    const handleMultipleFileChange = (files)=>{\n        if (files && selectedCategory === 'university') {\n            const fileArray = Array.from(files);\n            setUniversityFiles((prev)=>({\n                    ...prev,\n                    supporting_documents: [\n                        ...prev.supporting_documents,\n                        ...fileArray\n                    ]\n                }));\n        }\n    };\n    const removeFile = (index)=>{\n        setUniversityFiles((prev)=>({\n                ...prev,\n                supporting_documents: prev.supporting_documents.filter((_, i)=>i !== index)\n            }));\n    };\n    const validateForm = ()=>{\n        if (selectedCategory === 'primary') {\n            const required = [\n                'student_full_name',\n                'age',\n                'current_class',\n                'father_name',\n                'mother_name',\n                'parent_phone',\n                'home_address',\n                'school_name',\n                'headmaster_name',\n                'school_account_number',\n                'reason_for_scholarship',\n                'current_school_fee'\n            ];\n            return required.every((field)=>primaryFormData[field].trim() !== '') && primaryFiles.student_picture;\n        } else if (selectedCategory === 'secondary') {\n            const required = [\n                'student_full_name',\n                'age',\n                'class',\n                'parent_phone',\n                'address',\n                'school_name',\n                'principal_name',\n                'principal_account_number',\n                'reason_for_scholarship',\n                'school_fee_amount'\n            ];\n            return required.every((field)=>secondaryFormData[field].trim() !== '') && secondaryFiles.student_picture;\n        } else if (selectedCategory === 'university') {\n            const required = [\n                'full_name',\n                'age',\n                'course_of_study',\n                'current_level',\n                'phone_number',\n                'email_address',\n                'matriculation_number',\n                'reason_for_scholarship'\n            ];\n            return required.every((field)=>universityFormData[field].trim() !== '') && universityFiles.student_id_card && universityFiles.payment_evidence;\n        }\n        return false;\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        if (!validateForm()) {\n            toast({\n                title: \"Validation Error\",\n                description: \"Please fill in all required fields and upload required documents\",\n                variant: \"destructive\"\n            });\n            return;\n        }\n        try {\n            setSubmitting(true);\n            const formData = new FormData();\n            formData.append('category', selectedCategory);\n            // Add form data based on category\n            if (selectedCategory === 'primary') {\n                formData.append('form_data', JSON.stringify(primaryFormData));\n                if (primaryFiles.student_picture) {\n                    formData.append('student_picture', primaryFiles.student_picture);\n                }\n            } else if (selectedCategory === 'secondary') {\n                formData.append('form_data', JSON.stringify(secondaryFormData));\n                if (secondaryFiles.student_picture) {\n                    formData.append('student_picture', secondaryFiles.student_picture);\n                }\n            } else if (selectedCategory === 'university') {\n                formData.append('form_data', JSON.stringify(universityFormData));\n                if (universityFiles.student_id_card) {\n                    formData.append('student_id_card', universityFiles.student_id_card);\n                }\n                if (universityFiles.payment_evidence) {\n                    formData.append('payment_evidence', universityFiles.payment_evidence);\n                }\n                universityFiles.supporting_documents.forEach((file, index)=>{\n                    formData.append(\"supporting_documents[\".concat(index, \"]\"), file);\n                });\n            }\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_10__.apiClient.post('/scholarships/apply', formData, {\n                headers: {\n                    'Content-Type': 'multipart/form-data'\n                }\n            });\n            if (response.success) {\n                // Show success message\n                setShowSuccess(true);\n                // Reset form\n                if (selectedCategory === 'primary') {\n                    setPrimaryFormData({\n                        student_full_name: '',\n                        age: '',\n                        current_class: '',\n                        father_name: '',\n                        mother_name: '',\n                        parent_phone: '',\n                        home_address: '',\n                        school_name: '',\n                        headmaster_name: '',\n                        school_account_number: '',\n                        reason_for_scholarship: '',\n                        current_school_fee: '',\n                        supporting_information: ''\n                    });\n                    setPrimaryFiles({\n                        student_picture: null\n                    });\n                } else if (selectedCategory === 'secondary') {\n                    setSecondaryFormData({\n                        student_full_name: '',\n                        age: '',\n                        class: '',\n                        parent_phone: '',\n                        address: '',\n                        school_name: '',\n                        principal_name: '',\n                        principal_account_number: '',\n                        reason_for_scholarship: '',\n                        school_fee_amount: ''\n                    });\n                    setSecondaryFiles({\n                        student_picture: null\n                    });\n                } else if (selectedCategory === 'university') {\n                    setUniversityFormData({\n                        full_name: '',\n                        age: '',\n                        course_of_study: '',\n                        current_level: '',\n                        phone_number: '',\n                        email_address: '',\n                        matriculation_number: '',\n                        reason_for_scholarship: ''\n                    });\n                    setUniversityFiles({\n                        student_id_card: null,\n                        payment_evidence: null,\n                        supporting_documents: []\n                    });\n                }\n            } else {\n                toast({\n                    title: \"Error\",\n                    description: response.message || \"Failed to submit application\",\n                    variant: \"destructive\"\n                });\n            }\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error('Error submitting application:', error);\n            toast({\n                title: \"Error\",\n                description: ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || \"Failed to submit application\",\n                variant: \"destructive\"\n            });\n        } finally{\n            setSubmitting(false);\n        }\n    };\n    const handleBackToForm = ()=>{\n        setShowSuccess(false);\n    };\n    const handleGoHome = ()=>{\n        // Navigate to dashboard or home page\n        window.location.href = '/dashboard';\n    };\n    if (showSuccess) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_SuccessMessage__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n            onBackToForm: handleBackToForm,\n            onGoHome: handleGoHome\n        }, void 0, false, {\n            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n            lineNumber: 320,\n            columnNumber: 12\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gradient-to-r from-blue-600 via-indigo-600 to-purple-600 text-white py-16\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container mx-auto px-4 text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-4xl md:text-6xl font-bold mb-6\",\n                            children: \"Scholarship Application\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                            lineNumber: 328,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xl md:text-2xl mb-8 text-blue-100\",\n                            children: \"Choose your scholarship category and complete the application form\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                            lineNumber: 331,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                    lineNumber: 327,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                lineNumber: 326,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container mx-auto px-4 py-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                    className: \"bg-white/80 backdrop-blur-sm border-gray-200 shadow-xl overflow-hidden\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                            className: \"bg-white border-b border-gray-200\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                    className: \"text-2xl md:text-3xl font-bold text-gray-900\",\n                                    children: \"Apply for Scholarship\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                    lineNumber: 341,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                    className: \"text-gray-600 text-base md:text-lg\",\n                                    children: \"Select your education level and fill out the appropriate form\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                    lineNumber: 342,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                            lineNumber: 340,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            className: \"p-6 md:p-8\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_8__.Tabs, {\n                                value: selectedCategory,\n                                onValueChange: (value)=>setSelectedCategory(value),\n                                className: \"w-full\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_8__.TabsList, {\n                                        className: \"grid w-full grid-cols-1 md:grid-cols-3 mb-8 h-auto p-2 gap-2 bg-gray-100 rounded-lg\",\n                                        children: Object.entries(categoryConfig).map((param)=>{\n                                            let [key, config] = param;\n                                            const IconComponent = config.icon;\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_8__.TabsTrigger, {\n                                                value: key,\n                                                className: \"flex flex-col items-center p-4 space-y-3 rounded-lg transition-all duration-300 hover:scale-105 data-[state=active]:bg-white data-[state=active]:shadow-lg data-[state=active]:\".concat(config.borderColor, \" data-[state=active]:border-2\"),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"p-3 rounded-full \".concat(config.color, \" text-white shadow-lg\"),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(IconComponent, {\n                                                            className: \"h-6 w-6\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                            lineNumber: 359,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                        lineNumber: 358,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"font-bold text-sm text-gray-900 leading-tight\",\n                                                                children: config.title\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                lineNumber: 362,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs text-gray-600 mt-1\",\n                                                                children: config.description\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                lineNumber: 363,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                        lineNumber: 361,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, key, true, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                lineNumber: 353,\n                                                columnNumber: 21\n                                            }, this);\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                        lineNumber: 349,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_8__.TabsContent, {\n                                        value: \"primary\",\n                                        className: \"space-y-8\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                            onSubmit: handleSubmit,\n                                            className: \"space-y-8\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-blue-50 p-6 rounded-lg border-2 border-blue-200 shadow-sm\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BookOpen_Building_CheckCircle_CreditCard_FileUp_GraduationCap_Loader2_MapPin_School_User_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                className: \"h-6 w-6 text-blue-600 mr-3\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                lineNumber: 375,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-blue-800 font-semibold text-lg\",\n                                                                children: \"This form should be filled by a Parent or Guardian on behalf of the student\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                lineNumber: 376,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                        lineNumber: 374,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                    lineNumber: 373,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-1 lg:grid-cols-2 gap-8\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                                            className: \"border-2 border-blue-200 bg-blue-50/50 shadow-sm hover:shadow-md transition-shadow\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                                                    className: \"pb-4\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                                        className: \"text-xl font-bold text-gray-900 flex items-center\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BookOpen_Building_CheckCircle_CreditCard_FileUp_GraduationCap_Loader2_MapPin_School_User_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                                className: \"h-6 w-6 mr-3 text-blue-600\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                lineNumber: 387,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            \"Student Information\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                        lineNumber: 386,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                    lineNumber: 385,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                                                    className: \"space-y-4\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                                                    htmlFor: \"student_full_name\",\n                                                                                    children: \"Student Full Name *\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                    lineNumber: 394,\n                                                                                    columnNumber: 25\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                                    id: \"student_full_name\",\n                                                                                    value: primaryFormData.student_full_name,\n                                                                                    onChange: (e)=>setPrimaryFormData((prev)=>({\n                                                                                                ...prev,\n                                                                                                student_full_name: e.target.value\n                                                                                            })),\n                                                                                    placeholder: \"Enter student's full name\",\n                                                                                    className: \"mt-1\",\n                                                                                    required: true\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                    lineNumber: 395,\n                                                                                    columnNumber: 25\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                            lineNumber: 393,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"grid grid-cols-2 gap-4\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                                                            htmlFor: \"age\",\n                                                                                            children: \"Age *\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                            lineNumber: 407,\n                                                                                            columnNumber: 27\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                                            id: \"age\",\n                                                                                            type: \"number\",\n                                                                                            value: primaryFormData.age,\n                                                                                            onChange: (e)=>setPrimaryFormData((prev)=>({\n                                                                                                        ...prev,\n                                                                                                        age: e.target.value\n                                                                                                    })),\n                                                                                            placeholder: \"Age\",\n                                                                                            className: \"mt-1\",\n                                                                                            required: true\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                            lineNumber: 408,\n                                                                                            columnNumber: 27\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                    lineNumber: 406,\n                                                                                    columnNumber: 25\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                                                            htmlFor: \"current_class\",\n                                                                                            children: \"Current Class *\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                            lineNumber: 419,\n                                                                                            columnNumber: 27\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.Select, {\n                                                                                            value: primaryFormData.current_class,\n                                                                                            onValueChange: (value)=>setPrimaryFormData((prev)=>({\n                                                                                                        ...prev,\n                                                                                                        current_class: value\n                                                                                                    })),\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectTrigger, {\n                                                                                                    className: \"mt-1\",\n                                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectValue, {\n                                                                                                        placeholder: \"Select class\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                                        lineNumber: 422,\n                                                                                                        columnNumber: 31\n                                                                                                    }, this)\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                                    lineNumber: 421,\n                                                                                                    columnNumber: 29\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectContent, {\n                                                                                                    children: [\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                                                            value: \"Primary 1\",\n                                                                                                            children: \"Primary 1\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                                            lineNumber: 425,\n                                                                                                            columnNumber: 31\n                                                                                                        }, this),\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                                                            value: \"Primary 2\",\n                                                                                                            children: \"Primary 2\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                                            lineNumber: 426,\n                                                                                                            columnNumber: 31\n                                                                                                        }, this),\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                                                            value: \"Primary 3\",\n                                                                                                            children: \"Primary 3\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                                            lineNumber: 427,\n                                                                                                            columnNumber: 31\n                                                                                                        }, this),\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                                                            value: \"Primary 4\",\n                                                                                                            children: \"Primary 4\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                                            lineNumber: 428,\n                                                                                                            columnNumber: 31\n                                                                                                        }, this),\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                                                            value: \"Primary 5\",\n                                                                                                            children: \"Primary 5\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                                            lineNumber: 429,\n                                                                                                            columnNumber: 31\n                                                                                                        }, this),\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                                                            value: \"Primary 6\",\n                                                                                                            children: \"Primary 6\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                                            lineNumber: 430,\n                                                                                                            columnNumber: 31\n                                                                                                        }, this)\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                                    lineNumber: 424,\n                                                                                                    columnNumber: 29\n                                                                                                }, this)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                            lineNumber: 420,\n                                                                                            columnNumber: 27\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                    lineNumber: 418,\n                                                                                    columnNumber: 25\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                            lineNumber: 405,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                    lineNumber: 391,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                            lineNumber: 384,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                                            className: \"border-2 border-green-200 bg-green-50/50 shadow-sm hover:shadow-md transition-shadow\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                                                    className: \"pb-4\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                                        className: \"text-xl font-bold text-gray-900 flex items-center\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BookOpen_Building_CheckCircle_CreditCard_FileUp_GraduationCap_Loader2_MapPin_School_User_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                                className: \"h-6 w-6 mr-3 text-green-600\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                lineNumber: 442,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            \"Parent/Guardian Information\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                        lineNumber: 441,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                    lineNumber: 440,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                                                    className: \"space-y-4\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                                                    htmlFor: \"father_name\",\n                                                                                    children: \"Father's Name *\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                    lineNumber: 449,\n                                                                                    columnNumber: 25\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                                    id: \"father_name\",\n                                                                                    value: primaryFormData.father_name,\n                                                                                    onChange: (e)=>setPrimaryFormData((prev)=>({\n                                                                                                ...prev,\n                                                                                                father_name: e.target.value\n                                                                                            })),\n                                                                                    placeholder: \"Enter father's name\",\n                                                                                    className: \"mt-1\",\n                                                                                    required: true\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                    lineNumber: 450,\n                                                                                    columnNumber: 25\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                            lineNumber: 448,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                                                    htmlFor: \"mother_name\",\n                                                                                    children: \"Mother's Name *\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                    lineNumber: 461,\n                                                                                    columnNumber: 25\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                                    id: \"mother_name\",\n                                                                                    value: primaryFormData.mother_name,\n                                                                                    onChange: (e)=>setPrimaryFormData((prev)=>({\n                                                                                                ...prev,\n                                                                                                mother_name: e.target.value\n                                                                                            })),\n                                                                                    placeholder: \"Enter mother's name\",\n                                                                                    className: \"mt-1\",\n                                                                                    required: true\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                    lineNumber: 462,\n                                                                                    columnNumber: 25\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                            lineNumber: 460,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                                                    htmlFor: \"parent_phone\",\n                                                                                    children: \"Father's or Mother's Phone Number *\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                    lineNumber: 473,\n                                                                                    columnNumber: 25\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                                    id: \"parent_phone\",\n                                                                                    type: \"tel\",\n                                                                                    value: primaryFormData.parent_phone,\n                                                                                    onChange: (e)=>setPrimaryFormData((prev)=>({\n                                                                                                ...prev,\n                                                                                                parent_phone: e.target.value\n                                                                                            })),\n                                                                                    placeholder: \"Enter phone number\",\n                                                                                    className: \"mt-1\",\n                                                                                    required: true\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                    lineNumber: 474,\n                                                                                    columnNumber: 25\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                            lineNumber: 472,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                    lineNumber: 446,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                            lineNumber: 439,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                    lineNumber: 382,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-8\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                                            className: \"border-2 border-red-200 bg-red-50/50 shadow-sm hover:shadow-md transition-shadow\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                                                    className: \"pb-4\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                                        className: \"text-xl font-bold text-gray-900 flex items-center\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BookOpen_Building_CheckCircle_CreditCard_FileUp_GraduationCap_Loader2_MapPin_School_User_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                                className: \"h-6 w-6 mr-3 text-red-600\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                lineNumber: 493,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            \"Address Information\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                        lineNumber: 492,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                    lineNumber: 491,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                                                    className: \"space-y-4\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                                                htmlFor: \"home_address\",\n                                                                                children: \"Home Address *\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                lineNumber: 499,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_6__.Textarea, {\n                                                                                id: \"home_address\",\n                                                                                value: primaryFormData.home_address,\n                                                                                onChange: (e)=>setPrimaryFormData((prev)=>({\n                                                                                            ...prev,\n                                                                                            home_address: e.target.value\n                                                                                        })),\n                                                                                placeholder: \"Enter complete home address\",\n                                                                                className: \"mt-1\",\n                                                                                rows: 3,\n                                                                                required: true\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                lineNumber: 500,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                        lineNumber: 498,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                    lineNumber: 497,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                            lineNumber: 490,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                                            className: \"border-2 border-purple-200 bg-purple-50/50 shadow-sm hover:shadow-md transition-shadow\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                                                    className: \"pb-4\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                                        className: \"text-xl font-bold text-gray-900 flex items-center\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BookOpen_Building_CheckCircle_CreditCard_FileUp_GraduationCap_Loader2_MapPin_School_User_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                                className: \"h-6 w-6 mr-3 text-purple-600\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                lineNumber: 516,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            \"School Information\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                        lineNumber: 515,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                    lineNumber: 514,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                                                    className: \"space-y-4\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                                                    htmlFor: \"school_name\",\n                                                                                    children: \"Name of the School *\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                    lineNumber: 523,\n                                                                                    columnNumber: 25\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                                    id: \"school_name\",\n                                                                                    value: primaryFormData.school_name,\n                                                                                    onChange: (e)=>setPrimaryFormData((prev)=>({\n                                                                                                ...prev,\n                                                                                                school_name: e.target.value\n                                                                                            })),\n                                                                                    placeholder: \"Enter school name\",\n                                                                                    className: \"mt-1\",\n                                                                                    required: true\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                    lineNumber: 524,\n                                                                                    columnNumber: 25\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                            lineNumber: 522,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                                                    htmlFor: \"headmaster_name\",\n                                                                                    children: \"Name of Headmaster/Director *\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                    lineNumber: 535,\n                                                                                    columnNumber: 25\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                                    id: \"headmaster_name\",\n                                                                                    value: primaryFormData.headmaster_name,\n                                                                                    onChange: (e)=>setPrimaryFormData((prev)=>({\n                                                                                                ...prev,\n                                                                                                headmaster_name: e.target.value\n                                                                                            })),\n                                                                                    placeholder: \"Enter headmaster's name\",\n                                                                                    className: \"mt-1\",\n                                                                                    required: true\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                    lineNumber: 536,\n                                                                                    columnNumber: 25\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                            lineNumber: 534,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                                                    htmlFor: \"school_account_number\",\n                                                                                    children: \"School Account Number *\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                    lineNumber: 547,\n                                                                                    columnNumber: 25\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                                    id: \"school_account_number\",\n                                                                                    value: primaryFormData.school_account_number,\n                                                                                    onChange: (e)=>setPrimaryFormData((prev)=>({\n                                                                                                ...prev,\n                                                                                                school_account_number: e.target.value\n                                                                                            })),\n                                                                                    placeholder: \"Enter account number for scholarship payment\",\n                                                                                    className: \"mt-1\",\n                                                                                    required: true\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                    lineNumber: 548,\n                                                                                    columnNumber: 25\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                            lineNumber: 546,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                    lineNumber: 520,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                            lineNumber: 513,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                    lineNumber: 489,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                                    className: \"border-2 border-orange-200 bg-orange-50/50 shadow-sm hover:shadow-md transition-shadow\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                                            className: \"pb-4\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                                className: \"text-xl font-bold text-gray-900 flex items-center\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BookOpen_Building_CheckCircle_CreditCard_FileUp_GraduationCap_Loader2_MapPin_School_User_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                        className: \"h-6 w-6 mr-3 text-orange-600\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                        lineNumber: 565,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    \"Financial & Additional Information\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                lineNumber: 564,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                            lineNumber: 563,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                                            className: \"space-y-6\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                                                    htmlFor: \"current_school_fee\",\n                                                                                    children: \"Current School Fee Amount *\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                    lineNumber: 573,\n                                                                                    columnNumber: 25\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                                    id: \"current_school_fee\",\n                                                                                    type: \"number\",\n                                                                                    value: primaryFormData.current_school_fee,\n                                                                                    onChange: (e)=>setPrimaryFormData((prev)=>({\n                                                                                                ...prev,\n                                                                                                current_school_fee: e.target.value\n                                                                                            })),\n                                                                                    placeholder: \"Enter amount in Naira\",\n                                                                                    className: \"mt-1\",\n                                                                                    required: true\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                    lineNumber: 574,\n                                                                                    columnNumber: 25\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                            lineNumber: 572,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                                                    htmlFor: \"student_picture\",\n                                                                                    children: \"Upload Student Picture *\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                    lineNumber: 586,\n                                                                                    columnNumber: 25\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"mt-1\",\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                                        id: \"student_picture\",\n                                                                                        type: \"file\",\n                                                                                        accept: \"image/*\",\n                                                                                        onChange: (e)=>{\n                                                                                            var _e_target_files;\n                                                                                            return handleFileChange('primary', 'student_picture', ((_e_target_files = e.target.files) === null || _e_target_files === void 0 ? void 0 : _e_target_files[0]) || null);\n                                                                                        },\n                                                                                        className: \"file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100\",\n                                                                                        required: true\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                        lineNumber: 588,\n                                                                                        columnNumber: 27\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                    lineNumber: 587,\n                                                                                    columnNumber: 25\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                            lineNumber: 585,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                    lineNumber: 571,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                                            htmlFor: \"reason_for_scholarship\",\n                                                                            children: \"Reason for the Scholarship *\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                            lineNumber: 601,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_6__.Textarea, {\n                                                                            id: \"reason_for_scholarship\",\n                                                                            value: primaryFormData.reason_for_scholarship,\n                                                                            onChange: (e)=>setPrimaryFormData((prev)=>({\n                                                                                        ...prev,\n                                                                                        reason_for_scholarship: e.target.value\n                                                                                    })),\n                                                                            placeholder: \"Please explain why your child needs this scholarship\",\n                                                                            className: \"mt-1\",\n                                                                            rows: 4,\n                                                                            required: true\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                            lineNumber: 602,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                    lineNumber: 600,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                                            htmlFor: \"supporting_information\",\n                                                                            children: \"Any Other Supporting Information\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                            lineNumber: 614,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_6__.Textarea, {\n                                                                            id: \"supporting_information\",\n                                                                            value: primaryFormData.supporting_information,\n                                                                            onChange: (e)=>setPrimaryFormData((prev)=>({\n                                                                                        ...prev,\n                                                                                        supporting_information: e.target.value\n                                                                                    })),\n                                                                            placeholder: \"Any additional information that supports your application\",\n                                                                            className: \"mt-1\",\n                                                                            rows: 3\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                            lineNumber: 615,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                    lineNumber: 613,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                            lineNumber: 569,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                    lineNumber: 562,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-center pt-8\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                        type: \"submit\",\n                                                        disabled: submitting || !validateForm(),\n                                                        size: \"lg\",\n                                                        className: \"bg-gradient-to-r from-blue-600 via-indigo-600 to-purple-600 hover:from-blue-700 hover:via-indigo-700 hover:to-purple-700 text-white px-12 py-4 text-lg font-bold shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105\",\n                                                        children: submitting ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BookOpen_Building_CheckCircle_CreditCard_FileUp_GraduationCap_Loader2_MapPin_School_User_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                    className: \"mr-3 h-6 w-6 animate-spin\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                    lineNumber: 636,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                \"Submitting Application...\"\n                                                            ]\n                                                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BookOpen_Building_CheckCircle_CreditCard_FileUp_GraduationCap_Loader2_MapPin_School_User_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                    className: \"mr-3 h-6 w-6\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                    lineNumber: 641,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                \"Submit Primary School Application\"\n                                                            ]\n                                                        }, void 0, true)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                        lineNumber: 628,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                    lineNumber: 627,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                            lineNumber: 372,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                        lineNumber: 371,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_8__.TabsContent, {\n                                        value: \"secondary\",\n                                        className: \"space-y-8\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                            onSubmit: handleSubmit,\n                                            className: \"space-y-8\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-green-50 p-6 rounded-lg border-2 border-green-200 shadow-sm\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BookOpen_Building_CheckCircle_CreditCard_FileUp_GraduationCap_Loader2_MapPin_School_User_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                className: \"h-6 w-6 text-green-600 mr-3\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                lineNumber: 655,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-green-800 font-semibold text-lg\",\n                                                                children: \"This form should be filled by the Student directly\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                lineNumber: 656,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                        lineNumber: 654,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                    lineNumber: 653,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"text-lg font-semibold text-gray-900 flex items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BookOpen_Building_CheckCircle_CreditCard_FileUp_GraduationCap_Loader2_MapPin_School_User_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                            className: \"h-5 w-5 mr-2 text-green-600\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                            lineNumber: 666,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        \"Student Information\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                    lineNumber: 665,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                                            htmlFor: \"sec_student_full_name\",\n                                                                            children: \"Student Full Name *\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                            lineNumber: 671,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                            id: \"sec_student_full_name\",\n                                                                            value: secondaryFormData.student_full_name,\n                                                                            onChange: (e)=>setSecondaryFormData((prev)=>({\n                                                                                        ...prev,\n                                                                                        student_full_name: e.target.value\n                                                                                    })),\n                                                                            placeholder: \"Enter your full name\",\n                                                                            className: \"mt-1\",\n                                                                            required: true\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                            lineNumber: 672,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                    lineNumber: 670,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"grid grid-cols-2 gap-4\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                                                    htmlFor: \"sec_age\",\n                                                                                    children: \"Age *\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                    lineNumber: 684,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                                    id: \"sec_age\",\n                                                                                    type: \"number\",\n                                                                                    value: secondaryFormData.age,\n                                                                                    onChange: (e)=>setSecondaryFormData((prev)=>({\n                                                                                                ...prev,\n                                                                                                age: e.target.value\n                                                                                            })),\n                                                                                    placeholder: \"Your age\",\n                                                                                    className: \"mt-1\",\n                                                                                    required: true\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                    lineNumber: 685,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                            lineNumber: 683,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                                                    htmlFor: \"sec_class\",\n                                                                                    children: \"Class *\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                    lineNumber: 696,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.Select, {\n                                                                                    value: secondaryFormData.class,\n                                                                                    onValueChange: (value)=>setSecondaryFormData((prev)=>({\n                                                                                                ...prev,\n                                                                                                class: value\n                                                                                            })),\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectTrigger, {\n                                                                                            className: \"mt-1\",\n                                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectValue, {\n                                                                                                placeholder: \"Select class\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                                lineNumber: 699,\n                                                                                                columnNumber: 31\n                                                                                            }, this)\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                            lineNumber: 698,\n                                                                                            columnNumber: 29\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectContent, {\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                                                    value: \"JSS 1\",\n                                                                                                    children: \"JSS 1\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                                    lineNumber: 702,\n                                                                                                    columnNumber: 31\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                                                    value: \"JSS 2\",\n                                                                                                    children: \"JSS 2\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                                    lineNumber: 703,\n                                                                                                    columnNumber: 31\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                                                    value: \"JSS 3\",\n                                                                                                    children: \"JSS 3\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                                    lineNumber: 704,\n                                                                                                    columnNumber: 31\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                                                    value: \"SS 1\",\n                                                                                                    children: \"SS 1\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                                    lineNumber: 705,\n                                                                                                    columnNumber: 31\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                                                    value: \"SS 2\",\n                                                                                                    children: \"SS 2\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                                    lineNumber: 706,\n                                                                                                    columnNumber: 31\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                                                    value: \"SS 3\",\n                                                                                                    children: \"SS 3\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                                    lineNumber: 707,\n                                                                                                    columnNumber: 31\n                                                                                                }, this)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                            lineNumber: 701,\n                                                                                            columnNumber: 29\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                    lineNumber: 697,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                            lineNumber: 695,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                    lineNumber: 682,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                                            htmlFor: \"sec_parent_phone\",\n                                                                            children: \"Parent's Phone Number *\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                            lineNumber: 714,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                            id: \"sec_parent_phone\",\n                                                                            type: \"tel\",\n                                                                            value: secondaryFormData.parent_phone,\n                                                                            onChange: (e)=>setSecondaryFormData((prev)=>({\n                                                                                        ...prev,\n                                                                                        parent_phone: e.target.value\n                                                                                    })),\n                                                                            placeholder: \"Enter parent's phone number\",\n                                                                            className: \"mt-1\",\n                                                                            required: true\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                            lineNumber: 715,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                    lineNumber: 713,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                            lineNumber: 664,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"text-lg font-semibold text-gray-900 flex items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BookOpen_Building_CheckCircle_CreditCard_FileUp_GraduationCap_Loader2_MapPin_School_User_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                            className: \"h-5 w-5 mr-2 text-red-600\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                            lineNumber: 730,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        \"Address & School Information\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                    lineNumber: 729,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                                            htmlFor: \"sec_address\",\n                                                                            children: \"Address *\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                            lineNumber: 735,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_6__.Textarea, {\n                                                                            id: \"sec_address\",\n                                                                            value: secondaryFormData.address,\n                                                                            onChange: (e)=>setSecondaryFormData((prev)=>({\n                                                                                        ...prev,\n                                                                                        address: e.target.value\n                                                                                    })),\n                                                                            placeholder: \"Enter your complete address\",\n                                                                            className: \"mt-1\",\n                                                                            rows: 3,\n                                                                            required: true\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                            lineNumber: 736,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                    lineNumber: 734,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                                            htmlFor: \"sec_school_name\",\n                                                                            children: \"School Name *\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                            lineNumber: 748,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                            id: \"sec_school_name\",\n                                                                            value: secondaryFormData.school_name,\n                                                                            onChange: (e)=>setSecondaryFormData((prev)=>({\n                                                                                        ...prev,\n                                                                                        school_name: e.target.value\n                                                                                    })),\n                                                                            placeholder: \"Enter your school name\",\n                                                                            className: \"mt-1\",\n                                                                            required: true\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                            lineNumber: 749,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                    lineNumber: 747,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                                            htmlFor: \"sec_principal_name\",\n                                                                            children: \"School Principal or Director Name *\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                            lineNumber: 760,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                            id: \"sec_principal_name\",\n                                                                            value: secondaryFormData.principal_name,\n                                                                            onChange: (e)=>setSecondaryFormData((prev)=>({\n                                                                                        ...prev,\n                                                                                        principal_name: e.target.value\n                                                                                    })),\n                                                                            placeholder: \"Enter principal's name\",\n                                                                            className: \"mt-1\",\n                                                                            required: true\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                            lineNumber: 761,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                    lineNumber: 759,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                            lineNumber: 728,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                    lineNumber: 662,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-lg font-semibold text-gray-900 flex items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BookOpen_Building_CheckCircle_CreditCard_FileUp_GraduationCap_Loader2_MapPin_School_User_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                    className: \"h-5 w-5 mr-2 text-orange-600\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                    lineNumber: 776,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                \"Financial Information\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                            lineNumber: 775,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                                            htmlFor: \"sec_principal_account_number\",\n                                                                            children: \"Principal or Financial Officer Account Number *\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                            lineNumber: 782,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                            id: \"sec_principal_account_number\",\n                                                                            value: secondaryFormData.principal_account_number,\n                                                                            onChange: (e)=>setSecondaryFormData((prev)=>({\n                                                                                        ...prev,\n                                                                                        principal_account_number: e.target.value\n                                                                                    })),\n                                                                            placeholder: \"Enter account number for scholarship payment\",\n                                                                            className: \"mt-1\",\n                                                                            required: true\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                            lineNumber: 783,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                    lineNumber: 781,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                                            htmlFor: \"sec_school_fee_amount\",\n                                                                            children: \"School Fee Amount *\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                            lineNumber: 794,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                            id: \"sec_school_fee_amount\",\n                                                                            type: \"number\",\n                                                                            value: secondaryFormData.school_fee_amount,\n                                                                            onChange: (e)=>setSecondaryFormData((prev)=>({\n                                                                                        ...prev,\n                                                                                        school_fee_amount: e.target.value\n                                                                                    })),\n                                                                            placeholder: \"Enter amount in Naira\",\n                                                                            className: \"mt-1\",\n                                                                            required: true\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                            lineNumber: 795,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                    lineNumber: 793,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                            lineNumber: 780,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                    lineNumber: 774,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                                        htmlFor: \"sec_student_picture\",\n                                                                        children: \"Upload Student Picture *\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                        lineNumber: 812,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"mt-1\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                            id: \"sec_student_picture\",\n                                                                            type: \"file\",\n                                                                            accept: \"image/*\",\n                                                                            onChange: (e)=>{\n                                                                                var _e_target_files;\n                                                                                return handleFileChange('secondary', 'student_picture', ((_e_target_files = e.target.files) === null || _e_target_files === void 0 ? void 0 : _e_target_files[0]) || null);\n                                                                            },\n                                                                            className: \"file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-green-50 file:text-green-700 hover:file:bg-green-100\",\n                                                                            required: true\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                            lineNumber: 814,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                        lineNumber: 813,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                lineNumber: 811,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                            lineNumber: 810,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                                    htmlFor: \"sec_reason_for_scholarship\",\n                                                                    children: \"Reason for the Scholarship *\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                    lineNumber: 827,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_6__.Textarea, {\n                                                                    id: \"sec_reason_for_scholarship\",\n                                                                    value: secondaryFormData.reason_for_scholarship,\n                                                                    onChange: (e)=>setSecondaryFormData((prev)=>({\n                                                                                ...prev,\n                                                                                reason_for_scholarship: e.target.value\n                                                                            })),\n                                                                    placeholder: \"Please explain why you need this scholarship\",\n                                                                    className: \"mt-1\",\n                                                                    rows: 4,\n                                                                    required: true\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                    lineNumber: 828,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                            lineNumber: 826,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                    lineNumber: 809,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-end pt-6\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                        type: \"submit\",\n                                                        disabled: submitting || !validateForm(),\n                                                        className: \"bg-green-600 hover:bg-green-700 text-white px-8 py-3 text-lg font-semibold\",\n                                                        children: submitting ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BookOpen_Building_CheckCircle_CreditCard_FileUp_GraduationCap_Loader2_MapPin_School_User_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                    className: \"mr-2 h-5 w-5 animate-spin\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                    lineNumber: 848,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                \"Submitting...\"\n                                                            ]\n                                                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BookOpen_Building_CheckCircle_CreditCard_FileUp_GraduationCap_Loader2_MapPin_School_User_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                    className: \"mr-2 h-5 w-5\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                    lineNumber: 853,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                \"Submit Application\"\n                                                            ]\n                                                        }, void 0, true)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                        lineNumber: 841,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                    lineNumber: 840,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                            lineNumber: 652,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                        lineNumber: 651,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_8__.TabsContent, {\n                                        value: \"university\",\n                                        className: \"space-y-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                            onSubmit: handleSubmit,\n                                            className: \"space-y-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-purple-50 p-4 rounded-lg border-l-4 border-purple-500\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BookOpen_Building_CheckCircle_CreditCard_FileUp_GraduationCap_Loader2_MapPin_School_User_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                className: \"h-5 w-5 text-purple-600 mr-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                lineNumber: 867,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-purple-800 font-medium\",\n                                                                children: \"This form should be filled by the University Student directly\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                lineNumber: 868,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                        lineNumber: 866,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                    lineNumber: 865,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"text-lg font-semibold text-gray-900 flex items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BookOpen_Building_CheckCircle_CreditCard_FileUp_GraduationCap_Loader2_MapPin_School_User_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                            className: \"h-5 w-5 mr-2 text-purple-600\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                            lineNumber: 878,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        \"Personal Information\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                    lineNumber: 877,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                                            htmlFor: \"uni_full_name\",\n                                                                            children: \"Full Name *\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                            lineNumber: 883,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                            id: \"uni_full_name\",\n                                                                            value: universityFormData.full_name,\n                                                                            onChange: (e)=>setUniversityFormData((prev)=>({\n                                                                                        ...prev,\n                                                                                        full_name: e.target.value\n                                                                                    })),\n                                                                            placeholder: \"Enter your full name\",\n                                                                            className: \"mt-1\",\n                                                                            required: true\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                            lineNumber: 884,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                    lineNumber: 882,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                                            htmlFor: \"uni_age\",\n                                                                            children: \"Age *\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                            lineNumber: 895,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                            id: \"uni_age\",\n                                                                            type: \"number\",\n                                                                            value: universityFormData.age,\n                                                                            onChange: (e)=>setUniversityFormData((prev)=>({\n                                                                                        ...prev,\n                                                                                        age: e.target.value\n                                                                                    })),\n                                                                            placeholder: \"Your age\",\n                                                                            className: \"mt-1\",\n                                                                            required: true\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                            lineNumber: 896,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                    lineNumber: 894,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                                            htmlFor: \"uni_phone_number\",\n                                                                            children: \"Phone Number *\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                            lineNumber: 908,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                            id: \"uni_phone_number\",\n                                                                            type: \"tel\",\n                                                                            value: universityFormData.phone_number,\n                                                                            onChange: (e)=>setUniversityFormData((prev)=>({\n                                                                                        ...prev,\n                                                                                        phone_number: e.target.value\n                                                                                    })),\n                                                                            placeholder: \"Enter your phone number\",\n                                                                            className: \"mt-1\",\n                                                                            required: true\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                            lineNumber: 909,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                    lineNumber: 907,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                                            htmlFor: \"uni_email_address\",\n                                                                            children: \"Email Address *\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                            lineNumber: 921,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                            id: \"uni_email_address\",\n                                                                            type: \"email\",\n                                                                            value: universityFormData.email_address,\n                                                                            onChange: (e)=>setUniversityFormData((prev)=>({\n                                                                                        ...prev,\n                                                                                        email_address: e.target.value\n                                                                                    })),\n                                                                            placeholder: \"Enter your email address\",\n                                                                            className: \"mt-1\",\n                                                                            required: true\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                            lineNumber: 922,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                    lineNumber: 920,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                            lineNumber: 876,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"text-lg font-semibold text-gray-900 flex items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BookOpen_Building_CheckCircle_CreditCard_FileUp_GraduationCap_Loader2_MapPin_School_User_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                            className: \"h-5 w-5 mr-2 text-blue-600\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                            lineNumber: 937,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        \"Academic Information\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                    lineNumber: 936,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                                            htmlFor: \"uni_course_of_study\",\n                                                                            children: \"Course of Study *\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                            lineNumber: 942,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                            id: \"uni_course_of_study\",\n                                                                            value: universityFormData.course_of_study,\n                                                                            onChange: (e)=>setUniversityFormData((prev)=>({\n                                                                                        ...prev,\n                                                                                        course_of_study: e.target.value\n                                                                                    })),\n                                                                            placeholder: \"Enter your course of study\",\n                                                                            className: \"mt-1\",\n                                                                            required: true\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                            lineNumber: 943,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                    lineNumber: 941,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                                            htmlFor: \"uni_current_level\",\n                                                                            children: \"Current Level *\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                            lineNumber: 954,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.Select, {\n                                                                            value: universityFormData.current_level,\n                                                                            onValueChange: (value)=>setUniversityFormData((prev)=>({\n                                                                                        ...prev,\n                                                                                        current_level: value\n                                                                                    })),\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectTrigger, {\n                                                                                    className: \"mt-1\",\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectValue, {\n                                                                                        placeholder: \"Select your current level\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                        lineNumber: 957,\n                                                                                        columnNumber: 29\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                    lineNumber: 956,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectContent, {\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                                            value: \"100L\",\n                                                                                            children: \"100 Level\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                            lineNumber: 960,\n                                                                                            columnNumber: 29\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                                            value: \"200L\",\n                                                                                            children: \"200 Level\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                            lineNumber: 961,\n                                                                                            columnNumber: 29\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                                            value: \"300L\",\n                                                                                            children: \"300 Level\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                            lineNumber: 962,\n                                                                                            columnNumber: 29\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                                            value: \"400L\",\n                                                                                            children: \"400 Level\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                            lineNumber: 963,\n                                                                                            columnNumber: 29\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                                            value: \"500L\",\n                                                                                            children: \"500 Level\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                            lineNumber: 964,\n                                                                                            columnNumber: 29\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                                            value: \"600L\",\n                                                                                            children: \"600 Level\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                            lineNumber: 965,\n                                                                                            columnNumber: 29\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                    lineNumber: 959,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                            lineNumber: 955,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                    lineNumber: 953,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                                            htmlFor: \"uni_matriculation_number\",\n                                                                            children: \"Matriculation Number *\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                            lineNumber: 971,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                            id: \"uni_matriculation_number\",\n                                                                            value: universityFormData.matriculation_number,\n                                                                            onChange: (e)=>setUniversityFormData((prev)=>({\n                                                                                        ...prev,\n                                                                                        matriculation_number: e.target.value\n                                                                                    })),\n                                                                            placeholder: \"Enter your matriculation number\",\n                                                                            className: \"mt-1\",\n                                                                            required: true\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                            lineNumber: 972,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                    lineNumber: 970,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                            lineNumber: 935,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                    lineNumber: 874,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-lg font-semibold text-gray-900 flex items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BookOpen_Building_CheckCircle_CreditCard_FileUp_GraduationCap_Loader2_MapPin_School_User_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                    className: \"h-5 w-5 mr-2 text-orange-600\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                    lineNumber: 987,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                \"Required Documents\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                            lineNumber: 986,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                                            htmlFor: \"uni_student_id_card\",\n                                                                            children: \"Upload Student ID Card *\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                            lineNumber: 993,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"mt-1\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                                id: \"uni_student_id_card\",\n                                                                                type: \"file\",\n                                                                                accept: \"image/*,.pdf\",\n                                                                                onChange: (e)=>{\n                                                                                    var _e_target_files;\n                                                                                    return handleFileChange('university', 'student_id_card', ((_e_target_files = e.target.files) === null || _e_target_files === void 0 ? void 0 : _e_target_files[0]) || null);\n                                                                                },\n                                                                                className: \"file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-purple-50 file:text-purple-700 hover:file:bg-purple-100\",\n                                                                                required: true\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                lineNumber: 995,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                            lineNumber: 994,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                    lineNumber: 992,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                                            htmlFor: \"uni_payment_evidence\",\n                                                                            children: \"Upload Payment Evidence (Remita/Screenshot) *\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                            lineNumber: 1007,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"mt-1\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                                id: \"uni_payment_evidence\",\n                                                                                type: \"file\",\n                                                                                accept: \"image/*,.pdf\",\n                                                                                onChange: (e)=>{\n                                                                                    var _e_target_files;\n                                                                                    return handleFileChange('university', 'payment_evidence', ((_e_target_files = e.target.files) === null || _e_target_files === void 0 ? void 0 : _e_target_files[0]) || null);\n                                                                                },\n                                                                                className: \"file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-purple-50 file:text-purple-700 hover:file:bg-purple-100\",\n                                                                                required: true\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                lineNumber: 1009,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                            lineNumber: 1008,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                    lineNumber: 1006,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                            lineNumber: 991,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                                    htmlFor: \"uni_supporting_documents\",\n                                                                    children: \"Upload Supporting Documents (PDF, DOCX, JPG)\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                    lineNumber: 1022,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"mt-1\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                        id: \"uni_supporting_documents\",\n                                                                        type: \"file\",\n                                                                        accept: \".pdf,.docx,.doc,.jpg,.jpeg,.png\",\n                                                                        multiple: true,\n                                                                        onChange: (e)=>handleMultipleFileChange(e.target.files),\n                                                                        className: \"file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-purple-50 file:text-purple-700 hover:file:bg-purple-100\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                        lineNumber: 1024,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                    lineNumber: 1023,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                universityFiles.supporting_documents.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"mt-2 space-y-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm text-gray-600\",\n                                                                            children: \"Selected files:\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                            lineNumber: 1035,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        universityFiles.supporting_documents.map((file, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex items-center justify-between bg-gray-50 p-2 rounded\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"text-sm text-gray-700\",\n                                                                                        children: file.name\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                        lineNumber: 1038,\n                                                                                        columnNumber: 31\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                                        type: \"button\",\n                                                                                        variant: \"ghost\",\n                                                                                        size: \"sm\",\n                                                                                        onClick: ()=>removeFile(index),\n                                                                                        className: \"text-red-600 hover:text-red-800\",\n                                                                                        children: \"Remove\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                        lineNumber: 1039,\n                                                                                        columnNumber: 31\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, index, true, {\n                                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                lineNumber: 1037,\n                                                                                columnNumber: 29\n                                                                            }, this))\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                    lineNumber: 1034,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                            lineNumber: 1021,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                    lineNumber: 985,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                                htmlFor: \"uni_reason_for_scholarship\",\n                                                                children: \"Reason for the Scholarship *\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                lineNumber: 1058,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_6__.Textarea, {\n                                                                id: \"uni_reason_for_scholarship\",\n                                                                value: universityFormData.reason_for_scholarship,\n                                                                onChange: (e)=>setUniversityFormData((prev)=>({\n                                                                            ...prev,\n                                                                            reason_for_scholarship: e.target.value\n                                                                        })),\n                                                                placeholder: \"Please explain why you need this scholarship\",\n                                                                className: \"mt-1\",\n                                                                rows: 4,\n                                                                required: true\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                lineNumber: 1059,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                        lineNumber: 1057,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                    lineNumber: 1056,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-end pt-6\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                        type: \"submit\",\n                                                        disabled: submitting || !validateForm(),\n                                                        className: \"bg-purple-600 hover:bg-purple-700 text-white px-8 py-3 text-lg font-semibold\",\n                                                        children: submitting ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BookOpen_Building_CheckCircle_CreditCard_FileUp_GraduationCap_Loader2_MapPin_School_User_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                    className: \"mr-2 h-5 w-5 animate-spin\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                    lineNumber: 1079,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                \"Submitting...\"\n                                                            ]\n                                                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BookOpen_Building_CheckCircle_CreditCard_FileUp_GraduationCap_Loader2_MapPin_School_User_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                    className: \"mr-2 h-5 w-5\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                    lineNumber: 1084,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                \"Submit Application\"\n                                                            ]\n                                                        }, void 0, true)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                        lineNumber: 1072,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                    lineNumber: 1071,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                            lineNumber: 864,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                        lineNumber: 863,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                lineNumber: 348,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                            lineNumber: 346,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                    lineNumber: 339,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                lineNumber: 338,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n        lineNumber: 324,\n        columnNumber: 5\n    }, this);\n}\n_s(ScholarshipApplicationPage, \"Sti8wAfvn58MX1lRHs4cA5vIto0=\", false, function() {\n    return [\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_9__.useToast\n    ];\n});\n_c = ScholarshipApplicationPage;\nvar _c;\n$RefreshReg$(_c, \"ScholarshipApplicationPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/scholarship/ScholarshipApplicationPage.tsx\n"));

/***/ })

});