<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Scholarship;
use App\Models\ScholarshipApplication;
use App\Models\ScholarshipApplicationFile;
use App\Models\Student;
use App\Models\StudentProgression;
use App\Models\PartnerOrganization;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\DB;

class PartnerScholarshipController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth:sanctum');
        $this->middleware('partner.auth');
    }

    /**
     * Get available scholarships for Partner Organizations (Primary/Secondary only)
     */
    public function getAvailableScholarships(): JsonResponse
    {
        try {
            $scholarships = Scholarship::where('status', 'active')
                ->where('is_open', true)
                ->whereIn('category', ['primary', 'secondary'])
                ->where('application_deadline', '>', now())
                ->with(['fields' => function($query) {
                    $query->where('is_active', true);
                }])
                ->orderBy('application_deadline', 'asc')
                ->get();

            return response()->json([
                'success' => true,
                'data' => $scholarships,
                'message' => 'Available scholarships retrieved successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve scholarships',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get scholarship details for Partner Organizations
     */
    public function getScholarshipDetails($id): JsonResponse
    {
        try {
            $scholarship = Scholarship::where('status', 'active')
                ->where('is_open', true)
                ->whereIn('category', ['primary', 'secondary'])
                ->with(['fields' => function($query) {
                    $query->where('is_active', true);
                }])
                ->findOrFail($id);

            return response()->json([
                'success' => true,
                'data' => $scholarship,
                'message' => 'Scholarship details retrieved successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Scholarship not found or not available for Partner Organizations',
                'error' => $e->getMessage()
            ], 404);
        }
    }

    /**
     * Apply for scholarship on behalf of a student (Primary/Secondary)
     */
    public function applyForStudent(Request $request, $scholarshipId): JsonResponse
    {
        try {
            DB::beginTransaction();

            $user = Auth::user();
            $partnerOrg = PartnerOrganization::findOrFail($user->partner_organization_id);

            // Get scholarship and validate
            $scholarship = Scholarship::where('status', 'active')
                ->where('is_open', true)
                ->whereIn('category', ['primary', 'secondary'])
                ->findOrFail($scholarshipId);

            // Check if application deadline has passed
            if ($scholarship->application_deadline <= now()) {
                return response()->json([
                    'success' => false,
                    'message' => 'This scholarship application deadline has passed'
                ], 410);
            }

            // Validate request data
            $validator = $this->validatePartnerApplication($request, $scholarship);
            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation error',
                    'errors' => $validator->errors()
                ], 422);
            }

            // Create or get student record
            $student = $this->createOrUpdateStudent($request, $partnerOrg);

            // Check if student already has an application for this scholarship
            $existingApplication = ScholarshipApplication::where('scholarship_id', $scholarshipId)
                ->where('student_id', $student->id)
                ->where('school_id', $partnerOrg->id)
                ->first();

            if ($existingApplication) {
                return response()->json([
                    'success' => false,
                    'message' => 'This student already has an application for this scholarship'
                ], 400);
            }

            // Handle file uploads
            $uploadedFiles = $this->handleFileUploads($request, $scholarship);

            // Create application
            $application = $this->createPartnerApplication(
                $scholarship,
                $student,
                $partnerOrg,
                $user,
                $request,
                $uploadedFiles
            );

            DB::commit();

            return response()->json([
                'success' => true,
                'data' => [
                    'application' => $application->load(['scholarship', 'student', 'school']),
                    'student' => $student,
                ],
                'message' => 'Scholarship application submitted successfully'
            ], 201);

        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => 'Failed to submit application',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get applications submitted by this Partner Organization
     */
    public function getMyApplications(Request $request): JsonResponse
    {
        try {
            $user = Auth::user();
            $partnerOrg = PartnerOrganization::findOrFail($user->partner_organization_id);

            $query = ScholarshipApplication::where('school_id', $partnerOrg->id)
                ->with(['scholarship', 'student', 'files']);

            // Filter by status
            if ($request->has('status')) {
                $query->where('status', $request->status);
            }

            // Filter by scholarship category
            if ($request->has('category')) {
                $query->whereHas('scholarship', function ($q) use ($request) {
                    $q->where('category', $request->category);
                });
            }

            // Filter by academic year
            if ($request->has('academic_year')) {
                $query->where('academic_year', $request->academic_year);
            }

            $applications = $query->orderBy('created_at', 'desc')
                                ->paginate($request->get('per_page', 15));

            return response()->json([
                'success' => true,
                'data' => $applications,
                'message' => 'Applications retrieved successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve applications',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get students managed by this Partner Organization
     */
    public function getMyStudents(Request $request): JsonResponse
    {
        try {
            $user = Auth::user();
            $partnerOrg = PartnerOrganization::findOrFail($user->partner_organization_id);

            $query = Student::where('school_id', $partnerOrg->id)
                ->with(['progressions' => function($q) {
                    $q->orderBy('academic_year', 'desc');
                }]);

            // Filter by grade level
            if ($request->has('grade_level')) {
                $query->whereHas('progressions', function ($q) use ($request) {
                    $q->where('grade_level', $request->grade_level)
                      ->where('academic_year', $request->get('academic_year', '2024/2025'));
                });
            }

            // Search by name or student ID
            if ($request->has('search')) {
                $search = $request->search;
                $query->where(function ($q) use ($search) {
                    $q->where('full_name', 'like', "%{$search}%")
                      ->orWhere('student_id', 'like', "%{$search}%");
                });
            }

            $students = $query->orderBy('full_name', 'asc')
                            ->paginate($request->get('per_page', 20));

            return response()->json([
                'success' => true,
                'data' => $students,
                'message' => 'Students retrieved successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve students',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Validate partner organization application data
     */
    private function validatePartnerApplication(Request $request, Scholarship $scholarship)
    {
        $rules = [
            // Student information
            'student_full_name' => 'required|string|max:255',
            'student_age' => 'required|integer|min:5|max:20',
            'student_class' => 'required|string|max:50',
            'student_date_of_birth' => 'nullable|date|before:today',

            // Parent/Guardian information
            'parent_name' => 'required|string|max:255',
            'parent_phone' => 'required|string|regex:/^[\+]?[0-9\s\-\(\)]+$/',
            'parent_email' => 'nullable|email|max:255',
            'home_address' => 'required|string|max:500',

            // Application details
            'reason_for_scholarship' => 'required|string|min:50|max:1000',
            'current_school_fee' => 'required|numeric|min:0',
            'supporting_information' => 'nullable|string|max:1000',

            // Academic information
            'academic_year' => 'required|string|regex:/^\d{4}\/\d{4}$/',
            'grade_level' => 'required|string',
        ];

        // Category-specific validation
        if ($scholarship->category === 'primary') {
            $rules['headmaster_name'] = 'required|string|max:255';
            $rules['headmaster_phone'] = 'nullable|string|regex:/^[\+]?[0-9\s\-\(\)]+$/';
            $rules['school_account_number'] = 'required|string|max:50';
            $rules['student_age'] = 'required|integer|min:5|max:13';
        } elseif ($scholarship->category === 'secondary') {
            $rules['principal_name'] = 'required|string|max:255';
            $rules['principal_phone'] = 'nullable|string|regex:/^[\+]?[0-9\s\-\(\)]+$/';
            $rules['principal_account_number'] = 'required|string|max:50';
            $rules['financial_officer_name'] = 'nullable|string|max:255';
            $rules['student_age'] = 'required|integer|min:12|max:20';
        }

        // File upload validation
        $rules['student_photo'] = 'required|file|mimes:jpeg,png,jpg|max:2048';
        $rules['birth_certificate'] = 'nullable|file|mimes:jpeg,png,jpg,pdf|max:2048';
        $rules['school_id_card'] = 'nullable|file|mimes:jpeg,png,jpg,pdf|max:2048';
        $rules['previous_result'] = 'nullable|file|mimes:jpeg,png,jpg,pdf|max:2048';

        return Validator::make($request->all(), $rules);
    }

    /**
     * Create or update student record
     */
    private function createOrUpdateStudent(Request $request, PartnerOrganization $partnerOrg): Student
    {
        // Check if student already exists
        $existingStudent = Student::where('full_name', $request->student_full_name)
            ->where('school_id', $partnerOrg->id)
            ->where('date_of_birth', $request->student_date_of_birth)
            ->first();

        if ($existingStudent) {
            // Update existing student
            $existingStudent->update([
                'parent_name' => $request->parent_name,
                'parent_phone' => $request->parent_phone,
                'parent_email' => $request->parent_email,
                'home_address' => $request->home_address,
                'updated_at' => now(),
            ]);

            // Update or create progression record
            $this->updateStudentProgression($existingStudent, $request);

            return $existingStudent;
        }

        // Create new student
        $student = Student::create([
            'student_id' => Student::generateStudentId(),
            'full_name' => $request->student_full_name,
            'date_of_birth' => $request->student_date_of_birth,
            'age' => $request->student_age,
            'gender' => $request->student_gender ?? 'not_specified',
            'parent_name' => $request->parent_name,
            'parent_phone' => $request->parent_phone,
            'parent_email' => $request->parent_email,
            'home_address' => $request->home_address,
            'school_id' => $partnerOrg->id,
            'enrollment_date' => now(),
            'status' => 'active',
        ]);

        // Create progression record
        $this->createStudentProgression($student, $request);

        return $student;
    }

    /**
     * Create student progression record
     */
    private function createStudentProgression(Student $student, Request $request): void
    {
        StudentProgression::create([
            'student_id' => $student->id,
            'academic_year' => $request->academic_year,
            'grade_level' => $request->grade_level,
            'school_id' => $student->school_id,
            'enrollment_date' => now(),
            'status' => 'active',
            'is_current' => true,
        ]);
    }

    /**
     * Update student progression record
     */
    private function updateStudentProgression(Student $student, Request $request): void
    {
        // Mark previous progressions as not current
        StudentProgression::where('student_id', $student->id)
            ->update(['is_current' => false]);

        // Create or update current progression
        StudentProgression::updateOrCreate(
            [
                'student_id' => $student->id,
                'academic_year' => $request->academic_year,
            ],
            [
                'grade_level' => $request->grade_level,
                'school_id' => $student->school_id,
                'enrollment_date' => now(),
                'status' => 'active',
                'is_current' => true,
            ]
        );
    }

    /**
     * Handle file uploads for application
     */
    private function handleFileUploads(Request $request, Scholarship $scholarship): array
    {
        $uploadedFiles = [];
        $fileFields = ['student_photo', 'birth_certificate', 'school_id_card', 'previous_result'];

        foreach ($fileFields as $field) {
            if ($request->hasFile($field)) {
                $file = $request->file($field);
                if ($file && $file->isValid()) {
                    $filename = time() . '_' . $field . '_' . $file->getClientOriginalName();
                    $path = $file->storeAs('partner_applications', $filename, 'public');

                    $uploadedFiles[] = [
                        'field_name' => $field,
                        'original_name' => $file->getClientOriginalName(),
                        'file_path' => $path,
                        'file_size' => $file->getSize(),
                        'file_type' => $file->getMimeType(),
                    ];
                }
            }
        }

        return $uploadedFiles;
    }

    /**
     * Create scholarship application for partner organization
     */
    private function createPartnerApplication(
        Scholarship $scholarship,
        Student $student,
        PartnerOrganization $partnerOrg,
        $user,
        Request $request,
        array $uploadedFiles
    ): ScholarshipApplication {
        // Prepare form data
        $formData = [
            'student_full_name' => $request->student_full_name,
            'student_age' => $request->student_age,
            'student_class' => $request->student_class,
            'student_date_of_birth' => $request->student_date_of_birth,
            'parent_name' => $request->parent_name,
            'parent_phone' => $request->parent_phone,
            'parent_email' => $request->parent_email,
            'home_address' => $request->home_address,
            'reason_for_scholarship' => $request->reason_for_scholarship,
            'current_school_fee' => $request->current_school_fee,
            'supporting_information' => $request->supporting_information,
            'academic_year' => $request->academic_year,
            'grade_level' => $request->grade_level,
            'school_name' => $partnerOrg->name,
            'school_type' => $partnerOrg->type,
        ];

        // Add category-specific data
        if ($scholarship->category === 'primary') {
            $formData['headmaster_name'] = $request->headmaster_name;
            $formData['headmaster_phone'] = $request->headmaster_phone;
            $formData['school_account_number'] = $request->school_account_number;
        } elseif ($scholarship->category === 'secondary') {
            $formData['principal_name'] = $request->principal_name;
            $formData['principal_phone'] = $request->principal_phone;
            $formData['principal_account_number'] = $request->principal_account_number;
            $formData['financial_officer_name'] = $request->financial_officer_name;
        }

        // Create application
        $application = ScholarshipApplication::create([
            'scholarship_id' => $scholarship->id,
            'user_id' => $user->id,
            'student_id' => $student->id,
            'school_id' => $partnerOrg->id,
            'grade_level' => $request->grade_level,
            'academic_year' => $request->academic_year,
            'application_type' => 'institutional',
            'form_data' => $formData,
            'status' => 'pending',
            'submitted_at' => now(),
        ]);

        // Create file records
        foreach ($uploadedFiles as $fileData) {
            ScholarshipApplicationFile::create([
                'application_id' => $application->id,
                'field_name' => $fileData['field_name'],
                'original_name' => $fileData['original_name'],
                'file_path' => $fileData['file_path'],
                'file_size' => $fileData['file_size'],
                'file_type' => $fileData['file_type'],
                'upload_date' => now(),
            ]);
        }

        return $application;
    }
}
