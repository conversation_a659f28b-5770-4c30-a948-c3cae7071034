<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\PartnerOrganization;
use App\Models\User;
use Illuminate\Support\Facades\Hash;

class PartnerOrganizationSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create sample partner organizations
        $organizations = [
            [
                'name' => 'Lagos State Primary School',
                'type' => 'primary_school',
                'description' => 'A leading primary school in Lagos State providing quality education to children.',
                'email' => '<EMAIL>',
                'phone' => '+234-************',
                'address' => '123 Education Avenue, Victoria Island',
                'city' => 'Lagos',
                'state' => 'Lagos',
                'country' => 'Nigeria',
                'registration_number' => 'LS/PS/001/2020',
                'license_number' => 'EDU/LS/PRI/001',
                'established_date' => '2020-01-15',
                'student_capacity' => 500,
                'current_enrollment' => 350,
                'education_levels' => ['primary'],
                'grade_levels' => ['primary_1', 'primary_2', 'primary_3', 'primary_4', 'primary_5', 'primary_6'],
                'contact_person_name' => 'Mrs. <PERSON><PERSON><PERSON>',
                'contact_person_title' => 'Principal',
                'contact_person_phone' => '+234-************',
                'contact_person_email' => '<EMAIL>',
                'principal_name' => 'Mrs. Adunni Okafor',
                'principal_phone' => '+234-************',
                'principal_email' => '<EMAIL>',
                'principal_qualification' => 'M.Ed in Primary Education',
                'principal_experience_years' => 15,
                'verification_status' => 'verified',
                'verified_at' => now(),
                'status' => 'active',
                'partnership_status' => 'active',
                'partnership_start_date' => '2024-01-01',
                'facilities' => ['library', 'computer_lab', 'playground', 'cafeteria'],
                'programs_offered' => ['regular_curriculum', 'extra_curricular', 'sports'],
                'mission_statement' => 'To provide quality primary education that nurtures young minds.',
            ],
            [
                'name' => 'Abuja International Secondary School',
                'type' => 'secondary_school',
                'description' => 'A prestigious secondary school in Abuja offering comprehensive education.',
                'email' => '<EMAIL>',
                'phone' => '+234-************',
                'address' => '456 Academic Road, Wuse II',
                'city' => 'Abuja',
                'state' => 'FCT',
                'country' => 'Nigeria',
                'registration_number' => 'FCT/SS/002/2018',
                'license_number' => 'EDU/FCT/SEC/002',
                'established_date' => '2018-09-01',
                'student_capacity' => 800,
                'current_enrollment' => 650,
                'education_levels' => ['secondary'],
                'grade_levels' => ['secondary_1', 'secondary_2', 'secondary_3', 'secondary_4', 'secondary_5', 'secondary_6'],
                'contact_person_name' => 'Dr. Ibrahim Musa',
                'contact_person_title' => 'Principal',
                'contact_person_phone' => '+234-************',
                'contact_person_email' => '<EMAIL>',
                'principal_name' => 'Dr. Ibrahim Musa',
                'principal_phone' => '+234-************',
                'principal_email' => '<EMAIL>',
                'principal_qualification' => 'Ph.D in Educational Administration',
                'principal_experience_years' => 20,
                'verification_status' => 'verified',
                'verified_at' => now(),
                'status' => 'active',
                'partnership_status' => 'active',
                'partnership_start_date' => '2024-01-01',
                'facilities' => ['library', 'science_labs', 'computer_lab', 'sports_complex', 'auditorium'],
                'programs_offered' => ['science', 'arts', 'commercial', 'technical'],
                'mission_statement' => 'Excellence in secondary education and character development.',
            ],
            [
                'name' => 'Kano Community Primary School',
                'type' => 'primary_school',
                'description' => 'A community-based primary school serving rural areas in Kano State.',
                'email' => '<EMAIL>',
                'phone' => '+234-************',
                'address' => '789 Community Street, Fagge',
                'city' => 'Kano',
                'state' => 'Kano',
                'country' => 'Nigeria',
                'registration_number' => 'KN/PS/003/2019',
                'license_number' => 'EDU/KN/PRI/003',
                'established_date' => '2019-03-10',
                'student_capacity' => 300,
                'current_enrollment' => 280,
                'education_levels' => ['primary'],
                'grade_levels' => ['primary_1', 'primary_2', 'primary_3', 'primary_4', 'primary_5', 'primary_6'],
                'contact_person_name' => 'Malam Sani Abdullahi',
                'contact_person_title' => 'Head Teacher',
                'contact_person_phone' => '+234-************',
                'contact_person_email' => '<EMAIL>',
                'principal_name' => 'Malam Sani Abdullahi',
                'principal_phone' => '+234-************',
                'principal_email' => '<EMAIL>',
                'principal_qualification' => 'B.Ed in Primary Education',
                'principal_experience_years' => 12,
                'verification_status' => 'verified',
                'verified_at' => now(),
                'status' => 'active',
                'partnership_status' => 'active',
                'partnership_start_date' => '2024-02-01',
                'facilities' => ['library', 'playground', 'water_well'],
                'programs_offered' => ['regular_curriculum', 'vocational_training'],
                'mission_statement' => 'Providing accessible quality education to rural communities.',
            ],
            [
                'name' => 'Port Harcourt Girls Secondary School',
                'type' => 'secondary_school',
                'description' => 'An all-girls secondary school focused on empowering young women.',
                'email' => '<EMAIL>',
                'phone' => '+234-************',
                'address' => '321 Girls Education Lane, GRA',
                'city' => 'Port Harcourt',
                'state' => 'Rivers',
                'country' => 'Nigeria',
                'registration_number' => 'RV/SS/004/2017',
                'license_number' => 'EDU/RV/SEC/004',
                'established_date' => '2017-01-20',
                'student_capacity' => 600,
                'current_enrollment' => 520,
                'education_levels' => ['secondary'],
                'grade_levels' => ['secondary_1', 'secondary_2', 'secondary_3', 'secondary_4', 'secondary_5', 'secondary_6'],
                'contact_person_name' => 'Mrs. Grace Okoro',
                'contact_person_title' => 'Principal',
                'contact_person_phone' => '+234-************',
                'contact_person_email' => '<EMAIL>',
                'principal_name' => 'Mrs. Grace Okoro',
                'principal_phone' => '+234-************',
                'principal_email' => '<EMAIL>',
                'principal_qualification' => 'M.Ed in Educational Leadership',
                'principal_experience_years' => 18,
                'verification_status' => 'pending',
                'status' => 'active',
                'partnership_status' => 'pending',
                'facilities' => ['library', 'science_labs', 'home_economics_lab', 'sports_field'],
                'programs_offered' => ['science', 'arts', 'home_economics', 'leadership_training'],
                'mission_statement' => 'Empowering girls through quality education and leadership development.',
            ],
        ];

        foreach ($organizations as $orgData) {
            $org = PartnerOrganization::create($orgData);

            // Create a user account for each organization
            $userData = [
                'first_name' => explode(' ', $orgData['contact_person_name'])[0] ?? 'Contact',
                'last_name' => explode(' ', $orgData['contact_person_name'])[1] ?? 'Person',
                'email' => $orgData['contact_person_email'],
                'password' => Hash::make('password123'),
                'phone_number' => $orgData['contact_person_phone'],
                'role' => 'partner_organization',
                'partner_organization_id' => $org->id,
                'institutional_role' => strtolower($orgData['contact_person_title']),
                'institutional_verification_status' => $orgData['verification_status'] === 'verified' ? 'verified' : 'pending',
                'status' => 'active',
                'email_verified_at' => now(),
                'city' => $orgData['city'],
                'state' => $orgData['state'],
                'country' => $orgData['country'],
            ];

            $user = User::create($userData);

            // Link user to organization through pivot table
            $org->users()->attach($user->id, [
                'role' => strtolower($orgData['contact_person_title']),
                'status' => 'active',
                'start_date' => now()->subMonths(6),
                'permissions' => json_encode(['manage_students', 'submit_applications', 'view_reports']),
            ]);

            // Update organization with creator
            $org->update([
                'created_by' => $user->id,
                'verified_by' => 1, // Assuming admin user with ID 1 exists
            ]);
        }
    }
}
