<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Scholarship;
use App\Models\ScholarshipApplication;
use App\Models\ScholarshipApplicationFile;
use App\Models\ScholarshipCategory;
use App\Http\Requests\ScholarshipApplicationRequest;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;
use Carbon\Carbon;

class PublicScholarshipController extends Controller
{
    /**
     * Get all active scholarships for public viewing
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $perPage = $request->get('per_page', 12);
            $category = $request->get('category');
            $search = $request->get('search');
            $sortBy = $request->get('sort_by', 'created_at');
            $sortOrder = $request->get('sort_order', 'desc');

            $query = Scholarship::where('status', 'active')
                ->where('is_open', true)
                ->where('application_deadline', '>', now())
                ->with(['categoryDetails'])
                ->withCount(['applications as current_applicants']);

            // Filter by category
            if ($category && $category !== 'all') {
                $query->where('category', $category);
            }

            // Search functionality
            if ($search) {
                $query->where(function($q) use ($search) {
                    $q->where('title', 'like', "%{$search}%")
                      ->orWhere('description', 'like', "%{$search}%")
                      ->orWhere('eligibility_criteria', 'like', "%{$search}%");
                });
            }

            // Sorting
            if (in_array($sortBy, ['title', 'amount', 'application_deadline', 'created_at'])) {
                $query->orderBy($sortBy, $sortOrder);
            } else {
                $query->orderBy('created_at', 'desc');
            }

            $scholarships = $query->paginate($perPage);

            // Get categories for filtering
            $categories = ScholarshipCategory::active()->ordered()->get(['name', 'slug', 'description']);

            return response()->json([
                'success' => true,
                'message' => 'Scholarships retrieved successfully',
                'data' => $scholarships,
                'categories' => $categories
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve scholarships',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get a specific scholarship for public viewing
     */
    public function show($id): JsonResponse
    {
        try {
            $scholarship = Scholarship::where('status', 'active')
                ->where('is_open', true)
                ->with(['categoryDetails', 'fields' => function($query) {
                    $query->where('is_active', true)->orderBy('field_order');
                }])
                ->withCount('applications as current_applicants')
                ->findOrFail($id);

            // Check if application deadline has passed
            if ($scholarship->application_deadline <= now()) {
                return response()->json([
                    'success' => false,
                    'message' => 'This scholarship application deadline has passed'
                ], 410);
            }

            // Check if maximum applicants reached
            if ($scholarship->max_applicants && $scholarship->current_applicants >= $scholarship->max_applicants) {
                return response()->json([
                    'success' => false,
                    'message' => 'This scholarship has reached its maximum number of applicants'
                ], 410);
            }

            return response()->json([
                'success' => true,
                'message' => 'Scholarship retrieved successfully',
                'data' => $scholarship
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Scholarship not found',
                'error' => $e->getMessage()
            ], 404);
        }
    }

    /**
     * Apply for a scholarship
     */
    public function apply(ScholarshipApplicationRequest $request, $id): JsonResponse
    {
        try {
            $scholarship = Scholarship::where('status', 'active')
                ->where('is_open', true)
                ->with(['fields' => function($query) {
                    $query->where('is_active', true);
                }])
                ->findOrFail($id);

            // Check if application deadline has passed
            if ($scholarship->application_deadline <= now()) {
                return response()->json([
                    'success' => false,
                    'message' => 'This scholarship application deadline has passed'
                ], 410);
            }

            // Check if user already applied
            $existingApplication = ScholarshipApplication::where('scholarship_id', $id)
                ->where('user_id', Auth::id())
                ->first();

            if ($existingApplication) {
                return response()->json([
                    'success' => false,
                    'message' => 'You have already applied for this scholarship'
                ], 409);
            }

            // Check if maximum applicants reached
            if ($scholarship->max_applicants && $scholarship->current_applicants >= $scholarship->max_applicants) {
                return response()->json([
                    'success' => false,
                    'message' => 'This scholarship has reached its maximum number of applicants'
                ], 410);
            }

            // Validation is handled by ScholarshipApplicationRequest

            // Generate unique application ID
            $applicationId = 'APP-' . strtoupper(Str::random(8)) . '-' . date('Y');
            
            // Ensure unique application ID
            while (ScholarshipApplication::where('application_id', $applicationId)->exists()) {
                $applicationId = 'APP-' . strtoupper(Str::random(8)) . '-' . date('Y');
            }

            // Prepare form data (excluding files)
            $formData = $request->except(['_token']);
            $uploadedFiles = [];

            // Handle file uploads
            foreach ($scholarship->fields as $field) {
                if ($field->field_type === 'file' && $request->hasFile($field->field_name)) {
                    $file = $request->file($field->field_name);
                    $uploadedFile = $this->handleFileUpload($file, $field->field_name, $applicationId);
                    
                    if ($uploadedFile) {
                        $uploadedFiles[] = $uploadedFile;
                        $formData[$field->field_name] = $uploadedFile['file_path'];
                    }
                }
            }

            // Create scholarship application
            $application = ScholarshipApplication::create([
                'application_id' => $applicationId,
                'scholarship_id' => $id,
                'user_id' => Auth::id(),
                'form_data' => $formData,
                'uploaded_files' => array_column($uploadedFiles, 'file_path'),
                'status' => 'pending',
                'submitted_at' => now()
            ]);

            // Create file records
            foreach ($uploadedFiles as $fileData) {
                ScholarshipApplicationFile::create([
                    'application_id' => $application->id,
                    'field_name' => $fileData['field_name'],
                    'original_name' => $fileData['original_name'],
                    'file_path' => $fileData['file_path'],
                    'file_size' => $fileData['file_size'],
                    'file_type' => $fileData['file_type'],
                    'upload_date' => now()
                ]);
            }

            // Update scholarship current applicants count
            $scholarship->increment('current_applicants');

            return response()->json([
                'success' => true,
                'message' => 'Application submitted successfully',
                'data' => [
                    'application_id' => $applicationId,
                    'scholarship_title' => $scholarship->title,
                    'submitted_at' => $application->submitted_at,
                    'status' => $application->status
                ]
            ], 201);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to submit application',
                'error' => $e->getMessage()
            ], 500);
        }
    }



    /**
     * Handle file upload
     */
    private function handleFileUpload($file, string $fieldName, string $applicationId): ?array
    {
        try {
            $originalName = $file->getClientOriginalName();
            $extension = $file->getClientOriginalExtension();
            $fileName = $fieldName . '_' . $applicationId . '_' . time() . '.' . $extension;
            $filePath = 'scholarship-applications/' . date('Y/m') . '/' . $fileName;
            
            // Store the file
            $storedPath = $file->storeAs('scholarship-applications/' . date('Y/m'), $fileName, 'public');
            
            if ($storedPath) {
                return [
                    'field_name' => $fieldName,
                    'original_name' => $originalName,
                    'file_path' => $storedPath,
                    'file_size' => $file->getSize(),
                    'file_type' => $file->getMimeType()
                ];
            }
            
            return null;
        } catch (\Exception $e) {
            \Log::error('File upload failed: ' . $e->getMessage());
            return null;
        }
    }
}
