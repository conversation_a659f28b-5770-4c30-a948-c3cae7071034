

<?php $__env->startSection('title', $scholarship->title); ?>

<?php $__env->startSection('content'); ?>
<div class="container">
    <div class="row">
        <div class="col-lg-8">
            <div class="card shadow-lg">
                <div class="card-header bg-primary text-white">
                    <h1 class="h3 mb-0"><?php echo e($scholarship->title); ?></h1>
                </div>
                <div class="card-body">
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <h5 class="text-primary">Scholarship Details</h5>
                            <p><strong>Amount:</strong> ₦<?php echo e(number_format($scholarship->amount, 2)); ?></p>
                            <p><strong>Application Deadline:</strong> <?php echo e($scholarship->application_deadline->format('F d, Y')); ?></p>
                            <p><strong>Status:</strong> 
                                <span class="badge bg-<?php echo e($scholarship->status === 'open' ? 'success' : 'secondary'); ?>">
                                    <?php echo e(ucfirst($scholarship->status)); ?>

                                </span>
                            </p>
                            <?php if($scholarship->max_applicants): ?>
                                <p><strong>Max Applicants:</strong> <?php echo e($scholarship->max_applicants); ?></p>
                            <?php endif; ?>
                        </div>
                        <div class="col-md-6">
                            <div class="alert alert-info">
                                <h6><i class="fas fa-clock"></i> Application Deadline</h6>
                                <p class="mb-0"><?php echo e($scholarship->application_deadline->diffForHumans()); ?></p>
                            </div>
                        </div>
                    </div>

                    <div class="mb-4">
                        <h5 class="text-primary">Description</h5>
                        <p><?php echo e($scholarship->description); ?></p>
                    </div>

                    <div class="mb-4">
                        <h5 class="text-primary">Eligibility Criteria</h5>
                        <div class="bg-light p-3 rounded">
                            <?php echo nl2br(e($scholarship->eligibility_criteria)); ?>

                        </div>
                    </div>

                    <?php if($scholarship->requirements): ?>
                        <div class="mb-4">
                            <h5 class="text-primary">Requirements</h5>
                            <ul class="list-group list-group-flush">
                                <?php $__currentLoopData = $scholarship->requirements; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $requirement): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <li class="list-group-item"><i class="fas fa-check text-success me-2"></i><?php echo e($requirement); ?></li>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </ul>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
        
        <div class="col-lg-4">
            <div class="card shadow">
                <div class="card-header bg-success text-white">
                    <h5 class="mb-0"><i class="fas fa-graduation-cap me-2"></i>Apply Now</h5>
                </div>
                <div class="card-body">
                    <?php if($scholarship->status === 'open' && $scholarship->application_deadline > now()): ?>
                        <p class="text-muted">Ready to apply for this scholarship?</p>
                        <button class="btn btn-success btn-lg w-100 mb-3">
                            <i class="fas fa-file-alt me-2"></i>Start Application
                        </button>
                        <p class="small text-muted">
                            <i class="fas fa-info-circle me-1"></i>
                            Make sure you meet all eligibility criteria before applying.
                        </p>
                    <?php else: ?>
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            Applications are currently closed.
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <div class="card shadow mt-4">
                <div class="card-header">
                    <h6 class="mb-0">Need Help?</h6>
                </div>
                <div class="card-body">
                    <p class="small text-muted">Have questions about this scholarship?</p>
                    <a href="<?php echo e(route('contact')); ?>" class="btn btn-outline-primary btn-sm w-100">
                        <i class="fas fa-envelope me-2"></i>Contact Us
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?> 
<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\laragon\www\laravel-api-ngo\resources\views/scholarship.blade.php ENDPATH**/ ?>