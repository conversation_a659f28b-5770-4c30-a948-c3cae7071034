(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7177],{5040:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(19946).A)("BookOpen",[["path",{d:"M12 7v14",key:"1akyts"}],["path",{d:"M3 18a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1h5a4 4 0 0 1 4 4 4 4 0 0 1 4-4h5a1 1 0 0 1 1 1v13a1 1 0 0 1-1 1h-6a3 3 0 0 0-3 3 3 3 0 0 0-3-3z",key:"ruj8y"}]])},10488:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(19946).A)("Facebook",[["path",{d:"M18 2h-3a5 5 0 0 0-5 5v3H7v4h3v8h4v-8h3l1-4h-4V7a1 1 0 0 1 1-1h3z",key:"1jg4f8"}]])},18175:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(19946).A)("Twitter",[["path",{d:"M22 4s-.7 2.1-2 3.4c1.6 10-9.4 17.3-18 11.6 2.2.1 4.4-.6 6-2C3 15.5.5 9.6 3 5c2.2 2.6 5.6 4.1 9 4-.9-4.2 4-6.6 7-3.8 1.1 0 3-1.2 3-1.2z",key:"pff0z6"}]])},19324:()=>{},19420:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(19946).A)("Phone",[["path",{d:"M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z",key:"foiqr5"}]])},28883:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(19946).A)("Mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]])},33109:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(19946).A)("TrendingUp",[["polyline",{points:"22 7 13.5 15.5 8.5 10.5 2 17",key:"126l90"}],["polyline",{points:"16 7 22 7 22 13",key:"kwv8wd"}]])},44531:(e,t,r)=>{"use strict";r.d(t,{t:()=>n});var a=r(12115),s=r(31886);let n=()=>{let[e,t]=(0,a.useState)(null),[r,n]=(0,a.useState)(!0),[o,l]=(0,a.useState)(null);return(0,a.useEffect)(()=>{(async()=>{try{let e=await s.uE.getSettings();if(e.success)console.log("Settings loaded:",e.data),t(e.data);else throw Error("Invalid settings response")}catch(e){console.error("Error fetching settings:",e),l(e instanceof Error?e.message:"Unknown error"),t({app_name:"HLTKKQ Foundation",site_description:"Transforming Lives, Building Communities",contact_email:"<EMAIL>",contact_phone:"+234 ************"})}finally{n(!1)}})()},[]),{settings:e,loading:r,error:o}}},47780:(e,t,r)=>{"use strict";r.d(t,{ThemeProvider:()=>p});var a=r(95155),s=r(12115),n=(e,t,r,a,s,n,o,l)=>{let i=document.documentElement,d=["light","dark"];function c(t){var r;(Array.isArray(e)?e:[e]).forEach(e=>{let r="class"===e,a=r&&n?s.map(e=>n[e]||e):s;r?(i.classList.remove(...a),i.classList.add(n&&n[t]?n[t]:t)):i.setAttribute(e,t)}),r=t,l&&d.includes(r)&&(i.style.colorScheme=r)}if(a)c(a);else try{let e=localStorage.getItem(t)||r,a=o&&"system"===e?window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light":e;c(a)}catch(e){}},o=["light","dark"],l="(prefers-color-scheme: dark)",i=s.createContext(void 0),d=e=>s.useContext(i)?s.createElement(s.Fragment,null,e.children):s.createElement(m,{...e}),c=["light","dark"],m=e=>{let{forcedTheme:t,disableTransitionOnChange:r=!1,enableSystem:a=!0,enableColorScheme:n=!0,storageKey:d="theme",themes:m=c,defaultTheme:p=a?"system":"light",attribute:f="data-theme",value:v,children:b,nonce:y,scriptProps:j}=e,[N,w]=s.useState(()=>g(d,p)),[k,A]=s.useState(()=>"system"===N?x():N),_=v?Object.values(v):m,z=s.useCallback(e=>{let t=e;if(!t)return;"system"===e&&a&&(t=x());let s=v?v[t]:t,l=r?u(y):null,i=document.documentElement,d=e=>{"class"===e?(i.classList.remove(..._),s&&i.classList.add(s)):e.startsWith("data-")&&(s?i.setAttribute(e,s):i.removeAttribute(e))};if(Array.isArray(f)?f.forEach(d):d(f),n){let e=o.includes(p)?p:null,r=o.includes(t)?t:e;i.style.colorScheme=r}null==l||l()},[y]),C=s.useCallback(e=>{let t="function"==typeof e?e(N):e;w(t);try{localStorage.setItem(d,t)}catch(e){}},[N]),L=s.useCallback(e=>{A(x(e)),"system"===N&&a&&!t&&z("system")},[N,t]);s.useEffect(()=>{let e=window.matchMedia(l);return e.addListener(L),L(e),()=>e.removeListener(L)},[L]),s.useEffect(()=>{let e=e=>{e.key===d&&(e.newValue?w(e.newValue):C(p))};return window.addEventListener("storage",e),()=>window.removeEventListener("storage",e)},[C]),s.useEffect(()=>{z(null!=t?t:N)},[t,N]);let S=s.useMemo(()=>({theme:N,setTheme:C,forcedTheme:t,resolvedTheme:"system"===N?k:N,themes:a?[...m,"system"]:m,systemTheme:a?k:void 0}),[N,C,t,k,a,m]);return s.createElement(i.Provider,{value:S},s.createElement(h,{forcedTheme:t,storageKey:d,attribute:f,enableSystem:a,enableColorScheme:n,defaultTheme:p,value:v,themes:m,nonce:y,scriptProps:j}),b)},h=s.memo(e=>{let{forcedTheme:t,storageKey:r,attribute:a,enableSystem:o,enableColorScheme:l,defaultTheme:i,value:d,themes:c,nonce:m,scriptProps:h}=e,g=JSON.stringify([a,r,i,t,c,d,o,l]).slice(1,-1);return s.createElement("script",{...h,suppressHydrationWarning:!0,nonce:"",dangerouslySetInnerHTML:{__html:"(".concat(n.toString(),")(").concat(g,")")}})}),g=(e,t)=>{let r;try{r=localStorage.getItem(e)||void 0}catch(e){}return r||t},u=e=>{let t=document.createElement("style");return e&&t.setAttribute("nonce",e),t.appendChild(document.createTextNode("*,*::before,*::after{-webkit-transition:none!important;-moz-transition:none!important;-o-transition:none!important;-ms-transition:none!important;transition:none!important}")),document.head.appendChild(t),()=>{window.getComputedStyle(document.body),setTimeout(()=>{document.head.removeChild(t)},1)}},x=e=>(e||(e=window.matchMedia(l)),e.matches?"dark":"light");function p(e){let{children:t,...r}=e;return(0,a.jsx)(d,{...r,children:t})}},51976:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(19946).A)("Heart",[["path",{d:"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z",key:"c3ymky"}]])},53999:(e,t,r)=>{"use strict";r.d(t,{cn:()=>n});var a=r(52596),s=r(39688);function n(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,s.QP)((0,a.$)(t))}},60768:(e,t,r)=>{"use strict";r.d(t,{Navigation:()=>E});var a=r(95155),s=r(12115),n=r(35695),o=r(6874),l=r.n(o),i=r(97168),d=r(15452),c=r(74466),m=r(54416),h=r(53999);let g=d.bL,u=d.l9;d.bm;let x=d.ZL,p=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)(d.hJ,{className:(0,h.cn)("fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",r),...s,ref:t})});p.displayName=d.hJ.displayName;let f=(0,c.F)("fixed z-50 gap-4 bg-background p-6 shadow-lg transition ease-in-out data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:duration-300 data-[state=open]:duration-500",{variants:{side:{top:"inset-x-0 top-0 border-b data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top",bottom:"inset-x-0 bottom-0 border-t data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom",left:"inset-y-0 left-0 h-full w-3/4 border-r data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left sm:max-w-sm",right:"inset-y-0 right-0 h-full w-3/4  border-l data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right sm:max-w-sm"}},defaultVariants:{side:"right"}}),v=s.forwardRef((e,t)=>{let{side:r="right",className:s,children:n,...o}=e;return(0,a.jsxs)(x,{children:[(0,a.jsx)(p,{}),(0,a.jsxs)(d.UC,{ref:t,className:(0,h.cn)(f({side:r}),s),...o,children:[n,(0,a.jsxs)(d.bm,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-secondary",children:[(0,a.jsx)(m.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})});v.displayName=d.UC.displayName,s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)(d.hE,{ref:t,className:(0,h.cn)("text-lg font-semibold text-foreground",r),...s})}).displayName=d.hE.displayName,s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)(d.VY,{ref:t,className:(0,h.cn)("text-sm text-muted-foreground",r),...s})}).displayName=d.VY.displayName;var b=r(67133),y=r(51976),j=r(17580),N=r(16785),w=r(87949),k=r(5040),A=r(81497),_=r(66474);let z=(0,r(19946).A)("Menu",[["line",{x1:"4",x2:"20",y1:"12",y2:"12",key:"1e0a9i"}],["line",{x1:"4",x2:"20",y1:"6",y2:"6",key:"1owob3"}],["line",{x1:"4",x2:"20",y1:"18",y2:"18",key:"yk5zj1"}]]);var C=r(33109),L=r(31886),S=r(44531);function E(){let[e,t]=(0,s.useState)(!1),[r,o]=(0,s.useState)(!1),[c,h]=(0,s.useState)(!1),[x,p]=(0,s.useState)(null),[f,E]=(0,s.useState)(!1),M=(0,n.usePathname)(),{settings:T,loading:P}=(0,S.t)(),I=(0,s.useRef)(null);(0,s.useEffect)(()=>{E(!0)},[]),(0,s.useEffect)(()=>{o((0,L.wR)())},[M]),(0,s.useEffect)(()=>{let e=()=>{h(window.scrollY>10)};return window.addEventListener("scroll",e,{passive:!0}),()=>window.removeEventListener("scroll",e)},[]);let O=e=>{I.current&&clearTimeout(I.current),p(e)},H=()=>{I.current=setTimeout(()=>{p(null)},150)};if((null==M?void 0:M.startsWith("/dashboard"))&&r)return null;let $=[{href:"/about",label:"About Us",icon:y.A,description:"Learn about our mission"},{href:"/team",label:"Our Team",icon:j.A,description:"Meet our dedicated team"}],R=[{href:"/projects",label:"Projects",icon:N.A,description:"Our community projects"},{href:"/scholarships",label:"Scholarships",icon:w.A,description:"Educational support"},{href:"/learn-with-us",label:"Learn With Us",icon:k.A,description:"Educational programs"}],W=[{href:"/contact",label:"Contact",icon:A.A,description:"Get in touch"},{href:"/blog",label:"Blog",icon:k.A,description:"Latest updates"}],F=e=>e.some(e=>M===e.href),U=(null==T?void 0:T.app_name)||"Laravel NGO",V=null==T?void 0:T.app_logo;return console.log("Navigation - Settings:",T),console.log("Navigation - App Logo:",V),(0,a.jsx)("header",{className:"fixed top-0 z-50 w-full transition-all duration-300 ease-in-out ".concat(c?"bg-white/90 backdrop-blur-xl border-b border-gray-200/50 shadow-lg shadow-black/5":"bg-white/95 backdrop-blur-md border-b border-gray-200/30"," dark:bg-gray-950/95 dark:supports-[backdrop-filter]:bg-gray-950/80"),children:(0,a.jsxs)("div",{className:"container flex h-16 items-center justify-between px-4 sm:px-6 lg:px-8",children:[(0,a.jsxs)(l(),{href:"/",className:"group flex items-center gap-3 hover:scale-105 transition-all duration-300 ease-out",children:[V?(0,a.jsx)("div",{className:"relative overflow-hidden rounded-full",children:(0,a.jsx)("img",{src:V,alt:"".concat(U," Logo"),className:"h-9 w-9 object-cover transition-transform duration-300 group-hover:scale-110"})}):(0,a.jsxs)("div",{className:"relative h-9 w-9 overflow-hidden rounded-full bg-gradient-to-br from-green-500 to-green-700 shadow-lg group-hover:shadow-xl transition-all duration-300",children:[(0,a.jsx)("div",{className:"absolute inset-0 bg-gradient-to-br from-white/20 to-transparent"}),(0,a.jsx)(y.A,{className:"absolute inset-0 m-auto h-5 w-5 text-white transition-transform duration-300 group-hover:scale-110"})]}),(0,a.jsx)("span",{className:"text-xl font-bold text-gray-900 dark:text-white transition-colors duration-300",children:(0,a.jsx)("span",{className:"bg-gradient-to-r from-green-600 to-green-700 bg-clip-text text-transparent",children:U})})]}),(0,a.jsxs)("nav",{className:"hidden lg:flex items-center space-x-1",children:[(0,a.jsxs)(l(),{href:"/",className:"group relative px-4 py-2.5 rounded-xl text-sm font-medium transition-all duration-300 ease-out ".concat("/"===M?"text-green-600 bg-green-50/80 shadow-sm ring-1 ring-green-100 dark:text-green-400 dark:bg-green-900/30":"text-gray-600 hover:text-green-600 hover:bg-green-50/60 dark:text-gray-300 dark:hover:text-green-400 dark:hover:bg-green-900/20"),children:[(0,a.jsx)("span",{className:"relative z-10",children:"Home"}),"/"===M&&(0,a.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-green-500/10 to-green-600/10 rounded-xl"})]}),(0,a.jsx)("div",{className:"relative",onMouseEnter:()=>O("about"),onMouseLeave:H,children:(0,a.jsxs)(b.rI,{open:"about"===x,onOpenChange:e=>p(e?"about":null),children:[(0,a.jsx)(b.ty,{asChild:!0,children:(0,a.jsxs)(i.$,{variant:"ghost",className:"group relative px-4 py-2.5 h-auto text-sm font-medium transition-all duration-300 ease-out rounded-xl ".concat(F($)||"about"===x?"text-green-600 bg-green-50/80 shadow-sm ring-1 ring-green-100 dark:text-green-400 dark:bg-green-900/30":"text-gray-600 hover:text-green-600 hover:bg-green-50/60 dark:text-gray-300 dark:hover:text-green-400 dark:hover:bg-green-900/20"),children:[(0,a.jsx)("span",{className:"relative z-10",children:"About"}),(0,a.jsx)(_.A,{className:"ml-1.5 h-3.5 w-3.5 transition-transform duration-300 ".concat("about"===x?"rotate-180":"")}),(F($)||"about"===x)&&(0,a.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-green-500/10 to-green-600/10 rounded-xl"})]})}),(0,a.jsx)(b.SQ,{align:"start",className:"w-64 p-3 mt-2 bg-white/95 backdrop-blur-xl border border-gray-200/50 shadow-xl rounded-2xl",sideOffset:8,children:$.map(e=>{let t=e.icon;return(0,a.jsx)(b._2,{asChild:!0,className:"rounded-xl p-0",children:(0,a.jsxs)(l(),{href:e.href,className:"group flex items-start gap-3 px-4 py-3 rounded-xl hover:bg-green-50/80 transition-all duration-200",children:[(0,a.jsx)("div",{className:"flex-shrink-0 w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center group-hover:bg-green-200 transition-colors duration-200",children:(0,a.jsx)(t,{className:"h-4 w-4 text-green-600"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"font-medium text-gray-900",children:e.label}),(0,a.jsx)("div",{className:"text-xs text-gray-500 mt-0.5",children:e.description})]})]})},e.href)})})]})}),(0,a.jsx)("div",{className:"relative",onMouseEnter:()=>O("programs"),onMouseLeave:H,children:(0,a.jsxs)(b.rI,{open:"programs"===x,onOpenChange:e=>p(e?"programs":null),children:[(0,a.jsx)(b.ty,{asChild:!0,children:(0,a.jsxs)(i.$,{variant:"ghost",className:"group relative px-4 py-2.5 h-auto text-sm font-medium transition-all duration-300 ease-out rounded-xl ".concat(F(R)||"programs"===x?"text-green-600 bg-green-50/80 shadow-sm ring-1 ring-green-100 dark:text-green-400 dark:bg-green-900/30":"text-gray-600 hover:text-green-600 hover:bg-green-50/60 dark:text-gray-300 dark:hover:text-green-400 dark:hover:bg-green-900/20"),children:[(0,a.jsx)("span",{className:"relative z-10",children:"Programs"}),(0,a.jsx)(_.A,{className:"ml-1.5 h-3.5 w-3.5 transition-transform duration-300 ".concat("programs"===x?"rotate-180":"")}),(F(R)||"programs"===x)&&(0,a.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-green-500/10 to-green-600/10 rounded-xl"})]})}),(0,a.jsx)(b.SQ,{align:"start",className:"w-64 p-3 mt-2 bg-white/95 backdrop-blur-xl border border-gray-200/50 shadow-xl rounded-2xl",sideOffset:8,children:R.map(e=>{let t=e.icon;return(0,a.jsx)(b._2,{asChild:!0,className:"rounded-xl p-0",children:(0,a.jsxs)(l(),{href:e.href,className:"group flex items-start gap-3 px-4 py-3 rounded-xl hover:bg-green-50/80 transition-all duration-200",children:[(0,a.jsx)("div",{className:"flex-shrink-0 w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center group-hover:bg-green-200 transition-colors duration-200",children:(0,a.jsx)(t,{className:"h-4 w-4 text-green-600"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"font-medium text-gray-900",children:e.label}),(0,a.jsx)("div",{className:"text-xs text-gray-500 mt-0.5",children:e.description})]})]})},e.href)})})]})}),(0,a.jsxs)(l(),{href:"/impact",className:"group relative px-4 py-2.5 rounded-xl text-sm font-medium transition-all duration-300 ease-out ".concat("/impact"===M?"text-green-600 bg-green-50/80 shadow-sm ring-1 ring-green-100 dark:text-green-400 dark:bg-green-900/30":"text-gray-600 hover:text-green-600 hover:bg-green-50/60 dark:text-gray-300 dark:hover:text-green-400 dark:hover:bg-green-900/20"),children:[(0,a.jsx)("span",{className:"relative z-10",children:"Impact"}),"/impact"===M&&(0,a.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-green-500/10 to-green-600/10 rounded-xl"})]}),(0,a.jsx)("div",{className:"relative",onMouseEnter:()=>O("involved"),onMouseLeave:H,children:(0,a.jsxs)(b.rI,{open:"involved"===x,onOpenChange:e=>p(e?"involved":null),children:[(0,a.jsx)(b.ty,{asChild:!0,children:(0,a.jsxs)(i.$,{variant:"ghost",className:"group relative px-4 py-2.5 h-auto text-sm font-medium transition-all duration-300 ease-out rounded-xl ".concat(F(W)||"involved"===x?"text-green-600 bg-green-50/80 shadow-sm ring-1 ring-green-100 dark:text-green-400 dark:bg-green-900/30":"text-gray-600 hover:text-green-600 hover:bg-green-50/60 dark:text-gray-300 dark:hover:text-green-400 dark:hover:bg-green-900/20"),children:[(0,a.jsx)("span",{className:"relative z-10",children:"Get Involved"}),(0,a.jsx)(_.A,{className:"ml-1.5 h-3.5 w-3.5 transition-transform duration-300 ".concat("involved"===x?"rotate-180":"")}),(F(W)||"involved"===x)&&(0,a.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-green-500/10 to-green-600/10 rounded-xl"})]})}),(0,a.jsx)(b.SQ,{align:"start",className:"w-64 p-3 mt-2 bg-white/95 backdrop-blur-xl border border-gray-200/50 shadow-xl rounded-2xl",sideOffset:8,children:W.map(e=>{let t=e.icon;return(0,a.jsx)(b._2,{asChild:!0,className:"rounded-xl p-0",children:(0,a.jsxs)(l(),{href:e.href,className:"group flex items-start gap-3 px-4 py-3 rounded-xl hover:bg-green-50/80 transition-all duration-200",children:[(0,a.jsx)("div",{className:"flex-shrink-0 w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center group-hover:bg-green-200 transition-colors duration-200",children:(0,a.jsx)(t,{className:"h-4 w-4 text-green-600"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"font-medium text-gray-900",children:e.label}),(0,a.jsx)("div",{className:"text-xs text-gray-500 mt-0.5",children:e.description})]})]})},e.href)})})]})})]}),f&&(0,a.jsx)("div",{className:"hidden lg:flex items-center gap-3",suppressHydrationWarning:!0,children:r?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(l(),{href:"/dashboard",children:(0,a.jsxs)(i.$,{variant:"outline",className:"group relative overflow-hidden border-gray-300 hover:border-green-500 transition-all duration-300 rounded-full px-6 py-2.5 hover:shadow-lg hover:shadow-green-500/20",suppressHydrationWarning:!0,children:[(0,a.jsx)("span",{className:"relative z-10 font-medium",children:"Dashboard"}),(0,a.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-green-500/10 to-green-600/10 translate-x-full group-hover:translate-x-0 transition-transform duration-300"})]})}),(0,a.jsx)(l(),{href:"/donate",children:(0,a.jsxs)(i.$,{className:"group relative overflow-hidden bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 text-white transition-all duration-300 rounded-full px-6 py-2.5 hover:shadow-lg hover:shadow-green-500/30 hover:scale-105",suppressHydrationWarning:!0,children:[(0,a.jsx)("span",{className:"relative z-10 font-medium",children:"Donate Now"}),(0,a.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-white/20 to-transparent translate-x-full group-hover:translate-x-0 transition-transform duration-500"})]})})]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(l(),{href:"/auth/login",children:(0,a.jsxs)(i.$,{variant:"outline",className:"group relative overflow-hidden border-gray-300 hover:border-green-500 transition-all duration-300 rounded-full px-6 py-2.5 hover:shadow-lg hover:shadow-green-500/20",suppressHydrationWarning:!0,children:[(0,a.jsx)("span",{className:"relative z-10 font-medium",children:"Login"}),(0,a.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-green-500/10 to-green-600/10 translate-x-full group-hover:translate-x-0 transition-transform duration-300"})]})}),(0,a.jsx)(l(),{href:"/donate",children:(0,a.jsxs)(i.$,{className:"group relative overflow-hidden bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 text-white transition-all duration-300 rounded-full px-6 py-2.5 hover:shadow-lg hover:shadow-green-500/30 hover:scale-105",suppressHydrationWarning:!0,children:[(0,a.jsx)("span",{className:"relative z-10 font-medium",children:"Donate Now"}),(0,a.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-white/20 to-transparent translate-x-full group-hover:translate-x-0 transition-transform duration-500"})]})})]})}),(0,a.jsxs)(g,{open:e,onOpenChange:t,children:[(0,a.jsx)(u,{asChild:!0,className:"lg:hidden",children:(0,a.jsxs)(i.$,{variant:"ghost",size:"icon",className:"group relative overflow-hidden transition-all duration-300 hover:scale-110 focus:scale-110 rounded-xl hover:bg-green-50",children:[(0,a.jsx)("div",{className:"absolute inset-0 transition-all duration-300 ".concat(e?"rotate-180 scale-75":""),children:e?(0,a.jsx)(m.A,{className:"absolute inset-0 m-auto h-5 w-5 text-gray-600"}):(0,a.jsx)(z,{className:"absolute inset-0 m-auto h-5 w-5 text-gray-600"})}),(0,a.jsx)("span",{className:"sr-only",children:"Toggle menu"})]})}),(0,a.jsxs)(v,{side:"right",className:"w-[320px] sm:w-[400px] z-[100] p-0 bg-white/95 backdrop-blur-xl border-l border-gray-200/50",children:[(0,a.jsx)(d.hE,{className:"sr-only",children:"Mobile Navigation Menu"}),(0,a.jsxs)("div",{className:"flex flex-col h-full",children:[(0,a.jsx)("div",{className:"p-6 border-b border-gray-200/50",children:(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[V?(0,a.jsx)("div",{className:"relative overflow-hidden rounded-full",children:(0,a.jsx)("img",{src:V,alt:"".concat(U," Logo"),className:"h-8 w-auto object-contain"})}):(0,a.jsx)("div",{className:"h-8 w-8 bg-gradient-to-br from-green-600 to-green-700 rounded-full flex items-center justify-center shadow-lg",children:(0,a.jsx)(y.A,{className:"h-5 w-5 text-white"})}),(0,a.jsx)("span",{className:"text-lg font-bold text-gray-900",children:(0,a.jsx)("span",{className:"bg-gradient-to-r from-green-600 to-green-700 bg-clip-text text-transparent",children:U})})]})}),(0,a.jsxs)("div",{className:"flex-1 p-6 space-y-6 overflow-y-auto",children:[(0,a.jsx)(l(),{href:"/",className:"block text-lg font-medium transition-all duration-200 p-3 rounded-xl ".concat("/"===M?"text-green-600 bg-green-50":"text-gray-600 hover:text-green-600 hover:bg-green-50/50"),onClick:()=>t(!1),children:"Home"}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsx)("h3",{className:"text-sm font-semibold text-gray-400 uppercase tracking-wider",children:"About"}),$.map(e=>{let r=e.icon;return(0,a.jsxs)(l(),{href:e.href,className:"flex items-center gap-3 text-lg font-medium transition-all duration-200 p-3 rounded-xl ".concat(M===e.href?"text-green-600 bg-green-50":"text-gray-600 hover:text-green-600 hover:bg-green-50/50"),onClick:()=>t(!1),children:[(0,a.jsx)(r,{className:"h-5 w-5"}),e.label]},e.href)})]}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsx)("h3",{className:"text-sm font-semibold text-gray-400 uppercase tracking-wider",children:"Programs"}),R.map(e=>{let r=e.icon;return(0,a.jsxs)(l(),{href:e.href,className:"flex items-center gap-3 text-lg font-medium transition-all duration-200 p-3 rounded-xl ".concat(M===e.href?"text-green-600 bg-green-50":"text-gray-600 hover:text-green-600 hover:bg-green-50/50"),onClick:()=>t(!1),children:[(0,a.jsx)(r,{className:"h-5 w-5"}),e.label]},e.href)})]}),(0,a.jsxs)(l(),{href:"/impact",className:"flex items-center gap-3 text-lg font-medium transition-all duration-200 p-3 rounded-xl ".concat("/impact"===M?"text-green-600 bg-green-50":"text-gray-600 hover:text-green-600 hover:bg-green-50/50"),onClick:()=>t(!1),children:[(0,a.jsx)(C.A,{className:"h-5 w-5"}),"Impact"]}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsx)("h3",{className:"text-sm font-semibold text-gray-400 uppercase tracking-wider",children:"Get Involved"}),W.map(e=>{let r=e.icon;return(0,a.jsxs)(l(),{href:e.href,className:"flex items-center gap-3 text-lg font-medium transition-all duration-200 p-3 rounded-xl ".concat(M===e.href?"text-green-600 bg-green-50":"text-gray-600 hover:text-green-600 hover:bg-green-50/50"),onClick:()=>t(!1),children:[(0,a.jsx)(r,{className:"h-5 w-5"}),e.label]},e.href)})]}),!r&&(0,a.jsx)("div",{className:"pt-4 space-y-3",children:(0,a.jsx)(l(),{href:"/auth/login",onClick:()=>t(!1),children:(0,a.jsx)(i.$,{variant:"outline",className:"w-full border-green-300 text-green-600 hover:bg-green-50 rounded-xl py-3",children:"Login"})})}),r&&(0,a.jsx)("div",{className:"pt-4",children:(0,a.jsx)(l(),{href:"/dashboard",onClick:()=>t(!1),children:(0,a.jsx)(i.$,{variant:"outline",className:"w-full border-green-300 text-green-600 hover:bg-green-50 rounded-xl py-3 mb-3",children:"Dashboard"})})})]}),(0,a.jsx)("div",{className:"p-6 border-t border-gray-200/50",children:(0,a.jsx)(l(),{href:"/donate",onClick:()=>t(!1),children:(0,a.jsx)(i.$,{className:"w-full bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 text-white transition-all duration-300 rounded-xl py-3 font-medium hover:shadow-lg",children:"Donate Now"})})})]})]})]})]})})}},67133:(e,t,r)=>{"use strict";r.d(t,{SQ:()=>h,_2:()=>g,mB:()=>u,rI:()=>c,ty:()=>m});var a=r(95155),s=r(12115),n=r(48698),o=r(13052),l=r(5196),i=r(9428),d=r(53999);let c=n.bL,m=n.l9;n.YJ,n.ZL,n.Pb,n.z6,s.forwardRef((e,t)=>{let{className:r,inset:s,children:l,...i}=e;return(0,a.jsxs)(n.ZP,{ref:t,className:(0,d.cn)("flex cursor-default gap-2 select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent data-[state=open]:bg-accent [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",s&&"pl-8",r),...i,children:[l,(0,a.jsx)(o.A,{className:"ml-auto"})]})}).displayName=n.ZP.displayName,s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)(n.G5,{ref:t,className:(0,d.cn)("z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",r),...s})}).displayName=n.G5.displayName;let h=s.forwardRef((e,t)=>{let{className:r,sideOffset:s=4,...o}=e;return(0,a.jsx)(n.ZL,{children:(0,a.jsx)(n.UC,{ref:t,sideOffset:s,className:(0,d.cn)("z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",r),...o})})});h.displayName=n.UC.displayName;let g=s.forwardRef((e,t)=>{let{className:r,inset:s,...o}=e;return(0,a.jsx)(n.q7,{ref:t,className:(0,d.cn)("relative flex cursor-default select-none items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",s&&"pl-8",r),...o})});g.displayName=n.q7.displayName,s.forwardRef((e,t)=>{let{className:r,children:s,checked:o,...i}=e;return(0,a.jsxs)(n.H_,{ref:t,className:(0,d.cn)("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",r),checked:o,...i,children:[(0,a.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,a.jsx)(n.VF,{children:(0,a.jsx)(l.A,{className:"h-4 w-4"})})}),s]})}).displayName=n.H_.displayName,s.forwardRef((e,t)=>{let{className:r,children:s,...o}=e;return(0,a.jsxs)(n.hN,{ref:t,className:(0,d.cn)("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",r),...o,children:[(0,a.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,a.jsx)(n.VF,{children:(0,a.jsx)(i.A,{className:"h-2 w-2 fill-current"})})}),s]})}).displayName=n.hN.displayName,s.forwardRef((e,t)=>{let{className:r,inset:s,...o}=e;return(0,a.jsx)(n.JU,{ref:t,className:(0,d.cn)("px-2 py-1.5 text-sm font-semibold",s&&"pl-8",r),...o})}).displayName=n.JU.displayName;let u=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)(n.wv,{ref:t,className:(0,d.cn)("-mx-1 my-1 h-px bg-muted",r),...s})});u.displayName=n.wv.displayName},71366:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(19946).A)("MessageCircle",[["path",{d:"M7.9 20A9 9 0 1 0 4 16.1L2 22Z",key:"vv11sd"}]])},72894:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(19946).A)("Linkedin",[["path",{d:"M16 8a6 6 0 0 1 6 6v7h-4v-7a2 2 0 0 0-2-2 2 2 0 0 0-2 2v7h-4v-7a6 6 0 0 1 6-6z",key:"c2jq9f"}],["rect",{width:"4",height:"12",x:"2",y:"9",key:"mk3on5"}],["circle",{cx:"4",cy:"4",r:"2",key:"bt5ra8"}]])},74699:e=>{e.exports={style:{fontFamily:"'Inter', 'Inter Fallback'",fontStyle:"normal"},className:"__className_e8ce0c",variable:"__variable_e8ce0c"}},75684:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(19946).A)("Instagram",[["rect",{width:"20",height:"20",x:"2",y:"2",rx:"5",ry:"5",key:"2e1cvw"}],["path",{d:"M16 11.37A4 4 0 1 1 12.63 8 4 4 0 0 1 16 11.37z",key:"9exkf1"}],["line",{x1:"17.5",x2:"17.51",y1:"6.5",y2:"6.5",key:"r4j83e"}]])},77261:(e,t,r)=>{"use strict";r.d(t,{Footer:()=>y});var a=r(95155),s=r(12115),n=r(6874),o=r.n(n),l=r(10488),i=r(18175),d=r(75684),c=r(72894);let m=(0,r(19946).A)("Youtube",[["path",{d:"M2.5 17a24.12 24.12 0 0 1 0-10 2 2 0 0 1 1.4-1.4 49.56 49.56 0 0 1 16.2 0A2 2 0 0 1 21.5 7a24.12 24.12 0 0 1 0 10 2 2 0 0 1-1.4 1.4 49.55 49.55 0 0 1-16.2 0A2 2 0 0 1 2.5 17",key:"1q2vi4"}],["path",{d:"m10 15 5-3-5-3z",key:"1jp15x"}]]);var h=r(51976),g=r(71366),u=r(4516),x=r(19420),p=r(28883),f=r(97168),v=r(89852),b=r(44531);function y(){let[e,t]=(0,s.useState)(!1);(0,s.useEffect)(()=>{t(!0)},[]);let{settings:r,loading:n}=(0,b.t)(),y=(null==r?void 0:r.app_name)||"Laravel NGO",j=null==r?void 0:r.app_logo,N=(null==r?void 0:r.contact_email)||"<EMAIL>",w=(null==r?void 0:r.contact_phone)||"+234 ************",k=(null==r?void 0:r.site_description)||"Empowering communities through education, development programs, and sustainable initiatives.",A=null==r?void 0:r.organization_address,_=null==r?void 0:r.organization_city,z=null==r?void 0:r.organization_state,C=[A,_,z,null==r?void 0:r.organization_country,null==r?void 0:r.organization_postal_code].filter(Boolean).join(", "),L=[{key:"social_facebook",icon:l.A,label:"Facebook"},{key:"social_twitter",icon:i.A,label:"Twitter"},{key:"social_instagram",icon:d.A,label:"Instagram"},{key:"social_linkedin",icon:c.A,label:"LinkedIn"},{key:"social_youtube",icon:m,label:"YouTube"}];return(0,a.jsx)("footer",{className:"bg-green-950 dark:bg-black text-white",children:(0,a.jsxs)("div",{className:"container mx-auto px-4 sm:px-6 lg:px-8 py-12",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8",children:[(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)(o(),{href:"/",className:"flex items-center gap-2",children:[j?(0,a.jsx)("img",{src:j,alt:"".concat(y," Logo"),className:"h-8 w-auto object-contain"}):(0,a.jsx)("div",{className:"relative h-8 w-8 overflow-hidden rounded-full bg-green-600",children:(0,a.jsx)(h.A,{className:"absolute inset-0 m-auto h-5 w-5 text-white"})}),(0,a.jsx)("span",{className:"text-xl font-bold",children:(0,a.jsx)("span",{className:"text-amber-400 dark:text-amber-400",children:y})})]}),(0,a.jsx)("p",{className:"text-green-200 text-sm",children:k}),(0,a.jsxs)("div",{className:"flex flex-wrap gap-2",children:[L.map(e=>{let{key:t,icon:s,label:n}=e,l=null==r?void 0:r[t];return l?(0,a.jsx)(o(),{href:l,target:"_blank",rel:"noopener noreferrer",children:(0,a.jsx)(f.$,{size:"icon",variant:"ghost",className:"text-green-200 hover:text-amber-400 hover:bg-green-800/50 rounded-full transition-colors duration-200","aria-label":n,children:(0,a.jsx)(s,{className:"h-4 w-4"})})},t):null}),(null==r?void 0:r.social_whatsapp)&&(0,a.jsx)(o(),{href:"https://wa.me/".concat(r.social_whatsapp.replace(/[^0-9]/g,"")),target:"_blank",rel:"noopener noreferrer",children:(0,a.jsx)(f.$,{size:"icon",variant:"ghost",className:"text-green-200 hover:text-amber-400 hover:bg-green-800/50 rounded-full transition-colors duration-200","aria-label":"WhatsApp",children:(0,a.jsx)(g.A,{className:"h-4 w-4"})})}),(null==r?void 0:r.social_telegram)&&(0,a.jsx)(o(),{href:r.social_telegram,target:"_blank",rel:"noopener noreferrer",children:(0,a.jsx)(f.$,{size:"icon",variant:"ghost",className:"text-green-200 hover:text-amber-400 hover:bg-green-800/50 rounded-full transition-colors duration-200","aria-label":"Telegram",children:(0,a.jsx)("svg",{className:"h-4 w-4",fill:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{d:"M11.944 0A12 12 0 0 0 0 12a12 12 0 0 0 12 12 12 12 0 0 0 12-12A12 12 0 0 0 12 0a12 12 0 0 0-.056 0zm4.962 7.224c.1-.002.321.023.465.14a.506.506 0 0 1 .171.325c.016.093.036.306.02.472-.18 1.898-.962 6.502-1.36 8.627-.168.9-.499 1.201-.82 1.23-.696.065-1.225-.46-1.9-.902-1.056-.693-1.653-1.124-2.678-1.8-1.185-.78-.417-1.21.258-1.91.177-.184 3.247-2.977 3.307-3.23.007-.032.014-.15-.056-.212s-.174-.041-.249-.024c-.106.024-1.793 1.14-5.061 3.345-.48.33-.913.49-1.302.48-.428-.008-1.252-.241-1.865-.44-.752-.245-1.349-.374-1.297-.789.027-.216.325-.437.893-.663 3.498-1.524 5.83-2.529 6.998-3.014 3.332-1.386 4.025-1.627 4.476-1.635z"})})})}),(null==r?void 0:r.social_tiktok)&&(0,a.jsx)(o(),{href:r.social_tiktok,target:"_blank",rel:"noopener noreferrer",children:(0,a.jsx)(f.$,{size:"icon",variant:"ghost",className:"text-green-200 hover:text-amber-400 hover:bg-green-800/50 rounded-full transition-colors duration-200","aria-label":"TikTok",children:(0,a.jsx)("svg",{className:"h-4 w-4",fill:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{d:"M12.525.02c1.31-.02 2.61-.01 3.91-.02.08 1.53.63 3.09 1.75 4.17 1.12 1.11 2.7 1.62 4.24 1.79v4.03c-1.44-.05-2.89-.35-4.2-.97-.57-.26-1.1-.59-1.62-.93-.01 2.92.01 5.84-.02 8.75-.08 1.4-.54 2.79-1.35 3.94-1.31 1.92-3.58 3.17-5.91 3.21-1.43.08-2.86-.31-4.08-1.03-2.02-1.19-3.44-3.37-3.65-5.71-.02-.5-.03-1-.01-1.49.18-1.9 1.12-3.72 2.58-4.96 1.66-1.44 3.98-2.13 6.15-1.72.02 1.48-.04 2.96-.04 4.44-.99-.32-2.15-.23-3.02.37-.63.41-1.11 1.04-1.36 1.75-.21.51-.15 1.07-.14 1.61.24 1.64 1.82 3.02 3.5 2.87 1.12-.01 2.19-.66 2.77-1.61.19-.33.4-.67.41-1.06.1-1.79.06-3.57.07-5.36.01-4.03-.01-8.05.02-12.07z"})})})})]})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold",children:"Quick Links"}),(0,a.jsxs)("ul",{className:"space-y-2",children:[(0,a.jsx)("li",{children:(0,a.jsx)(o(),{href:"/about",className:"text-green-200 hover:text-amber-400 transition-colors",children:"About Us"})}),(0,a.jsx)("li",{children:(0,a.jsx)(o(),{href:"/projects",className:"text-green-200 hover:text-amber-400 transition-colors",children:"Our Projects"})}),(0,a.jsx)("li",{children:(0,a.jsx)(o(),{href:"/scholarships",className:"text-green-200 hover:text-amber-400 transition-colors",children:"Scholarships"})}),(0,a.jsx)("li",{children:(0,a.jsx)(o(),{href:"/learn-with-us",className:"text-green-200 hover:text-amber-400 transition-colors",children:"Learn With Us"})}),(0,a.jsx)("li",{children:(0,a.jsx)(o(),{href:"/impact",className:"text-green-200 hover:text-amber-400 transition-colors",children:"Our Impact"})}),(0,a.jsx)("li",{children:(0,a.jsx)(o(),{href:"/team",className:"text-green-200 hover:text-amber-400 transition-colors",children:"Our Team"})})]})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold",children:"Contact Info"}),(0,a.jsxs)("div",{className:"space-y-3",children:[C&&(0,a.jsxs)("div",{className:"flex items-start gap-2",children:[(0,a.jsx)(u.A,{className:"h-4 w-4 text-green-400 mt-0.5 flex-shrink-0"}),(0,a.jsx)("span",{className:"text-green-200 text-sm",children:C})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(x.A,{className:"h-4 w-4 text-green-400"}),(0,a.jsx)("a",{href:"tel:".concat(w),className:"text-green-200 text-sm hover:text-amber-400 transition-colors",children:w})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(p.A,{className:"h-4 w-4 text-green-400"}),(0,a.jsx)("a",{href:"mailto:".concat(N),className:"text-green-200 text-sm hover:text-amber-400 transition-colors",children:N})]})]})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold",children:"Stay Updated"}),(0,a.jsx)("p",{className:"text-green-200 text-sm",children:"Subscribe to our newsletter for updates on our programs and impact."}),e&&(0,a.jsxs)("div",{className:"flex gap-2",suppressHydrationWarning:!0,children:[(0,a.jsx)(v.p,{type:"email",placeholder:"Your email",className:"bg-green-900 border-green-700 text-white placeholder:text-green-300 focus:ring-amber-500 focus:border-amber-500 rounded-md",suppressHydrationWarning:!0}),(0,a.jsx)(f.$,{className:"bg-amber-500 hover:bg-amber-600 text-green-950 dark:text-green-950",suppressHydrationWarning:!0,children:"Subscribe"})]}),(0,a.jsxs)("div",{className:"pt-4 space-y-1",children:[(0,a.jsx)(o(),{href:"/privacy",className:"block text-green-300 text-xs hover:text-amber-400 transition-colors",children:"Privacy Policy"}),(0,a.jsx)(o(),{href:"/terms",className:"block text-green-300 text-xs hover:text-amber-400 transition-colors",children:"Terms of Service"})]})]})]}),(0,a.jsxs)("div",{className:"border-t border-green-800 mt-8 pt-8 text-center",children:[(0,a.jsxs)("p",{className:"text-green-200 text-sm",children:["\xa9 ",new Date().getFullYear()," ",y,". All rights reserved."]}),(0,a.jsx)("p",{className:"text-green-300 text-xs mt-2",children:"Built with ❤️ to empower communities and create lasting impact."})]})]})})}},86552:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,19324,23)),Promise.resolve().then(r.bind(r,77261)),Promise.resolve().then(r.bind(r,60768)),Promise.resolve().then(r.bind(r,47780)),Promise.resolve().then(r.t.bind(r,74699,23))},87949:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(19946).A)("GraduationCap",[["path",{d:"M21.42 10.922a1 1 0 0 0-.019-1.838L12.83 5.18a2 2 0 0 0-1.66 0L2.6 9.08a1 1 0 0 0 0 1.832l8.57 3.908a2 2 0 0 0 1.66 0z",key:"j76jl0"}],["path",{d:"M22 10v6",key:"1lu8f3"}],["path",{d:"M6 12.5V16a6 3 0 0 0 12 0v-3.5",key:"1r8lef"}]])},89852:(e,t,r)=>{"use strict";r.d(t,{p:()=>o});var a=r(95155),s=r(12115),n=r(53999);let o=s.forwardRef((e,t)=>{let{className:r,type:s,...o}=e;return(0,a.jsx)("input",{type:s,className:(0,n.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",r),ref:t,suppressHydrationWarning:!0,...o})});o.displayName="Input"},97168:(e,t,r)=>{"use strict";r.d(t,{$:()=>d,r:()=>i});var a=r(95155),s=r(12115),n=r(99708),o=r(74466),l=r(53999);let i=(0,o.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-all duration-200 ease-in-out focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 transform hover:scale-105",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90 hover:brightness-110",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90 hover:brightness-110",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground hover:brightness-105",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80 hover:brightness-105",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),d=s.forwardRef((e,t)=>{let{className:r,variant:s,size:o,asChild:d=!1,...c}=e,m=d?n.DX:"button";return(0,a.jsx)(m,{className:(0,l.cn)(i({variant:s,size:o,className:r})),ref:t,suppressHydrationWarning:!0,...c})});d.displayName="Button"}},e=>{var t=t=>e(e.s=t);e.O(0,[2533,8702,1778,6874,598,4057,797,1475,1886,8441,1684,7358],()=>t(86552)),_N_E=e.O()}]);