

<?php $__env->startSection('title', 'Dashboard'); ?>

<?php $__env->startSection('breadcrumb'); ?>
<li class="breadcrumb-item active">Dashboard</li>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
<!-- Welcome Section -->
<div class="row mb-4">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center flex-wrap">
            <div class="mb-3 mb-md-0">
                <h2 class="mb-1 text-gradient fw-bold">Welcome back, <?php echo e(session('admin_user.first_name', 'Admin')); ?>! 👋</h2>
                <p class="text-muted mb-0">Here's what's happening with HLTKKQ Foundation today.</p>
            </div>
            <div class="d-flex align-items-center gap-3">
                <div class="text-end">
                    <div class="text-muted small"><?php echo e(now()->format('l')); ?></div>
                    <div class="fw-semibold"><?php echo e(now()->format('F j, Y')); ?></div>
                </div>
                <span class="badge bg-success-subtle text-success px-3 py-2">
                    <i class="fas fa-circle me-2" style="font-size: 0.6rem;"></i>
                    System Online
                </span>
                <div class="dropdown">
                    <button class="btn btn-outline-primary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                        <i class="fas fa-download me-2"></i>Export
                    </button>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="#" onclick="exportDashboardData('csv')">
                            <i class="fas fa-file-csv me-2"></i>Export CSV
                        </a></li>
                        <li><a class="dropdown-item" href="#" onclick="exportDashboardData('pdf')">
                            <i class="fas fa-file-pdf me-2"></i>Export PDF
                        </a></li>
                        <li><a class="dropdown-item" href="#" onclick="exportDashboardData('excel')">
                            <i class="fas fa-file-excel me-2"></i>Export Excel
                        </a></li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Quick Stats -->
<div class="row mb-4">
    <div class="col-xl-3 col-lg-6 col-md-6 mb-4">
        <div class="stats-card">
            <div class="d-flex justify-content-between align-items-start mb-3">
                <div>
                    <div class="stats-label">Total Users</div>
                    <div class="stats-number"><?php echo e(number_format($stats['totalUsers'])); ?></div>
                    <div class="stats-change positive">
                        <i class="fas fa-arrow-up me-1"></i>
                        +12% from last month
                    </div>
                </div>
                <div class="rounded-circle p-3" style="background: rgba(37, 99, 235, 0.1); color: var(--primary-color);">
                    <i class="fas fa-users fa-lg"></i>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-lg-6 col-md-6 mb-4">
        <div class="stats-card">
            <div class="d-flex justify-content-between align-items-start mb-3">
                <div>
                    <div class="stats-label">Total Donations</div>
                    <div class="stats-number">₦<?php echo e(number_format($stats['totalDonations'])); ?></div>
                    <div class="stats-change positive">
                        <i class="fas fa-arrow-up me-1"></i>
                        +8.5% from last month
                    </div>
                </div>
                <div class="rounded-circle p-3" style="background: rgba(16, 185, 129, 0.1); color: var(--success-color);">
                    <i class="fas fa-hand-holding-heart fa-lg"></i>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-lg-6 col-md-6 mb-4">
        <div class="stats-card">
            <div class="d-flex justify-content-between align-items-start mb-3">
                <div>
                    <div class="stats-label">Active Programs</div>
                    <div class="stats-number"><?php echo e($stats['activePrograms']); ?></div>
                    <div class="stats-change positive">
                        <i class="fas fa-arrow-up me-1"></i>
                        +2 new this month
                    </div>
                </div>
                <div class="rounded-circle p-3" style="background: rgba(245, 158, 11, 0.1); color: var(--warning-color);">
                    <i class="fas fa-project-diagram fa-lg"></i>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-lg-6 col-md-6 mb-4">
        <div class="stats-card">
            <div class="d-flex justify-content-between align-items-start mb-3">
                <div>
                    <div class="stats-label">Volunteers</div>
                    <div class="stats-number"><?php echo e($stats['totalVolunteers']); ?></div>
                    <div class="stats-change positive">
                        <i class="fas fa-arrow-up me-1"></i>
                        +15% from last month
                    </div>
                </div>
                <div class="rounded-circle p-3" style="background: rgba(139, 69, 19, 0.1); color: #8b4513;">
                    <i class="fas fa-hands-helping fa-lg"></i>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Main Content Row -->
<div class="row">
    <!-- Charts Section -->
    <div class="col-xl-8 col-lg-7 mb-4">
        <div class="card h-100">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-area me-2 text-primary"></i>
                    Donation Analytics
                </h5>
                <div class="dropdown">
                    <button class="btn btn-outline-secondary btn-sm dropdown-toggle" type="button" data-bs-toggle="dropdown">
                        Last 12 Months
                    </button>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="#">Last 30 Days</a></li>
                        <li><a class="dropdown-item" href="#">Last 6 Months</a></li>
                        <li><a class="dropdown-item" href="#">Last 12 Months</a></li>
                        <li><a class="dropdown-item" href="#">All Time</a></li>
                    </ul>
                </div>
            </div>
            <div class="card-body">
                <div class="position-relative" style="height: 350px;">
                    <canvas id="donationChart"></canvas>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Recent Activity -->
    <div class="col-xl-4 col-lg-5 mb-4">
        <div class="card h-100">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-bell me-2 text-primary"></i>
                    Recent Activity
                </h5>
            </div>
            <div class="card-body p-0">
                <div class="list-group list-group-flush">
                    <?php $__currentLoopData = $recentActivities; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $activity): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <div class="list-group-item">
                        <div class="d-flex align-items-center">
                            <div class="flex-shrink-0">
                                <div class="rounded-circle p-2" style="background: rgba(37, 99, 235, 0.1);">
                                    <i class="fas fa-<?php echo e($activity['icon']); ?> text-primary"></i>
                                </div>
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <p class="mb-1"><?php echo e($activity['description']); ?></p>
                                <small class="text-muted"><?php echo e(\Carbon\Carbon::parse($activity['timestamp'])->diffForHumans()); ?></small>
                            </div>
                        </div>
                    </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions & Recent Items -->
<div class="row">
    <!-- Quick Actions -->
    <div class="col-xl-4 col-lg-6 mb-4">
        <div class="card h-100">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-bolt me-2 text-primary"></i>
                    Quick Actions
                </h5>
            </div>
            <div class="card-body">
                <div class="row g-3">
                    <div class="col-6">
                        <a href="<?php echo e(route('admin.donations.create')); ?>" class="btn btn-outline-primary w-100">
                            <i class="fas fa-plus me-2"></i>New Donation
                        </a>
                    </div>
                    <div class="col-6">
                        <a href="<?php echo e(route('admin.events.create')); ?>" class="btn btn-outline-primary w-100">
                            <i class="fas fa-calendar-plus me-2"></i>New Event
                        </a>
                    </div>
                    <div class="col-6">
                        <a href="<?php echo e(route('admin.programs.create')); ?>" class="btn btn-outline-primary w-100">
                            <i class="fas fa-project-diagram me-2"></i>New Program
                        </a>
                    </div>
                    <div class="col-6">
                        <a href="<?php echo e(route('admin.blog.posts.create')); ?>" class="btn btn-outline-primary w-100">
                            <i class="fas fa-blog me-2"></i>New Post
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Recent Donations -->
    <div class="col-xl-8 col-lg-6 mb-4">
        <div class="card h-100">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-hand-holding-heart me-2 text-primary"></i>
                    Recent Donations
                </h5>
                <a href="<?php echo e(route('admin.donations.index')); ?>" class="btn btn-link btn-sm">View All</a>
            </div>
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead>
                            <tr>
                                <th>Donor</th>
                                <th>Amount</th>
                                <th>Campaign</th>
                                <th>Date</th>
                                <th>Status</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php $__currentLoopData = $recentDonations; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $donation): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <?php if($donation->donor): ?>
                                            <img src="<?php echo e($donation->donor->profile_picture_url); ?>" alt="<?php echo e($donation->donor->full_name); ?>" class="rounded-circle me-2" width="32" height="32">
                                            <div>
                                                <div class="fw-semibold"><?php echo e($donation->donor->full_name); ?></div>
                                                <div class="small text-muted"><?php echo e($donation->donor->email); ?></div>
                                            </div>
                                        <?php else: ?>
                                            <img src="<?php echo e(asset('images/default-avatar.svg')); ?>" alt="Anonymous" class="rounded-circle me-2" width="32" height="32">
                                            <div>
                                                <div class="fw-semibold"><?php echo e($donation->donor_name ?? 'Anonymous'); ?></div>
                                                <div class="small text-muted"><?php echo e($donation->donor_email ?? 'N/A'); ?></div>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                </td>
                                <td>₦<?php echo e(number_format($donation->amount)); ?></td>
                                <td><?php echo e($donation->campaign->name ?? 'General Fund'); ?></td>
                                <td><?php echo e($donation->created_at->format('M d, Y')); ?></td>
                                <td>
                                    <?php
                                        $statusColor = match($donation->payment_status) {
                                            'completed' => 'success',
                                            'pending' => 'warning',
                                            'failed' => 'danger',
                                            'refunded' => 'info',
                                            default => 'secondary'
                                        };
                                    ?>
                                    <span class="badge bg-<?php echo e($statusColor); ?>-subtle text-<?php echo e($statusColor); ?>">
                                        <?php echo e(ucfirst($donation->payment_status)); ?>

                                    </span>
                                </td>
                            </tr>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
// Initialize Donation Chart
const ctx = document.getElementById('donationChart').getContext('2d');
const donationChart = new Chart(ctx, {
    type: 'line',
    data: {
        labels: <?php echo json_encode($donationStats->pluck('month')); ?>,
        datasets: [{
            label: 'Donations',
            data: <?php echo json_encode($donationStats->pluck('amount')); ?>,
            borderColor: '#2563eb',
            backgroundColor: 'rgba(37, 99, 235, 0.1)',
            tension: 0.4,
            fill: true
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                display: false
            }
        },
        scales: {
            y: {
                beginAtZero: true,
                ticks: {
                    callback: function(value) {
                        return '₦' + value.toLocaleString();
                    }
                }
            }
        }
    }
});

// Export Dashboard Data
function exportDashboardData(format) {
    showNotification(`Preparing ${format.toUpperCase()} export...`, 'info');

    // Show loading state
    const exportBtn = event.target.closest('.dropdown-item');
    if (exportBtn) {
        const originalText = exportBtn.innerHTML;
        exportBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Exporting...';

        // Simulate export process
        setTimeout(() => {
            showNotification(`Dashboard data exported as ${format.toUpperCase()}!`, 'success');
            exportBtn.innerHTML = originalText;

            // Create download link
            const link = document.createElement('a');
            link.href = `<?php echo e(route('admin.dashboard')); ?>?export=${format}&period=${document.getElementById('timePeriod')?.value || '30'}`;
            link.download = `dashboard-${new Date().toISOString().split('T')[0]}.${format}`;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }, 2000);
    }
}

// Enhanced Dashboard Functions
function updateDashboard() {
    const timePeriod = document.getElementById('timePeriod')?.value || '30';
    const programType = document.getElementById('programType')?.value || 'all';
    const location = document.getElementById('location')?.value || 'all';

    showNotification('Updating dashboard...', 'info');

    // Show loading state
    const updateBtn = document.querySelector('button[onclick="updateDashboard()"]');
    if (updateBtn) {
        const originalText = updateBtn.innerHTML;
        updateBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Updating...';
        updateBtn.disabled = true;

        // Simulate API call
        setTimeout(() => {
            showNotification('Dashboard updated successfully!', 'success');
            updateBtn.innerHTML = originalText;
            updateBtn.disabled = false;

            // Here you would make actual API calls to update the data
            loadRealtimeData();
        }, 1500);
    }
}

function resetFilters() {
    if (document.getElementById('timePeriod')) {
        document.getElementById('timePeriod').value = '30';
    }
    if (document.getElementById('programType')) {
        document.getElementById('programType').value = 'all';
    }
    if (document.getElementById('location')) {
        document.getElementById('location').value = 'all';
    }

    // Trigger Select2 update if available
    if (typeof $ !== 'undefined' && typeof $.fn.select2 !== 'undefined') {
        $('.select2').trigger('change');
    }

    showNotification('Filters reset to default', 'info');
    updateDashboard();
}

function loadRealtimeData() {
    // Simulate real-time data updates
    const statsNumbers = document.querySelectorAll('.stats-number');
    statsNumbers.forEach(stat => {
        const currentText = stat.textContent.replace(/[₦,]/g, '');
        const currentValue = parseInt(currentText) || 0;
        const variation = Math.floor(Math.random() * 20) - 10; // -10 to +10
        const newValue = Math.max(0, currentValue + variation);

        if (stat.textContent.includes('₦')) {
            animateValue(stat, currentValue, newValue, 1000, '₦');
        } else {
            animateValue(stat, currentValue, newValue, 1000);
        }
    });
}

function animateValue(element, start, end, duration, prefix = '') {
    const startTime = performance.now();

    function update(currentTime) {
        const elapsed = currentTime - startTime;
        const progress = Math.min(elapsed / duration, 1);

        const current = Math.floor(start + (end - start) * progress);
        element.textContent = prefix + new Intl.NumberFormat().format(current);

        if (progress < 1) {
            requestAnimationFrame(update);
        }
    }

    requestAnimationFrame(update);
}

// Initialize enhanced dashboard features
document.addEventListener('DOMContentLoaded', function() {
    // Initialize Select2 dropdowns
    if (typeof $ !== 'undefined' && typeof $.fn.select2 !== 'undefined') {
        $('.select2').select2({
            theme: 'bootstrap-5',
            width: '100%'
        });
    }

    // Auto-refresh every 5 minutes
    setInterval(loadRealtimeData, 300000);

    // Add hover effects to cards
    const cards = document.querySelectorAll('.card');
    cards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transition = 'all 0.3s ease';
            this.style.transform = 'translateY(-2px)';
            this.style.boxShadow = '0 10px 25px rgba(0,0,0,0.1)';
        });

        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
            this.style.boxShadow = '';
        });
    });
});
</script>
<?php $__env->stopPush(); ?>
<?php echo $__env->make('layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\laragon\www\laravel-api-ngo\resources\views/admin/dashboard.blade.php ENDPATH**/ ?>