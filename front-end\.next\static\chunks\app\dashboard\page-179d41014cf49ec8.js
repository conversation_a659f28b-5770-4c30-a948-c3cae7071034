(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5105],{5294:(e,t,a)=>{Promise.resolve().then(a.bind(a,91650))},67133:(e,t,a)=>{"use strict";a.d(t,{SQ:()=>u,_2:()=>f,mB:()=>p,rI:()=>c,ty:()=>m});var s=a(95155),r=a(12115),l=a(48698),n=a(13052),o=a(5196),i=a(9428),d=a(53999);let c=l.bL,m=l.l9;l.YJ,l.Z<PERSON>,l.Pb,l.z6,r.forwardRef((e,t)=>{let{className:a,inset:r,children:o,...i}=e;return(0,s.jsxs)(l.ZP,{ref:t,className:(0,d.cn)("flex cursor-default gap-2 select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent data-[state=open]:bg-accent [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",r&&"pl-8",a),...i,children:[o,(0,s.jsx)(n.A,{className:"ml-auto"})]})}).displayName=l.ZP.displayName,r.forwardRef((e,t)=>{let{className:a,...r}=e;return(0,s.jsx)(l.G5,{ref:t,className:(0,d.cn)("z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",a),...r})}).displayName=l.G5.displayName;let u=r.forwardRef((e,t)=>{let{className:a,sideOffset:r=4,...n}=e;return(0,s.jsx)(l.ZL,{children:(0,s.jsx)(l.UC,{ref:t,sideOffset:r,className:(0,d.cn)("z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",a),...n})})});u.displayName=l.UC.displayName;let f=r.forwardRef((e,t)=>{let{className:a,inset:r,...n}=e;return(0,s.jsx)(l.q7,{ref:t,className:(0,d.cn)("relative flex cursor-default select-none items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",r&&"pl-8",a),...n})});f.displayName=l.q7.displayName,r.forwardRef((e,t)=>{let{className:a,children:r,checked:n,...i}=e;return(0,s.jsxs)(l.H_,{ref:t,className:(0,d.cn)("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",a),checked:n,...i,children:[(0,s.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,s.jsx)(l.VF,{children:(0,s.jsx)(o.A,{className:"h-4 w-4"})})}),r]})}).displayName=l.H_.displayName,r.forwardRef((e,t)=>{let{className:a,children:r,...n}=e;return(0,s.jsxs)(l.hN,{ref:t,className:(0,d.cn)("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",a),...n,children:[(0,s.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,s.jsx)(l.VF,{children:(0,s.jsx)(i.A,{className:"h-2 w-2 fill-current"})})}),r]})}).displayName=l.hN.displayName,r.forwardRef((e,t)=>{let{className:a,inset:r,...n}=e;return(0,s.jsx)(l.JU,{ref:t,className:(0,d.cn)("px-2 py-1.5 text-sm font-semibold",r&&"pl-8",a),...n})}).displayName=l.JU.displayName;let p=r.forwardRef((e,t)=>{let{className:a,...r}=e;return(0,s.jsx)(l.wv,{ref:t,className:(0,d.cn)("-mx-1 my-1 h-px bg-muted",a),...r})});p.displayName=l.wv.displayName},91650:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>es});var s=a(95155),r=a(12115),l=a(35695),n=a(31886),o=a(51154),i=a(51976),d=a(19946);let c=(0,d.A)("ExternalLink",[["path",{d:"M15 3h6v6",key:"1q9fwt"}],["path",{d:"M10 14 21 3",key:"gplh6r"}],["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}]]);var m=a(381);let u=(0,d.A)("LogOut",[["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}],["polyline",{points:"16 17 21 12 16 7",key:"1gabdz"}],["line",{x1:"21",x2:"9",y1:"12",y2:"12",key:"1uyos4"}]]);var f=a(97168),p=a(67133),x=a(46081),h=a(6101),g=a(15452),y=a(85185),N=a(99708),j="AlertDialog",[v,b]=(0,x.A)(j,[g.Hs]),w=(0,g.Hs)(),A=e=>{let{__scopeAlertDialog:t,...a}=e,r=w(t);return(0,s.jsx)(g.bL,{...r,...a,modal:!0})};A.displayName=j;var _=r.forwardRef((e,t)=>{let{__scopeAlertDialog:a,...r}=e,l=w(a);return(0,s.jsx)(g.l9,{...l,...r,ref:t})});_.displayName="AlertDialogTrigger";var R=e=>{let{__scopeAlertDialog:t,...a}=e,r=w(t);return(0,s.jsx)(g.ZL,{...r,...a})};R.displayName="AlertDialogPortal";var k=r.forwardRef((e,t)=>{let{__scopeAlertDialog:a,...r}=e,l=w(a);return(0,s.jsx)(g.hJ,{...l,...r,ref:t})});k.displayName="AlertDialogOverlay";var D="AlertDialogContent",[S,z]=v(D),L=r.forwardRef((e,t)=>{let{__scopeAlertDialog:a,children:l,...n}=e,o=w(a),i=r.useRef(null),d=(0,h.s)(t,i),c=r.useRef(null);return(0,s.jsx)(g.G$,{contentName:D,titleName:C,docsSlug:"alert-dialog",children:(0,s.jsx)(S,{scope:a,cancelRef:c,children:(0,s.jsxs)(g.UC,{role:"alertdialog",...o,...n,ref:d,onOpenAutoFocus:(0,y.m)(n.onOpenAutoFocus,e=>{var t;e.preventDefault(),null===(t=c.current)||void 0===t||t.focus({preventScroll:!0})}),onPointerDownOutside:e=>e.preventDefault(),onInteractOutside:e=>e.preventDefault(),children:[(0,s.jsx)(N.xV,{children:l}),(0,s.jsx)(q,{contentRef:i})]})})})});L.displayName=D;var C="AlertDialogTitle",O=r.forwardRef((e,t)=>{let{__scopeAlertDialog:a,...r}=e,l=w(a);return(0,s.jsx)(g.hE,{...l,...r,ref:t})});O.displayName=C;var P="AlertDialogDescription",E=r.forwardRef((e,t)=>{let{__scopeAlertDialog:a,...r}=e,l=w(a);return(0,s.jsx)(g.VY,{...l,...r,ref:t})});E.displayName=P;var I=r.forwardRef((e,t)=>{let{__scopeAlertDialog:a,...r}=e,l=w(a);return(0,s.jsx)(g.bm,{...l,...r,ref:t})});I.displayName="AlertDialogAction";var F="AlertDialogCancel",H=r.forwardRef((e,t)=>{let{__scopeAlertDialog:a,...r}=e,{cancelRef:l}=z(F,a),n=w(a),o=(0,h.s)(t,l);return(0,s.jsx)(g.bm,{...n,...r,ref:o})});H.displayName=F;var q=e=>{let{contentRef:t}=e,a="`".concat(D,"` requires a description for the component to be accessible for screen reader users.\n\nYou can add a description to the `").concat(D,"` by passing a `").concat(P,"` component as a child, which also benefits sighted users by adding visible context to the dialog.\n\nAlternatively, you can use your own component as a description by assigning it an `id` and passing the same value to the `aria-describedby` prop in `").concat(D,"`. If the description is confusing or duplicative for sighted users, you can use the `@radix-ui/react-visually-hidden` primitive as a wrapper around your description component.\n\nFor more information, see https://radix-ui.com/primitives/docs/components/alert-dialog");return r.useEffect(()=>{var e;document.getElementById(null===(e=t.current)||void 0===e?void 0:e.getAttribute("aria-describedby"))||console.warn(a)},[a,t]),null},U=a(53999);let V=r.forwardRef((e,t)=>{let{className:a,...r}=e;return(0,s.jsx)(k,{className:(0,U.cn)("fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",a),...r,ref:t})});V.displayName=k.displayName;let J=r.forwardRef((e,t)=>{let{className:a,...r}=e;return(0,s.jsxs)(R,{children:[(0,s.jsx)(V,{}),(0,s.jsx)(L,{ref:t,className:(0,U.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",a),...r})]})});J.displayName=L.displayName;let Z=e=>{let{className:t,...a}=e;return(0,s.jsx)("div",{className:(0,U.cn)("flex flex-col space-y-2 text-center sm:text-left",t),...a})};Z.displayName="AlertDialogHeader";let B=e=>{let{className:t,...a}=e;return(0,s.jsx)("div",{className:(0,U.cn)("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",t),...a})};B.displayName="AlertDialogFooter";let G=r.forwardRef((e,t)=>{let{className:a,...r}=e;return(0,s.jsx)(O,{ref:t,className:(0,U.cn)("text-lg font-semibold",a),...r})});G.displayName=O.displayName;let M=r.forwardRef((e,t)=>{let{className:a,...r}=e;return(0,s.jsx)(E,{ref:t,className:(0,U.cn)("text-sm text-muted-foreground",a),...r})});M.displayName=E.displayName;let W=r.forwardRef((e,t)=>{let{className:a,...r}=e;return(0,s.jsx)(I,{ref:t,className:(0,U.cn)((0,f.r)(),a),...r})});W.displayName=I.displayName;let Y=r.forwardRef((e,t)=>{let{className:a,...r}=e;return(0,s.jsx)(H,{ref:t,className:(0,U.cn)((0,f.r)({variant:"outline"}),"mt-2 sm:mt-0",a),...r})});Y.displayName=H.displayName;var T=a(6874),$=a.n(T),Q=a(44531),K=a(98274),X=a(49087),ee=a(92146),et=a(36752),ea=a(21324);function es(){var e;let[t,a]=(0,r.useState)(null),[d,x]=(0,r.useState)(!0),[h,g]=(0,r.useState)(!1),y=(0,l.useRouter)(),{settings:N}=(0,Q.t)(),j=(null==N?void 0:N.app_name)||"Laravel NGO",v=null==N?void 0:N.app_logo,b=async()=>{try{g(!0),await (0,n.ri)(),y.push("/auth/login")}catch(e){console.error("Logout failed:",e),localStorage.removeItem("authToken"),localStorage.removeItem("user"),y.push("/auth/login")}finally{g(!1)}};if((0,r.useEffect)(()=>{if(!(0,n.wR)()){console.log("User not authenticated, redirecting to login"),y.push("/auth/login");return}let e=(0,n.HW)();e&&(console.log("Current user from localStorage:",e),a(e)),(async()=>{try{console.log("Fetching user profile from API...");let t=await n.uE.getProfile();if(console.log("Profile API response:",t),t.success&&t.user){let e=t.user;console.log("Extracted user data:",e),a(e),localStorage.setItem("user",JSON.stringify(e))}else if(t.success&&t.data){let e=t.data.user||t.data;console.log("Extracted user data (fallback):",e),a(e),localStorage.setItem("user",JSON.stringify(e))}else console.error("Failed to fetch profile - invalid response structure:",t),e||y.push("/auth/login")}catch(t){console.error("Failed to fetch user profile:",t),e||y.push("/auth/login")}finally{x(!1)}})()},[y]),d)return(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)(o.A,{className:"h-8 w-8 animate-spin mx-auto mb-4 text-green-600"}),(0,s.jsx)("p",{className:"text-gray-600",children:"Loading your dashboard..."})]})});if(!t)return(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50",children:(0,s.jsx)("div",{className:"text-center",children:(0,s.jsx)("p",{className:"text-red-600",children:"Unable to load user data. Please try logging in again."})})});let w=(null===(e=t.preferences)||void 0===e?void 0:e.user_type)?t.preferences.user_type:"volunteer"===t.role?"volunteer":"admin"===t.role?"admin":"user";return(0,s.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,s.jsx)(()=>(0,s.jsx)("div",{className:"bg-white border-b border-gray-200 px-4 py-3 sm:px-6",children:(0,s.jsxs)("div",{className:"flex justify-between items-center max-w-7xl mx-auto",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-4",children:[v?(0,s.jsx)("img",{src:v,alt:"".concat(j," Logo"),className:"h-8 w-auto object-contain"}):(0,s.jsx)("div",{className:"relative h-8 w-8 overflow-hidden rounded-full bg-gradient-to-br from-green-600 to-green-700 shadow-lg",children:(0,s.jsx)(i.A,{className:"absolute inset-0 m-auto h-5 w-5 text-white"})}),(0,s.jsxs)("h1",{className:"text-xl sm:text-2xl font-bold text-gray-900",children:[j," Dashboard"]})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,s.jsxs)("div",{className:"hidden sm:block text-sm text-gray-600",children:["Welcome, ",t.first_name]}),(0,s.jsx)($(),{href:"/",children:(0,s.jsxs)(f.$,{variant:"outline",size:"sm",className:"flex items-center space-x-2",children:[(0,s.jsx)(c,{className:"h-4 w-4"}),(0,s.jsx)("span",{className:"hidden sm:inline",children:"Visit Website"}),(0,s.jsx)("span",{className:"sm:hidden",children:"Website"})]})}),(0,s.jsxs)(p.rI,{children:[(0,s.jsx)(p.ty,{asChild:!0,children:(0,s.jsxs)(f.$,{variant:"ghost",size:"sm",className:"flex items-center space-x-2",children:[(0,s.jsxs)("div",{className:"w-8 h-8 bg-green-600 rounded-full flex items-center justify-center text-white text-sm font-medium",children:[t.first_name.charAt(0),t.last_name.charAt(0)]}),(0,s.jsx)("span",{className:"hidden sm:inline",children:t.first_name})]})}),(0,s.jsxs)(p.SQ,{align:"end",className:"w-56",children:[(0,s.jsxs)("div",{className:"px-2 py-1.5 text-sm font-medium",children:[t.first_name," ",t.last_name]}),(0,s.jsx)("div",{className:"px-2 py-1.5 text-xs text-gray-500",children:t.email}),(0,s.jsx)(p.mB,{}),(0,s.jsxs)(p._2,{onClick:()=>{let e=document.querySelector('[data-value="profile"]');e&&e.click()},children:[(0,s.jsx)(m.A,{className:"mr-2 h-4 w-4"}),"Profile Settings"]}),(0,s.jsx)(p.mB,{}),(0,s.jsxs)(A,{children:[(0,s.jsx)(_,{asChild:!0,children:(0,s.jsxs)(p._2,{onSelect:e=>e.preventDefault(),children:[(0,s.jsx)(u,{className:"mr-2 h-4 w-4"}),"Logout"]})}),(0,s.jsxs)(J,{children:[(0,s.jsxs)(Z,{children:[(0,s.jsx)(G,{children:"Are you sure you want to logout?"}),(0,s.jsx)(M,{children:"You will need to login again to access your dashboard."})]}),(0,s.jsxs)(B,{children:[(0,s.jsx)(Y,{children:"Cancel"}),(0,s.jsx)(W,{onClick:b,disabled:h,className:"bg-red-600 hover:bg-red-700",children:h?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(o.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Logging out..."]}):"Logout"})]})]})]})]})]})]})]})}),{}),(()=>{let e={user:t};switch(w){case"student":return(0,s.jsx)(K.default,{...e});case"volunteer":return(0,s.jsx)(X.default,{...e});case"partner":return(0,s.jsx)(ee.default,{...e});case"admin":return(0,s.jsx)(ea.default,{...e});default:return(0,s.jsx)(et.default,{...e})}})()]})}}},e=>{var t=t=>e(e.s=t);e.O(0,[4316,3930,1778,6874,598,4057,461,797,7258,9521,1475,3996,1886,1484,1324,6752,2146,9087,8274,8441,1684,7358],()=>t(5294)),_N_E=e.O()}]);