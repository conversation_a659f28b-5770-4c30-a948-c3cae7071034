"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[598],{5845:(e,t,n)=>{n.d(t,{i:()=>u});var r=n(12115),o=n(39033);function u({prop:e,defaultProp:t,onChange:n=()=>{}}){let[u,i]=function({defaultProp:e,onChange:t}){let n=r.useState(e),[u]=n,i=r.useRef(u),l=(0,o.c)(t);return r.useEffect(()=>{i.current!==u&&(l(u),i.current=u)},[u,i,l]),n}({defaultProp:t,onChange:n}),l=void 0!==e,a=l?e:u,c=(0,o.c)(n);return[a,r.useCallback(t=>{if(l){let n="function"==typeof t?t(e):t;n!==e&&c(n)}else i(t)},[l,e,i,c])]}},6101:(e,t,n)=>{n.d(t,{s:()=>i,t:()=>u});var r=n(12115);function o(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function u(...e){return t=>{let n=!1,r=e.map(e=>{let r=o(e,t);return n||"function"!=typeof r||(n=!0),r});if(n)return()=>{for(let t=0;t<r.length;t++){let n=r[t];"function"==typeof n?n():o(e[t],null)}}}}function i(...e){return r.useCallback(u(...e),e)}},28905:(e,t,n)=>{n.d(t,{C:()=>i});var r=n(12115),o=n(6101),u=n(52712),i=e=>{let{present:t,children:n}=e,i=function(e){var t,n;let[o,i]=r.useState(),a=r.useRef({}),c=r.useRef(e),s=r.useRef("none"),[f,d]=(t=e?"mounted":"unmounted",n={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},r.useReducer((e,t)=>{let r=n[e][t];return null!=r?r:e},t));return r.useEffect(()=>{let e=l(a.current);s.current="mounted"===f?e:"none"},[f]),(0,u.N)(()=>{let t=a.current,n=c.current;if(n!==e){let r=s.current,o=l(t);e?d("MOUNT"):"none"===o||(null==t?void 0:t.display)==="none"?d("UNMOUNT"):n&&r!==o?d("ANIMATION_OUT"):d("UNMOUNT"),c.current=e}},[e,d]),(0,u.N)(()=>{if(o){var e;let t;let n=null!==(e=o.ownerDocument.defaultView)&&void 0!==e?e:window,r=e=>{let r=l(a.current).includes(e.animationName);if(e.target===o&&r&&(d("ANIMATION_END"),!c.current)){let e=o.style.animationFillMode;o.style.animationFillMode="forwards",t=n.setTimeout(()=>{"forwards"===o.style.animationFillMode&&(o.style.animationFillMode=e)})}},u=e=>{e.target===o&&(s.current=l(a.current))};return o.addEventListener("animationstart",u),o.addEventListener("animationcancel",r),o.addEventListener("animationend",r),()=>{n.clearTimeout(t),o.removeEventListener("animationstart",u),o.removeEventListener("animationcancel",r),o.removeEventListener("animationend",r)}}d("ANIMATION_END")},[o,d]),{isPresent:["mounted","unmountSuspended"].includes(f),ref:r.useCallback(e=>{e&&(a.current=getComputedStyle(e)),i(e)},[])}}(t),a="function"==typeof n?n({present:i.isPresent}):r.Children.only(n),c=(0,o.s)(i.ref,function(e){var t,n;let r=null===(t=Object.getOwnPropertyDescriptor(e.props,"ref"))||void 0===t?void 0:t.get,o=r&&"isReactWarning"in r&&r.isReactWarning;return o?e.ref:(o=(r=null===(n=Object.getOwnPropertyDescriptor(e,"ref"))||void 0===n?void 0:n.get)&&"isReactWarning"in r&&r.isReactWarning)?e.props.ref:e.props.ref||e.ref}(a));return"function"==typeof n||i.isPresent?r.cloneElement(a,{ref:c}):null};function l(e){return(null==e?void 0:e.animationName)||"none"}i.displayName="Presence"},39033:(e,t,n)=>{n.d(t,{c:()=>o});var r=n(12115);function o(e){let t=r.useRef(e);return r.useEffect(()=>{t.current=e}),r.useMemo(()=>(...e)=>t.current?.(...e),[])}},46081:(e,t,n)=>{n.d(t,{A:()=>i,q:()=>u});var r=n(12115),o=n(95155);function u(e,t){let n=r.createContext(t),u=e=>{let{children:t,...u}=e,i=r.useMemo(()=>u,Object.values(u));return(0,o.jsx)(n.Provider,{value:i,children:t})};return u.displayName=e+"Provider",[u,function(o){let u=r.useContext(n);if(u)return u;if(void 0!==t)return t;throw Error(`\`${o}\` must be used within \`${e}\``)}]}function i(e,t=[]){let n=[],u=()=>{let t=n.map(e=>r.createContext(e));return function(n){let o=n?.[e]||t;return r.useMemo(()=>({[`__scope${e}`]:{...n,[e]:o}}),[n,o])}};return u.scopeName=e,[function(t,u){let i=r.createContext(u),l=n.length;n=[...n,u];let a=t=>{let{scope:n,children:u,...a}=t,c=n?.[e]?.[l]||i,s=r.useMemo(()=>a,Object.values(a));return(0,o.jsx)(c.Provider,{value:s,children:u})};return a.displayName=t+"Provider",[a,function(n,o){let a=o?.[e]?.[l]||i,c=r.useContext(a);if(c)return c;if(void 0!==u)return u;throw Error(`\`${n}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let n=()=>{let n=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let o=n.reduce((t,{useScope:n,scopeName:r})=>{let o=n(e)[`__scope${r}`];return{...t,...o}},{});return r.useMemo(()=>({[`__scope${t.scopeName}`]:o}),[o])}};return n.scopeName=t.scopeName,n}(u,...t)]}},52712:(e,t,n)=>{n.d(t,{N:()=>o});var r=n(12115),o=globalThis?.document?r.useLayoutEffect:()=>{}},61285:(e,t,n)=>{n.d(t,{B:()=>a});var r,o=n(12115),u=n(52712),i=(r||(r=n.t(o,2)))["useId".toString()]||(()=>void 0),l=0;function a(e){let[t,n]=o.useState(i());return(0,u.N)(()=>{e||n(e=>e??String(l++))},[e]),e||(t?`radix-${t}`:"")}},63655:(e,t,n)=>{n.d(t,{hO:()=>a,sG:()=>l});var r=n(12115),o=n(47650),u=n(99708),i=n(95155),l=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"].reduce((e,t)=>{let n=r.forwardRef((e,n)=>{let{asChild:r,...o}=e,l=r?u.DX:t;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,i.jsx)(l,{...o,ref:n})});return n.displayName=`Primitive.${t}`,{...e,[t]:n}},{});function a(e,t){e&&o.flushSync(()=>e.dispatchEvent(t))}},82284:(e,t,n)=>{n.d(t,{N:()=>a});var r=n(12115),o=n(46081),u=n(6101),i=n(99708),l=n(95155);function a(e){let t=e+"CollectionProvider",[n,a]=(0,o.A)(t),[c,s]=n(t,{collectionRef:{current:null},itemMap:new Map}),f=e=>{let{scope:t,children:n}=e,o=r.useRef(null),u=r.useRef(new Map).current;return(0,l.jsx)(c,{scope:t,itemMap:u,collectionRef:o,children:n})};f.displayName=t;let d=e+"CollectionSlot",m=r.forwardRef((e,t)=>{let{scope:n,children:r}=e,o=s(d,n),a=(0,u.s)(t,o.collectionRef);return(0,l.jsx)(i.DX,{ref:a,children:r})});m.displayName=d;let p=e+"CollectionItemSlot",v="data-radix-collection-item",N=r.forwardRef((e,t)=>{let{scope:n,children:o,...a}=e,c=r.useRef(null),f=(0,u.s)(t,c),d=s(p,n);return r.useEffect(()=>(d.itemMap.set(c,{ref:c,...a}),()=>void d.itemMap.delete(c))),(0,l.jsx)(i.DX,{[v]:"",ref:f,children:o})});return N.displayName=p,[{Provider:f,Slot:m,ItemSlot:N},function(t){let n=s(e+"CollectionConsumer",t);return r.useCallback(()=>{let e=n.collectionRef.current;if(!e)return[];let t=Array.from(e.querySelectorAll("[".concat(v,"]")));return Array.from(n.itemMap.values()).sort((e,n)=>t.indexOf(e.ref.current)-t.indexOf(n.ref.current))},[n.collectionRef,n.itemMap])},a]}},85185:(e,t,n)=>{n.d(t,{m:()=>r});function r(e,t,{checkForDefaultPrevented:n=!0}={}){return function(r){if(e?.(r),!1===n||!r.defaultPrevented)return t?.(r)}}},94315:(e,t,n)=>{n.d(t,{jH:()=>u});var r=n(12115);n(95155);var o=r.createContext(void 0);function u(e){let t=r.useContext(o);return e||t||"ltr"}},99708:(e,t,n)=>{n.d(t,{DX:()=>i,xV:()=>a});var r=n(12115),o=n(6101),u=n(95155),i=r.forwardRef((e,t)=>{let{children:n,...o}=e,i=r.Children.toArray(n),a=i.find(c);if(a){let e=a.props.children,n=i.map(t=>t!==a?t:r.Children.count(e)>1?r.Children.only(null):r.isValidElement(e)?e.props.children:null);return(0,u.jsx)(l,{...o,ref:t,children:r.isValidElement(e)?r.cloneElement(e,void 0,n):null})}return(0,u.jsx)(l,{...o,ref:t,children:n})});i.displayName="Slot";var l=r.forwardRef((e,t)=>{let{children:n,...u}=e;if(r.isValidElement(n)){let e=function(e){let t=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,n=t&&"isReactWarning"in t&&t.isReactWarning;return n?e.ref:(n=(t=Object.getOwnPropertyDescriptor(e,"ref")?.get)&&"isReactWarning"in t&&t.isReactWarning)?e.props.ref:e.props.ref||e.ref}(n);return r.cloneElement(n,{...function(e,t){let n={...t};for(let r in t){let o=e[r],u=t[r];/^on[A-Z]/.test(r)?o&&u?n[r]=(...e)=>{u(...e),o(...e)}:o&&(n[r]=o):"style"===r?n[r]={...o,...u}:"className"===r&&(n[r]=[o,u].filter(Boolean).join(" "))}return{...e,...n}}(u,n.props),ref:t?(0,o.t)(t,e):e})}return r.Children.count(n)>1?r.Children.only(null):null});l.displayName="SlotClone";var a=({children:e})=>(0,u.jsx)(u.Fragment,{children:e});function c(e){return r.isValidElement(e)&&e.type===a}}}]);