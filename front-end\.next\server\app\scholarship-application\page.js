(()=>{var e={};e.id=94,e.ids=[94],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5336:(e,r,s)=>{"use strict";s.d(r,{A:()=>a});let a=(0,s(62688).A)("CircleCheckBig",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},7430:(e,r,s)=>{"use strict";s.d(r,{A:()=>a});let a=(0,s(62688).A)("School",[["path",{d:"M14 22v-4a2 2 0 1 0-4 0v4",key:"hhkicm"}],["path",{d:"m18 10 4 2v8a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2v-8l4-2",key:"1vwozw"}],["path",{d:"M18 5v17",key:"1sw6gf"}],["path",{d:"m4 6 8-4 8 4",key:"1q0ilc"}],["path",{d:"M6 5v17",key:"1xfsm0"}],["circle",{cx:"12",cy:"9",r:"2",key:"1092wv"}]])},10022:(e,r,s)=>{"use strict";s.d(r,{A:()=>a});let a=(0,s(62688).A)("FileText",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]])},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},15616:(e,r,s)=>{"use strict";s.d(r,{T:()=>l});var a=s(60687),t=s(43210),n=s(96241);let l=t.forwardRef(({className:e,...r},s)=>(0,a.jsx)("textarea",{className:(0,n.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",e),ref:s,...r}));l.displayName="Textarea"},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},25613:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>n});var a=s(37413),t=s(43565);function n(){return(0,a.jsx)(t.default,{})}},28943:(e,r,s)=>{"use strict";s.d(r,{default:()=>J});var a=s(60687),t=s(43210),n=s(16189),l=s(24934),i=s(68988),d=s(39390),o=s(15616),c=s(63974),m=s(85910),u=s(82080),h=s(7430),p=s(27351),x=s(43649),g=s(58869),f=s(48340),b=s(97992),v=s(79410),j=s(85778),y=s(41862),_=s(5336);let N=(0,s(62688).A)("FileUp",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M12 12v6",key:"3ahymv"}],["path",{d:"m15 15-3-3-3 3",key:"15xj92"}]]);var w=s(41550),k=s(58887),S=s(71702),A=s(59556),C=s(55192),F=s(10022),P=s(32192);function q({onBackToForm:e,onGoHome:r}){return(0,a.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-green-50 via-blue-50 to-indigo-50 py-8 flex items-center justify-center",children:(0,a.jsx)("div",{className:"container mx-auto px-4 max-w-2xl",children:(0,a.jsxs)(C.Zp,{className:"shadow-xl border-0 overflow-hidden",children:[(0,a.jsxs)(C.aR,{className:"bg-gradient-to-r from-green-500 to-blue-500 text-white text-center py-12",children:[(0,a.jsx)("div",{className:"flex justify-center mb-4",children:(0,a.jsx)("div",{className:"p-4 bg-white bg-opacity-20 rounded-full",children:(0,a.jsx)(_.A,{className:"h-16 w-16 text-white"})})}),(0,a.jsx)(C.ZB,{className:"text-3xl font-bold mb-2",children:"Application Submitted Successfully!"}),(0,a.jsx)(C.BT,{className:"text-green-100 text-lg",children:"Your scholarship application has been received and is being processed"})]}),(0,a.jsx)(C.Wu,{className:"p-8 text-center",children:(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"bg-blue-50 p-6 rounded-lg border-l-4 border-blue-500",children:[(0,a.jsx)("h3",{className:"font-semibold text-blue-900 mb-2",children:"What happens next?"}),(0,a.jsxs)("ul",{className:"text-blue-800 text-left space-y-2",children:[(0,a.jsxs)("li",{className:"flex items-start",children:[(0,a.jsx)("span",{className:"inline-block w-2 h-2 bg-blue-500 rounded-full mt-2 mr-3 flex-shrink-0"}),"Your application will be reviewed by our scholarship committee"]}),(0,a.jsxs)("li",{className:"flex items-start",children:[(0,a.jsx)("span",{className:"inline-block w-2 h-2 bg-blue-500 rounded-full mt-2 mr-3 flex-shrink-0"}),"You will receive an email confirmation within 24 hours"]}),(0,a.jsxs)("li",{className:"flex items-start",children:[(0,a.jsx)("span",{className:"inline-block w-2 h-2 bg-blue-500 rounded-full mt-2 mr-3 flex-shrink-0"}),"The review process typically takes 5-10 business days"]}),(0,a.jsxs)("li",{className:"flex items-start",children:[(0,a.jsx)("span",{className:"inline-block w-2 h-2 bg-blue-500 rounded-full mt-2 mr-3 flex-shrink-0"}),"You will be notified of the decision via email and SMS"]})]})]}),(0,a.jsx)("div",{className:"bg-yellow-50 p-4 rounded-lg border-l-4 border-yellow-500",children:(0,a.jsxs)("p",{className:"text-yellow-800 font-medium",children:[(0,a.jsx)("strong",{children:"Important:"})," Please keep your phone and email accessible for any follow-up communications."]})}),(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center pt-4",children:[(0,a.jsxs)(l.$,{onClick:e,variant:"outline",className:"flex items-center justify-center px-6 py-3",children:[(0,a.jsx)(F.A,{className:"mr-2 h-5 w-5"}),"Submit Another Application"]}),(0,a.jsxs)(l.$,{onClick:r,className:"bg-blue-600 hover:bg-blue-700 text-white flex items-center justify-center px-6 py-3",children:[(0,a.jsx)(P.A,{className:"mr-2 h-5 w-5"}),"Go to Dashboard"]})]})]})})]})})})}function J(){(0,n.useSearchParams)();let[e,r]=(0,t.useState)("primary"),[s,C]=(0,t.useState)(!1),[F,P]=(0,t.useState)(!1),[J,E]=(0,t.useState)(null),{toast:T}=(0,S.dj)(),[M,R]=(0,t.useState)({student_full_name:"",age:"",current_class:"",father_name:"",mother_name:"",parent_phone:"",home_address:"",school_name:"",headmaster_name:"",school_account_number:"",reason_for_scholarship:"",current_school_fee:"",supporting_information:""}),[I,D]=(0,t.useState)({student_full_name:"",age:"",class:"",parent_phone:"",address:"",school_name:"",principal_name:"",principal_account_number:"",reason_for_scholarship:"",school_fee_amount:""}),[L,U]=(0,t.useState)({full_name:"",age:"",course_of_study:"",current_level:"",phone_number:"",email_address:"",matriculation_number:"",reason_for_scholarship:""}),[O,z]=(0,t.useState)({student_picture:null}),[G,V]=(0,t.useState)({student_picture:null}),[H,B]=(0,t.useState)({student_id_card:null,payment_evidence:null,supporting_documents:[]}),$={primary:{icon:u.A,title:"Primary School Scholarship",description:"For Primary 1-6 students (Filled by Parent/Guardian)",color:"bg-blue-500",bgColor:"bg-blue-50",textColor:"text-blue-700",borderColor:"border-blue-200"},secondary:{icon:h.A,title:"Secondary School Scholarship",description:"For Secondary school students (Filled by Student)",color:"bg-green-500",bgColor:"bg-green-50",textColor:"text-green-700",borderColor:"border-green-200"},university:{icon:p.A,title:"University Scholarship",description:"For University students (Filled by Student)",color:"bg-purple-500",bgColor:"bg-purple-50",textColor:"text-purple-700",borderColor:"border-purple-200"}},Y=(e,r,s)=>{"primary"===e?z(e=>({...e,[r]:s})):"secondary"===e?V(e=>({...e,[r]:s})):"university"===e&&("supporting_documents"===r&&s?B(e=>({...e,supporting_documents:[...e.supporting_documents,s]})):B(e=>({...e,[r]:s})))},Z=r=>{if(r&&"university"===e){let e=Array.from(r);B(r=>({...r,supporting_documents:[...r.supporting_documents,...e]}))}},X=e=>{B(r=>({...r,supporting_documents:r.supporting_documents.filter((r,s)=>s!==e)}))},K=()=>"primary"===e?["student_full_name","age","current_class","father_name","mother_name","parent_phone","home_address","school_name","headmaster_name","school_account_number","reason_for_scholarship","current_school_fee"].every(e=>""!==M[e].trim())&&O.student_picture:"secondary"===e?["student_full_name","age","class","parent_phone","address","school_name","principal_name","principal_account_number","reason_for_scholarship","school_fee_amount"].every(e=>""!==I[e].trim())&&G.student_picture:"university"===e&&["full_name","age","course_of_study","current_level","phone_number","email_address","matriculation_number","reason_for_scholarship"].every(e=>""!==L[e].trim())&&H.student_id_card&&H.payment_evidence,W=async r=>{if(r.preventDefault(),!K()){T({title:"Validation Error",description:"Please fill in all required fields and upload required documents",variant:"destructive"});return}try{C(!0);let r=new FormData;if(r.append("category",e),"primary"===e?(r.append("form_data",JSON.stringify(M)),O.student_picture&&r.append("student_picture",O.student_picture)):"secondary"===e?(r.append("form_data",JSON.stringify(I)),G.student_picture&&r.append("student_picture",G.student_picture)):"university"===e&&(r.append("form_data",JSON.stringify(L)),H.student_id_card&&r.append("student_id_card",H.student_id_card),H.payment_evidence&&r.append("payment_evidence",H.payment_evidence),H.supporting_documents.forEach((e,s)=>{r.append(`supporting_documents[${s}]`,e)})),!J){T({title:"Error",description:"Scholarship ID is required for application submission.",variant:"destructive"});return}let s=await A.uE.submitScholarshipApplication(J,r);s.success?(P(!0),"primary"===e?(R({student_full_name:"",age:"",current_class:"",father_name:"",mother_name:"",parent_phone:"",home_address:"",school_name:"",headmaster_name:"",school_account_number:"",reason_for_scholarship:"",current_school_fee:"",supporting_information:""}),z({student_picture:null})):"secondary"===e?(D({student_full_name:"",age:"",class:"",parent_phone:"",address:"",school_name:"",principal_name:"",principal_account_number:"",reason_for_scholarship:"",school_fee_amount:""}),V({student_picture:null})):"university"===e&&(U({full_name:"",age:"",course_of_study:"",current_level:"",phone_number:"",email_address:"",matriculation_number:"",reason_for_scholarship:""}),B({student_id_card:null,payment_evidence:null,supporting_documents:[]}))):T({title:"Error",description:s.message||"Failed to submit application",variant:"destructive"})}catch(e){console.error("Error submitting application:",e),T({title:"Error",description:e.response?.data?.message||"Failed to submit application",variant:"destructive"})}finally{C(!1)}};return F?(0,a.jsx)(q,{onBackToForm:()=>{P(!1)},onGoHome:()=>{window.location.href="/dashboard"}}):(0,a.jsxs)("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900",children:[(0,a.jsx)("section",{className:"relative py-12 sm:py-16 md:py-20 bg-gradient-to-br from-green-600 to-green-800 text-white",children:(0,a.jsx)("div",{className:"container mx-auto px-3 sm:px-4 md:px-6 lg:px-8",children:(0,a.jsxs)("div",{className:"max-w-4xl mx-auto text-center space-y-4 sm:space-y-6",children:[(0,a.jsx)("h1",{className:"text-2xl sm:text-3xl md:text-4xl lg:text-5xl xl:text-6xl font-bold tracking-tight leading-tight",children:"Scholarship Application"}),(0,a.jsx)("p",{className:"text-base sm:text-lg md:text-xl text-green-100 max-w-3xl mx-auto px-2",children:"Choose your scholarship category and complete the application form"})]})})}),(0,a.jsx)("section",{className:"py-6 sm:py-8 md:py-12 bg-white dark:bg-gray-900",children:(0,a.jsx)("div",{className:"container mx-auto px-3 sm:px-4 md:px-6 lg:px-8",children:(0,a.jsxs)("div",{className:"max-w-4xl mx-auto",children:[(0,a.jsxs)("div",{className:"mb-4 sm:mb-6 md:mb-8",children:[(0,a.jsx)("h2",{className:"text-xl sm:text-2xl md:text-3xl font-bold tracking-tight mb-2 text-center sm:text-left",children:"Apply for Scholarship"}),(0,a.jsx)("p",{className:"text-sm sm:text-base text-gray-600 dark:text-gray-400 text-center sm:text-left",children:"Select your education level and fill out the appropriate form"})]}),(0,a.jsxs)(m.tU,{value:e,onValueChange:e=>r(e),className:"w-full",children:[(0,a.jsx)(m.j7,{className:"grid w-full grid-cols-1 sm:grid-cols-3 mb-4 sm:mb-6 h-auto p-1 gap-1 sm:gap-2 bg-gray-100 rounded-lg",children:Object.entries($).map(([e,r])=>{let s=r.icon;return(0,a.jsxs)(m.Xi,{value:e,className:"flex flex-col sm:flex-col items-center p-2 sm:p-3 space-y-1 sm:space-y-2 rounded-md transition-all duration-300 data-[state=active]:bg-white data-[state=active]:shadow-md min-h-[60px] sm:min-h-[80px]",children:[(0,a.jsx)("div",{className:"p-1.5 sm:p-2 rounded-full bg-green-600 text-white shadow-sm",children:(0,a.jsx)(s,{className:"h-3 w-3 sm:h-4 sm:w-4"})}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"font-medium text-xs sm:text-xs text-gray-900 leading-tight",children:r.title}),(0,a.jsx)("div",{className:"text-xs sm:text-xs text-gray-600 mt-0.5 hidden sm:block",children:r.description})]})]},e)})}),(0,a.jsx)(m.av,{value:"primary",className:"space-y-4 sm:space-y-6 md:space-y-8",children:(0,a.jsxs)("form",{onSubmit:W,className:"space-y-4 sm:space-y-6 md:space-y-8",children:[(0,a.jsx)("div",{className:"bg-green-50 p-3 sm:p-4 md:p-6 rounded-lg border border-green-200",children:(0,a.jsxs)("div",{className:"flex items-start sm:items-center",children:[(0,a.jsx)(x.A,{className:"h-4 w-4 sm:h-5 sm:w-5 text-green-600 mr-2 sm:mr-3 mt-0.5 sm:mt-0 flex-shrink-0"}),(0,a.jsx)("p",{className:"text-green-800 font-medium text-sm sm:text-base",children:"This form should be filled by a Parent or Guardian on behalf of the student"})]})}),(0,a.jsxs)("div",{className:"space-y-4 sm:space-y-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)("h3",{className:"text-base sm:text-lg font-semibold text-green-800 dark:text-green-200 mb-3 sm:mb-4 flex items-center",children:[(0,a.jsx)(g.A,{className:"h-4 w-4 sm:h-5 sm:w-5 mr-2 text-green-600"}),"Student Information"]}),(0,a.jsxs)("div",{className:"space-y-3 sm:space-y-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(d.J,{htmlFor:"student_full_name",className:"text-green-800 dark:text-green-200 text-sm sm:text-base",children:"Student Full Name *"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(g.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-green-500"}),(0,a.jsx)(i.p,{id:"student_full_name",value:M.student_full_name,onChange:e=>R(r=>({...r,student_full_name:e.target.value})),placeholder:"Enter student's full name",className:"pl-10 rounded-xl border-green-200 dark:border-green-700 focus:border-green-500 text-sm sm:text-base",required:!0})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(d.J,{htmlFor:"age",className:"text-green-800 dark:text-green-200 text-sm sm:text-base",children:"Age *"}),(0,a.jsx)(i.p,{id:"age",type:"number",value:M.age,onChange:e=>R(r=>({...r,age:e.target.value})),placeholder:"Age",className:"rounded-xl border-green-200 dark:border-green-700 focus:border-green-500 text-sm sm:text-base",required:!0})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(d.J,{htmlFor:"current_class",className:"text-green-800 dark:text-green-200 text-sm sm:text-base",children:"Current Class *"}),(0,a.jsxs)(c.l6,{value:M.current_class,onValueChange:e=>R(r=>({...r,current_class:e})),children:[(0,a.jsx)(c.bq,{className:"rounded-xl border-green-200 dark:border-green-700 focus:border-green-500 text-sm sm:text-base",children:(0,a.jsx)(c.yv,{placeholder:"Select class"})}),(0,a.jsxs)(c.gC,{children:[(0,a.jsx)(c.eb,{value:"Primary 1",children:"Primary 1"}),(0,a.jsx)(c.eb,{value:"Primary 2",children:"Primary 2"}),(0,a.jsx)(c.eb,{value:"Primary 3",children:"Primary 3"}),(0,a.jsx)(c.eb,{value:"Primary 4",children:"Primary 4"}),(0,a.jsx)(c.eb,{value:"Primary 5",children:"Primary 5"}),(0,a.jsx)(c.eb,{value:"Primary 6",children:"Primary 6"})]})]})]})]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("h3",{className:"text-lg font-semibold text-green-800 dark:text-green-200 mb-4 flex items-center",children:[(0,a.jsx)(g.A,{className:"h-5 w-5 mr-2 text-green-600"}),"Parent/Guardian Information"]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(d.J,{htmlFor:"father_name",className:"text-green-800 dark:text-green-200",children:"Father's Name *"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(g.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-green-500"}),(0,a.jsx)(i.p,{id:"father_name",value:M.father_name,onChange:e=>R(r=>({...r,father_name:e.target.value})),placeholder:"Enter father's name",className:"pl-10 rounded-xl border-green-200 dark:border-green-700 focus:border-green-500",required:!0})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(d.J,{htmlFor:"mother_name",className:"text-green-800 dark:text-green-200",children:"Mother's Name *"}),(0,a.jsx)(i.p,{id:"mother_name",value:M.mother_name,onChange:e=>R(r=>({...r,mother_name:e.target.value})),placeholder:"Enter mother's name",className:"rounded-xl border-green-200 dark:border-green-700 focus:border-green-500",required:!0})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(d.J,{htmlFor:"parent_phone",className:"text-green-800 dark:text-green-200",children:"Father's or Mother's Phone Number *"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(f.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-green-500"}),(0,a.jsx)(i.p,{id:"parent_phone",type:"tel",value:M.parent_phone,onChange:e=>R(r=>({...r,parent_phone:e.target.value})),placeholder:"Enter phone number",className:"pl-10 rounded-xl border-green-200 dark:border-green-700 focus:border-green-500",required:!0})]})]})]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("h3",{className:"text-lg font-semibold text-green-800 dark:text-green-200 mb-4 flex items-center",children:[(0,a.jsx)(b.A,{className:"h-5 w-5 mr-2 text-green-600"}),"Address Information"]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(d.J,{htmlFor:"home_address",className:"text-green-800 dark:text-green-200",children:"Home Address *"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(b.A,{className:"absolute left-3 top-3 h-4 w-4 text-green-500"}),(0,a.jsx)(o.T,{id:"home_address",value:M.home_address,onChange:e=>R(r=>({...r,home_address:e.target.value})),placeholder:"Enter complete home address",className:"pl-10 rounded-xl border-green-200 dark:border-green-700 focus:border-green-500",rows:3,required:!0})]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("h3",{className:"text-lg font-semibold text-green-800 dark:text-green-200 mb-4 flex items-center",children:[(0,a.jsx)(v.A,{className:"h-5 w-5 mr-2 text-green-600"}),"School Information"]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(d.J,{htmlFor:"school_name",className:"text-green-800 dark:text-green-200",children:"Name of the School *"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(v.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-green-500"}),(0,a.jsx)(i.p,{id:"school_name",value:M.school_name,onChange:e=>R(r=>({...r,school_name:e.target.value})),placeholder:"Enter school name",className:"pl-10 rounded-xl border-green-200 dark:border-green-700 focus:border-green-500",required:!0})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(d.J,{htmlFor:"headmaster_name",className:"text-green-800 dark:text-green-200",children:"Name of Headmaster/Director *"}),(0,a.jsx)(i.p,{id:"headmaster_name",value:M.headmaster_name,onChange:e=>R(r=>({...r,headmaster_name:e.target.value})),placeholder:"Enter headmaster's name",className:"rounded-xl border-green-200 dark:border-green-700 focus:border-green-500",required:!0})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(d.J,{htmlFor:"school_account_number",className:"text-green-800 dark:text-green-200",children:"School Account Number *"}),(0,a.jsx)(i.p,{id:"school_account_number",value:M.school_account_number,onChange:e=>R(r=>({...r,school_account_number:e.target.value})),placeholder:"Enter account number for scholarship payment",className:"rounded-xl border-green-200 dark:border-green-700 focus:border-green-500",required:!0})]})]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("h3",{className:"text-lg font-semibold text-green-800 dark:text-green-200 mb-4 flex items-center",children:[(0,a.jsx)(j.A,{className:"h-5 w-5 mr-2 text-green-600"}),"Financial & Additional Information"]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(d.J,{htmlFor:"current_school_fee",className:"text-green-800 dark:text-green-200",children:"Current School Fee Amount *"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(j.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-green-500"}),(0,a.jsx)(i.p,{id:"current_school_fee",type:"number",value:M.current_school_fee,onChange:e=>R(r=>({...r,current_school_fee:e.target.value})),placeholder:"Enter amount in Naira",className:"pl-10 rounded-xl border-green-200 dark:border-green-700 focus:border-green-500",required:!0})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(d.J,{htmlFor:"student_picture",className:"text-green-800 dark:text-green-200",children:"Upload Student Picture *"}),(0,a.jsx)(i.p,{id:"student_picture",type:"file",accept:"image/*",onChange:e=>Y("primary","student_picture",e.target.files?.[0]||null),className:"rounded-xl border-green-200 dark:border-green-700 focus:border-green-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-green-50 file:text-green-700 hover:file:bg-green-100",required:!0})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(d.J,{htmlFor:"reason_for_scholarship",className:"text-green-800 dark:text-green-200",children:"Reason for the Scholarship *"}),(0,a.jsx)(o.T,{id:"reason_for_scholarship",value:M.reason_for_scholarship,onChange:e=>R(r=>({...r,reason_for_scholarship:e.target.value})),placeholder:"Please explain why your child needs this scholarship",className:"rounded-xl border-green-200 dark:border-green-700 focus:border-green-500",rows:4,required:!0})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(d.J,{htmlFor:"supporting_information",className:"text-green-800 dark:text-green-200",children:"Any Other Supporting Information"}),(0,a.jsx)(o.T,{id:"supporting_information",value:M.supporting_information,onChange:e=>R(r=>({...r,supporting_information:e.target.value})),placeholder:"Any additional information that supports your application",className:"rounded-xl border-green-200 dark:border-green-700 focus:border-green-500",rows:3})]})]})]}),(0,a.jsx)("div",{className:"flex justify-center pt-6 sm:pt-8",children:(0,a.jsx)(l.$,{type:"submit",disabled:s||!K(),size:"lg",className:"w-full sm:w-auto bg-green-600 hover:bg-green-700 text-white px-6 sm:px-12 py-3 sm:py-4 text-base sm:text-lg font-semibold shadow-lg hover:shadow-xl transition-all duration-300",children:s?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(y.A,{className:"mr-2 sm:mr-3 h-5 w-5 sm:h-6 sm:w-6 animate-spin"}),(0,a.jsx)("span",{className:"text-sm sm:text-base",children:"Submitting..."})]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(_.A,{className:"mr-2 sm:mr-3 h-5 w-5 sm:h-6 sm:w-6"}),(0,a.jsx)("span",{className:"text-sm sm:text-base",children:"Submit Primary School Application"})]})})})]})}),(0,a.jsx)(m.av,{value:"secondary",className:"space-y-4 sm:space-y-6 md:space-y-8",children:(0,a.jsxs)("form",{onSubmit:W,className:"space-y-4 sm:space-y-6 md:space-y-8",children:[(0,a.jsx)("div",{className:"bg-green-50 p-3 sm:p-4 md:p-6 rounded-lg border border-green-200",children:(0,a.jsxs)("div",{className:"flex items-start sm:items-center",children:[(0,a.jsx)(x.A,{className:"h-4 w-4 sm:h-5 sm:w-5 text-green-600 mr-2 sm:mr-3 mt-0.5 sm:mt-0 flex-shrink-0"}),(0,a.jsx)("p",{className:"text-green-800 font-medium text-sm sm:text-base",children:"This form should be filled by the Student directly"})]})}),(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)("h3",{className:"text-lg font-semibold text-green-800 dark:text-green-200 mb-4 flex items-center",children:[(0,a.jsx)(g.A,{className:"h-5 w-5 mr-2 text-green-600"}),"Student Information"]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(d.J,{htmlFor:"sec_student_full_name",className:"text-green-800 dark:text-green-200",children:"Student Full Name *"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(g.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-green-500"}),(0,a.jsx)(i.p,{id:"sec_student_full_name",value:I.student_full_name,onChange:e=>D(r=>({...r,student_full_name:e.target.value})),placeholder:"Enter your full name",className:"pl-10 rounded-xl border-green-200 dark:border-green-700 focus:border-green-500",required:!0})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 sm:gap-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(d.J,{htmlFor:"sec_age",className:"text-green-800 dark:text-green-200",children:"Age *"}),(0,a.jsx)(i.p,{id:"sec_age",type:"number",value:I.age,onChange:e=>D(r=>({...r,age:e.target.value})),placeholder:"Your age",className:"rounded-xl border-green-200 dark:border-green-700 focus:border-green-500",required:!0})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(d.J,{htmlFor:"sec_class",className:"text-green-800 dark:text-green-200",children:"Class *"}),(0,a.jsxs)(c.l6,{value:I.class,onValueChange:e=>D(r=>({...r,class:e})),children:[(0,a.jsx)(c.bq,{className:"rounded-xl border-green-200 dark:border-green-700 focus:border-green-500",children:(0,a.jsx)(c.yv,{placeholder:"Select class"})}),(0,a.jsxs)(c.gC,{children:[(0,a.jsx)(c.eb,{value:"JSS 1",children:"JSS 1"}),(0,a.jsx)(c.eb,{value:"JSS 2",children:"JSS 2"}),(0,a.jsx)(c.eb,{value:"JSS 3",children:"JSS 3"}),(0,a.jsx)(c.eb,{value:"SS 1",children:"SS 1"}),(0,a.jsx)(c.eb,{value:"SS 2",children:"SS 2"}),(0,a.jsx)(c.eb,{value:"SS 3",children:"SS 3"})]})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(d.J,{htmlFor:"sec_parent_phone",className:"text-green-800 dark:text-green-200",children:"Parent's Phone Number *"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(f.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-green-500"}),(0,a.jsx)(i.p,{id:"sec_parent_phone",type:"tel",value:I.parent_phone,onChange:e=>D(r=>({...r,parent_phone:e.target.value})),placeholder:"Enter parent's phone number",className:"pl-10 rounded-xl border-green-200 dark:border-green-700 focus:border-green-500",required:!0})]})]})]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("h3",{className:"text-lg font-semibold text-green-800 dark:text-green-200 mb-4 flex items-center",children:[(0,a.jsx)(b.A,{className:"h-5 w-5 mr-2 text-green-600"}),"Address & School Information"]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(d.J,{htmlFor:"sec_address",className:"text-green-800 dark:text-green-200",children:"Address *"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(b.A,{className:"absolute left-3 top-3 h-4 w-4 text-green-500"}),(0,a.jsx)(o.T,{id:"sec_address",value:I.address,onChange:e=>D(r=>({...r,address:e.target.value})),placeholder:"Enter your complete address",className:"pl-10 rounded-xl border-green-200 dark:border-green-700 focus:border-green-500",rows:3,required:!0})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(d.J,{htmlFor:"sec_school_name",className:"text-green-800 dark:text-green-200",children:"School Name *"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(v.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-green-500"}),(0,a.jsx)(i.p,{id:"sec_school_name",value:I.school_name,onChange:e=>D(r=>({...r,school_name:e.target.value})),placeholder:"Enter your school name",className:"pl-10 rounded-xl border-green-200 dark:border-green-700 focus:border-green-500",required:!0})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(d.J,{htmlFor:"sec_principal_name",className:"text-green-800 dark:text-green-200",children:"School Principal or Director Name *"}),(0,a.jsx)(i.p,{id:"sec_principal_name",value:I.principal_name,onChange:e=>D(r=>({...r,principal_name:e.target.value})),placeholder:"Enter principal's name",className:"rounded-xl border-green-200 dark:border-green-700 focus:border-green-500",required:!0})]})]})]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("h3",{className:"text-lg font-semibold text-green-800 dark:text-green-200 mb-4 flex items-center",children:[(0,a.jsx)(j.A,{className:"h-5 w-5 mr-2 text-green-600"}),"Financial Information"]}),(0,a.jsx)("div",{className:"space-y-4",children:(0,a.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(d.J,{htmlFor:"sec_principal_account_number",className:"text-green-800 dark:text-green-200",children:"Principal or Financial Officer Account Number *"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(j.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-green-500"}),(0,a.jsx)(i.p,{id:"sec_principal_account_number",value:I.principal_account_number,onChange:e=>D(r=>({...r,principal_account_number:e.target.value})),placeholder:"Enter account number for scholarship payment",className:"pl-10 rounded-xl border-green-200 dark:border-green-700 focus:border-green-500",required:!0})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(d.J,{htmlFor:"sec_school_fee_amount",className:"text-green-800 dark:text-green-200",children:"School Fee Amount *"}),(0,a.jsx)(i.p,{id:"sec_school_fee_amount",type:"number",value:I.school_fee_amount,onChange:e=>D(r=>({...r,school_fee_amount:e.target.value})),placeholder:"Enter amount in Naira",className:"rounded-xl border-green-200 dark:border-green-700 focus:border-green-500",required:!0})]})]})})]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("h3",{className:"text-lg font-semibold text-green-800 dark:text-green-200 mb-4 flex items-center",children:[(0,a.jsx)(N,{className:"h-5 w-5 mr-2 text-green-600"}),"Picture & Application Reason"]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(d.J,{htmlFor:"sec_student_picture",className:"text-green-800 dark:text-green-200",children:"Upload Student Picture *"}),(0,a.jsx)(i.p,{id:"sec_student_picture",type:"file",accept:"image/*",onChange:e=>Y("secondary","student_picture",e.target.files?.[0]||null),className:"rounded-xl border-green-200 dark:border-green-700 focus:border-green-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-green-50 file:text-green-700 hover:file:bg-green-100",required:!0})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(d.J,{htmlFor:"sec_reason_for_scholarship",className:"text-green-800 dark:text-green-200",children:"Reason for the Scholarship *"}),(0,a.jsx)(o.T,{id:"sec_reason_for_scholarship",value:I.reason_for_scholarship,onChange:e=>D(r=>({...r,reason_for_scholarship:e.target.value})),placeholder:"Please explain why you need this scholarship",className:"rounded-xl border-green-200 dark:border-green-700 focus:border-green-500",rows:4,required:!0})]})]})]}),(0,a.jsx)("div",{className:"flex justify-center pt-6 sm:pt-8",children:(0,a.jsx)(l.$,{type:"submit",disabled:s||!K(),size:"lg",className:"w-full sm:w-auto bg-gradient-to-r from-green-600 via-emerald-600 to-teal-600 hover:from-green-700 hover:via-emerald-700 hover:to-teal-700 text-white px-6 sm:px-12 py-3 sm:py-4 text-base sm:text-lg font-bold shadow-lg hover:shadow-xl transition-all duration-300",children:s?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(y.A,{className:"mr-2 sm:mr-3 h-5 w-5 sm:h-6 sm:w-6 animate-spin"}),(0,a.jsx)("span",{className:"text-sm sm:text-base",children:"Submitting..."})]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(_.A,{className:"mr-2 sm:mr-3 h-5 w-5 sm:h-6 sm:w-6"}),(0,a.jsx)("span",{className:"text-sm sm:text-base",children:"Submit Secondary School Application"})]})})})]})}),(0,a.jsx)(m.av,{value:"university",className:"space-y-4 sm:space-y-6 md:space-y-8",children:(0,a.jsxs)("form",{onSubmit:W,className:"space-y-4 sm:space-y-6 md:space-y-8",children:[(0,a.jsx)("div",{className:"bg-green-50 p-3 sm:p-4 md:p-6 rounded-lg border border-green-200",children:(0,a.jsxs)("div",{className:"flex items-start sm:items-center",children:[(0,a.jsx)(x.A,{className:"h-4 w-4 sm:h-5 sm:w-5 text-green-600 mr-2 sm:mr-3 mt-0.5 sm:mt-0 flex-shrink-0"}),(0,a.jsx)("p",{className:"text-green-800 font-medium text-sm sm:text-base",children:"This form should be filled by the University Student directly"})]})}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("h3",{className:"text-lg font-semibold text-green-800 dark:text-green-200 mb-4 flex items-center",children:[(0,a.jsx)(g.A,{className:"h-5 w-5 mr-2 text-green-600"}),"Personal Information"]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(d.J,{htmlFor:"uni_full_name",className:"text-green-800 dark:text-green-200",children:"Full Name *"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(g.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-green-500"}),(0,a.jsx)(i.p,{id:"uni_full_name",value:L.full_name,onChange:e=>U(r=>({...r,full_name:e.target.value})),placeholder:"Enter your full name",className:"pl-10 rounded-xl border-green-200 dark:border-green-700 focus:border-green-500",required:!0})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(d.J,{htmlFor:"uni_age",className:"text-green-800 dark:text-green-200",children:"Age *"}),(0,a.jsx)(i.p,{id:"uni_age",type:"number",value:L.age,onChange:e=>U(r=>({...r,age:e.target.value})),placeholder:"Your age",className:"rounded-xl border-green-200 dark:border-green-700 focus:border-green-500",required:!0})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(d.J,{htmlFor:"uni_phone_number",className:"text-green-800 dark:text-green-200",children:"Phone Number *"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(f.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-green-500"}),(0,a.jsx)(i.p,{id:"uni_phone_number",type:"tel",value:L.phone_number,onChange:e=>U(r=>({...r,phone_number:e.target.value})),placeholder:"Enter your phone number",className:"pl-10 rounded-xl border-green-200 dark:border-green-700 focus:border-green-500",required:!0})]})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(d.J,{htmlFor:"uni_email_address",className:"text-green-800 dark:text-green-200",children:"Email Address *"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(w.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-green-500"}),(0,a.jsx)(i.p,{id:"uni_email_address",type:"email",value:L.email_address,onChange:e=>U(r=>({...r,email_address:e.target.value})),placeholder:"Enter your email address",className:"pl-10 rounded-xl border-green-200 dark:border-green-700 focus:border-green-500",required:!0})]})]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("h3",{className:"text-lg font-semibold text-green-800 dark:text-green-200 mb-4 flex items-center",children:[(0,a.jsx)(p.A,{className:"h-5 w-5 mr-2 text-green-600"}),"Academic Information"]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(d.J,{htmlFor:"uni_course_of_study",className:"text-green-800 dark:text-green-200",children:"Course of Study *"}),(0,a.jsx)(i.p,{id:"uni_course_of_study",value:L.course_of_study,onChange:e=>U(r=>({...r,course_of_study:e.target.value})),placeholder:"Enter your course of study",className:"rounded-xl border-green-200 dark:border-green-700 focus:border-green-500",required:!0})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(d.J,{htmlFor:"uni_current_level",className:"text-green-800 dark:text-green-200",children:"Current Level *"}),(0,a.jsxs)(c.l6,{value:L.current_level,onValueChange:e=>U(r=>({...r,current_level:e})),children:[(0,a.jsx)(c.bq,{className:"rounded-xl border-green-200 dark:border-green-700 focus:border-green-500",children:(0,a.jsx)(c.yv,{placeholder:"Select your current level"})}),(0,a.jsxs)(c.gC,{children:[(0,a.jsx)(c.eb,{value:"100L",children:"100 Level"}),(0,a.jsx)(c.eb,{value:"200L",children:"200 Level"}),(0,a.jsx)(c.eb,{value:"300L",children:"300 Level"}),(0,a.jsx)(c.eb,{value:"400L",children:"400 Level"}),(0,a.jsx)(c.eb,{value:"500L",children:"500 Level"}),(0,a.jsx)(c.eb,{value:"600L",children:"600 Level"})]})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(d.J,{htmlFor:"uni_matriculation_number",className:"text-green-800 dark:text-green-200",children:"Matriculation Number *"}),(0,a.jsx)(i.p,{id:"uni_matriculation_number",value:L.matriculation_number,onChange:e=>U(r=>({...r,matriculation_number:e.target.value})),placeholder:"Enter your matriculation number",className:"rounded-xl border-green-200 dark:border-green-700 focus:border-green-500",required:!0})]})]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("h3",{className:"text-lg font-semibold text-green-800 dark:text-green-200 mb-4 flex items-center",children:[(0,a.jsx)(N,{className:"h-5 w-5 mr-2 text-green-600"}),"Required Documents"]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(d.J,{htmlFor:"uni_student_id_card",className:"text-green-800 dark:text-green-200",children:"Upload Student ID Card *"}),(0,a.jsx)(i.p,{id:"uni_student_id_card",type:"file",accept:"image/*,.pdf",onChange:e=>Y("university","student_id_card",e.target.files?.[0]||null),className:"rounded-xl border-green-200 dark:border-green-700 focus:border-green-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-green-50 file:text-green-700 hover:file:bg-green-100",required:!0})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(d.J,{htmlFor:"uni_payment_evidence",className:"text-green-800 dark:text-green-200",children:"Upload Payment Evidence (Remita/Screenshot) *"}),(0,a.jsx)(i.p,{id:"uni_payment_evidence",type:"file",accept:"image/*,.pdf",onChange:e=>Y("university","payment_evidence",e.target.files?.[0]||null),className:"rounded-xl border-green-200 dark:border-green-700 focus:border-green-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-green-50 file:text-green-700 hover:file:bg-green-100",required:!0})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(d.J,{htmlFor:"uni_supporting_documents",className:"text-green-800 dark:text-green-200",children:"Upload Supporting Documents (PDF, DOCX, JPG)"}),(0,a.jsx)(i.p,{id:"uni_supporting_documents",type:"file",accept:".pdf,.docx,.doc,.jpg,.jpeg,.png",multiple:!0,onChange:e=>Z(e.target.files),className:"rounded-xl border-green-200 dark:border-green-700 focus:border-green-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-green-50 file:text-green-700 hover:file:bg-green-100"}),H.supporting_documents.length>0&&(0,a.jsxs)("div",{className:"mt-2 space-y-2",children:[(0,a.jsx)("p",{className:"text-sm text-green-600",children:"Selected files:"}),H.supporting_documents.map((e,r)=>(0,a.jsxs)("div",{className:"flex items-center justify-between bg-green-50 p-2 rounded-xl border border-green-200",children:[(0,a.jsx)("span",{className:"text-sm text-green-700",children:e.name}),(0,a.jsx)(l.$,{type:"button",variant:"ghost",size:"sm",onClick:()=>X(r),className:"text-red-600 hover:text-red-800",children:"Remove"})]},r))]})]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("h3",{className:"text-lg font-semibold text-green-800 dark:text-green-200 mb-4 flex items-center",children:[(0,a.jsx)(k.A,{className:"h-5 w-5 mr-2 text-green-600"}),"Application Reason"]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(d.J,{htmlFor:"uni_reason_for_scholarship",className:"text-green-800 dark:text-green-200",children:"Reason for the Scholarship *"}),(0,a.jsx)(o.T,{id:"uni_reason_for_scholarship",value:L.reason_for_scholarship,onChange:e=>U(r=>({...r,reason_for_scholarship:e.target.value})),placeholder:"Please explain why you need this scholarship",className:"rounded-xl border-green-200 dark:border-green-700 focus:border-green-500",rows:4,required:!0})]})]}),(0,a.jsx)("div",{className:"flex justify-center pt-6 sm:pt-8",children:(0,a.jsx)(l.$,{type:"submit",disabled:s||!K(),size:"lg",className:"w-full sm:w-auto bg-gradient-to-r from-green-600 via-emerald-600 to-teal-600 hover:from-green-700 hover:via-emerald-700 hover:to-teal-700 text-white px-6 sm:px-12 py-3 sm:py-4 text-base sm:text-lg font-bold shadow-lg hover:shadow-xl transition-all duration-300",children:s?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(y.A,{className:"mr-2 sm:mr-3 h-5 w-5 sm:h-6 sm:w-6 animate-spin"}),(0,a.jsx)("span",{className:"text-sm sm:text-base",children:"Submitting..."})]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(_.A,{className:"mr-2 sm:mr-3 h-5 w-5 sm:h-6 sm:w-6"}),(0,a.jsx)("span",{className:"text-sm sm:text-base",children:"Submit University Application"})]})})})]})})]})]})})})]})}},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},36924:(e,r,s)=>{Promise.resolve().then(s.bind(s,28943))},39390:(e,r,s)=>{"use strict";s.d(r,{J:()=>o});var a=s(60687),t=s(43210),n=s(78148),l=s(24224),i=s(96241);let d=(0,l.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),o=t.forwardRef(({className:e,...r},s)=>(0,a.jsx)(n.b,{ref:s,className:(0,i.cn)(d(),e),...r}));o.displayName=n.b.displayName},41862:(e,r,s)=>{"use strict";s.d(r,{A:()=>a});let a=(0,s(62688).A)("LoaderCircle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},43565:(e,r,s)=>{"use strict";s.d(r,{default:()=>a});let a=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\laragon\\www\\laravel-api-ngo\\front-end\\components\\scholarship\\ScholarshipApplicationPage.tsx","default")},55146:(e,r,s)=>{"use strict";s.d(r,{B8:()=>F,UC:()=>q,bL:()=>C,l9:()=>P});var a=s(43210),t=s(70569),n=s(11273),l=s(72942),i=s(46059),d=s(14163),o=s(43),c=s(65551),m=s(96963),u=s(60687),h="Tabs",[p,x]=(0,n.A)(h,[l.RG]),g=(0,l.RG)(),[f,b]=p(h),v=a.forwardRef((e,r)=>{let{__scopeTabs:s,value:a,onValueChange:t,defaultValue:n,orientation:l="horizontal",dir:i,activationMode:h="automatic",...p}=e,x=(0,o.jH)(i),[g,b]=(0,c.i)({prop:a,onChange:t,defaultProp:n});return(0,u.jsx)(f,{scope:s,baseId:(0,m.B)(),value:g,onValueChange:b,orientation:l,dir:x,activationMode:h,children:(0,u.jsx)(d.sG.div,{dir:x,"data-orientation":l,...p,ref:r})})});v.displayName=h;var j="TabsList",y=a.forwardRef((e,r)=>{let{__scopeTabs:s,loop:a=!0,...t}=e,n=b(j,s),i=g(s);return(0,u.jsx)(l.bL,{asChild:!0,...i,orientation:n.orientation,dir:n.dir,loop:a,children:(0,u.jsx)(d.sG.div,{role:"tablist","aria-orientation":n.orientation,...t,ref:r})})});y.displayName=j;var _="TabsTrigger",N=a.forwardRef((e,r)=>{let{__scopeTabs:s,value:a,disabled:n=!1,...i}=e,o=b(_,s),c=g(s),m=S(o.baseId,a),h=A(o.baseId,a),p=a===o.value;return(0,u.jsx)(l.q7,{asChild:!0,...c,focusable:!n,active:p,children:(0,u.jsx)(d.sG.button,{type:"button",role:"tab","aria-selected":p,"aria-controls":h,"data-state":p?"active":"inactive","data-disabled":n?"":void 0,disabled:n,id:m,...i,ref:r,onMouseDown:(0,t.m)(e.onMouseDown,e=>{n||0!==e.button||!1!==e.ctrlKey?e.preventDefault():o.onValueChange(a)}),onKeyDown:(0,t.m)(e.onKeyDown,e=>{[" ","Enter"].includes(e.key)&&o.onValueChange(a)}),onFocus:(0,t.m)(e.onFocus,()=>{let e="manual"!==o.activationMode;p||n||!e||o.onValueChange(a)})})})});N.displayName=_;var w="TabsContent",k=a.forwardRef((e,r)=>{let{__scopeTabs:s,value:t,forceMount:n,children:l,...o}=e,c=b(w,s),m=S(c.baseId,t),h=A(c.baseId,t),p=t===c.value,x=a.useRef(p);return a.useEffect(()=>{let e=requestAnimationFrame(()=>x.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,u.jsx)(i.C,{present:n||p,children:({present:s})=>(0,u.jsx)(d.sG.div,{"data-state":p?"active":"inactive","data-orientation":c.orientation,role:"tabpanel","aria-labelledby":m,hidden:!s,id:h,tabIndex:0,...o,ref:r,style:{...e.style,animationDuration:x.current?"0s":void 0},children:s&&l})})});function S(e,r){return`${e}-trigger-${r}`}function A(e,r){return`${e}-content-${r}`}k.displayName=w;var C=v,F=y,P=N,q=k},55192:(e,r,s)=>{"use strict";s.d(r,{BT:()=>o,Wu:()=>c,ZB:()=>d,Zp:()=>l,aR:()=>i,wL:()=>m});var a=s(60687),t=s(43210),n=s(96241);let l=t.forwardRef(({className:e,...r},s)=>(0,a.jsx)("div",{ref:s,className:(0,n.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",e),...r}));l.displayName="Card";let i=t.forwardRef(({className:e,...r},s)=>(0,a.jsx)("div",{ref:s,className:(0,n.cn)("flex flex-col space-y-1.5 p-6",e),...r}));i.displayName="CardHeader";let d=t.forwardRef(({className:e,...r},s)=>(0,a.jsx)("div",{ref:s,className:(0,n.cn)("text-2xl font-semibold leading-none tracking-tight",e),...r}));d.displayName="CardTitle";let o=t.forwardRef(({className:e,...r},s)=>(0,a.jsx)("div",{ref:s,className:(0,n.cn)("text-sm text-muted-foreground",e),...r}));o.displayName="CardDescription";let c=t.forwardRef(({className:e,...r},s)=>(0,a.jsx)("div",{ref:s,className:(0,n.cn)("p-6 pt-0",e),...r}));c.displayName="CardContent";let m=t.forwardRef(({className:e,...r},s)=>(0,a.jsx)("div",{ref:s,className:(0,n.cn)("flex items-center p-6 pt-0",e),...r}));m.displayName="CardFooter"},58869:(e,r,s)=>{"use strict";s.d(r,{A:()=>a});let a=(0,s(62688).A)("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63974:(e,r,s)=>{"use strict";s.d(r,{bq:()=>u,eb:()=>g,gC:()=>x,l6:()=>c,yv:()=>m});var a=s(60687),t=s(43210),n=s(97822),l=s(78272),i=s(3589),d=s(13964),o=s(96241);let c=n.bL;n.YJ;let m=n.WT,u=t.forwardRef(({className:e,children:r,...s},t)=>(0,a.jsxs)(n.l9,{ref:t,className:(0,o.cn)("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",e),...s,children:[r,(0,a.jsx)(n.In,{asChild:!0,children:(0,a.jsx)(l.A,{className:"h-4 w-4 opacity-50"})})]}));u.displayName=n.l9.displayName;let h=t.forwardRef(({className:e,...r},s)=>(0,a.jsx)(n.PP,{ref:s,className:(0,o.cn)("flex cursor-default items-center justify-center py-1",e),...r,children:(0,a.jsx)(i.A,{className:"h-4 w-4"})}));h.displayName=n.PP.displayName;let p=t.forwardRef(({className:e,...r},s)=>(0,a.jsx)(n.wn,{ref:s,className:(0,o.cn)("flex cursor-default items-center justify-center py-1",e),...r,children:(0,a.jsx)(l.A,{className:"h-4 w-4"})}));p.displayName=n.wn.displayName;let x=t.forwardRef(({className:e,children:r,position:s="popper",...t},l)=>(0,a.jsx)(n.ZL,{children:(0,a.jsxs)(n.UC,{ref:l,className:(0,o.cn)("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===s&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:s,...t,children:[(0,a.jsx)(h,{}),(0,a.jsx)(n.LM,{className:(0,o.cn)("p-1","popper"===s&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:r}),(0,a.jsx)(p,{})]})}));x.displayName=n.UC.displayName,t.forwardRef(({className:e,...r},s)=>(0,a.jsx)(n.JU,{ref:s,className:(0,o.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",e),...r})).displayName=n.JU.displayName;let g=t.forwardRef(({className:e,children:r,...s},t)=>(0,a.jsxs)(n.q7,{ref:t,className:(0,o.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),...s,children:[(0,a.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,a.jsx)(n.VF,{children:(0,a.jsx)(d.A,{className:"h-4 w-4"})})}),(0,a.jsx)(n.p4,{children:r})]}));g.displayName=n.q7.displayName,t.forwardRef(({className:e,...r},s)=>(0,a.jsx)(n.wv,{ref:s,className:(0,o.cn)("-mx-1 my-1 h-px bg-muted",e),...r})).displayName=n.wv.displayName},71702:(e,r,s)=>{"use strict";s.d(r,{dj:()=>u});var a=s(43210);let t=0,n=new Map,l=e=>{if(n.has(e))return;let r=setTimeout(()=>{n.delete(e),c({type:"REMOVE_TOAST",toastId:e})},1e6);n.set(e,r)},i=(e,r)=>{switch(r.type){case"ADD_TOAST":return{...e,toasts:[r.toast,...e.toasts].slice(0,1)};case"UPDATE_TOAST":return{...e,toasts:e.toasts.map(e=>e.id===r.toast.id?{...e,...r.toast}:e)};case"DISMISS_TOAST":{let{toastId:s}=r;return s?l(s):e.toasts.forEach(e=>{l(e.id)}),{...e,toasts:e.toasts.map(e=>e.id===s||void 0===s?{...e,open:!1}:e)}}case"REMOVE_TOAST":if(void 0===r.toastId)return{...e,toasts:[]};return{...e,toasts:e.toasts.filter(e=>e.id!==r.toastId)}}},d=[],o={toasts:[]};function c(e){o=i(o,e),d.forEach(e=>{e(o)})}function m({...e}){let r=(t=(t+1)%Number.MAX_SAFE_INTEGER).toString(),s=()=>c({type:"DISMISS_TOAST",toastId:r});return c({type:"ADD_TOAST",toast:{...e,id:r,open:!0,onOpenChange:e=>{e||s()}}}),{id:r,dismiss:s,update:e=>c({type:"UPDATE_TOAST",toast:{...e,id:r}})}}function u(){let[e,r]=a.useState(o);return a.useEffect(()=>(d.push(r),()=>{let e=d.indexOf(r);e>-1&&d.splice(e,1)}),[e]),{...e,toast:m,dismiss:e=>c({type:"DISMISS_TOAST",toastId:e})}}},78148:(e,r,s)=>{"use strict";s.d(r,{b:()=>i});var a=s(43210),t=s(14163),n=s(60687),l=a.forwardRef((e,r)=>(0,n.jsx)(t.sG.label,{...e,ref:r,onMouseDown:r=>{r.target.closest("button, input, select, textarea")||(e.onMouseDown?.(r),!r.defaultPrevented&&r.detail>1&&r.preventDefault())}}));l.displayName="Label";var i=l},78780:(e,r,s)=>{Promise.resolve().then(s.bind(s,43565))},79410:(e,r,s)=>{"use strict";s.d(r,{A:()=>a});let a=(0,s(62688).A)("Building",[["rect",{width:"16",height:"20",x:"4",y:"2",rx:"2",ry:"2",key:"76otgf"}],["path",{d:"M9 22v-4h6v4",key:"r93iot"}],["path",{d:"M8 6h.01",key:"1dz90k"}],["path",{d:"M16 6h.01",key:"1x0f13"}],["path",{d:"M12 6h.01",key:"1vi96p"}],["path",{d:"M12 10h.01",key:"1nrarc"}],["path",{d:"M12 14h.01",key:"1etili"}],["path",{d:"M16 10h.01",key:"1m94wz"}],["path",{d:"M16 14h.01",key:"1gbofw"}],["path",{d:"M8 10h.01",key:"19clt8"}],["path",{d:"M8 14h.01",key:"6423bh"}]])},85778:(e,r,s)=>{"use strict";s.d(r,{A:()=>a});let a=(0,s(62688).A)("CreditCard",[["rect",{width:"20",height:"14",x:"2",y:"5",rx:"2",key:"ynyp8z"}],["line",{x1:"2",x2:"22",y1:"10",y2:"10",key:"1b3vmo"}]])},85910:(e,r,s)=>{"use strict";s.d(r,{Xi:()=>o,av:()=>c,j7:()=>d,tU:()=>i});var a=s(60687),t=s(43210),n=s(55146),l=s(96241);let i=n.bL,d=t.forwardRef(({className:e,...r},s)=>(0,a.jsx)(n.B8,{ref:s,className:(0,l.cn)("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",e),...r}));d.displayName=n.B8.displayName;let o=t.forwardRef(({className:e,...r},s)=>(0,a.jsx)(n.l9,{ref:s,className:(0,l.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",e),...r}));o.displayName=n.l9.displayName;let c=t.forwardRef(({className:e,...r},s)=>(0,a.jsx)(n.UC,{ref:s,className:(0,l.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",e),...r}));c.displayName=n.UC.displayName},92193:(e,r,s)=>{"use strict";s.r(r),s.d(r,{GlobalError:()=>l.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>u,tree:()=>o});var a=s(65239),t=s(48088),n=s(88170),l=s.n(n),i=s(30893),d={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>i[e]);s.d(r,d);let o={children:["",{children:["scholarship-application",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,25613)),"C:\\laragon\\www\\laravel-api-ngo\\front-end\\app\\scholarship-application\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,58014)),"C:\\laragon\\www\\laravel-api-ngo\\front-end\\app\\layout.tsx"],error:[()=>Promise.resolve().then(s.bind(s,52608)),"C:\\laragon\\www\\laravel-api-ngo\\front-end\\app\\error.tsx"],loading:[()=>Promise.resolve().then(s.bind(s,99766)),"C:\\laragon\\www\\laravel-api-ngo\\front-end\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(s.bind(s,82366)),"C:\\laragon\\www\\laravel-api-ngo\\front-end\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["C:\\laragon\\www\\laravel-api-ngo\\front-end\\app\\scholarship-application\\page.tsx"],m={require:s,loadChunk:()=>Promise.resolve()},u=new a.AppPageRouteModule({definition:{kind:t.RouteKind.APP_PAGE,page:"/scholarship-application/page",pathname:"/scholarship-application",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})}};var r=require("../../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),a=r.X(0,[555,394,702],()=>s(92193));module.exports=a})();