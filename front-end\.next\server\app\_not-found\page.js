/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/_not-found/page";
exports.ids = ["app/_not-found/page"];
exports.modules = {

/***/ "(rsc)/./app/error.tsx":
/*!***********************!*\
  !*** ./app/error.tsx ***!
  \***********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\error.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\laragon\\www\\laravel-api-ngo\\front-end\\app\\error.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"b1819735dab4\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIkM6XFxsYXJhZ29uXFx3d3dcXGxhcmF2ZWwtYXBpLW5nb1xcZnJvbnQtZW5kXFxhcHBcXGdsb2JhbHMuY3NzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiYjE4MTk3MzVkYWI0XCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata),\n/* harmony export */   viewport: () => (/* binding */ viewport)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_display_swap_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"],\"display\":\"swap\",\"variable\":\"--font-inter\"}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"],\\\"display\\\":\\\"swap\\\",\\\"variable\\\":\\\"--font-inter\\\"}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_display_swap_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_display_swap_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n/* harmony import */ var _components_theme_provider__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/theme-provider */ \"(rsc)/./components/theme-provider.tsx\");\n/* harmony import */ var _components_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/navigation */ \"(rsc)/./components/navigation.tsx\");\n/* harmony import */ var _components_footer__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/footer */ \"(rsc)/./components/footer.tsx\");\n\n\n\n\n\n\nconst metadata = {\n    title: \"NGO Foundation | Empowering Communities\",\n    description: \"We provide scholarships, school supplies, and community development programs to support students, underprivileged individuals, and those in need.\",\n    other: {\n        \"Content-Security-Policy\": \"script-src 'self' 'unsafe-inline' 'unsafe-eval'; object-src 'none';\"\n    },\n    generator: 'v0.dev'\n};\nconst viewport = {\n    width: 'device-width',\n    initialScale: 1,\n    maximumScale: 5\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        suppressHydrationWarning: true,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"head\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"preconnect\",\n                        href: \"https://fonts.googleapis.com\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\layout.tsx\",\n                        lineNumber: 40,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"preconnect\",\n                        href: \"https://fonts.gstatic.com\",\n                        crossOrigin: \"\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\layout.tsx\",\n                        lineNumber: 41,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\layout.tsx\",\n                lineNumber: 38,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                className: `${(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_display_swap_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_5___default().className)} flex flex-col min-h-screen`,\n                suppressHydrationWarning: true,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_theme_provider__WEBPACK_IMPORTED_MODULE_2__.ThemeProvider, {\n                    attribute: \"class\",\n                    defaultTheme: \"light\",\n                    enableSystem: true,\n                    disableTransitionOnChange: false,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_navigation__WEBPACK_IMPORTED_MODULE_3__.Navigation, {}, void 0, false, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\layout.tsx\",\n                            lineNumber: 45,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                            className: \"flex-grow\",\n                            children: children\n                        }, void 0, false, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\layout.tsx\",\n                            lineNumber: 46,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_footer__WEBPACK_IMPORTED_MODULE_4__.Footer, {}, void 0, false, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\layout.tsx\",\n                            lineNumber: 47,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\layout.tsx\",\n                    lineNumber: 44,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\layout.tsx\",\n                lineNumber: 43,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\layout.tsx\",\n        lineNumber: 37,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./app/loading.tsx":
/*!*************************!*\
  !*** ./app/loading.tsx ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Loading)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n\nfunction Loading() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n            children: \"Loading...\"\n        }, void 0, false, {\n            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\loading.tsx\",\n            lineNumber: 4,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\loading.tsx\",\n        lineNumber: 3,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvbG9hZGluZy50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUFlLFNBQVNBO0lBQ3RCLHFCQUNFLDhEQUFDQztrQkFDQyw0RUFBQ0M7c0JBQUU7Ozs7Ozs7Ozs7O0FBR1QiLCJzb3VyY2VzIjpbIkM6XFxsYXJhZ29uXFx3d3dcXGxhcmF2ZWwtYXBpLW5nb1xcZnJvbnQtZW5kXFxhcHBcXGxvYWRpbmcudHN4Il0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIExvYWRpbmcoKSB7XHJcbiAgcmV0dXJuIChcclxuICAgIDxkaXY+XHJcbiAgICAgIDxwPkxvYWRpbmcuLi48L3A+XHJcbiAgICA8L2Rpdj5cclxuICApO1xyXG59ICJdLCJuYW1lcyI6WyJMb2FkaW5nIiwiZGl2IiwicCJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./app/loading.tsx\n");

/***/ }),

/***/ "(rsc)/./app/not-found.tsx":
/*!***************************!*\
  !*** ./app/not-found.tsx ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ NotFound)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(rsc)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n\n\nfunction NotFound() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            textAlign: 'center',\n            marginTop: '50px'\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                children: \"404 - Page Not Found\"\n            }, void 0, false, {\n                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\not-found.tsx\",\n                lineNumber: 6,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                children: \"Sorry, the page you are looking for does not exist.\"\n            }, void 0, false, {\n                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\not-found.tsx\",\n                lineNumber: 7,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                href: \"/\",\n                children: \"Go back home\"\n            }, void 0, false, {\n                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\not-found.tsx\",\n                lineNumber: 8,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\not-found.tsx\",\n        lineNumber: 5,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvbm90LWZvdW5kLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFBNEI7QUFFYixTQUFTQztJQUN0QixxQkFDRSw4REFBQ0M7UUFBSUMsT0FBTztZQUFFQyxXQUFXO1lBQVVDLFdBQVc7UUFBTzs7MEJBQ25ELDhEQUFDQzswQkFBRzs7Ozs7OzBCQUNKLDhEQUFDQzswQkFBRTs7Ozs7OzBCQUNILDhEQUFDUCxrREFBSUE7Z0JBQUNRLE1BQUs7MEJBQUk7Ozs7Ozs7Ozs7OztBQUtyQiIsInNvdXJjZXMiOlsiQzpcXGxhcmFnb25cXHd3d1xcbGFyYXZlbC1hcGktbmdvXFxmcm9udC1lbmRcXGFwcFxcbm90LWZvdW5kLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgTGluayBmcm9tICduZXh0L2xpbmsnXG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIE5vdEZvdW5kKCkge1xuICByZXR1cm4gKFxuICAgIDxkaXYgc3R5bGU9e3sgdGV4dEFsaWduOiAnY2VudGVyJywgbWFyZ2luVG9wOiAnNTBweCcgfX0+XG4gICAgICA8aDE+NDA0IC0gUGFnZSBOb3QgRm91bmQ8L2gxPlxuICAgICAgPHA+U29ycnksIHRoZSBwYWdlIHlvdSBhcmUgbG9va2luZyBmb3IgZG9lcyBub3QgZXhpc3QuPC9wPlxuICAgICAgPExpbmsgaHJlZj1cIi9cIj5cbiAgICAgICAgR28gYmFjayBob21lXG4gICAgICA8L0xpbms+XG4gICAgPC9kaXY+XG4gIClcbn1cbiAiXSwibmFtZXMiOlsiTGluayIsIk5vdEZvdW5kIiwiZGl2Iiwic3R5bGUiLCJ0ZXh0QWxpZ24iLCJtYXJnaW5Ub3AiLCJoMSIsInAiLCJocmVmIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./app/not-found.tsx\n");

/***/ }),

/***/ "(rsc)/./components/footer.tsx":
/*!*******************************!*\
  !*** ./components/footer.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Footer: () => (/* binding */ Footer)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const Footer = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call Footer() from the server but Footer is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\laragon\\www\\laravel-api-ngo\\front-end\\components\\footer.tsx",
"Footer",
);

/***/ }),

/***/ "(rsc)/./components/navigation.tsx":
/*!***********************************!*\
  !*** ./components/navigation.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Navigation: () => (/* binding */ Navigation)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const Navigation = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call Navigation() from the server but Navigation is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\laragon\\www\\laravel-api-ngo\\front-end\\components\\navigation.tsx",
"Navigation",
);

/***/ }),

/***/ "(rsc)/./components/theme-provider.tsx":
/*!***************************************!*\
  !*** ./components/theme-provider.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ThemeProvider: () => (/* binding */ ThemeProvider)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const ThemeProvider = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call ThemeProvider() from the server but ThemeProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\laragon\\www\\laravel-api-ngo\\front-end\\components\\theme-provider.tsx",
"ThemeProvider",
);

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=private-next-app-dir%2Fnot-found.tsx&appDir=C%3A%5Claragon%5Cwww%5Claravel-api-ngo%5Cfront-end%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Claragon%5Cwww%5Claravel-api-ngo%5Cfront-end&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=private-next-app-dir%2Fnot-found.tsx&appDir=C%3A%5Claragon%5Cwww%5Claravel-api-ngo%5Cfront-end%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Claragon%5Cwww%5Claravel-api-ngo%5Cfront-end&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst notFound0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/not-found.tsx */ \"(rsc)/./app/not-found.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\"));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/error.tsx */ \"(rsc)/./app/error.tsx\"));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/loading.tsx */ \"(rsc)/./app/loading.tsx\"));\nconst module4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/not-found.tsx */ \"(rsc)/./app/not-found.tsx\"));\nconst module5 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module6 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n          children: [\"/_not-found\", {\n            children: ['__PAGE__', {}, {\n              page: [\n                notFound0,\n                \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\not-found.tsx\"\n              ]\n            }]\n          }, {}]\n        },\n        {\n        'layout': [module1, \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\layout.tsx\"],\n'error': [module2, \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\error.tsx\"],\n'loading': [module3, \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\loading.tsx\"],\n'not-found': [module4, \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\not-found.tsx\"],\n'forbidden': [module5, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module6, \"next/dist/client/components/unauthorized-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/_not-found/page\",\n        pathname: \"/_not-found\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=private-next-app-dir%2Fnot-found.tsx&appDir=C%3A%5Claragon%5Cwww%5Claravel-api-ngo%5Cfront-end%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Claragon%5Cwww%5Claravel-api-ngo%5Cfront-end&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5Claravel-api-ngo%5C%5Cfront-end%5C%5Capp%5C%5Cerror.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5Claravel-api-ngo%5C%5Cfront-end%5C%5Capp%5C%5Cerror.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/error.tsx */ \"(rsc)/./app/error.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNsYXJhZ29uJTVDJTVDd3d3JTVDJTVDbGFyYXZlbC1hcGktbmdvJTVDJTVDZnJvbnQtZW5kJTVDJTVDYXBwJTVDJTVDZXJyb3IudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSwwSUFBaUciLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXGxhcmFnb25cXFxcd3d3XFxcXGxhcmF2ZWwtYXBpLW5nb1xcXFxmcm9udC1lbmRcXFxcYXBwXFxcXGVycm9yLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5Claravel-api-ngo%5C%5Cfront-end%5C%5Capp%5C%5Cerror.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5Claravel-api-ngo%5C%5Cfront-end%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5Claravel-api-ngo%5C%5Cfront-end%5C%5Ccomponents%5C%5Cfooter.tsx%22%2C%22ids%22%3A%5B%22Footer%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5Claravel-api-ngo%5C%5Cfront-end%5C%5Ccomponents%5C%5Cnavigation.tsx%22%2C%22ids%22%3A%5B%22Navigation%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5Claravel-api-ngo%5C%5Cfront-end%5C%5Ccomponents%5C%5Ctheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5Claravel-api-ngo%5C%5Cfront-end%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5Claravel-api-ngo%5C%5Cfront-end%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5Claravel-api-ngo%5C%5Cfront-end%5C%5Ccomponents%5C%5Cfooter.tsx%22%2C%22ids%22%3A%5B%22Footer%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5Claravel-api-ngo%5C%5Cfront-end%5C%5Ccomponents%5C%5Cnavigation.tsx%22%2C%22ids%22%3A%5B%22Navigation%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5Claravel-api-ngo%5C%5Cfront-end%5C%5Ccomponents%5C%5Ctheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5Claravel-api-ngo%5C%5Cfront-end%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/footer.tsx */ \"(rsc)/./components/footer.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/navigation.tsx */ \"(rsc)/./components/navigation.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/theme-provider.tsx */ \"(rsc)/./components/theme-provider.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5Claravel-api-ngo%5C%5Cfront-end%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5Claravel-api-ngo%5C%5Cfront-end%5C%5Ccomponents%5C%5Cfooter.tsx%22%2C%22ids%22%3A%5B%22Footer%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5Claravel-api-ngo%5C%5Cfront-end%5C%5Ccomponents%5C%5Cnavigation.tsx%22%2C%22ids%22%3A%5B%22Navigation%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5Claravel-api-ngo%5C%5Cfront-end%5C%5Ccomponents%5C%5Ctheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5Claravel-api-ngo%5C%5Cfront-end%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5Claravel-api-ngo%5C%5Cfront-end%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Capp-dir%5C%5Clink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5Claravel-api-ngo%5C%5Cfront-end%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Capp-dir%5C%5Clink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/app-dir/link.js */ \"(rsc)/./node_modules/next/dist/client/app-dir/link.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNsYXJhZ29uJTVDJTVDd3d3JTVDJTVDbGFyYXZlbC1hcGktbmdvJTVDJTVDZnJvbnQtZW5kJTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDbmV4dCU1QyU1Q2Rpc3QlNUMlNUNjbGllbnQlNUMlNUNhcHAtZGlyJTVDJTVDbGluay5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMl9fZXNNb2R1bGUlMjIlMkMlMjJkZWZhdWx0JTIyJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxnTkFBK0siLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiwgd2VicGFja0V4cG9ydHM6IFtcIl9fZXNNb2R1bGVcIixcImRlZmF1bHRcIl0gKi8gXCJDOlxcXFxsYXJhZ29uXFxcXHd3d1xcXFxsYXJhdmVsLWFwaS1uZ29cXFxcZnJvbnQtZW5kXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGFwcC1kaXJcXFxcbGluay5qc1wiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5Claravel-api-ngo%5C%5Cfront-end%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Capp-dir%5C%5Clink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5Claravel-api-ngo%5C%5Cfront-end%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5Claravel-api-ngo%5C%5Cfront-end%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5Claravel-api-ngo%5C%5Cfront-end%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5Claravel-api-ngo%5C%5Cfront-end%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5Claravel-api-ngo%5C%5Cfront-end%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5Claravel-api-ngo%5C%5Cfront-end%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5Claravel-api-ngo%5C%5Cfront-end%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5Claravel-api-ngo%5C%5Cfront-end%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5Claravel-api-ngo%5C%5Cfront-end%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5Claravel-api-ngo%5C%5Cfront-end%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5Claravel-api-ngo%5C%5Cfront-end%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5Claravel-api-ngo%5C%5Cfront-end%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5Claravel-api-ngo%5C%5Cfront-end%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5Claravel-api-ngo%5C%5Cfront-end%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5Claravel-api-ngo%5C%5Cfront-end%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5Claravel-api-ngo%5C%5Cfront-end%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5Claravel-api-ngo%5C%5Cfront-end%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5Claravel-api-ngo%5C%5Cfront-end%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5Claravel-api-ngo%5C%5Cfront-end%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5Claravel-api-ngo%5C%5Cfront-end%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5Claravel-api-ngo%5C%5Cfront-end%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5Claravel-api-ngo%5C%5Cfront-end%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5Claravel-api-ngo%5C%5Cfront-end%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5Claravel-api-ngo%5C%5Cfront-end%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./app/error.tsx":
/*!***********************!*\
  !*** ./app/error.tsx ***!
  \***********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Error)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Home_RefreshCcw_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Home,RefreshCcw!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/triangle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Home_RefreshCcw_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Home,RefreshCcw!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/refresh-ccw.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Home_RefreshCcw_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Home,RefreshCcw!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/house.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_3__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nfunction Error({ error, reset }) {\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Error.useEffect\": ()=>{\n            // Log the error to an error reporting service\n            console.error(error);\n        }\n    }[\"Error.useEffect\"], [\n        error\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-red-50 via-white to-orange-50 dark:from-gray-900 dark:via-gray-950 dark:to-gray-900 flex items-center justify-center px-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-center max-w-md mx-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Home_RefreshCcw_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            className: \"h-16 w-16 text-red-500 mx-auto mb-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\error.tsx\",\n                            lineNumber: 24,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-2xl font-bold text-gray-900 dark:text-white mb-2\",\n                            children: \"Something went wrong!\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\error.tsx\",\n                            lineNumber: 25,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 dark:text-gray-300 mb-8\",\n                            children: \"We're sorry, but something unexpected happened. Please try again or go back to the home page.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\error.tsx\",\n                            lineNumber: 28,\n                            columnNumber: 11\n                        }, this),\n                        error.message && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4 mb-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-red-600 dark:text-red-400 font-mono break-all\",\n                                children: error.message\n                            }, void 0, false, {\n                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\error.tsx\",\n                                lineNumber: 33,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\error.tsx\",\n                            lineNumber: 32,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\error.tsx\",\n                    lineNumber: 23,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col sm:flex-row gap-4 justify-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                            onClick: reset,\n                            className: \"bg-green-600 hover:bg-green-700 text-white flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Home_RefreshCcw_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\error.tsx\",\n                                    lineNumber: 45,\n                                    columnNumber: 13\n                                }, this),\n                                \"Try Again\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\error.tsx\",\n                            lineNumber: 41,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                            href: \"/\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                variant: \"outline\",\n                                className: \"border-green-600 text-green-600 hover:bg-green-600 hover:text-white flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Home_RefreshCcw_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\error.tsx\",\n                                        lineNumber: 50,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"Go Home\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\error.tsx\",\n                                lineNumber: 49,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\error.tsx\",\n                            lineNumber: 48,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\error.tsx\",\n                    lineNumber: 40,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\error.tsx\",\n            lineNumber: 22,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\error.tsx\",\n        lineNumber: 21,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/error.tsx\n");

/***/ }),

/***/ "(ssr)/./components/footer.tsx":
/*!*******************************!*\
  !*** ./components/footer.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Footer: () => (/* binding */ Footer)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_Facebook_Heart_Instagram_Linkedin_Mail_MapPin_MessageCircle_Phone_Twitter_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Facebook,Heart,Instagram,Linkedin,Mail,MapPin,MessageCircle,Phone,Twitter,Youtube!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/facebook.js\");\n/* harmony import */ var _barrel_optimize_names_Facebook_Heart_Instagram_Linkedin_Mail_MapPin_MessageCircle_Phone_Twitter_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Facebook,Heart,Instagram,Linkedin,Mail,MapPin,MessageCircle,Phone,Twitter,Youtube!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/twitter.js\");\n/* harmony import */ var _barrel_optimize_names_Facebook_Heart_Instagram_Linkedin_Mail_MapPin_MessageCircle_Phone_Twitter_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Facebook,Heart,Instagram,Linkedin,Mail,MapPin,MessageCircle,Phone,Twitter,Youtube!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/instagram.js\");\n/* harmony import */ var _barrel_optimize_names_Facebook_Heart_Instagram_Linkedin_Mail_MapPin_MessageCircle_Phone_Twitter_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Facebook,Heart,Instagram,Linkedin,Mail,MapPin,MessageCircle,Phone,Twitter,Youtube!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/linkedin.js\");\n/* harmony import */ var _barrel_optimize_names_Facebook_Heart_Instagram_Linkedin_Mail_MapPin_MessageCircle_Phone_Twitter_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Facebook,Heart,Instagram,Linkedin,Mail,MapPin,MessageCircle,Phone,Twitter,Youtube!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/youtube.js\");\n/* harmony import */ var _barrel_optimize_names_Facebook_Heart_Instagram_Linkedin_Mail_MapPin_MessageCircle_Phone_Twitter_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Facebook,Heart,Instagram,Linkedin,Mail,MapPin,MessageCircle,Phone,Twitter,Youtube!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* harmony import */ var _barrel_optimize_names_Facebook_Heart_Instagram_Linkedin_Mail_MapPin_MessageCircle_Phone_Twitter_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Facebook,Heart,Instagram,Linkedin,Mail,MapPin,MessageCircle,Phone,Twitter,Youtube!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/message-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Facebook_Heart_Instagram_Linkedin_Mail_MapPin_MessageCircle_Phone_Twitter_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Facebook,Heart,Instagram,Linkedin,Mail,MapPin,MessageCircle,Phone,Twitter,Youtube!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* harmony import */ var _barrel_optimize_names_Facebook_Heart_Instagram_Linkedin_Mail_MapPin_MessageCircle_Phone_Twitter_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Facebook,Heart,Instagram,Linkedin,Mail,MapPin,MessageCircle,Phone,Twitter,Youtube!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/phone.js\");\n/* harmony import */ var _barrel_optimize_names_Facebook_Heart_Instagram_Linkedin_Mail_MapPin_MessageCircle_Phone_Twitter_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Facebook,Heart,Instagram,Linkedin,Mail,MapPin,MessageCircle,Phone,Twitter,Youtube!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/input */ \"(ssr)/./components/ui/input.tsx\");\n/* harmony import */ var _hooks_useSettings__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/hooks/useSettings */ \"(ssr)/./hooks/useSettings.ts\");\n/* __next_internal_client_entry_do_not_use__ Footer auto */ \n\n\n\n\n\n\nfunction Footer() {\n    const [isClient, setIsClient] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Footer.useEffect\": ()=>{\n            setIsClient(true);\n        }\n    }[\"Footer.useEffect\"], []);\n    const { settings, loading: settingsLoading } = (0,_hooks_useSettings__WEBPACK_IMPORTED_MODULE_5__.useSettings)();\n    // Get dynamic settings or use defaults\n    const appName = settings?.app_name || 'Laravel NGO';\n    const appLogo = settings?.app_logo;\n    const contactEmail = settings?.contact_email || '<EMAIL>';\n    const contactPhone = settings?.contact_phone || '+234 ************';\n    const siteDescription = settings?.site_description || 'Empowering communities through education, development programs, and sustainable initiatives.';\n    // Address information\n    const organizationAddress = settings?.organization_address;\n    const organizationCity = settings?.organization_city;\n    const organizationState = settings?.organization_state;\n    const organizationCountry = settings?.organization_country;\n    const organizationPostalCode = settings?.organization_postal_code;\n    // Construct full address\n    const fullAddress = [\n        organizationAddress,\n        organizationCity,\n        organizationState,\n        organizationCountry,\n        organizationPostalCode\n    ].filter(Boolean).join(', ');\n    // Social media links\n    const socialLinks = [\n        {\n            key: 'social_facebook',\n            icon: _barrel_optimize_names_Facebook_Heart_Instagram_Linkedin_Mail_MapPin_MessageCircle_Phone_Twitter_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n            label: 'Facebook'\n        },\n        {\n            key: 'social_twitter',\n            icon: _barrel_optimize_names_Facebook_Heart_Instagram_Linkedin_Mail_MapPin_MessageCircle_Phone_Twitter_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n            label: 'Twitter'\n        },\n        {\n            key: 'social_instagram',\n            icon: _barrel_optimize_names_Facebook_Heart_Instagram_Linkedin_Mail_MapPin_MessageCircle_Phone_Twitter_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n            label: 'Instagram'\n        },\n        {\n            key: 'social_linkedin',\n            icon: _barrel_optimize_names_Facebook_Heart_Instagram_Linkedin_Mail_MapPin_MessageCircle_Phone_Twitter_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n            label: 'LinkedIn'\n        },\n        {\n            key: 'social_youtube',\n            icon: _barrel_optimize_names_Facebook_Heart_Instagram_Linkedin_Mail_MapPin_MessageCircle_Phone_Twitter_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n            label: 'YouTube'\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n        className: \"bg-green-950 dark:bg-black text-white\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-4 sm:px-6 lg:px-8 py-12\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/\",\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        appLogo ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                            src: appLogo,\n                                            alt: `${appName} Logo`,\n                                            className: \"h-8 w-auto object-contain\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\footer.tsx\",\n                                            lineNumber: 58,\n                                            columnNumber: 17\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative h-8 w-8 overflow-hidden rounded-full bg-green-600\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Facebook_Heart_Instagram_Linkedin_Mail_MapPin_MessageCircle_Phone_Twitter_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                className: \"absolute inset-0 m-auto h-5 w-5 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\footer.tsx\",\n                                                lineNumber: 65,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\footer.tsx\",\n                                            lineNumber: 64,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xl font-bold\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-amber-400 dark:text-amber-400\",\n                                                children: appName\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\footer.tsx\",\n                                                lineNumber: 69,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\footer.tsx\",\n                                            lineNumber: 68,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\footer.tsx\",\n                                    lineNumber: 56,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-green-200 text-sm\",\n                                    children: siteDescription\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\footer.tsx\",\n                                    lineNumber: 72,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-wrap gap-2\",\n                                    children: [\n                                        socialLinks.map(({ key, icon: Icon, label })=>{\n                                            const url = settings?.[key];\n                                            if (!url) return null;\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: url,\n                                                target: \"_blank\",\n                                                rel: \"noopener noreferrer\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                    size: \"icon\",\n                                                    variant: \"ghost\",\n                                                    className: \"text-green-200 hover:text-amber-400 hover:bg-green-800/50 rounded-full transition-colors duration-200\",\n                                                    \"aria-label\": label,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\footer.tsx\",\n                                                        lineNumber: 90,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\footer.tsx\",\n                                                    lineNumber: 84,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, key, false, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\footer.tsx\",\n                                                lineNumber: 83,\n                                                columnNumber: 19\n                                            }, this);\n                                        }),\n                                        settings?.social_whatsapp && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            href: `https://wa.me/${settings.social_whatsapp.replace(/[^0-9]/g, '')}`,\n                                            target: \"_blank\",\n                                            rel: \"noopener noreferrer\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                size: \"icon\",\n                                                variant: \"ghost\",\n                                                className: \"text-green-200 hover:text-amber-400 hover:bg-green-800/50 rounded-full transition-colors duration-200\",\n                                                \"aria-label\": \"WhatsApp\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Facebook_Heart_Instagram_Linkedin_Mail_MapPin_MessageCircle_Phone_Twitter_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\footer.tsx\",\n                                                    lineNumber: 109,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\footer.tsx\",\n                                                lineNumber: 103,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\footer.tsx\",\n                                            lineNumber: 98,\n                                            columnNumber: 17\n                                        }, this),\n                                        settings?.social_telegram && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            href: settings.social_telegram,\n                                            target: \"_blank\",\n                                            rel: \"noopener noreferrer\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                size: \"icon\",\n                                                variant: \"ghost\",\n                                                className: \"text-green-200 hover:text-amber-400 hover:bg-green-800/50 rounded-full transition-colors duration-200\",\n                                                \"aria-label\": \"Telegram\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"h-4 w-4\",\n                                                    fill: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        d: \"M11.944 0A12 12 0 0 0 0 12a12 12 0 0 0 12 12 12 12 0 0 0 12-12A12 12 0 0 0 12 0a12 12 0 0 0-.056 0zm4.962 7.224c.1-.002.321.023.465.14a.506.506 0 0 1 .171.325c.016.093.036.306.02.472-.18 1.898-.962 6.502-1.36 8.627-.168.9-.499 1.201-.82 1.23-.696.065-1.225-.46-1.9-.902-1.056-.693-1.653-1.124-2.678-1.8-1.185-.78-.417-1.21.258-1.91.177-.184 3.247-2.977 3.307-3.23.007-.032.014-.15-.056-.212s-.174-.041-.249-.024c-.106.024-1.793 1.14-5.061 3.345-.48.33-.913.49-1.302.48-.428-.008-1.252-.241-1.865-.44-.752-.245-1.349-.374-1.297-.789.027-.216.325-.437.893-.663 3.498-1.524 5.83-2.529 6.998-3.014 3.332-1.386 4.025-1.627 4.476-1.635z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\footer.tsx\",\n                                                        lineNumber: 124,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\footer.tsx\",\n                                                    lineNumber: 123,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\footer.tsx\",\n                                                lineNumber: 117,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\footer.tsx\",\n                                            lineNumber: 116,\n                                            columnNumber: 17\n                                        }, this),\n                                        settings?.social_tiktok && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            href: settings.social_tiktok,\n                                            target: \"_blank\",\n                                            rel: \"noopener noreferrer\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                size: \"icon\",\n                                                variant: \"ghost\",\n                                                className: \"text-green-200 hover:text-amber-400 hover:bg-green-800/50 rounded-full transition-colors duration-200\",\n                                                \"aria-label\": \"TikTok\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"h-4 w-4\",\n                                                    fill: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        d: \"M12.525.02c1.31-.02 2.61-.01 3.91-.02.08 1.53.63 3.09 1.75 4.17 1.12 1.11 2.7 1.62 4.24 1.79v4.03c-1.44-.05-2.89-.35-4.2-.97-.57-.26-1.1-.59-1.62-.93-.01 2.92.01 5.84-.02 8.75-.08 1.4-.54 2.79-1.35 3.94-1.31 1.92-3.58 3.17-5.91 3.21-1.43.08-2.86-.31-4.08-1.03-2.02-1.19-3.44-3.37-3.65-5.71-.02-.5-.03-1-.01-1.49.18-1.9 1.12-3.72 2.58-4.96 1.66-1.44 3.98-2.13 6.15-1.72.02 1.48-.04 2.96-.04 4.44-.99-.32-2.15-.23-3.02.37-.63.41-1.11 1.04-1.36 1.75-.21.51-.15 1.07-.14 1.61.24 1.64 1.82 3.02 3.5 2.87 1.12-.01 2.19-.66 2.77-1.61.19-.33.4-.67.41-1.06.1-1.79.06-3.57.07-5.36.01-4.03-.01-8.05.02-12.07z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\footer.tsx\",\n                                                        lineNumber: 140,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\footer.tsx\",\n                                                    lineNumber: 139,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\footer.tsx\",\n                                                lineNumber: 133,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\footer.tsx\",\n                                            lineNumber: 132,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\footer.tsx\",\n                                    lineNumber: 77,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\footer.tsx\",\n                            lineNumber: 55,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold\",\n                                    children: \"Quick Links\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\footer.tsx\",\n                                    lineNumber: 150,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/about\",\n                                                className: \"text-green-200 hover:text-amber-400 transition-colors\",\n                                                children: \"About Us\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\footer.tsx\",\n                                                lineNumber: 153,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\footer.tsx\",\n                                            lineNumber: 152,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/projects\",\n                                                className: \"text-green-200 hover:text-amber-400 transition-colors\",\n                                                children: \"Our Projects\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\footer.tsx\",\n                                                lineNumber: 158,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\footer.tsx\",\n                                            lineNumber: 157,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/scholarships\",\n                                                className: \"text-green-200 hover:text-amber-400 transition-colors\",\n                                                children: \"Scholarships\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\footer.tsx\",\n                                                lineNumber: 163,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\footer.tsx\",\n                                            lineNumber: 162,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/learn-with-us\",\n                                                className: \"text-green-200 hover:text-amber-400 transition-colors\",\n                                                children: \"Learn With Us\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\footer.tsx\",\n                                                lineNumber: 168,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\footer.tsx\",\n                                            lineNumber: 167,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/impact\",\n                                                className: \"text-green-200 hover:text-amber-400 transition-colors\",\n                                                children: \"Our Impact\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\footer.tsx\",\n                                                lineNumber: 173,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\footer.tsx\",\n                                            lineNumber: 172,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/team\",\n                                                className: \"text-green-200 hover:text-amber-400 transition-colors\",\n                                                children: \"Our Team\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\footer.tsx\",\n                                                lineNumber: 178,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\footer.tsx\",\n                                            lineNumber: 177,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\footer.tsx\",\n                                    lineNumber: 151,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\footer.tsx\",\n                            lineNumber: 149,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold\",\n                                    children: \"Contact Info\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\footer.tsx\",\n                                    lineNumber: 187,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-3\",\n                                    children: [\n                                        fullAddress && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-start gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Facebook_Heart_Instagram_Linkedin_Mail_MapPin_MessageCircle_Phone_Twitter_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                    className: \"h-4 w-4 text-green-400 mt-0.5 flex-shrink-0\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\footer.tsx\",\n                                                    lineNumber: 191,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-green-200 text-sm\",\n                                                    children: fullAddress\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\footer.tsx\",\n                                                    lineNumber: 192,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\footer.tsx\",\n                                            lineNumber: 190,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Facebook_Heart_Instagram_Linkedin_Mail_MapPin_MessageCircle_Phone_Twitter_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                    className: \"h-4 w-4 text-green-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\footer.tsx\",\n                                                    lineNumber: 196,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: `tel:${contactPhone}`,\n                                                    className: \"text-green-200 text-sm hover:text-amber-400 transition-colors\",\n                                                    children: contactPhone\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\footer.tsx\",\n                                                    lineNumber: 197,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\footer.tsx\",\n                                            lineNumber: 195,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Facebook_Heart_Instagram_Linkedin_Mail_MapPin_MessageCircle_Phone_Twitter_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                    className: \"h-4 w-4 text-green-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\footer.tsx\",\n                                                    lineNumber: 205,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: `mailto:${contactEmail}`,\n                                                    className: \"text-green-200 text-sm hover:text-amber-400 transition-colors\",\n                                                    children: contactEmail\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\footer.tsx\",\n                                                    lineNumber: 206,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\footer.tsx\",\n                                            lineNumber: 204,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\footer.tsx\",\n                                    lineNumber: 188,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\footer.tsx\",\n                            lineNumber: 186,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold\",\n                                    children: \"Stay Updated\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\footer.tsx\",\n                                    lineNumber: 218,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-green-200 text-sm\",\n                                    children: \"Subscribe to our newsletter for updates on our programs and impact.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\footer.tsx\",\n                                    lineNumber: 219,\n                                    columnNumber: 13\n                                }, this),\n                                isClient && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex gap-2\",\n                                    suppressHydrationWarning: true,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                            type: \"email\",\n                                            placeholder: \"Your email\",\n                                            className: \"bg-green-900 border-green-700 text-white placeholder:text-green-300 focus:ring-amber-500 focus:border-amber-500 rounded-md\",\n                                            suppressHydrationWarning: true\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\footer.tsx\",\n                                            lineNumber: 224,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            className: \"bg-amber-500 hover:bg-amber-600 text-green-950 dark:text-green-950\",\n                                            suppressHydrationWarning: true,\n                                            children: \"Subscribe\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\footer.tsx\",\n                                            lineNumber: 230,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\footer.tsx\",\n                                    lineNumber: 223,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"pt-4 space-y-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            href: \"/privacy\",\n                                            className: \"block text-green-300 text-xs hover:text-amber-400 transition-colors\",\n                                            children: \"Privacy Policy\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\footer.tsx\",\n                                            lineNumber: 241,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            href: \"/terms\",\n                                            className: \"block text-green-300 text-xs hover:text-amber-400 transition-colors\",\n                                            children: \"Terms of Service\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\footer.tsx\",\n                                            lineNumber: 244,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\footer.tsx\",\n                                    lineNumber: 240,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\footer.tsx\",\n                            lineNumber: 217,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\footer.tsx\",\n                    lineNumber: 53,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"border-t border-green-800 mt-8 pt-8 text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-green-200 text-sm\",\n                            children: [\n                                \"\\xa9 \",\n                                new Date().getFullYear(),\n                                \" \",\n                                appName,\n                                \". All rights reserved.\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\footer.tsx\",\n                            lineNumber: 252,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-green-300 text-xs mt-2\",\n                            children: \"Built with ❤️ to empower communities and create lasting impact.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\footer.tsx\",\n                            lineNumber: 255,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\footer.tsx\",\n                    lineNumber: 251,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\footer.tsx\",\n            lineNumber: 52,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\footer.tsx\",\n        lineNumber: 51,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(ssr)/./components/footer.tsx\n");

/***/ }),

/***/ "(ssr)/./components/navigation.tsx":
/*!***********************************!*\
  !*** ./components/navigation.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Navigation: () => (/* binding */ Navigation)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_sheet__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/sheet */ \"(ssr)/./components/ui/sheet.tsx\");\n/* harmony import */ var _components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/dropdown-menu */ \"(ssr)/./components/ui/dropdown-menu.tsx\");\n/* harmony import */ var _radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @radix-ui/react-dialog */ \"(ssr)/./node_modules/@radix-ui/react-dialog/dist/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_ChevronDown_GraduationCap_Heart_Menu_MessageSquare_Target_TrendingUp_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,ChevronDown,GraduationCap,Heart,Menu,MessageSquare,Target,TrendingUp,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_ChevronDown_GraduationCap_Heart_Menu_MessageSquare_Target_TrendingUp_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,ChevronDown,GraduationCap,Heart,Menu,MessageSquare,Target,TrendingUp,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_ChevronDown_GraduationCap_Heart_Menu_MessageSquare_Target_TrendingUp_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,ChevronDown,GraduationCap,Heart,Menu,MessageSquare,Target,TrendingUp,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/target.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_ChevronDown_GraduationCap_Heart_Menu_MessageSquare_Target_TrendingUp_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,ChevronDown,GraduationCap,Heart,Menu,MessageSquare,Target,TrendingUp,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/graduation-cap.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_ChevronDown_GraduationCap_Heart_Menu_MessageSquare_Target_TrendingUp_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,ChevronDown,GraduationCap,Heart,Menu,MessageSquare,Target,TrendingUp,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/book-open.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_ChevronDown_GraduationCap_Heart_Menu_MessageSquare_Target_TrendingUp_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,ChevronDown,GraduationCap,Heart,Menu,MessageSquare,Target,TrendingUp,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/message-square.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_ChevronDown_GraduationCap_Heart_Menu_MessageSquare_Target_TrendingUp_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,ChevronDown,GraduationCap,Heart,Menu,MessageSquare,Target,TrendingUp,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_ChevronDown_GraduationCap_Heart_Menu_MessageSquare_Target_TrendingUp_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,ChevronDown,GraduationCap,Heart,Menu,MessageSquare,Target,TrendingUp,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_ChevronDown_GraduationCap_Heart_Menu_MessageSquare_Target_TrendingUp_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,ChevronDown,GraduationCap,Heart,Menu,MessageSquare,Target,TrendingUp,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_ChevronDown_GraduationCap_Heart_Menu_MessageSquare_Target_TrendingUp_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,ChevronDown,GraduationCap,Heart,Menu,MessageSquare,Target,TrendingUp,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/api */ \"(ssr)/./lib/api.ts\");\n/* harmony import */ var _hooks_useSettings__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/hooks/useSettings */ \"(ssr)/./hooks/useSettings.ts\");\n/* __next_internal_client_entry_do_not_use__ Navigation auto */ \n\n\n\n\n\n\n\n\n\n\nfunction Navigation() {\n    const [isOpen, setIsOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isUserAuthenticated, setIsUserAuthenticated] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [scrolled, setScrolled] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [activeDropdown, setActiveDropdown] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isClient, setIsClient] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    const { settings, loading: settingsLoading } = (0,_hooks_useSettings__WEBPACK_IMPORTED_MODULE_8__.useSettings)();\n    const timeoutRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Handle client-side hydration\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Navigation.useEffect\": ()=>{\n            setIsClient(true);\n        }\n    }[\"Navigation.useEffect\"], []);\n    // Check authentication status on component mount and route changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Navigation.useEffect\": ()=>{\n            setIsUserAuthenticated((0,_lib_api__WEBPACK_IMPORTED_MODULE_7__.isAuthenticated)());\n        }\n    }[\"Navigation.useEffect\"], [\n        pathname\n    ]);\n    // Handle scroll effect for navigation bar\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Navigation.useEffect\": ()=>{\n            const handleScroll = {\n                \"Navigation.useEffect.handleScroll\": ()=>{\n                    const isScrolled = window.scrollY > 10;\n                    setScrolled(isScrolled);\n                }\n            }[\"Navigation.useEffect.handleScroll\"];\n            window.addEventListener('scroll', handleScroll, {\n                passive: true\n            });\n            return ({\n                \"Navigation.useEffect\": ()=>window.removeEventListener('scroll', handleScroll)\n            })[\"Navigation.useEffect\"];\n        }\n    }[\"Navigation.useEffect\"], []);\n    // Handle dropdown interactions with delay\n    const handleDropdownEnter = (dropdownName)=>{\n        if (timeoutRef.current) {\n            clearTimeout(timeoutRef.current);\n        }\n        setActiveDropdown(dropdownName);\n    };\n    const handleDropdownLeave = ()=>{\n        timeoutRef.current = setTimeout(()=>{\n            setActiveDropdown(null);\n        }, 150) // Small delay for smooth UX\n        ;\n    };\n    // Don't render navigation on dashboard pages - let dashboard handle its own header\n    const isDashboardPage = pathname?.startsWith('/dashboard');\n    // Don't render main navigation for authenticated users on dashboard pages\n    if (isDashboardPage && isUserAuthenticated) {\n        return null;\n    }\n    const aboutItems = [\n        {\n            href: \"/about\",\n            label: \"About Us\",\n            icon: _barrel_optimize_names_BookOpen_ChevronDown_GraduationCap_Heart_Menu_MessageSquare_Target_TrendingUp_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n            description: \"Learn about our mission\"\n        },\n        {\n            href: \"/team\",\n            label: \"Our Team\",\n            icon: _barrel_optimize_names_BookOpen_ChevronDown_GraduationCap_Heart_Menu_MessageSquare_Target_TrendingUp_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n            description: \"Meet our dedicated team\"\n        }\n    ];\n    const programItems = [\n        {\n            href: \"/projects\",\n            label: \"Projects\",\n            icon: _barrel_optimize_names_BookOpen_ChevronDown_GraduationCap_Heart_Menu_MessageSquare_Target_TrendingUp_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n            description: \"Our community projects\"\n        },\n        {\n            href: \"/scholarships\",\n            label: \"Scholarships\",\n            icon: _barrel_optimize_names_BookOpen_ChevronDown_GraduationCap_Heart_Menu_MessageSquare_Target_TrendingUp_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n            description: \"Educational support\"\n        },\n        {\n            href: \"/learn-with-us\",\n            label: \"Learn With Us\",\n            icon: _barrel_optimize_names_BookOpen_ChevronDown_GraduationCap_Heart_Menu_MessageSquare_Target_TrendingUp_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n            description: \"Educational programs\"\n        }\n    ];\n    const engagementItems = [\n        {\n            href: \"/contact\",\n            label: \"Contact\",\n            icon: _barrel_optimize_names_BookOpen_ChevronDown_GraduationCap_Heart_Menu_MessageSquare_Target_TrendingUp_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"],\n            description: \"Get in touch\"\n        },\n        {\n            href: \"/blog\",\n            label: \"Blog\",\n            icon: _barrel_optimize_names_BookOpen_ChevronDown_GraduationCap_Heart_Menu_MessageSquare_Target_TrendingUp_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n            description: \"Latest updates\"\n        }\n    ];\n    const isActiveDropdown = (items)=>{\n        return items.some((item)=>pathname === item.href);\n    };\n    // Get app name and logo from settings or use defaults\n    const appName = settings?.app_name || 'Laravel NGO';\n    const appLogo = settings?.app_logo;\n    // Debug logging\n    console.log('Navigation - Settings:', settings);\n    console.log('Navigation - App Logo:', appLogo);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n        className: `fixed top-0 z-50 w-full transition-all duration-300 ease-in-out ${scrolled ? \"bg-white/90 backdrop-blur-xl border-b border-gray-200/50 shadow-lg shadow-black/5\" : \"bg-white/95 backdrop-blur-md border-b border-gray-200/30\"} dark:bg-gray-950/95 dark:supports-[backdrop-filter]:bg-gray-950/80`,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container flex h-16 items-center justify-between px-4 sm:px-6 lg:px-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                    href: \"/\",\n                    className: \"group flex items-center gap-3 hover:scale-105 transition-all duration-300 ease-out\",\n                    children: [\n                        appLogo ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative overflow-hidden rounded-full\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                src: appLogo,\n                                alt: `${appName} Logo`,\n                                className: \"h-9 w-9 object-cover transition-transform duration-300 group-hover:scale-110\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\navigation.tsx\",\n                                lineNumber: 114,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\navigation.tsx\",\n                            lineNumber: 113,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative h-9 w-9 overflow-hidden rounded-full bg-gradient-to-br from-green-500 to-green-700 shadow-lg group-hover:shadow-xl transition-all duration-300\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute inset-0 bg-gradient-to-br from-white/20 to-transparent\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\navigation.tsx\",\n                                    lineNumber: 122,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_ChevronDown_GraduationCap_Heart_Menu_MessageSquare_Target_TrendingUp_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    className: \"absolute inset-0 m-auto h-5 w-5 text-white transition-transform duration-300 group-hover:scale-110\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\navigation.tsx\",\n                                    lineNumber: 123,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\navigation.tsx\",\n                            lineNumber: 121,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-xl font-bold text-gray-900 dark:text-white transition-colors duration-300\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"bg-gradient-to-r from-green-600 to-green-700 bg-clip-text text-transparent\",\n                                children: appName\n                            }, void 0, false, {\n                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\navigation.tsx\",\n                                lineNumber: 127,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\navigation.tsx\",\n                            lineNumber: 126,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\navigation.tsx\",\n                    lineNumber: 108,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                    className: \"hidden lg:flex items-center space-x-1\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                            href: \"/\",\n                            className: `group relative px-4 py-2.5 rounded-xl text-sm font-medium transition-all duration-300 ease-out ${pathname === \"/\" ? \"text-green-600 bg-green-50/80 shadow-sm ring-1 ring-green-100 dark:text-green-400 dark:bg-green-900/30\" : \"text-gray-600 hover:text-green-600 hover:bg-green-50/60 dark:text-gray-300 dark:hover:text-green-400 dark:hover:bg-green-900/20\"}`,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"relative z-10\",\n                                    children: \"Home\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\navigation.tsx\",\n                                    lineNumber: 144,\n                                    columnNumber: 13\n                                }, this),\n                                pathname === \"/\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute inset-0 bg-gradient-to-r from-green-500/10 to-green-600/10 rounded-xl\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\navigation.tsx\",\n                                    lineNumber: 146,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\navigation.tsx\",\n                            lineNumber: 136,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative\",\n                            onMouseEnter: ()=>handleDropdownEnter('about'),\n                            onMouseLeave: handleDropdownLeave,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_6__.DropdownMenu, {\n                                open: activeDropdown === 'about',\n                                onOpenChange: (open)=>setActiveDropdown(open ? 'about' : null),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_6__.DropdownMenuTrigger, {\n                                        asChild: true,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            variant: \"ghost\",\n                                            className: `group relative px-4 py-2.5 h-auto text-sm font-medium transition-all duration-300 ease-out rounded-xl ${isActiveDropdown(aboutItems) || activeDropdown === 'about' ? \"text-green-600 bg-green-50/80 shadow-sm ring-1 ring-green-100 dark:text-green-400 dark:bg-green-900/30\" : \"text-gray-600 hover:text-green-600 hover:bg-green-50/60 dark:text-gray-300 dark:hover:text-green-400 dark:hover:bg-green-900/20\"}`,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"relative z-10\",\n                                                    children: \"About\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\navigation.tsx\",\n                                                    lineNumber: 166,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_ChevronDown_GraduationCap_Heart_Menu_MessageSquare_Target_TrendingUp_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                    className: `ml-1.5 h-3.5 w-3.5 transition-transform duration-300 ${activeDropdown === 'about' ? 'rotate-180' : ''}`\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\navigation.tsx\",\n                                                    lineNumber: 167,\n                                                    columnNumber: 19\n                                                }, this),\n                                                (isActiveDropdown(aboutItems) || activeDropdown === 'about') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute inset-0 bg-gradient-to-r from-green-500/10 to-green-600/10 rounded-xl\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\navigation.tsx\",\n                                                    lineNumber: 171,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\navigation.tsx\",\n                                            lineNumber: 158,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\navigation.tsx\",\n                                        lineNumber: 157,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_6__.DropdownMenuContent, {\n                                        align: \"start\",\n                                        className: \"w-64 p-3 mt-2 bg-white/95 backdrop-blur-xl border border-gray-200/50 shadow-xl rounded-2xl\",\n                                        sideOffset: 8,\n                                        children: aboutItems.map((item)=>{\n                                            const Icon = item.icon;\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_6__.DropdownMenuItem, {\n                                                asChild: true,\n                                                className: \"rounded-xl p-0\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                                    href: item.href,\n                                                    className: \"group flex items-start gap-3 px-4 py-3 rounded-xl hover:bg-green-50/80 transition-all duration-200\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex-shrink-0 w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center group-hover:bg-green-200 transition-colors duration-200\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                                className: \"h-4 w-4 text-green-600\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\navigation.tsx\",\n                                                                lineNumber: 189,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\navigation.tsx\",\n                                                            lineNumber: 188,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"font-medium text-gray-900\",\n                                                                    children: item.label\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\navigation.tsx\",\n                                                                    lineNumber: 192,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-xs text-gray-500 mt-0.5\",\n                                                                    children: item.description\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\navigation.tsx\",\n                                                                    lineNumber: 193,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\navigation.tsx\",\n                                                            lineNumber: 191,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\navigation.tsx\",\n                                                    lineNumber: 184,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, item.href, false, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\navigation.tsx\",\n                                                lineNumber: 183,\n                                                columnNumber: 21\n                                            }, this);\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\navigation.tsx\",\n                                        lineNumber: 175,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\navigation.tsx\",\n                                lineNumber: 156,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\navigation.tsx\",\n                            lineNumber: 151,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative\",\n                            onMouseEnter: ()=>handleDropdownEnter('programs'),\n                            onMouseLeave: handleDropdownLeave,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_6__.DropdownMenu, {\n                                open: activeDropdown === 'programs',\n                                onOpenChange: (open)=>setActiveDropdown(open ? 'programs' : null),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_6__.DropdownMenuTrigger, {\n                                        asChild: true,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            variant: \"ghost\",\n                                            className: `group relative px-4 py-2.5 h-auto text-sm font-medium transition-all duration-300 ease-out rounded-xl ${isActiveDropdown(programItems) || activeDropdown === 'programs' ? \"text-green-600 bg-green-50/80 shadow-sm ring-1 ring-green-100 dark:text-green-400 dark:bg-green-900/30\" : \"text-gray-600 hover:text-green-600 hover:bg-green-50/60 dark:text-gray-300 dark:hover:text-green-400 dark:hover:bg-green-900/20\"}`,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"relative z-10\",\n                                                    children: \"Programs\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\navigation.tsx\",\n                                                    lineNumber: 219,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_ChevronDown_GraduationCap_Heart_Menu_MessageSquare_Target_TrendingUp_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                    className: `ml-1.5 h-3.5 w-3.5 transition-transform duration-300 ${activeDropdown === 'programs' ? 'rotate-180' : ''}`\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\navigation.tsx\",\n                                                    lineNumber: 220,\n                                                    columnNumber: 19\n                                                }, this),\n                                                (isActiveDropdown(programItems) || activeDropdown === 'programs') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute inset-0 bg-gradient-to-r from-green-500/10 to-green-600/10 rounded-xl\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\navigation.tsx\",\n                                                    lineNumber: 224,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\navigation.tsx\",\n                                            lineNumber: 211,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\navigation.tsx\",\n                                        lineNumber: 210,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_6__.DropdownMenuContent, {\n                                        align: \"start\",\n                                        className: \"w-64 p-3 mt-2 bg-white/95 backdrop-blur-xl border border-gray-200/50 shadow-xl rounded-2xl\",\n                                        sideOffset: 8,\n                                        children: programItems.map((item)=>{\n                                            const Icon = item.icon;\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_6__.DropdownMenuItem, {\n                                                asChild: true,\n                                                className: \"rounded-xl p-0\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                                    href: item.href,\n                                                    className: \"group flex items-start gap-3 px-4 py-3 rounded-xl hover:bg-green-50/80 transition-all duration-200\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex-shrink-0 w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center group-hover:bg-green-200 transition-colors duration-200\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                                className: \"h-4 w-4 text-green-600\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\navigation.tsx\",\n                                                                lineNumber: 242,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\navigation.tsx\",\n                                                            lineNumber: 241,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"font-medium text-gray-900\",\n                                                                    children: item.label\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\navigation.tsx\",\n                                                                    lineNumber: 245,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-xs text-gray-500 mt-0.5\",\n                                                                    children: item.description\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\navigation.tsx\",\n                                                                    lineNumber: 246,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\navigation.tsx\",\n                                                            lineNumber: 244,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\navigation.tsx\",\n                                                    lineNumber: 237,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, item.href, false, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\navigation.tsx\",\n                                                lineNumber: 236,\n                                                columnNumber: 21\n                                            }, this);\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\navigation.tsx\",\n                                        lineNumber: 228,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\navigation.tsx\",\n                                lineNumber: 209,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\navigation.tsx\",\n                            lineNumber: 204,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                            href: \"/impact\",\n                            className: `group relative px-4 py-2.5 rounded-xl text-sm font-medium transition-all duration-300 ease-out ${pathname === \"/impact\" ? \"text-green-600 bg-green-50/80 shadow-sm ring-1 ring-green-100 dark:text-green-400 dark:bg-green-900/30\" : \"text-gray-600 hover:text-green-600 hover:bg-green-50/60 dark:text-gray-300 dark:hover:text-green-400 dark:hover:bg-green-900/20\"}`,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"relative z-10\",\n                                    children: \"Impact\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\navigation.tsx\",\n                                    lineNumber: 265,\n                                    columnNumber: 13\n                                }, this),\n                                pathname === \"/impact\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute inset-0 bg-gradient-to-r from-green-500/10 to-green-600/10 rounded-xl\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\navigation.tsx\",\n                                    lineNumber: 267,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\navigation.tsx\",\n                            lineNumber: 257,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative\",\n                            onMouseEnter: ()=>handleDropdownEnter('involved'),\n                            onMouseLeave: handleDropdownLeave,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_6__.DropdownMenu, {\n                                open: activeDropdown === 'involved',\n                                onOpenChange: (open)=>setActiveDropdown(open ? 'involved' : null),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_6__.DropdownMenuTrigger, {\n                                        asChild: true,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            variant: \"ghost\",\n                                            className: `group relative px-4 py-2.5 h-auto text-sm font-medium transition-all duration-300 ease-out rounded-xl ${isActiveDropdown(engagementItems) || activeDropdown === 'involved' ? \"text-green-600 bg-green-50/80 shadow-sm ring-1 ring-green-100 dark:text-green-400 dark:bg-green-900/30\" : \"text-gray-600 hover:text-green-600 hover:bg-green-50/60 dark:text-gray-300 dark:hover:text-green-400 dark:hover:bg-green-900/20\"}`,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"relative z-10\",\n                                                    children: \"Get Involved\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\navigation.tsx\",\n                                                    lineNumber: 287,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_ChevronDown_GraduationCap_Heart_Menu_MessageSquare_Target_TrendingUp_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                    className: `ml-1.5 h-3.5 w-3.5 transition-transform duration-300 ${activeDropdown === 'involved' ? 'rotate-180' : ''}`\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\navigation.tsx\",\n                                                    lineNumber: 288,\n                                                    columnNumber: 19\n                                                }, this),\n                                                (isActiveDropdown(engagementItems) || activeDropdown === 'involved') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute inset-0 bg-gradient-to-r from-green-500/10 to-green-600/10 rounded-xl\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\navigation.tsx\",\n                                                    lineNumber: 292,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\navigation.tsx\",\n                                            lineNumber: 279,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\navigation.tsx\",\n                                        lineNumber: 278,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_6__.DropdownMenuContent, {\n                                        align: \"start\",\n                                        className: \"w-64 p-3 mt-2 bg-white/95 backdrop-blur-xl border border-gray-200/50 shadow-xl rounded-2xl\",\n                                        sideOffset: 8,\n                                        children: engagementItems.map((item)=>{\n                                            const Icon = item.icon;\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_6__.DropdownMenuItem, {\n                                                asChild: true,\n                                                className: \"rounded-xl p-0\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                                    href: item.href,\n                                                    className: \"group flex items-start gap-3 px-4 py-3 rounded-xl hover:bg-green-50/80 transition-all duration-200\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex-shrink-0 w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center group-hover:bg-green-200 transition-colors duration-200\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                                className: \"h-4 w-4 text-green-600\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\navigation.tsx\",\n                                                                lineNumber: 310,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\navigation.tsx\",\n                                                            lineNumber: 309,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"font-medium text-gray-900\",\n                                                                    children: item.label\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\navigation.tsx\",\n                                                                    lineNumber: 313,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-xs text-gray-500 mt-0.5\",\n                                                                    children: item.description\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\navigation.tsx\",\n                                                                    lineNumber: 314,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\navigation.tsx\",\n                                                            lineNumber: 312,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\navigation.tsx\",\n                                                    lineNumber: 305,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, item.href, false, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\navigation.tsx\",\n                                                lineNumber: 304,\n                                                columnNumber: 21\n                                            }, this);\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\navigation.tsx\",\n                                        lineNumber: 296,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\navigation.tsx\",\n                                lineNumber: 277,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\navigation.tsx\",\n                            lineNumber: 272,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\navigation.tsx\",\n                    lineNumber: 134,\n                    columnNumber: 9\n                }, this),\n                isClient && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"hidden lg:flex items-center gap-3\",\n                    suppressHydrationWarning: true,\n                    children: isUserAuthenticated ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                href: \"/dashboard\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    variant: \"outline\",\n                                    className: \"group relative overflow-hidden border-gray-300 hover:border-green-500 transition-all duration-300 rounded-full px-6 py-2.5 hover:shadow-lg hover:shadow-green-500/20\",\n                                    suppressHydrationWarning: true,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"relative z-10 font-medium\",\n                                            children: \"Dashboard\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\navigation.tsx\",\n                                            lineNumber: 337,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute inset-0 bg-gradient-to-r from-green-500/10 to-green-600/10 translate-x-full group-hover:translate-x-0 transition-transform duration-300\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\navigation.tsx\",\n                                            lineNumber: 338,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\navigation.tsx\",\n                                    lineNumber: 332,\n                                    columnNumber: 19\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\navigation.tsx\",\n                                lineNumber: 331,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                href: \"/donate\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    className: \"group relative overflow-hidden bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 text-white transition-all duration-300 rounded-full px-6 py-2.5 hover:shadow-lg hover:shadow-green-500/30 hover:scale-105\",\n                                    suppressHydrationWarning: true,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"relative z-10 font-medium\",\n                                            children: \"Donate Now\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\navigation.tsx\",\n                                            lineNumber: 346,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute inset-0 bg-gradient-to-r from-white/20 to-transparent translate-x-full group-hover:translate-x-0 transition-transform duration-500\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\navigation.tsx\",\n                                            lineNumber: 347,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\navigation.tsx\",\n                                    lineNumber: 342,\n                                    columnNumber: 19\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\navigation.tsx\",\n                                lineNumber: 341,\n                                columnNumber: 17\n                            }, this)\n                        ]\n                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                href: \"/auth/login\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    variant: \"outline\",\n                                    className: \"group relative overflow-hidden border-gray-300 hover:border-green-500 transition-all duration-300 rounded-full px-6 py-2.5 hover:shadow-lg hover:shadow-green-500/20\",\n                                    suppressHydrationWarning: true,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"relative z-10 font-medium\",\n                                            children: \"Login\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\navigation.tsx\",\n                                            lineNumber: 359,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute inset-0 bg-gradient-to-r from-green-500/10 to-green-600/10 translate-x-full group-hover:translate-x-0 transition-transform duration-300\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\navigation.tsx\",\n                                            lineNumber: 360,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\navigation.tsx\",\n                                    lineNumber: 354,\n                                    columnNumber: 19\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\navigation.tsx\",\n                                lineNumber: 353,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                href: \"/donate\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    className: \"group relative overflow-hidden bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 text-white transition-all duration-300 rounded-full px-6 py-2.5 hover:shadow-lg hover:shadow-green-500/30 hover:scale-105\",\n                                    suppressHydrationWarning: true,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"relative z-10 font-medium\",\n                                            children: \"Donate Now\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\navigation.tsx\",\n                                            lineNumber: 368,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute inset-0 bg-gradient-to-r from-white/20 to-transparent translate-x-full group-hover:translate-x-0 transition-transform duration-500\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\navigation.tsx\",\n                                            lineNumber: 369,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\navigation.tsx\",\n                                    lineNumber: 364,\n                                    columnNumber: 19\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\navigation.tsx\",\n                                lineNumber: 363,\n                                columnNumber: 17\n                            }, this)\n                        ]\n                    }, void 0, true)\n                }, void 0, false, {\n                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\navigation.tsx\",\n                    lineNumber: 327,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sheet__WEBPACK_IMPORTED_MODULE_5__.Sheet, {\n                    open: isOpen,\n                    onOpenChange: setIsOpen,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sheet__WEBPACK_IMPORTED_MODULE_5__.SheetTrigger, {\n                            asChild: true,\n                            className: \"lg:hidden\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                variant: \"ghost\",\n                                size: \"icon\",\n                                className: \"group relative overflow-hidden transition-all duration-300 hover:scale-110 focus:scale-110 rounded-xl hover:bg-green-50\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: `absolute inset-0 transition-all duration-300 ${isOpen ? 'rotate-180 scale-75' : ''}`,\n                                        children: isOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_ChevronDown_GraduationCap_Heart_Menu_MessageSquare_Target_TrendingUp_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            className: \"absolute inset-0 m-auto h-5 w-5 text-gray-600\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\navigation.tsx\",\n                                            lineNumber: 387,\n                                            columnNumber: 19\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_ChevronDown_GraduationCap_Heart_Menu_MessageSquare_Target_TrendingUp_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                            className: \"absolute inset-0 m-auto h-5 w-5 text-gray-600\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\navigation.tsx\",\n                                            lineNumber: 389,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\navigation.tsx\",\n                                        lineNumber: 385,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"sr-only\",\n                                        children: \"Toggle menu\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\navigation.tsx\",\n                                        lineNumber: 392,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\navigation.tsx\",\n                                lineNumber: 380,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\navigation.tsx\",\n                            lineNumber: 379,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sheet__WEBPACK_IMPORTED_MODULE_5__.SheetContent, {\n                            side: \"right\",\n                            className: \"w-[320px] sm:w-[400px] z-[100] p-0 bg-white/95 backdrop-blur-xl border-l border-gray-200/50\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_18__.Title, {\n                                    className: \"sr-only\",\n                                    children: \"Mobile Navigation Menu\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\navigation.tsx\",\n                                    lineNumber: 399,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col h-full\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-6 border-b border-gray-200/50\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-3\",\n                                                children: [\n                                                    appLogo ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"relative overflow-hidden rounded-full\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                            src: appLogo,\n                                                            alt: `${appName} Logo`,\n                                                            className: \"h-8 w-auto object-contain\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\navigation.tsx\",\n                                                            lineNumber: 406,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\navigation.tsx\",\n                                                        lineNumber: 405,\n                                                        columnNumber: 21\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"h-8 w-8 bg-gradient-to-br from-green-600 to-green-700 rounded-full flex items-center justify-center shadow-lg\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_ChevronDown_GraduationCap_Heart_Menu_MessageSquare_Target_TrendingUp_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                            className: \"h-5 w-5 text-white\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\navigation.tsx\",\n                                                            lineNumber: 414,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\navigation.tsx\",\n                                                        lineNumber: 413,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-lg font-bold text-gray-900\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"bg-gradient-to-r from-green-600 to-green-700 bg-clip-text text-transparent\",\n                                                            children: appName\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\navigation.tsx\",\n                                                            lineNumber: 418,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\navigation.tsx\",\n                                                        lineNumber: 417,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\navigation.tsx\",\n                                                lineNumber: 403,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\navigation.tsx\",\n                                            lineNumber: 402,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-1 p-6 space-y-6 overflow-y-auto\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                                    href: \"/\",\n                                                    className: `block text-lg font-medium transition-all duration-200 p-3 rounded-xl ${pathname === \"/\" ? \"text-green-600 bg-green-50\" : \"text-gray-600 hover:text-green-600 hover:bg-green-50/50\"}`,\n                                                    onClick: ()=>setIsOpen(false),\n                                                    children: \"Home\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\navigation.tsx\",\n                                                    lineNumber: 427,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-sm font-semibold text-gray-400 uppercase tracking-wider\",\n                                                            children: \"About\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\navigation.tsx\",\n                                                            lineNumber: 441,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        aboutItems.map((item)=>{\n                                                            const Icon = item.icon;\n                                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                                                href: item.href,\n                                                                className: `flex items-center gap-3 text-lg font-medium transition-all duration-200 p-3 rounded-xl ${pathname === item.href ? \"text-green-600 bg-green-50\" : \"text-gray-600 hover:text-green-600 hover:bg-green-50/50\"}`,\n                                                                onClick: ()=>setIsOpen(false),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                                        className: \"h-5 w-5\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\navigation.tsx\",\n                                                                        lineNumber: 455,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    item.label\n                                                                ]\n                                                            }, item.href, true, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\navigation.tsx\",\n                                                                lineNumber: 445,\n                                                                columnNumber: 23\n                                                            }, this);\n                                                        })\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\navigation.tsx\",\n                                                    lineNumber: 440,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-sm font-semibold text-gray-400 uppercase tracking-wider\",\n                                                            children: \"Programs\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\navigation.tsx\",\n                                                            lineNumber: 464,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        programItems.map((item)=>{\n                                                            const Icon = item.icon;\n                                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                                                href: item.href,\n                                                                className: `flex items-center gap-3 text-lg font-medium transition-all duration-200 p-3 rounded-xl ${pathname === item.href ? \"text-green-600 bg-green-50\" : \"text-gray-600 hover:text-green-600 hover:bg-green-50/50\"}`,\n                                                                onClick: ()=>setIsOpen(false),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                                        className: \"h-5 w-5\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\navigation.tsx\",\n                                                                        lineNumber: 478,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    item.label\n                                                                ]\n                                                            }, item.href, true, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\navigation.tsx\",\n                                                                lineNumber: 468,\n                                                                columnNumber: 23\n                                                            }, this);\n                                                        })\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\navigation.tsx\",\n                                                    lineNumber: 463,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                                    href: \"/impact\",\n                                                    className: `flex items-center gap-3 text-lg font-medium transition-all duration-200 p-3 rounded-xl ${pathname === \"/impact\" ? \"text-green-600 bg-green-50\" : \"text-gray-600 hover:text-green-600 hover:bg-green-50/50\"}`,\n                                                    onClick: ()=>setIsOpen(false),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_ChevronDown_GraduationCap_Heart_Menu_MessageSquare_Target_TrendingUp_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                            className: \"h-5 w-5\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\navigation.tsx\",\n                                                            lineNumber: 494,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"Impact\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\navigation.tsx\",\n                                                    lineNumber: 485,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-sm font-semibold text-gray-400 uppercase tracking-wider\",\n                                                            children: \"Get Involved\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\navigation.tsx\",\n                                                            lineNumber: 500,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        engagementItems.map((item)=>{\n                                                            const Icon = item.icon;\n                                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                                                href: item.href,\n                                                                className: `flex items-center gap-3 text-lg font-medium transition-all duration-200 p-3 rounded-xl ${pathname === item.href ? \"text-green-600 bg-green-50\" : \"text-gray-600 hover:text-green-600 hover:bg-green-50/50\"}`,\n                                                                onClick: ()=>setIsOpen(false),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                                        className: \"h-5 w-5\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\navigation.tsx\",\n                                                                        lineNumber: 514,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    item.label\n                                                                ]\n                                                            }, item.href, true, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\navigation.tsx\",\n                                                                lineNumber: 504,\n                                                                columnNumber: 23\n                                                            }, this);\n                                                        })\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\navigation.tsx\",\n                                                    lineNumber: 499,\n                                                    columnNumber: 17\n                                                }, this),\n                                                !isUserAuthenticated && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"pt-4 space-y-3\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                                        href: \"/auth/login\",\n                                                        onClick: ()=>setIsOpen(false),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                            variant: \"outline\",\n                                                            className: \"w-full border-green-300 text-green-600 hover:bg-green-50 rounded-xl py-3\",\n                                                            children: \"Login\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\navigation.tsx\",\n                                                            lineNumber: 525,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\navigation.tsx\",\n                                                        lineNumber: 524,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\navigation.tsx\",\n                                                    lineNumber: 523,\n                                                    columnNumber: 19\n                                                }, this),\n                                                isUserAuthenticated && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"pt-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                                        href: \"/dashboard\",\n                                                        onClick: ()=>setIsOpen(false),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                            variant: \"outline\",\n                                                            className: \"w-full border-green-300 text-green-600 hover:bg-green-50 rounded-xl py-3 mb-3\",\n                                                            children: \"Dashboard\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\navigation.tsx\",\n                                                            lineNumber: 538,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\navigation.tsx\",\n                                                        lineNumber: 537,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\navigation.tsx\",\n                                                    lineNumber: 536,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\navigation.tsx\",\n                                            lineNumber: 426,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-6 border-t border-gray-200/50\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                                href: \"/donate\",\n                                                onClick: ()=>setIsOpen(false),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                    className: \"w-full bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 text-white transition-all duration-300 rounded-xl py-3 font-medium hover:shadow-lg\",\n                                                    children: \"Donate Now\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\navigation.tsx\",\n                                                    lineNumber: 552,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\navigation.tsx\",\n                                                lineNumber: 551,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\navigation.tsx\",\n                                            lineNumber: 550,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\navigation.tsx\",\n                                    lineNumber: 400,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\navigation.tsx\",\n                            lineNumber: 395,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\navigation.tsx\",\n                    lineNumber: 378,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\navigation.tsx\",\n            lineNumber: 106,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\navigation.tsx\",\n        lineNumber: 101,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/navigation.tsx\n");

/***/ }),

/***/ "(ssr)/./components/theme-provider.tsx":
/*!***************************************!*\
  !*** ./components/theme-provider.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ThemeProvider: () => (/* binding */ ThemeProvider)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_themes__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-themes */ \"(ssr)/./node_modules/next-themes/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ ThemeProvider auto */ \n\nfunction ThemeProvider({ children, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_themes__WEBPACK_IMPORTED_MODULE_1__.ThemeProvider, {\n        ...props,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\theme-provider.tsx\",\n        lineNumber: 6,\n        columnNumber: 10\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb21wb25lbnRzL3RoZW1lLXByb3ZpZGVyLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7OztBQUNpRTtBQUcxRCxTQUFTQSxjQUFjLEVBQUVFLFFBQVEsRUFBRSxHQUFHQyxPQUEyQjtJQUN0RSxxQkFBTyw4REFBQ0Ysc0RBQWtCQTtRQUFFLEdBQUdFLEtBQUs7a0JBQUdEOzs7Ozs7QUFDekMiLCJzb3VyY2VzIjpbIkM6XFxsYXJhZ29uXFx3d3dcXGxhcmF2ZWwtYXBpLW5nb1xcZnJvbnQtZW5kXFxjb21wb25lbnRzXFx0aGVtZS1wcm92aWRlci50c3giXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2UgY2xpZW50XCJcbmltcG9ydCB7IFRoZW1lUHJvdmlkZXIgYXMgTmV4dFRoZW1lc1Byb3ZpZGVyIH0gZnJvbSBcIm5leHQtdGhlbWVzXCJcbmltcG9ydCB0eXBlIHsgVGhlbWVQcm92aWRlclByb3BzIH0gZnJvbSBcIm5leHQtdGhlbWVzXCJcblxuZXhwb3J0IGZ1bmN0aW9uIFRoZW1lUHJvdmlkZXIoeyBjaGlsZHJlbiwgLi4ucHJvcHMgfTogVGhlbWVQcm92aWRlclByb3BzKSB7XG4gIHJldHVybiA8TmV4dFRoZW1lc1Byb3ZpZGVyIHsuLi5wcm9wc30+e2NoaWxkcmVufTwvTmV4dFRoZW1lc1Byb3ZpZGVyPlxufVxuIl0sIm5hbWVzIjpbIlRoZW1lUHJvdmlkZXIiLCJOZXh0VGhlbWVzUHJvdmlkZXIiLCJjaGlsZHJlbiIsInByb3BzIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./components/theme-provider.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/button.tsx":
/*!**********************************!*\
  !*** ./components/ui/button.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Button: () => (/* binding */ Button),\n/* harmony export */   buttonVariants: () => (/* binding */ buttonVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(ssr)/./node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n\n\n\n\n\nconst buttonVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-all duration-200 ease-in-out focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 transform hover:scale-105\", {\n    variants: {\n        variant: {\n            default: \"bg-primary text-primary-foreground hover:bg-primary/90 hover:brightness-110\",\n            destructive: \"bg-destructive text-destructive-foreground hover:bg-destructive/90 hover:brightness-110\",\n            outline: \"border border-input bg-background hover:bg-accent hover:text-accent-foreground hover:brightness-105\",\n            secondary: \"bg-secondary text-secondary-foreground hover:bg-secondary/80 hover:brightness-105\",\n            ghost: \"hover:bg-accent hover:text-accent-foreground\",\n            link: \"text-primary underline-offset-4 hover:underline\"\n        },\n        size: {\n            default: \"h-10 px-4 py-2\",\n            sm: \"h-9 rounded-md px-3\",\n            lg: \"h-11 rounded-md px-8\",\n            icon: \"h-10 w-10\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\",\n        size: \"default\"\n    }\n});\nconst Button = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, variant, size, asChild = false, ...props }, ref)=>{\n    const Comp = asChild ? _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__.Slot : \"button\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Comp, {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(buttonVariants({\n            variant,\n            size,\n            className\n        })),\n        ref: ref,\n        suppressHydrationWarning: true,\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\ui\\\\button.tsx\",\n        lineNumber: 46,\n        columnNumber: 7\n    }, undefined);\n});\nButton.displayName = \"Button\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/button.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/dropdown-menu.tsx":
/*!*****************************************!*\
  !*** ./components/ui/dropdown-menu.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DropdownMenu: () => (/* binding */ DropdownMenu),\n/* harmony export */   DropdownMenuCheckboxItem: () => (/* binding */ DropdownMenuCheckboxItem),\n/* harmony export */   DropdownMenuContent: () => (/* binding */ DropdownMenuContent),\n/* harmony export */   DropdownMenuGroup: () => (/* binding */ DropdownMenuGroup),\n/* harmony export */   DropdownMenuItem: () => (/* binding */ DropdownMenuItem),\n/* harmony export */   DropdownMenuLabel: () => (/* binding */ DropdownMenuLabel),\n/* harmony export */   DropdownMenuPortal: () => (/* binding */ DropdownMenuPortal),\n/* harmony export */   DropdownMenuRadioGroup: () => (/* binding */ DropdownMenuRadioGroup),\n/* harmony export */   DropdownMenuRadioItem: () => (/* binding */ DropdownMenuRadioItem),\n/* harmony export */   DropdownMenuSeparator: () => (/* binding */ DropdownMenuSeparator),\n/* harmony export */   DropdownMenuShortcut: () => (/* binding */ DropdownMenuShortcut),\n/* harmony export */   DropdownMenuSub: () => (/* binding */ DropdownMenuSub),\n/* harmony export */   DropdownMenuSubContent: () => (/* binding */ DropdownMenuSubContent),\n/* harmony export */   DropdownMenuSubTrigger: () => (/* binding */ DropdownMenuSubTrigger),\n/* harmony export */   DropdownMenuTrigger: () => (/* binding */ DropdownMenuTrigger)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-dropdown-menu */ \"(ssr)/./node_modules/@radix-ui/react-dropdown-menu/dist/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_Check_ChevronRight_Circle_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronRight,Circle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _barrel_optimize_names_Check_ChevronRight_Circle_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronRight,Circle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _barrel_optimize_names_Check_ChevronRight_Circle_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronRight,Circle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/circle.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ DropdownMenu,DropdownMenuTrigger,DropdownMenuContent,DropdownMenuItem,DropdownMenuCheckboxItem,DropdownMenuRadioItem,DropdownMenuLabel,DropdownMenuSeparator,DropdownMenuShortcut,DropdownMenuGroup,DropdownMenuPortal,DropdownMenuSub,DropdownMenuSubContent,DropdownMenuSubTrigger,DropdownMenuRadioGroup auto */ \n\n\n\n\nconst DropdownMenu = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.Root;\nconst DropdownMenuTrigger = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.Trigger;\nconst DropdownMenuGroup = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.Group;\nconst DropdownMenuPortal = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.Portal;\nconst DropdownMenuSub = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.Sub;\nconst DropdownMenuRadioGroup = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.RadioGroup;\nconst DropdownMenuSubTrigger = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, inset, children, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.SubTrigger, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex cursor-default gap-2 select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent data-[state=open]:bg-accent [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0\", inset && \"pl-8\", className),\n        ...props,\n        children: [\n            children,\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronRight_Circle_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                className: \"ml-auto\"\n            }, void 0, false, {\n                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n                lineNumber: 37,\n                columnNumber: 5\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n        lineNumber: 27,\n        columnNumber: 3\n    }, undefined));\nDropdownMenuSubTrigger.displayName = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.SubTrigger.displayName;\nconst DropdownMenuSubContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.SubContent, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n        lineNumber: 47,\n        columnNumber: 3\n    }, undefined));\nDropdownMenuSubContent.displayName = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.SubContent.displayName;\nconst DropdownMenuContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, sideOffset = 4, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.Portal, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.Content, {\n            ref: ref,\n            sideOffset: sideOffset,\n            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\", className),\n            ...props\n        }, void 0, false, {\n            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n            lineNumber: 64,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n        lineNumber: 63,\n        columnNumber: 3\n    }, undefined));\nDropdownMenuContent.displayName = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.Content.displayName;\nconst DropdownMenuItem = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, inset, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.Item, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"relative flex cursor-default select-none items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0\", inset && \"pl-8\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n        lineNumber: 83,\n        columnNumber: 3\n    }, undefined));\nDropdownMenuItem.displayName = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.Item.displayName;\nconst DropdownMenuCheckboxItem = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, children, checked, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.CheckboxItem, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\", className),\n        checked: checked,\n        ...props,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.ItemIndicator, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronRight_Circle_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        className: \"h-4 w-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n                        lineNumber: 110,\n                        columnNumber: 9\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n                    lineNumber: 109,\n                    columnNumber: 7\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n                lineNumber: 108,\n                columnNumber: 5\n            }, undefined),\n            children\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n        lineNumber: 99,\n        columnNumber: 3\n    }, undefined));\nDropdownMenuCheckboxItem.displayName = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.CheckboxItem.displayName;\nconst DropdownMenuRadioItem = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, children, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.RadioItem, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\", className),\n        ...props,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.ItemIndicator, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronRight_Circle_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                        className: \"h-2 w-2 fill-current\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n                        lineNumber: 133,\n                        columnNumber: 9\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n                    lineNumber: 132,\n                    columnNumber: 7\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n                lineNumber: 131,\n                columnNumber: 5\n            }, undefined),\n            children\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n        lineNumber: 123,\n        columnNumber: 3\n    }, undefined));\nDropdownMenuRadioItem.displayName = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.RadioItem.displayName;\nconst DropdownMenuLabel = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, inset, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.Label, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"px-2 py-1.5 text-sm font-semibold\", inset && \"pl-8\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n        lineNumber: 147,\n        columnNumber: 3\n    }, undefined));\nDropdownMenuLabel.displayName = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.Label.displayName;\nconst DropdownMenuSeparator = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.Separator, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"-mx-1 my-1 h-px bg-muted\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n        lineNumber: 163,\n        columnNumber: 3\n    }, undefined));\nDropdownMenuSeparator.displayName = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.Separator.displayName;\nconst DropdownMenuShortcut = ({ className, ...props })=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"ml-auto text-xs tracking-widest opacity-60\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n        lineNumber: 176,\n        columnNumber: 5\n    }, undefined);\n};\nDropdownMenuShortcut.displayName = \"DropdownMenuShortcut\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/dropdown-menu.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/input.tsx":
/*!*********************************!*\
  !*** ./components/ui/input.tsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Input: () => (/* binding */ Input)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n\n\n\nconst Input = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, type, ...props }, ref)=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n        type: type,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\", className),\n        ref: ref,\n        suppressHydrationWarning: true,\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\ui\\\\input.tsx\",\n        lineNumber: 8,\n        columnNumber: 7\n    }, undefined);\n});\nInput.displayName = \"Input\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb21wb25lbnRzL3VpL2lucHV0LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7O0FBQThCO0FBRUU7QUFFaEMsTUFBTUUsc0JBQVFGLDZDQUFnQixDQUM1QixDQUFDLEVBQUVJLFNBQVMsRUFBRUMsSUFBSSxFQUFFLEdBQUdDLE9BQU8sRUFBRUM7SUFDOUIscUJBQ0UsOERBQUNDO1FBQ0NILE1BQU1BO1FBQ05ELFdBQVdILDhDQUFFQSxDQUNYLGtZQUNBRztRQUVGRyxLQUFLQTtRQUNMRSx3QkFBd0I7UUFDdkIsR0FBR0gsS0FBSzs7Ozs7O0FBR2Y7QUFFRkosTUFBTVEsV0FBVyxHQUFHO0FBRUoiLCJzb3VyY2VzIjpbIkM6XFxsYXJhZ29uXFx3d3dcXGxhcmF2ZWwtYXBpLW5nb1xcZnJvbnQtZW5kXFxjb21wb25lbnRzXFx1aVxcaW5wdXQudHN4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCAqIGFzIFJlYWN0IGZyb20gXCJyZWFjdFwiXG5cbmltcG9ydCB7IGNuIH0gZnJvbSBcIkAvbGliL3V0aWxzXCJcblxuY29uc3QgSW5wdXQgPSBSZWFjdC5mb3J3YXJkUmVmPEhUTUxJbnB1dEVsZW1lbnQsIFJlYWN0LkNvbXBvbmVudFByb3BzPFwiaW5wdXRcIj4+KFxuICAoeyBjbGFzc05hbWUsIHR5cGUsIC4uLnByb3BzIH0sIHJlZikgPT4ge1xuICAgIHJldHVybiAoXG4gICAgICA8aW5wdXRcbiAgICAgICAgdHlwZT17dHlwZX1cbiAgICAgICAgY2xhc3NOYW1lPXtjbihcbiAgICAgICAgICBcImZsZXggaC0xMCB3LWZ1bGwgcm91bmRlZC1tZCBib3JkZXIgYm9yZGVyLWlucHV0IGJnLWJhY2tncm91bmQgcHgtMyBweS0yIHRleHQtYmFzZSByaW5nLW9mZnNldC1iYWNrZ3JvdW5kIGZpbGU6Ym9yZGVyLTAgZmlsZTpiZy10cmFuc3BhcmVudCBmaWxlOnRleHQtc20gZmlsZTpmb250LW1lZGl1bSBmaWxlOnRleHQtZm9yZWdyb3VuZCBwbGFjZWhvbGRlcjp0ZXh0LW11dGVkLWZvcmVncm91bmQgZm9jdXMtdmlzaWJsZTpvdXRsaW5lLW5vbmUgZm9jdXMtdmlzaWJsZTpyaW5nLTIgZm9jdXMtdmlzaWJsZTpyaW5nLXJpbmcgZm9jdXMtdmlzaWJsZTpyaW5nLW9mZnNldC0yIGRpc2FibGVkOmN1cnNvci1ub3QtYWxsb3dlZCBkaXNhYmxlZDpvcGFjaXR5LTUwIG1kOnRleHQtc21cIixcbiAgICAgICAgICBjbGFzc05hbWVcbiAgICAgICAgKX1cbiAgICAgICAgcmVmPXtyZWZ9XG4gICAgICAgIHN1cHByZXNzSHlkcmF0aW9uV2FybmluZ1xuICAgICAgICB7Li4ucHJvcHN9XG4gICAgICAvPlxuICAgIClcbiAgfVxuKVxuSW5wdXQuZGlzcGxheU5hbWUgPSBcIklucHV0XCJcblxuZXhwb3J0IHsgSW5wdXQgfVxuIl0sIm5hbWVzIjpbIlJlYWN0IiwiY24iLCJJbnB1dCIsImZvcndhcmRSZWYiLCJjbGFzc05hbWUiLCJ0eXBlIiwicHJvcHMiLCJyZWYiLCJpbnB1dCIsInN1cHByZXNzSHlkcmF0aW9uV2FybmluZyIsImRpc3BsYXlOYW1lIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/input.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/sheet.tsx":
/*!*********************************!*\
  !*** ./components/ui/sheet.tsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Sheet: () => (/* binding */ Sheet),\n/* harmony export */   SheetClose: () => (/* binding */ SheetClose),\n/* harmony export */   SheetContent: () => (/* binding */ SheetContent),\n/* harmony export */   SheetDescription: () => (/* binding */ SheetDescription),\n/* harmony export */   SheetFooter: () => (/* binding */ SheetFooter),\n/* harmony export */   SheetHeader: () => (/* binding */ SheetHeader),\n/* harmony export */   SheetOverlay: () => (/* binding */ SheetOverlay),\n/* harmony export */   SheetPortal: () => (/* binding */ SheetPortal),\n/* harmony export */   SheetTitle: () => (/* binding */ SheetTitle),\n/* harmony export */   SheetTrigger: () => (/* binding */ SheetTrigger)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-dialog */ \"(ssr)/./node_modules/@radix-ui/react-dialog/dist/index.mjs\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ Sheet,SheetPortal,SheetOverlay,SheetTrigger,SheetClose,SheetContent,SheetHeader,SheetFooter,SheetTitle,SheetDescription auto */ \n\n\n\n\n\nconst Sheet = _radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_4__.Root;\nconst SheetTrigger = _radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_4__.Trigger;\nconst SheetClose = _radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_4__.Close;\nconst SheetPortal = _radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_4__.Portal;\nconst SheetOverlay = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_4__.Overlay, {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0\", className),\n        ...props,\n        ref: ref\n    }, void 0, false, {\n        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\ui\\\\sheet.tsx\",\n        lineNumber: 22,\n        columnNumber: 3\n    }, undefined));\nSheetOverlay.displayName = _radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_4__.Overlay.displayName;\nconst sheetVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"fixed z-50 gap-4 bg-background p-6 shadow-lg transition ease-in-out data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:duration-300 data-[state=open]:duration-500\", {\n    variants: {\n        side: {\n            top: \"inset-x-0 top-0 border-b data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top\",\n            bottom: \"inset-x-0 bottom-0 border-t data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom\",\n            left: \"inset-y-0 left-0 h-full w-3/4 border-r data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left sm:max-w-sm\",\n            right: \"inset-y-0 right-0 h-full w-3/4  border-l data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right sm:max-w-sm\"\n        }\n    },\n    defaultVariants: {\n        side: \"right\"\n    }\n});\nconst SheetContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ side = \"right\", className, children, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SheetPortal, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SheetOverlay, {}, void 0, false, {\n                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\ui\\\\sheet.tsx\",\n                lineNumber: 61,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_4__.Content, {\n                ref: ref,\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(sheetVariants({\n                    side\n                }), className),\n                ...props,\n                children: [\n                    children,\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_4__.Close, {\n                        className: \"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-secondary\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\ui\\\\sheet.tsx\",\n                                lineNumber: 69,\n                                columnNumber: 9\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"sr-only\",\n                                children: \"Close\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\ui\\\\sheet.tsx\",\n                                lineNumber: 70,\n                                columnNumber: 9\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\ui\\\\sheet.tsx\",\n                        lineNumber: 68,\n                        columnNumber: 7\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\ui\\\\sheet.tsx\",\n                lineNumber: 62,\n                columnNumber: 5\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\ui\\\\sheet.tsx\",\n        lineNumber: 60,\n        columnNumber: 3\n    }, undefined));\nSheetContent.displayName = _radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_4__.Content.displayName;\nconst SheetHeader = ({ className, ...props })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"flex flex-col space-y-2 text-center sm:text-left\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\ui\\\\sheet.tsx\",\n        lineNumber: 81,\n        columnNumber: 3\n    }, undefined);\nSheetHeader.displayName = \"SheetHeader\";\nconst SheetFooter = ({ className, ...props })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\ui\\\\sheet.tsx\",\n        lineNumber: 95,\n        columnNumber: 3\n    }, undefined);\nSheetFooter.displayName = \"SheetFooter\";\nconst SheetTitle = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_4__.Title, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"text-lg font-semibold text-foreground\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\ui\\\\sheet.tsx\",\n        lineNumber: 109,\n        columnNumber: 3\n    }, undefined));\nSheetTitle.displayName = _radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_4__.Title.displayName;\nconst SheetDescription = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_4__.Description, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"text-sm text-muted-foreground\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\ui\\\\sheet.tsx\",\n        lineNumber: 121,\n        columnNumber: 3\n    }, undefined));\nSheetDescription.displayName = _radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_4__.Description.displayName;\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/sheet.tsx\n");

/***/ }),

/***/ "(ssr)/./hooks/useSettings.ts":
/*!******************************!*\
  !*** ./hooks/useSettings.ts ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useSettings: () => (/* binding */ useSettings)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/api */ \"(ssr)/./lib/api.ts\");\n\n\nconst useSettings = ()=>{\n    const [settings, setSettings] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useSettings.useEffect\": ()=>{\n            const fetchSettings = {\n                \"useSettings.useEffect.fetchSettings\": async ()=>{\n                    try {\n                        const response = await _lib_api__WEBPACK_IMPORTED_MODULE_1__.apiClient.getSettings();\n                        if (response.success) {\n                            console.log('Settings loaded:', response.data);\n                            setSettings(response.data);\n                        } else {\n                            throw new Error('Invalid settings response');\n                        }\n                    } catch (err) {\n                        console.error('Error fetching settings:', err);\n                        setError(err instanceof Error ? err.message : 'Unknown error');\n                        // Set fallback settings\n                        setSettings({\n                            app_name: 'HLTKKQ Foundation',\n                            site_description: 'Transforming Lives, Building Communities',\n                            contact_email: '<EMAIL>',\n                            contact_phone: '+234 ************'\n                        });\n                    } finally{\n                        setLoading(false);\n                    }\n                }\n            }[\"useSettings.useEffect.fetchSettings\"];\n            fetchSettings();\n        }\n    }[\"useSettings.useEffect\"], []);\n    return {\n        settings,\n        loading,\n        error\n    };\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(ssr)/./hooks/useSettings.ts\n");

/***/ }),

/***/ "(ssr)/./lib/api.ts":
/*!********************!*\
  !*** ./lib/api.ts ***!
  \********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ApiClient: () => (/* binding */ ApiClient),\n/* harmony export */   apiClient: () => (/* binding */ apiClient),\n/* harmony export */   extractArrayData: () => (/* binding */ extractArrayData),\n/* harmony export */   getCurrentUser: () => (/* binding */ getCurrentUser),\n/* harmony export */   isAuthenticated: () => (/* binding */ isAuthenticated),\n/* harmony export */   logout: () => (/* binding */ logout)\n/* harmony export */ });\nconst API_BASE_URL = \"http://localhost:8000/api/v1\" || 0;\n// Utility function to extract array data from paginated or direct responses\nconst extractArrayData = (response)=>{\n    if (!response || !response.data) return [];\n    // If data is already an array, return it\n    if (Array.isArray(response.data)) {\n        return response.data;\n    }\n    // If data has a data property (paginated response), return that array\n    if (response.data.data && Array.isArray(response.data.data)) {\n        return response.data.data;\n    }\n    // Default to empty array\n    return [];\n};\nclass ApiClient {\n    constructor(baseURL = API_BASE_URL ?? \"\"){\n        this.baseURL = baseURL;\n    }\n    async request(endpoint, options = {}) {\n        // Use the base URL directly if it already contains /api/v1, otherwise add it\n        const url = this.baseURL.includes('/api/v1') ? `${this.baseURL}${endpoint}` : `${this.baseURL}/api/v1${endpoint}`;\n        const config = {\n            headers: {\n                'Content-Type': 'application/json',\n                'Accept': 'application/json',\n                ...options.headers\n            },\n            ...options\n        };\n        // Add authentication header if token exists\n        const token =  false ? 0 : null;\n        if (token) {\n            config.headers = {\n                ...config.headers,\n                'Authorization': `Bearer ${token}`\n            };\n        }\n        try {\n            console.log(`API Request: ${options.method || 'GET'} ${url}`) // Debug logging\n            ;\n            const response = await fetch(url, config);\n            const data = await response.json();\n            console.log(`API Response for ${endpoint}:`, data) // Debug logging\n            ;\n            // Handle 401 Unauthorized - redirect to login\n            if (response.status === 401) {\n                if (false) {}\n            }\n            return data;\n        } catch (error) {\n            console.error('API request failed:', error);\n            throw error;\n        }\n    }\n    // Auth endpoints\n    async login(email, password) {\n        const response = await this.request('/login', {\n            method: 'POST',\n            body: JSON.stringify({\n                email,\n                password\n            })\n        });\n        // Store token and user data if login successful\n        if (response.success && response.data?.access_token) {\n            if (false) {}\n        }\n        return response;\n    }\n    async register(userData) {\n        // First, register the basic user\n        const response = await this.request('/register', {\n            method: 'POST',\n            body: JSON.stringify({\n                first_name: userData.first_name,\n                last_name: userData.last_name,\n                email: userData.email,\n                password: userData.password,\n                password_confirmation: userData.password_confirmation,\n                phone_number: userData.phone_number,\n                address: userData.address,\n                date_of_birth: userData.date_of_birth,\n                city: userData.city,\n                state: userData.state,\n                country: userData.country\n            })\n        });\n        // Store token and user data if registration successful\n        if (response.success && response.data?.token) {\n            if (false) {}\n        }\n        // If registration is successful, handle user type specific data\n        if (response.success && userData.user_type && userData.additional_data) {\n            try {\n                if (userData.user_type === 'volunteer') {\n                    // Submit volunteer application\n                    await this.applyAsVolunteer(userData.additional_data);\n                } else if (userData.user_type === 'student' || userData.user_type === 'partner') {\n                    // Store additional data in user preferences\n                    const preferences = {\n                        user_type: userData.user_type,\n                        profile_data: userData.additional_data,\n                        profile_completed: true\n                    };\n                    await this.updateUserPreferences(preferences);\n                }\n            } catch (error) {\n                console.error('Additional data submission failed:', error);\n                // Return the user registration success but note additional data failed\n                return {\n                    ...response,\n                    message: response.message + ' However, additional profile information could not be saved. You can complete your profile later.'\n                };\n            }\n        }\n        return response;\n    }\n    async logout() {\n        const response = await this.request('/logout', {\n            method: 'POST'\n        });\n        // Clear stored token and user data on logout\n        if (false) {}\n        return response;\n    }\n    async getUser() {\n        return this.request('/user');\n    }\n    async forgotPassword(email) {\n        return this.request('/forgot-password', {\n            method: 'POST',\n            body: JSON.stringify({\n                email\n            })\n        });\n    }\n    // Profile endpoints\n    async getProfile() {\n        return this.request('/profile');\n    }\n    async updateProfile(profileData) {\n        return this.request('/profile', {\n            method: 'PUT',\n            body: JSON.stringify(profileData)\n        });\n    }\n    async updateUserPreferences(preferences) {\n        return this.request('/profile', {\n            method: 'PUT',\n            body: JSON.stringify({\n                preferences\n            })\n        });\n    }\n    async uploadAvatar(file) {\n        const formData = new FormData();\n        formData.append('avatar', file);\n        // Use the base URL directly if it already contains /api/v1, otherwise add it\n        const url = this.baseURL.includes('/api/v1') ? `${this.baseURL}/profile/avatar` : `${this.baseURL}/api/v1/profile/avatar`;\n        const token =  false ? 0 : null;\n        try {\n            const response = await fetch(url, {\n                method: 'POST',\n                headers: {\n                    'Authorization': token ? `Bearer ${token}` : '',\n                    'Accept': 'application/json'\n                },\n                body: formData\n            });\n            const data = await response.json();\n            if (response.status === 401) {\n                if (false) {}\n            }\n            return data;\n        } catch (error) {\n            console.error('Avatar upload failed:', error);\n            throw error;\n        }\n    }\n    async changePassword(passwordData) {\n        return this.request('/profile/password', {\n            method: 'PUT',\n            body: JSON.stringify(passwordData)\n        });\n    }\n    async generateQrCode() {\n        return this.request('/profile/generate-qr', {\n            method: 'POST'\n        });\n    }\n    async getIdCard() {\n        return this.request('/profile/id-card');\n    }\n    // Dashboard endpoints\n    async getDashboardSummary() {\n        return this.request('/dashboard/summary');\n    }\n    // Volunteer endpoints\n    async getVolunteerApplication() {\n        return this.request('/volunteer/application');\n    }\n    async getVolunteerHours() {\n        return this.request('/volunteer/hours');\n    }\n    async getVolunteerOpportunities() {\n        return this.request('/volunteer/opportunities');\n    }\n    async logVolunteerHours(hoursData) {\n        return this.request('/volunteer/hours', {\n            method: 'POST',\n            body: JSON.stringify(hoursData)\n        });\n    }\n    // Scholarship endpoints\n    async getMyScholarshipApplications() {\n        return this.request('/scholarships/my-applications');\n    }\n    async getScholarships() {\n        return this.request('/scholarships');\n    }\n    async applyForScholarship(scholarshipId, applicationData) {\n        return this.request(`/scholarships/${scholarshipId}/apply`, {\n            method: 'POST',\n            body: JSON.stringify(applicationData)\n        });\n    }\n    // Event endpoints\n    async getMyEventRegistrations() {\n        return this.request('/events/my-registrations');\n    }\n    async getUpcomingEvents() {\n        return this.request('/events/upcoming');\n    }\n    // Helper method for building query strings\n    buildQueryString(params) {\n        const searchParams = new URLSearchParams();\n        Object.entries(params).forEach(([key, value])=>{\n            if (value !== undefined && value !== null && value !== '') {\n                if (Array.isArray(value)) {\n                    value.forEach((item)=>searchParams.append(`${key}[]`, item));\n                } else {\n                    searchParams.append(key, value.toString());\n                }\n            }\n        });\n        return searchParams.toString();\n    }\n    // Admin Dashboard endpoints\n    async getAdminDashboard() {\n        return this.request('/admin/dashboard');\n    }\n    async getAdminAnalytics(params) {\n        const queryString = params ? `?${this.buildQueryString(params)}` : '';\n        return this.request(`/admin/dashboard/analytics${queryString}`);\n    }\n    async getAdminStats(params) {\n        const queryString = params ? `?${this.buildQueryString(params)}` : '';\n        return this.request(`/admin/dashboard/stats${queryString}`);\n    }\n    // Admin User Management\n    async getAdminUsers(params) {\n        const queryString = params ? `?${this.buildQueryString(params)}` : '';\n        return this.request(`/admin/users${queryString}`);\n    }\n    async createAdminUser(userData) {\n        return this.request('/admin/users', {\n            method: 'POST',\n            body: JSON.stringify(userData)\n        });\n    }\n    async updateAdminUser(userId, userData) {\n        return this.request(`/admin/users/${userId}`, {\n            method: 'PUT',\n            body: JSON.stringify(userData)\n        });\n    }\n    async deleteAdminUser(userId) {\n        return this.request(`/admin/users/${userId}`, {\n            method: 'DELETE'\n        });\n    }\n    async bulkActionUsers(data) {\n        return this.request('/admin/users/bulk-action', {\n            method: 'POST',\n            body: JSON.stringify(data)\n        });\n    }\n    async exportUsers(params) {\n        const queryString = params ? `?${this.buildQueryString(params)}` : '';\n        return this.request(`/admin/users/export${queryString}`);\n    }\n    // Admin Scholarship Management\n    async getScholarshipApplications(params) {\n        const queryString = params ? `?${this.buildQueryString(params)}` : '';\n        return this.request(`/admin/scholarship-applications${queryString}`);\n    }\n    async reviewScholarshipApplication(applicationId, reviewData) {\n        return this.request(`/admin/scholarship-applications/${applicationId}/review`, {\n            method: 'PUT',\n            body: JSON.stringify(reviewData)\n        });\n    }\n    async bulkActionScholarshipApplications(data) {\n        return this.request('/admin/scholarship-applications/bulk-action', {\n            method: 'POST',\n            body: JSON.stringify(data)\n        });\n    }\n    async exportScholarshipApplications(params) {\n        const queryString = params ? `?${this.buildQueryString(params)}` : '';\n        return this.request(`/admin/scholarship-applications/export${queryString}`);\n    }\n    async getScholarshipStatistics() {\n        return this.request('/admin/scholarships/statistics');\n    }\n    // Admin Event Management\n    async getAdminEvents(params) {\n        const queryString = params ? `?${this.buildQueryString(params)}` : '';\n        return this.request(`/admin/events${queryString}`);\n    }\n    async createAdminEvent(eventData) {\n        return this.request('/admin/events', {\n            method: 'POST',\n            body: JSON.stringify(eventData)\n        });\n    }\n    async updateAdminEvent(eventId, eventData) {\n        return this.request(`/admin/events/${eventId}`, {\n            method: 'PUT',\n            body: JSON.stringify(eventData)\n        });\n    }\n    async deleteAdminEvent(eventId) {\n        return this.request(`/admin/events/${eventId}`, {\n            method: 'DELETE'\n        });\n    }\n    async bulkActionEvents(data) {\n        return this.request('/admin/events/bulk-action', {\n            method: 'POST',\n            body: JSON.stringify(data)\n        });\n    }\n    async exportEvents(params) {\n        const queryString = params ? `?${this.buildQueryString(params)}` : '';\n        return this.request(`/admin/events/export${queryString}`);\n    }\n    async getEventStatistics() {\n        return this.request('/admin/events/statistics');\n    }\n    // Admin Program Management\n    async getAdminPrograms(params) {\n        const queryString = params ? `?${this.buildQueryString(params)}` : '';\n        return this.request(`/admin/programs${queryString}`);\n    }\n    async createAdminProgram(programData) {\n        return this.request('/admin/programs', {\n            method: 'POST',\n            body: JSON.stringify(programData)\n        });\n    }\n    async updateAdminProgram(programId, programData) {\n        return this.request(`/admin/programs/${programId}`, {\n            method: 'PUT',\n            body: JSON.stringify(programData)\n        });\n    }\n    async deleteAdminProgram(programId) {\n        return this.request(`/admin/programs/${programId}`, {\n            method: 'DELETE'\n        });\n    }\n    // Admin Blog Management\n    async getAdminBlogPosts(params) {\n        const queryString = params ? `?${this.buildQueryString(params)}` : '';\n        return this.request(`/admin/blog/posts${queryString}`);\n    }\n    async createAdminBlogPost(postData) {\n        return this.request('/admin/blog/posts', {\n            method: 'POST',\n            body: JSON.stringify(postData)\n        });\n    }\n    async updateAdminBlogPost(postId, postData) {\n        return this.request(`/admin/blog/posts/${postId}`, {\n            method: 'PUT',\n            body: JSON.stringify(postData)\n        });\n    }\n    async deleteAdminBlogPost(postId) {\n        return this.request(`/admin/blog/posts/${postId}`, {\n            method: 'DELETE'\n        });\n    }\n    async exportBlogPosts(params) {\n        const queryString = params ? `?${this.buildQueryString(params)}` : '';\n        return this.request(`/admin/blog/posts/export${queryString}`);\n    }\n    // Donation endpoints\n    async getMyDonations() {\n        return this.request('/donations/my-donations');\n    }\n    async getDonationCampaigns() {\n        return this.request('/donations/campaigns');\n    }\n    // Blog endpoints\n    async getBlogPosts(page = 1) {\n        return this.request(`/blog/posts?page=${page}`);\n    }\n    async getBlogPost(slug) {\n        return this.request(`/blog/posts/${slug}`);\n    }\n    // Events endpoints\n    async getEvents(page = 1) {\n        return this.request(`/events?page=${page}`);\n    }\n    async getEvent(id) {\n        return this.request(`/events/${id}`);\n    }\n    async registerForEvent(eventId, additionalInfo) {\n        return this.request(`/events/${eventId}/register`, {\n            method: 'POST',\n            body: JSON.stringify({\n                additional_info: additionalInfo\n            })\n        });\n    }\n    // Programs endpoints\n    async getPrograms(page = 1) {\n        return this.request(`/programs?page=${page}`);\n    }\n    async getProgram(slug) {\n        return this.request(`/programs/${slug}`);\n    }\n    // Additional scholarship endpoint\n    async getScholarship(id) {\n        return this.request(`/scholarships/${id}`);\n    }\n    // Public scholarships endpoints (no authentication required)\n    async getPublicScholarships(params) {\n        const queryString = params ? `?${this.buildQueryString(params)}` : '';\n        return this.request(`/public-scholarships${queryString}`);\n    }\n    async getPublicScholarship(id) {\n        return this.request(`/public-scholarships/${id}`);\n    }\n    async submitScholarshipApplication(formData) {\n        const token = this.getToken();\n        const url = `${this.baseURL}/api/v1/apply-scholarship`;\n        try {\n            const response = await fetch(url, {\n                method: 'POST',\n                headers: {\n                    'Authorization': token ? `Bearer ${token}` : '',\n                    'Accept': 'application/json'\n                },\n                body: formData\n            });\n            const data = await response.json();\n            console.log(`API Response for scholarship application:`, data);\n            if (!response.ok) {\n                throw new Error(data.message || `HTTP error! status: ${response.status}`);\n            }\n            return data;\n        } catch (error) {\n            console.error('Error submitting scholarship application:', error);\n            throw error;\n        }\n    }\n    async applyAsVolunteer(applicationData) {\n        return this.request('/volunteer/apply', {\n            method: 'POST',\n            body: JSON.stringify(applicationData)\n        });\n    }\n    // Contact endpoints\n    async submitContactForm(contactData) {\n        return this.request('/contact', {\n            method: 'POST',\n            body: JSON.stringify(contactData)\n        });\n    }\n    // Newsletter endpoints\n    async subscribeToNewsletter(email) {\n        return this.request('/newsletter/subscribe', {\n            method: 'POST',\n            body: JSON.stringify({\n                email\n            })\n        });\n    }\n    // Settings endpoint\n    async getSettings() {\n        return this.request('/settings');\n    }\n}\n// Create and export a default instance\nconst apiClient = new ApiClient();\n// Export the class for custom instances\n\n// Export the extractArrayData utility function\n\n// Helper function to check if user is authenticated\nconst isAuthenticated = ()=>{\n    if (true) return false;\n    return !!localStorage.getItem('authToken');\n};\n// Helper function to get current user\nconst getCurrentUser = ()=>{\n    if (true) return null;\n    const userStr = localStorage.getItem('user');\n    return userStr ? JSON.parse(userStr) : null;\n};\n// Helper function to logout\nconst logout = async ()=>{\n    try {\n        await apiClient.logout();\n    } catch (error) {\n        console.error('Logout error:', error);\n    } finally{\n        if (false) {}\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./lib/api.ts\n");

/***/ }),

/***/ "(ssr)/./lib/utils.ts":
/*!**********************!*\
  !*** ./lib/utils.ts ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(ssr)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9saWIvdXRpbHMudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQTRDO0FBQ0o7QUFFakMsU0FBU0UsR0FBRyxHQUFHQyxNQUFvQjtJQUN4QyxPQUFPRix1REFBT0EsQ0FBQ0QsMENBQUlBLENBQUNHO0FBQ3RCIiwic291cmNlcyI6WyJDOlxcbGFyYWdvblxcd3d3XFxsYXJhdmVsLWFwaS1uZ29cXGZyb250LWVuZFxcbGliXFx1dGlscy50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBjbHN4LCB0eXBlIENsYXNzVmFsdWUgfSBmcm9tIFwiY2xzeFwiXG5pbXBvcnQgeyB0d01lcmdlIH0gZnJvbSBcInRhaWx3aW5kLW1lcmdlXCJcblxuZXhwb3J0IGZ1bmN0aW9uIGNuKC4uLmlucHV0czogQ2xhc3NWYWx1ZVtdKSB7XG4gIHJldHVybiB0d01lcmdlKGNsc3goaW5wdXRzKSlcbn1cbiJdLCJuYW1lcyI6WyJjbHN4IiwidHdNZXJnZSIsImNuIiwiaW5wdXRzIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./lib/utils.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5Claravel-api-ngo%5C%5Cfront-end%5C%5Capp%5C%5Cerror.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5Claravel-api-ngo%5C%5Cfront-end%5C%5Capp%5C%5Cerror.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/error.tsx */ \"(ssr)/./app/error.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNsYXJhZ29uJTVDJTVDd3d3JTVDJTVDbGFyYXZlbC1hcGktbmdvJTVDJTVDZnJvbnQtZW5kJTVDJTVDYXBwJTVDJTVDZXJyb3IudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSwwSUFBaUciLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXGxhcmFnb25cXFxcd3d3XFxcXGxhcmF2ZWwtYXBpLW5nb1xcXFxmcm9udC1lbmRcXFxcYXBwXFxcXGVycm9yLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5Claravel-api-ngo%5C%5Cfront-end%5C%5Capp%5C%5Cerror.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5Claravel-api-ngo%5C%5Cfront-end%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5Claravel-api-ngo%5C%5Cfront-end%5C%5Ccomponents%5C%5Cfooter.tsx%22%2C%22ids%22%3A%5B%22Footer%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5Claravel-api-ngo%5C%5Cfront-end%5C%5Ccomponents%5C%5Cnavigation.tsx%22%2C%22ids%22%3A%5B%22Navigation%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5Claravel-api-ngo%5C%5Cfront-end%5C%5Ccomponents%5C%5Ctheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5Claravel-api-ngo%5C%5Cfront-end%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5Claravel-api-ngo%5C%5Cfront-end%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5Claravel-api-ngo%5C%5Cfront-end%5C%5Ccomponents%5C%5Cfooter.tsx%22%2C%22ids%22%3A%5B%22Footer%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5Claravel-api-ngo%5C%5Cfront-end%5C%5Ccomponents%5C%5Cnavigation.tsx%22%2C%22ids%22%3A%5B%22Navigation%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5Claravel-api-ngo%5C%5Cfront-end%5C%5Ccomponents%5C%5Ctheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5Claravel-api-ngo%5C%5Cfront-end%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/footer.tsx */ \"(ssr)/./components/footer.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/navigation.tsx */ \"(ssr)/./components/navigation.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/theme-provider.tsx */ \"(ssr)/./components/theme-provider.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5Claravel-api-ngo%5C%5Cfront-end%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5Claravel-api-ngo%5C%5Cfront-end%5C%5Ccomponents%5C%5Cfooter.tsx%22%2C%22ids%22%3A%5B%22Footer%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5Claravel-api-ngo%5C%5Cfront-end%5C%5Ccomponents%5C%5Cnavigation.tsx%22%2C%22ids%22%3A%5B%22Navigation%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5Claravel-api-ngo%5C%5Cfront-end%5C%5Ccomponents%5C%5Ctheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5Claravel-api-ngo%5C%5Cfront-end%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5Claravel-api-ngo%5C%5Cfront-end%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Capp-dir%5C%5Clink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5Claravel-api-ngo%5C%5Cfront-end%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Capp-dir%5C%5Clink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/app-dir/link.js */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNsYXJhZ29uJTVDJTVDd3d3JTVDJTVDbGFyYXZlbC1hcGktbmdvJTVDJTVDZnJvbnQtZW5kJTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDbmV4dCU1QyU1Q2Rpc3QlNUMlNUNjbGllbnQlNUMlNUNhcHAtZGlyJTVDJTVDbGluay5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMl9fZXNNb2R1bGUlMjIlMkMlMjJkZWZhdWx0JTIyJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxnTkFBK0siLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiwgd2VicGFja0V4cG9ydHM6IFtcIl9fZXNNb2R1bGVcIixcImRlZmF1bHRcIl0gKi8gXCJDOlxcXFxsYXJhZ29uXFxcXHd3d1xcXFxsYXJhdmVsLWFwaS1uZ29cXFxcZnJvbnQtZW5kXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGFwcC1kaXJcXFxcbGluay5qc1wiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5Claravel-api-ngo%5C%5Cfront-end%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Capp-dir%5C%5Clink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5Claravel-api-ngo%5C%5Cfront-end%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5Claravel-api-ngo%5C%5Cfront-end%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5Claravel-api-ngo%5C%5Cfront-end%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5Claravel-api-ngo%5C%5Cfront-end%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5Claravel-api-ngo%5C%5Cfront-end%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5Claravel-api-ngo%5C%5Cfront-end%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5Claravel-api-ngo%5C%5Cfront-end%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5Claravel-api-ngo%5C%5Cfront-end%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5Claravel-api-ngo%5C%5Cfront-end%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5Claravel-api-ngo%5C%5Cfront-end%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5Claravel-api-ngo%5C%5Cfront-end%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5Claravel-api-ngo%5C%5Cfront-end%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5Claravel-api-ngo%5C%5Cfront-end%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5Claravel-api-ngo%5C%5Cfront-end%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5Claravel-api-ngo%5C%5Cfront-end%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5Claravel-api-ngo%5C%5Cfront-end%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5Claravel-api-ngo%5C%5Cfront-end%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5Claravel-api-ngo%5C%5Cfront-end%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5Claravel-api-ngo%5C%5Cfront-end%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5Claravel-api-ngo%5C%5Cfront-end%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5Claravel-api-ngo%5C%5Cfront-end%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5Claravel-api-ngo%5C%5Cfront-end%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5Claravel-api-ngo%5C%5Cfront-end%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5Claravel-api-ngo%5C%5Cfront-end%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/@radix-ui","vendor-chunks/tailwind-merge","vendor-chunks/@floating-ui","vendor-chunks/lucide-react","vendor-chunks/tslib","vendor-chunks/react-remove-scroll","vendor-chunks/aria-hidden","vendor-chunks/class-variance-authority","vendor-chunks/next-themes","vendor-chunks/react-remove-scroll-bar","vendor-chunks/use-callback-ref","vendor-chunks/use-sidecar","vendor-chunks/react-style-singleton","vendor-chunks/clsx","vendor-chunks/get-nonce"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=private-next-app-dir%2Fnot-found.tsx&appDir=C%3A%5Claragon%5Cwww%5Claravel-api-ngo%5Cfront-end%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Claragon%5Cwww%5Claravel-api-ngo%5Cfront-end&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();