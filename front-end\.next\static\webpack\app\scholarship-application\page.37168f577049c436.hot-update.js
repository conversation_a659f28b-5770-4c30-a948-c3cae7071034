"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/scholarship-application/page",{

/***/ "(app-pages-browser)/./components/scholarship/ScholarshipApplicationPage.tsx":
/*!***************************************************************!*\
  !*** ./components/scholarship/ScholarshipApplicationPage.tsx ***!
  \***************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ScholarshipApplicationPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./components/ui/label.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/textarea */ \"(app-pages-browser)/./components/ui/textarea.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./components/ui/select.tsx\");\n/* harmony import */ var _components_ui_tabs__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/tabs */ \"(app-pages-browser)/./components/ui/tabs.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BookOpen_Building_CheckCircle_CreditCard_FileUp_GraduationCap_Loader2_MapPin_MessageSquare_Phone_School_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BookOpen,Building,CheckCircle,CreditCard,FileUp,GraduationCap,Loader2,MapPin,MessageSquare,Phone,School,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/book-open.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BookOpen_Building_CheckCircle_CreditCard_FileUp_GraduationCap_Loader2_MapPin_MessageSquare_Phone_School_User_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BookOpen,Building,CheckCircle,CreditCard,FileUp,GraduationCap,Loader2,MapPin,MessageSquare,Phone,School,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/school.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BookOpen_Building_CheckCircle_CreditCard_FileUp_GraduationCap_Loader2_MapPin_MessageSquare_Phone_School_User_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BookOpen,Building,CheckCircle,CreditCard,FileUp,GraduationCap,Loader2,MapPin,MessageSquare,Phone,School,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/graduation-cap.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BookOpen_Building_CheckCircle_CreditCard_FileUp_GraduationCap_Loader2_MapPin_MessageSquare_Phone_School_User_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BookOpen,Building,CheckCircle,CreditCard,FileUp,GraduationCap,Loader2,MapPin,MessageSquare,Phone,School,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/triangle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BookOpen_Building_CheckCircle_CreditCard_FileUp_GraduationCap_Loader2_MapPin_MessageSquare_Phone_School_User_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BookOpen,Building,CheckCircle,CreditCard,FileUp,GraduationCap,Loader2,MapPin,MessageSquare,Phone,School,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BookOpen_Building_CheckCircle_CreditCard_FileUp_GraduationCap_Loader2_MapPin_MessageSquare_Phone_School_User_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BookOpen,Building,CheckCircle,CreditCard,FileUp,GraduationCap,Loader2,MapPin,MessageSquare,Phone,School,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/phone.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BookOpen_Building_CheckCircle_CreditCard_FileUp_GraduationCap_Loader2_MapPin_MessageSquare_Phone_School_User_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BookOpen,Building,CheckCircle,CreditCard,FileUp,GraduationCap,Loader2,MapPin,MessageSquare,Phone,School,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BookOpen_Building_CheckCircle_CreditCard_FileUp_GraduationCap_Loader2_MapPin_MessageSquare_Phone_School_User_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BookOpen,Building,CheckCircle,CreditCard,FileUp,GraduationCap,Loader2,MapPin,MessageSquare,Phone,School,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/building.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BookOpen_Building_CheckCircle_CreditCard_FileUp_GraduationCap_Loader2_MapPin_MessageSquare_Phone_School_User_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BookOpen,Building,CheckCircle,CreditCard,FileUp,GraduationCap,Loader2,MapPin,MessageSquare,Phone,School,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/credit-card.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BookOpen_Building_CheckCircle_CreditCard_FileUp_GraduationCap_Loader2_MapPin_MessageSquare_Phone_School_User_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BookOpen,Building,CheckCircle,CreditCard,FileUp,GraduationCap,Loader2,MapPin,MessageSquare,Phone,School,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BookOpen_Building_CheckCircle_CreditCard_FileUp_GraduationCap_Loader2_MapPin_MessageSquare_Phone_School_User_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BookOpen,Building,CheckCircle,CreditCard,FileUp,GraduationCap,Loader2,MapPin,MessageSquare,Phone,School,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BookOpen_Building_CheckCircle_CreditCard_FileUp_GraduationCap_Loader2_MapPin_MessageSquare_Phone_School_User_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BookOpen,Building,CheckCircle,CreditCard,FileUp,GraduationCap,Loader2,MapPin,MessageSquare,Phone,School,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-up.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BookOpen_Building_CheckCircle_CreditCard_FileUp_GraduationCap_Loader2_MapPin_MessageSquare_Phone_School_User_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BookOpen,Building,CheckCircle,CreditCard,FileUp,GraduationCap,Loader2,MapPin,MessageSquare,Phone,School,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/message-square.js\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./hooks/use-toast.ts\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./lib/api.ts\");\n/* harmony import */ var _SuccessMessage__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./SuccessMessage */ \"(app-pages-browser)/./components/scholarship/SuccessMessage.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\nfunction ScholarshipApplicationPage() {\n    _s();\n    const [selectedCategory, setSelectedCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('primary');\n    const [submitting, setSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showSuccess, setShowSuccess] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_9__.useToast)();\n    // Form data for different categories\n    const [primaryFormData, setPrimaryFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        student_full_name: '',\n        age: '',\n        current_class: '',\n        father_name: '',\n        mother_name: '',\n        parent_phone: '',\n        home_address: '',\n        school_name: '',\n        headmaster_name: '',\n        school_account_number: '',\n        reason_for_scholarship: '',\n        current_school_fee: '',\n        supporting_information: ''\n    });\n    const [secondaryFormData, setSecondaryFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        student_full_name: '',\n        age: '',\n        class: '',\n        parent_phone: '',\n        address: '',\n        school_name: '',\n        principal_name: '',\n        principal_account_number: '',\n        reason_for_scholarship: '',\n        school_fee_amount: ''\n    });\n    const [universityFormData, setUniversityFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        full_name: '',\n        age: '',\n        course_of_study: '',\n        current_level: '',\n        phone_number: '',\n        email_address: '',\n        matriculation_number: '',\n        reason_for_scholarship: ''\n    });\n    // File uploads for different categories\n    const [primaryFiles, setPrimaryFiles] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        student_picture: null\n    });\n    const [secondaryFiles, setSecondaryFiles] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        student_picture: null\n    });\n    const [universityFiles, setUniversityFiles] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        student_id_card: null,\n        payment_evidence: null,\n        supporting_documents: []\n    });\n    // Category configuration - Updated to match platform design system\n    const categoryConfig = {\n        primary: {\n            icon: _barrel_optimize_names_AlertTriangle_BookOpen_Building_CheckCircle_CreditCard_FileUp_GraduationCap_Loader2_MapPin_MessageSquare_Phone_School_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n            title: \"Primary School Scholarship\",\n            description: \"For Primary 1-6 students (Filled by Parent/Guardian)\",\n            color: \"bg-blue-500\",\n            bgColor: \"bg-blue-50\",\n            textColor: \"text-blue-700\",\n            borderColor: \"border-blue-200\"\n        },\n        secondary: {\n            icon: _barrel_optimize_names_AlertTriangle_BookOpen_Building_CheckCircle_CreditCard_FileUp_GraduationCap_Loader2_MapPin_MessageSquare_Phone_School_User_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n            title: \"Secondary School Scholarship\",\n            description: \"For Secondary school students (Filled by Student)\",\n            color: \"bg-green-500\",\n            bgColor: \"bg-green-50\",\n            textColor: \"text-green-700\",\n            borderColor: \"border-green-200\"\n        },\n        university: {\n            icon: _barrel_optimize_names_AlertTriangle_BookOpen_Building_CheckCircle_CreditCard_FileUp_GraduationCap_Loader2_MapPin_MessageSquare_Phone_School_User_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"],\n            title: \"University Scholarship\",\n            description: \"For University students (Filled by Student)\",\n            color: \"bg-purple-500\",\n            bgColor: \"bg-purple-50\",\n            textColor: \"text-purple-700\",\n            borderColor: \"border-purple-200\"\n        }\n    };\n    const handleFileChange = (category, field, file)=>{\n        if (category === 'primary') {\n            setPrimaryFiles((prev)=>({\n                    ...prev,\n                    [field]: file\n                }));\n        } else if (category === 'secondary') {\n            setSecondaryFiles((prev)=>({\n                    ...prev,\n                    [field]: file\n                }));\n        } else if (category === 'university') {\n            if (field === 'supporting_documents' && file) {\n                setUniversityFiles((prev)=>({\n                        ...prev,\n                        supporting_documents: [\n                            ...prev.supporting_documents,\n                            file\n                        ]\n                    }));\n            } else {\n                setUniversityFiles((prev)=>({\n                        ...prev,\n                        [field]: file\n                    }));\n            }\n        }\n    };\n    const handleMultipleFileChange = (files)=>{\n        if (files && selectedCategory === 'university') {\n            const fileArray = Array.from(files);\n            setUniversityFiles((prev)=>({\n                    ...prev,\n                    supporting_documents: [\n                        ...prev.supporting_documents,\n                        ...fileArray\n                    ]\n                }));\n        }\n    };\n    const removeFile = (index)=>{\n        setUniversityFiles((prev)=>({\n                ...prev,\n                supporting_documents: prev.supporting_documents.filter((_, i)=>i !== index)\n            }));\n    };\n    const validateForm = ()=>{\n        if (selectedCategory === 'primary') {\n            const required = [\n                'student_full_name',\n                'age',\n                'current_class',\n                'father_name',\n                'mother_name',\n                'parent_phone',\n                'home_address',\n                'school_name',\n                'headmaster_name',\n                'school_account_number',\n                'reason_for_scholarship',\n                'current_school_fee'\n            ];\n            return required.every((field)=>primaryFormData[field].trim() !== '') && primaryFiles.student_picture;\n        } else if (selectedCategory === 'secondary') {\n            const required = [\n                'student_full_name',\n                'age',\n                'class',\n                'parent_phone',\n                'address',\n                'school_name',\n                'principal_name',\n                'principal_account_number',\n                'reason_for_scholarship',\n                'school_fee_amount'\n            ];\n            return required.every((field)=>secondaryFormData[field].trim() !== '') && secondaryFiles.student_picture;\n        } else if (selectedCategory === 'university') {\n            const required = [\n                'full_name',\n                'age',\n                'course_of_study',\n                'current_level',\n                'phone_number',\n                'email_address',\n                'matriculation_number',\n                'reason_for_scholarship'\n            ];\n            return required.every((field)=>universityFormData[field].trim() !== '') && universityFiles.student_id_card && universityFiles.payment_evidence;\n        }\n        return false;\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        if (!validateForm()) {\n            toast({\n                title: \"Validation Error\",\n                description: \"Please fill in all required fields and upload required documents\",\n                variant: \"destructive\"\n            });\n            return;\n        }\n        try {\n            setSubmitting(true);\n            const formData = new FormData();\n            formData.append('category', selectedCategory);\n            // Add form data based on category\n            if (selectedCategory === 'primary') {\n                formData.append('form_data', JSON.stringify(primaryFormData));\n                if (primaryFiles.student_picture) {\n                    formData.append('student_picture', primaryFiles.student_picture);\n                }\n            } else if (selectedCategory === 'secondary') {\n                formData.append('form_data', JSON.stringify(secondaryFormData));\n                if (secondaryFiles.student_picture) {\n                    formData.append('student_picture', secondaryFiles.student_picture);\n                }\n            } else if (selectedCategory === 'university') {\n                formData.append('form_data', JSON.stringify(universityFormData));\n                if (universityFiles.student_id_card) {\n                    formData.append('student_id_card', universityFiles.student_id_card);\n                }\n                if (universityFiles.payment_evidence) {\n                    formData.append('payment_evidence', universityFiles.payment_evidence);\n                }\n                universityFiles.supporting_documents.forEach((file, index)=>{\n                    formData.append(\"supporting_documents[\".concat(index, \"]\"), file);\n                });\n            }\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_10__.apiClient.post('/scholarships/apply', formData, {\n                headers: {\n                    'Content-Type': 'multipart/form-data'\n                }\n            });\n            if (response.success) {\n                // Show success message\n                setShowSuccess(true);\n                // Reset form\n                if (selectedCategory === 'primary') {\n                    setPrimaryFormData({\n                        student_full_name: '',\n                        age: '',\n                        current_class: '',\n                        father_name: '',\n                        mother_name: '',\n                        parent_phone: '',\n                        home_address: '',\n                        school_name: '',\n                        headmaster_name: '',\n                        school_account_number: '',\n                        reason_for_scholarship: '',\n                        current_school_fee: '',\n                        supporting_information: ''\n                    });\n                    setPrimaryFiles({\n                        student_picture: null\n                    });\n                } else if (selectedCategory === 'secondary') {\n                    setSecondaryFormData({\n                        student_full_name: '',\n                        age: '',\n                        class: '',\n                        parent_phone: '',\n                        address: '',\n                        school_name: '',\n                        principal_name: '',\n                        principal_account_number: '',\n                        reason_for_scholarship: '',\n                        school_fee_amount: ''\n                    });\n                    setSecondaryFiles({\n                        student_picture: null\n                    });\n                } else if (selectedCategory === 'university') {\n                    setUniversityFormData({\n                        full_name: '',\n                        age: '',\n                        course_of_study: '',\n                        current_level: '',\n                        phone_number: '',\n                        email_address: '',\n                        matriculation_number: '',\n                        reason_for_scholarship: ''\n                    });\n                    setUniversityFiles({\n                        student_id_card: null,\n                        payment_evidence: null,\n                        supporting_documents: []\n                    });\n                }\n            } else {\n                toast({\n                    title: \"Error\",\n                    description: response.message || \"Failed to submit application\",\n                    variant: \"destructive\"\n                });\n            }\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error('Error submitting application:', error);\n            toast({\n                title: \"Error\",\n                description: ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || \"Failed to submit application\",\n                variant: \"destructive\"\n            });\n        } finally{\n            setSubmitting(false);\n        }\n    };\n    const handleBackToForm = ()=>{\n        setShowSuccess(false);\n    };\n    const handleGoHome = ()=>{\n        // Navigate to dashboard or home page\n        window.location.href = '/dashboard';\n    };\n    if (showSuccess) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_SuccessMessage__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n            onBackToForm: handleBackToForm,\n            onGoHome: handleGoHome\n        }, void 0, false, {\n            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n            lineNumber: 321,\n            columnNumber: 12\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50 dark:bg-gray-900\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"relative py-20 bg-gradient-to-br from-green-600 to-green-800 text-white\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-w-4xl mx-auto text-center space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-4xl font-bold tracking-tight sm:text-5xl md:text-6xl\",\n                                children: \"Scholarship Application\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                lineNumber: 330,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xl text-green-100 max-w-3xl mx-auto\",\n                                children: \"Choose your scholarship category and complete the application form\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                lineNumber: 333,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                        lineNumber: 329,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                    lineNumber: 328,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                lineNumber: 327,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-12 bg-white dark:bg-gray-900\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-w-4xl mx-auto\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-3xl font-bold tracking-tight mb-2\",\n                                        children: \"Apply for Scholarship\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                        lineNumber: 345,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600 dark:text-gray-400\",\n                                        children: \"Select your education level and fill out the appropriate form\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                        lineNumber: 346,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                lineNumber: 344,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_8__.Tabs, {\n                                value: selectedCategory,\n                                onValueChange: (value)=>setSelectedCategory(value),\n                                className: \"w-full\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_8__.TabsList, {\n                                        className: \"grid w-full grid-cols-1 md:grid-cols-3 mb-8 h-auto p-2 gap-2 bg-gray-100 rounded-lg\",\n                                        children: Object.entries(categoryConfig).map((param)=>{\n                                            let [key, config] = param;\n                                            const IconComponent = config.icon;\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_8__.TabsTrigger, {\n                                                value: key,\n                                                className: \"flex flex-col items-center p-4 space-y-3 rounded-lg transition-all duration-300 data-[state=active]:bg-white data-[state=active]:shadow-lg\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"p-3 rounded-full bg-green-600 text-white shadow-lg\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(IconComponent, {\n                                                            className: \"h-6 w-6\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                            lineNumber: 362,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                        lineNumber: 361,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"font-bold text-sm text-gray-900 leading-tight\",\n                                                                children: config.title\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                lineNumber: 365,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs text-gray-600 mt-1\",\n                                                                children: config.description\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                lineNumber: 366,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                        lineNumber: 364,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, key, true, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                lineNumber: 356,\n                                                columnNumber: 21\n                                            }, this);\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                        lineNumber: 352,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_8__.TabsContent, {\n                                        value: \"primary\",\n                                        className: \"space-y-8\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                            onSubmit: handleSubmit,\n                                            className: \"space-y-8\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-green-50 p-6 rounded-lg border border-green-200\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BookOpen_Building_CheckCircle_CreditCard_FileUp_GraduationCap_Loader2_MapPin_MessageSquare_Phone_School_User_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                className: \"h-5 w-5 text-green-600 mr-3\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                lineNumber: 378,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-green-800 font-medium\",\n                                                                children: \"This form should be filled by a Parent or Guardian on behalf of the student\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                lineNumber: 379,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                        lineNumber: 377,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                    lineNumber: 376,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"text-lg font-semibold text-green-800 dark:text-green-200 mb-4 flex items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BookOpen_Building_CheckCircle_CreditCard_FileUp_GraduationCap_Loader2_MapPin_MessageSquare_Phone_School_User_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                            className: \"h-5 w-5 mr-2 text-green-600\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                            lineNumber: 389,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        \"Student Information\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                    lineNumber: 388,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"space-y-4\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"space-y-2\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                                                    htmlFor: \"student_full_name\",\n                                                                                    className: \"text-green-800 dark:text-green-200\",\n                                                                                    children: \"Student Full Name *\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                    lineNumber: 394,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"relative\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BookOpen_Building_CheckCircle_CreditCard_FileUp_GraduationCap_Loader2_MapPin_MessageSquare_Phone_School_User_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                                            className: \"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-green-500\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                            lineNumber: 398,\n                                                                                            columnNumber: 29\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                                            id: \"student_full_name\",\n                                                                                            value: primaryFormData.student_full_name,\n                                                                                            onChange: (e)=>setPrimaryFormData((prev)=>({\n                                                                                                        ...prev,\n                                                                                                        student_full_name: e.target.value\n                                                                                                    })),\n                                                                                            placeholder: \"Enter student's full name\",\n                                                                                            className: \"pl-10 rounded-xl border-green-200 dark:border-green-700 focus:border-green-500\",\n                                                                                            required: true\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                            lineNumber: 399,\n                                                                                            columnNumber: 29\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                    lineNumber: 397,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                            lineNumber: 393,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"space-y-2\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                                                            htmlFor: \"age\",\n                                                                                            className: \"text-green-800 dark:text-green-200\",\n                                                                                            children: \"Age *\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                            lineNumber: 412,\n                                                                                            columnNumber: 29\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                                            id: \"age\",\n                                                                                            type: \"number\",\n                                                                                            value: primaryFormData.age,\n                                                                                            onChange: (e)=>setPrimaryFormData((prev)=>({\n                                                                                                        ...prev,\n                                                                                                        age: e.target.value\n                                                                                                    })),\n                                                                                            placeholder: \"Age\",\n                                                                                            className: \"rounded-xl border-green-200 dark:border-green-700 focus:border-green-500\",\n                                                                                            required: true\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                            lineNumber: 415,\n                                                                                            columnNumber: 29\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                    lineNumber: 411,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"space-y-2\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                                                            htmlFor: \"current_class\",\n                                                                                            className: \"text-green-800 dark:text-green-200\",\n                                                                                            children: \"Current Class *\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                            lineNumber: 426,\n                                                                                            columnNumber: 29\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.Select, {\n                                                                                            value: primaryFormData.current_class,\n                                                                                            onValueChange: (value)=>setPrimaryFormData((prev)=>({\n                                                                                                        ...prev,\n                                                                                                        current_class: value\n                                                                                                    })),\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectTrigger, {\n                                                                                                    className: \"rounded-xl border-green-200 dark:border-green-700 focus:border-green-500\",\n                                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectValue, {\n                                                                                                        placeholder: \"Select class\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                                        lineNumber: 431,\n                                                                                                        columnNumber: 33\n                                                                                                    }, this)\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                                    lineNumber: 430,\n                                                                                                    columnNumber: 31\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectContent, {\n                                                                                                    children: [\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                                                            value: \"Primary 1\",\n                                                                                                            children: \"Primary 1\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                                            lineNumber: 434,\n                                                                                                            columnNumber: 33\n                                                                                                        }, this),\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                                                            value: \"Primary 2\",\n                                                                                                            children: \"Primary 2\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                                            lineNumber: 435,\n                                                                                                            columnNumber: 33\n                                                                                                        }, this),\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                                                            value: \"Primary 3\",\n                                                                                                            children: \"Primary 3\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                                            lineNumber: 436,\n                                                                                                            columnNumber: 33\n                                                                                                        }, this),\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                                                            value: \"Primary 4\",\n                                                                                                            children: \"Primary 4\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                                            lineNumber: 437,\n                                                                                                            columnNumber: 33\n                                                                                                        }, this),\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                                                            value: \"Primary 5\",\n                                                                                                            children: \"Primary 5\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                                            lineNumber: 438,\n                                                                                                            columnNumber: 33\n                                                                                                        }, this),\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                                                            value: \"Primary 6\",\n                                                                                                            children: \"Primary 6\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                                            lineNumber: 439,\n                                                                                                            columnNumber: 33\n                                                                                                        }, this)\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                                    lineNumber: 433,\n                                                                                                    columnNumber: 31\n                                                                                                }, this)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                            lineNumber: 429,\n                                                                                            columnNumber: 29\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                    lineNumber: 425,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                            lineNumber: 410,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                    lineNumber: 392,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                            lineNumber: 387,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"text-lg font-semibold text-green-800 dark:text-green-200 mb-4 flex items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BookOpen_Building_CheckCircle_CreditCard_FileUp_GraduationCap_Loader2_MapPin_MessageSquare_Phone_School_User_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                            className: \"h-5 w-5 mr-2 text-green-600\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                            lineNumber: 450,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        \"Parent/Guardian Information\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                    lineNumber: 449,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"space-y-4\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"space-y-2\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                                                            htmlFor: \"father_name\",\n                                                                                            className: \"text-green-800 dark:text-green-200\",\n                                                                                            children: \"Father's Name *\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                            lineNumber: 456,\n                                                                                            columnNumber: 29\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"relative\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BookOpen_Building_CheckCircle_CreditCard_FileUp_GraduationCap_Loader2_MapPin_MessageSquare_Phone_School_User_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                                                    className: \"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-green-500\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                                    lineNumber: 460,\n                                                                                                    columnNumber: 31\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                                                    id: \"father_name\",\n                                                                                                    value: primaryFormData.father_name,\n                                                                                                    onChange: (e)=>setPrimaryFormData((prev)=>({\n                                                                                                                ...prev,\n                                                                                                                father_name: e.target.value\n                                                                                                            })),\n                                                                                                    placeholder: \"Enter father's name\",\n                                                                                                    className: \"pl-10 rounded-xl border-green-200 dark:border-green-700 focus:border-green-500\",\n                                                                                                    required: true\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                                    lineNumber: 461,\n                                                                                                    columnNumber: 31\n                                                                                                }, this)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                            lineNumber: 459,\n                                                                                            columnNumber: 29\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                    lineNumber: 455,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"space-y-2\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                                                            htmlFor: \"mother_name\",\n                                                                                            className: \"text-green-800 dark:text-green-200\",\n                                                                                            children: \"Mother's Name *\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                            lineNumber: 472,\n                                                                                            columnNumber: 29\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                                            id: \"mother_name\",\n                                                                                            value: primaryFormData.mother_name,\n                                                                                            onChange: (e)=>setPrimaryFormData((prev)=>({\n                                                                                                        ...prev,\n                                                                                                        mother_name: e.target.value\n                                                                                                    })),\n                                                                                            placeholder: \"Enter mother's name\",\n                                                                                            className: \"rounded-xl border-green-200 dark:border-green-700 focus:border-green-500\",\n                                                                                            required: true\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                            lineNumber: 475,\n                                                                                            columnNumber: 29\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                    lineNumber: 471,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                            lineNumber: 454,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"space-y-2\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                                                    htmlFor: \"parent_phone\",\n                                                                                    className: \"text-green-800 dark:text-green-200\",\n                                                                                    children: \"Father's or Mother's Phone Number *\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                    lineNumber: 486,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"relative\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BookOpen_Building_CheckCircle_CreditCard_FileUp_GraduationCap_Loader2_MapPin_MessageSquare_Phone_School_User_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                                            className: \"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-green-500\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                            lineNumber: 490,\n                                                                                            columnNumber: 29\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                                            id: \"parent_phone\",\n                                                                                            type: \"tel\",\n                                                                                            value: primaryFormData.parent_phone,\n                                                                                            onChange: (e)=>setPrimaryFormData((prev)=>({\n                                                                                                        ...prev,\n                                                                                                        parent_phone: e.target.value\n                                                                                                    })),\n                                                                                            placeholder: \"Enter phone number\",\n                                                                                            className: \"pl-10 rounded-xl border-green-200 dark:border-green-700 focus:border-green-500\",\n                                                                                            required: true\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                            lineNumber: 491,\n                                                                                            columnNumber: 29\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                    lineNumber: 489,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                            lineNumber: 485,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                    lineNumber: 453,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                            lineNumber: 448,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                    lineNumber: 386,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-lg font-semibold text-green-800 dark:text-green-200 mb-4 flex items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BookOpen_Building_CheckCircle_CreditCard_FileUp_GraduationCap_Loader2_MapPin_MessageSquare_Phone_School_User_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                    className: \"h-5 w-5 mr-2 text-green-600\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                    lineNumber: 509,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                \"Address Information\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                            lineNumber: 508,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                                    htmlFor: \"home_address\",\n                                                                    className: \"text-green-800 dark:text-green-200\",\n                                                                    children: \"Home Address *\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                    lineNumber: 513,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"relative\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BookOpen_Building_CheckCircle_CreditCard_FileUp_GraduationCap_Loader2_MapPin_MessageSquare_Phone_School_User_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                            className: \"absolute left-3 top-3 h-4 w-4 text-green-500\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                            lineNumber: 517,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_6__.Textarea, {\n                                                                            id: \"home_address\",\n                                                                            value: primaryFormData.home_address,\n                                                                            onChange: (e)=>setPrimaryFormData((prev)=>({\n                                                                                        ...prev,\n                                                                                        home_address: e.target.value\n                                                                                    })),\n                                                                            placeholder: \"Enter complete home address\",\n                                                                            className: \"pl-10 rounded-xl border-green-200 dark:border-green-700 focus:border-green-500\",\n                                                                            rows: 3,\n                                                                            required: true\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                            lineNumber: 518,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                    lineNumber: 516,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                            lineNumber: 512,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                    lineNumber: 507,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-lg font-semibold text-gray-900 mb-4 flex items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BookOpen_Building_CheckCircle_CreditCard_FileUp_GraduationCap_Loader2_MapPin_MessageSquare_Phone_School_User_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                    className: \"h-5 w-5 mr-2 text-green-600\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                    lineNumber: 534,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                \"School Information\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                            lineNumber: 533,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                                            htmlFor: \"school_name\",\n                                                                            children: \"Name of the School *\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                            lineNumber: 539,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                            id: \"school_name\",\n                                                                            value: primaryFormData.school_name,\n                                                                            onChange: (e)=>setPrimaryFormData((prev)=>({\n                                                                                        ...prev,\n                                                                                        school_name: e.target.value\n                                                                                    })),\n                                                                            placeholder: \"Enter school name\",\n                                                                            className: \"mt-1\",\n                                                                            required: true\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                            lineNumber: 540,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                    lineNumber: 538,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                                            htmlFor: \"headmaster_name\",\n                                                                            children: \"Name of Headmaster/Director *\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                            lineNumber: 550,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                            id: \"headmaster_name\",\n                                                                            value: primaryFormData.headmaster_name,\n                                                                            onChange: (e)=>setPrimaryFormData((prev)=>({\n                                                                                        ...prev,\n                                                                                        headmaster_name: e.target.value\n                                                                                    })),\n                                                                            placeholder: \"Enter headmaster's name\",\n                                                                            className: \"mt-1\",\n                                                                            required: true\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                            lineNumber: 551,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                    lineNumber: 549,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                                            htmlFor: \"school_account_number\",\n                                                                            children: \"School Account Number *\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                            lineNumber: 561,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                            id: \"school_account_number\",\n                                                                            value: primaryFormData.school_account_number,\n                                                                            onChange: (e)=>setPrimaryFormData((prev)=>({\n                                                                                        ...prev,\n                                                                                        school_account_number: e.target.value\n                                                                                    })),\n                                                                            placeholder: \"Enter account number for scholarship payment\",\n                                                                            className: \"mt-1\",\n                                                                            required: true\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                            lineNumber: 562,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                    lineNumber: 560,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                            lineNumber: 537,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                    lineNumber: 532,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-lg font-semibold text-gray-900 mb-4 flex items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BookOpen_Building_CheckCircle_CreditCard_FileUp_GraduationCap_Loader2_MapPin_MessageSquare_Phone_School_User_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                    className: \"h-5 w-5 mr-2 text-green-600\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                    lineNumber: 577,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                \"Financial & Additional Information\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                            lineNumber: 576,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-6\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                                                    htmlFor: \"current_school_fee\",\n                                                                                    children: \"Current School Fee Amount *\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                    lineNumber: 583,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                                    id: \"current_school_fee\",\n                                                                                    type: \"number\",\n                                                                                    value: primaryFormData.current_school_fee,\n                                                                                    onChange: (e)=>setPrimaryFormData((prev)=>({\n                                                                                                ...prev,\n                                                                                                current_school_fee: e.target.value\n                                                                                            })),\n                                                                                    placeholder: \"Enter amount in Naira\",\n                                                                                    className: \"mt-1\",\n                                                                                    required: true\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                    lineNumber: 584,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                            lineNumber: 582,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                                                    htmlFor: \"student_picture\",\n                                                                                    children: \"Upload Student Picture *\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                    lineNumber: 595,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"mt-1\",\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                                        id: \"student_picture\",\n                                                                                        type: \"file\",\n                                                                                        accept: \"image/*\",\n                                                                                        onChange: (e)=>{\n                                                                                            var _e_target_files;\n                                                                                            return handleFileChange('primary', 'student_picture', ((_e_target_files = e.target.files) === null || _e_target_files === void 0 ? void 0 : _e_target_files[0]) || null);\n                                                                                        },\n                                                                                        className: \"file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-green-50 file:text-green-700 hover:file:bg-green-100\",\n                                                                                        required: true\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                        lineNumber: 597,\n                                                                                        columnNumber: 29\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                    lineNumber: 596,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                            lineNumber: 594,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                    lineNumber: 581,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                                            htmlFor: \"reason_for_scholarship\",\n                                                                            children: \"Reason for the Scholarship *\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                            lineNumber: 609,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_6__.Textarea, {\n                                                                            id: \"reason_for_scholarship\",\n                                                                            value: primaryFormData.reason_for_scholarship,\n                                                                            onChange: (e)=>setPrimaryFormData((prev)=>({\n                                                                                        ...prev,\n                                                                                        reason_for_scholarship: e.target.value\n                                                                                    })),\n                                                                            placeholder: \"Please explain why your child needs this scholarship\",\n                                                                            className: \"mt-1\",\n                                                                            rows: 4,\n                                                                            required: true\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                            lineNumber: 610,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                    lineNumber: 608,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                                            htmlFor: \"supporting_information\",\n                                                                            children: \"Any Other Supporting Information\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                            lineNumber: 621,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_6__.Textarea, {\n                                                                            id: \"supporting_information\",\n                                                                            value: primaryFormData.supporting_information,\n                                                                            onChange: (e)=>setPrimaryFormData((prev)=>({\n                                                                                        ...prev,\n                                                                                        supporting_information: e.target.value\n                                                                                    })),\n                                                                            placeholder: \"Any additional information that supports your application\",\n                                                                            className: \"mt-1\",\n                                                                            rows: 3\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                            lineNumber: 622,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                    lineNumber: 620,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                            lineNumber: 580,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                    lineNumber: 575,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-center pt-8\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                        type: \"submit\",\n                                                        disabled: submitting || !validateForm(),\n                                                        size: \"lg\",\n                                                        className: \"bg-green-600 hover:bg-green-700 text-white px-12 py-4 text-lg font-semibold shadow-lg hover:shadow-xl transition-all duration-300\",\n                                                        children: submitting ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BookOpen_Building_CheckCircle_CreditCard_FileUp_GraduationCap_Loader2_MapPin_MessageSquare_Phone_School_User_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                    className: \"mr-3 h-6 w-6 animate-spin\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                    lineNumber: 643,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                \"Submitting Application...\"\n                                                            ]\n                                                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BookOpen_Building_CheckCircle_CreditCard_FileUp_GraduationCap_Loader2_MapPin_MessageSquare_Phone_School_User_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                    className: \"mr-3 h-6 w-6\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                    lineNumber: 648,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                \"Submit Primary School Application\"\n                                                            ]\n                                                        }, void 0, true)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                        lineNumber: 635,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                    lineNumber: 634,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                            lineNumber: 375,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                        lineNumber: 374,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_8__.TabsContent, {\n                                        value: \"secondary\",\n                                        className: \"space-y-8\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                            onSubmit: handleSubmit,\n                                            className: \"space-y-8\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-green-50 p-6 rounded-lg border border-green-200\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BookOpen_Building_CheckCircle_CreditCard_FileUp_GraduationCap_Loader2_MapPin_MessageSquare_Phone_School_User_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                className: \"h-5 w-5 text-green-600 mr-3\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                lineNumber: 662,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-green-800 font-medium\",\n                                                                children: \"This form should be filled by the Student directly\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                lineNumber: 663,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                        lineNumber: 661,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                    lineNumber: 660,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"text-lg font-semibold text-gray-900 mb-4 flex items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BookOpen_Building_CheckCircle_CreditCard_FileUp_GraduationCap_Loader2_MapPin_MessageSquare_Phone_School_User_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                            className: \"h-5 w-5 mr-2 text-green-600\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                            lineNumber: 673,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        \"Student Information\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                    lineNumber: 672,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                                                    htmlFor: \"sec_student_full_name\",\n                                                                                    children: \"Student Full Name *\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                    lineNumber: 678,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                                    id: \"sec_student_full_name\",\n                                                                                    value: secondaryFormData.student_full_name,\n                                                                                    onChange: (e)=>setSecondaryFormData((prev)=>({\n                                                                                                ...prev,\n                                                                                                student_full_name: e.target.value\n                                                                                            })),\n                                                                                    placeholder: \"Enter your full name\",\n                                                                                    className: \"mt-1\",\n                                                                                    required: true\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                    lineNumber: 679,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                            lineNumber: 677,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                                                    htmlFor: \"sec_age\",\n                                                                                    children: \"Age *\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                    lineNumber: 689,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                                    id: \"sec_age\",\n                                                                                    type: \"number\",\n                                                                                    value: secondaryFormData.age,\n                                                                                    onChange: (e)=>setSecondaryFormData((prev)=>({\n                                                                                                ...prev,\n                                                                                                age: e.target.value\n                                                                                            })),\n                                                                                    placeholder: \"Your age\",\n                                                                                    className: \"mt-1\",\n                                                                                    required: true\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                    lineNumber: 690,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                            lineNumber: 688,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                                                    htmlFor: \"sec_class\",\n                                                                                    children: \"Class *\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                    lineNumber: 701,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.Select, {\n                                                                                    value: secondaryFormData.class,\n                                                                                    onValueChange: (value)=>setSecondaryFormData((prev)=>({\n                                                                                                ...prev,\n                                                                                                class: value\n                                                                                            })),\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectTrigger, {\n                                                                                            className: \"mt-1\",\n                                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectValue, {\n                                                                                                placeholder: \"Select class\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                                lineNumber: 704,\n                                                                                                columnNumber: 31\n                                                                                            }, this)\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                            lineNumber: 703,\n                                                                                            columnNumber: 29\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectContent, {\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                                                    value: \"JSS 1\",\n                                                                                                    children: \"JSS 1\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                                    lineNumber: 707,\n                                                                                                    columnNumber: 31\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                                                    value: \"JSS 2\",\n                                                                                                    children: \"JSS 2\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                                    lineNumber: 708,\n                                                                                                    columnNumber: 31\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                                                    value: \"JSS 3\",\n                                                                                                    children: \"JSS 3\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                                    lineNumber: 709,\n                                                                                                    columnNumber: 31\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                                                    value: \"SS 1\",\n                                                                                                    children: \"SS 1\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                                    lineNumber: 710,\n                                                                                                    columnNumber: 31\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                                                    value: \"SS 2\",\n                                                                                                    children: \"SS 2\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                                    lineNumber: 711,\n                                                                                                    columnNumber: 31\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                                                    value: \"SS 3\",\n                                                                                                    children: \"SS 3\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                                    lineNumber: 712,\n                                                                                                    columnNumber: 31\n                                                                                                }, this)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                            lineNumber: 706,\n                                                                                            columnNumber: 29\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                    lineNumber: 702,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                            lineNumber: 700,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                                                    htmlFor: \"sec_parent_phone\",\n                                                                                    children: \"Parent's Phone Number *\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                    lineNumber: 717,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                                    id: \"sec_parent_phone\",\n                                                                                    type: \"tel\",\n                                                                                    value: secondaryFormData.parent_phone,\n                                                                                    onChange: (e)=>setSecondaryFormData((prev)=>({\n                                                                                                ...prev,\n                                                                                                parent_phone: e.target.value\n                                                                                            })),\n                                                                                    placeholder: \"Enter parent's phone number\",\n                                                                                    className: \"mt-1\",\n                                                                                    required: true\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                    lineNumber: 718,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                            lineNumber: 716,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                    lineNumber: 676,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                            lineNumber: 671,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                                            className: \"border-2 border-red-200 bg-red-50/50 shadow-sm hover:shadow-md transition-shadow\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                                                    className: \"pb-4\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                                        className: \"text-xl font-bold text-gray-900 flex items-center\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BookOpen_Building_CheckCircle_CreditCard_FileUp_GraduationCap_Loader2_MapPin_MessageSquare_Phone_School_User_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                                className: \"h-6 w-6 mr-3 text-red-600\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                lineNumber: 735,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            \"Address & School Information\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                        lineNumber: 734,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                    lineNumber: 733,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                                                    className: \"space-y-4\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                                                    htmlFor: \"sec_address\",\n                                                                                    children: \"Address *\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                    lineNumber: 742,\n                                                                                    columnNumber: 25\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_6__.Textarea, {\n                                                                                    id: \"sec_address\",\n                                                                                    value: secondaryFormData.address,\n                                                                                    onChange: (e)=>setSecondaryFormData((prev)=>({\n                                                                                                ...prev,\n                                                                                                address: e.target.value\n                                                                                            })),\n                                                                                    placeholder: \"Enter your complete address\",\n                                                                                    className: \"mt-1\",\n                                                                                    rows: 3,\n                                                                                    required: true\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                    lineNumber: 743,\n                                                                                    columnNumber: 25\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                            lineNumber: 741,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                                                    htmlFor: \"sec_school_name\",\n                                                                                    children: \"School Name *\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                    lineNumber: 755,\n                                                                                    columnNumber: 25\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                                    id: \"sec_school_name\",\n                                                                                    value: secondaryFormData.school_name,\n                                                                                    onChange: (e)=>setSecondaryFormData((prev)=>({\n                                                                                                ...prev,\n                                                                                                school_name: e.target.value\n                                                                                            })),\n                                                                                    placeholder: \"Enter your school name\",\n                                                                                    className: \"mt-1\",\n                                                                                    required: true\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                    lineNumber: 756,\n                                                                                    columnNumber: 25\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                            lineNumber: 754,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                                                    htmlFor: \"sec_principal_name\",\n                                                                                    children: \"School Principal or Director Name *\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                    lineNumber: 767,\n                                                                                    columnNumber: 25\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                                    id: \"sec_principal_name\",\n                                                                                    value: secondaryFormData.principal_name,\n                                                                                    onChange: (e)=>setSecondaryFormData((prev)=>({\n                                                                                                ...prev,\n                                                                                                principal_name: e.target.value\n                                                                                            })),\n                                                                                    placeholder: \"Enter principal's name\",\n                                                                                    className: \"mt-1\",\n                                                                                    required: true\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                    lineNumber: 768,\n                                                                                    columnNumber: 25\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                            lineNumber: 766,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                    lineNumber: 739,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                            lineNumber: 732,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                    lineNumber: 670,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                                    className: \"border-2 border-orange-200 bg-orange-50/50 shadow-sm hover:shadow-md transition-shadow\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                                            className: \"pb-4\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                                className: \"text-xl font-bold text-gray-900 flex items-center\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BookOpen_Building_CheckCircle_CreditCard_FileUp_GraduationCap_Loader2_MapPin_MessageSquare_Phone_School_User_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                        className: \"h-6 w-6 mr-3 text-orange-600\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                        lineNumber: 785,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    \"Financial Information\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                lineNumber: 784,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                            lineNumber: 783,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                                            className: \"space-y-6\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                                                htmlFor: \"sec_principal_account_number\",\n                                                                                children: \"Principal or Financial Officer Account Number *\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                lineNumber: 793,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                                id: \"sec_principal_account_number\",\n                                                                                value: secondaryFormData.principal_account_number,\n                                                                                onChange: (e)=>setSecondaryFormData((prev)=>({\n                                                                                            ...prev,\n                                                                                            principal_account_number: e.target.value\n                                                                                        })),\n                                                                                placeholder: \"Enter account number for scholarship payment\",\n                                                                                className: \"mt-1\",\n                                                                                required: true\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                lineNumber: 794,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                        lineNumber: 792,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                                                htmlFor: \"sec_school_fee_amount\",\n                                                                                children: \"School Fee Amount *\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                lineNumber: 805,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                                id: \"sec_school_fee_amount\",\n                                                                                type: \"number\",\n                                                                                value: secondaryFormData.school_fee_amount,\n                                                                                onChange: (e)=>setSecondaryFormData((prev)=>({\n                                                                                            ...prev,\n                                                                                            school_fee_amount: e.target.value\n                                                                                        })),\n                                                                                placeholder: \"Enter amount in Naira\",\n                                                                                className: \"mt-1\",\n                                                                                required: true\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                lineNumber: 806,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                        lineNumber: 804,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                lineNumber: 791,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                            lineNumber: 789,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                    lineNumber: 782,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                                    className: \"border-2 border-indigo-200 bg-indigo-50/50 shadow-sm hover:shadow-md transition-shadow\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                                            className: \"pb-4\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                                className: \"text-xl font-bold text-gray-900 flex items-center\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BookOpen_Building_CheckCircle_CreditCard_FileUp_GraduationCap_Loader2_MapPin_MessageSquare_Phone_School_User_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                        className: \"h-6 w-6 mr-3 text-indigo-600\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                        lineNumber: 824,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    \"Picture & Application Reason\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                lineNumber: 823,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                            lineNumber: 822,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                                            className: \"space-y-6\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                                                htmlFor: \"sec_student_picture\",\n                                                                                children: \"Upload Student Picture *\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                lineNumber: 831,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"mt-1\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                                    id: \"sec_student_picture\",\n                                                                                    type: \"file\",\n                                                                                    accept: \"image/*\",\n                                                                                    onChange: (e)=>{\n                                                                                        var _e_target_files;\n                                                                                        return handleFileChange('secondary', 'student_picture', ((_e_target_files = e.target.files) === null || _e_target_files === void 0 ? void 0 : _e_target_files[0]) || null);\n                                                                                    },\n                                                                                    className: \"file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-green-50 file:text-green-700 hover:file:bg-green-100\",\n                                                                                    required: true\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                    lineNumber: 833,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                lineNumber: 832,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                        lineNumber: 830,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                    lineNumber: 829,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                                            htmlFor: \"sec_reason_for_scholarship\",\n                                                                            children: \"Reason for the Scholarship *\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                            lineNumber: 846,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_6__.Textarea, {\n                                                                            id: \"sec_reason_for_scholarship\",\n                                                                            value: secondaryFormData.reason_for_scholarship,\n                                                                            onChange: (e)=>setSecondaryFormData((prev)=>({\n                                                                                        ...prev,\n                                                                                        reason_for_scholarship: e.target.value\n                                                                                    })),\n                                                                            placeholder: \"Please explain why you need this scholarship\",\n                                                                            className: \"mt-1\",\n                                                                            rows: 4,\n                                                                            required: true\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                            lineNumber: 847,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                    lineNumber: 845,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                            lineNumber: 828,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                    lineNumber: 821,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-center pt-8\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                        type: \"submit\",\n                                                        disabled: submitting || !validateForm(),\n                                                        size: \"lg\",\n                                                        className: \"bg-gradient-to-r from-green-600 via-emerald-600 to-teal-600 hover:from-green-700 hover:via-emerald-700 hover:to-teal-700 text-white px-12 py-4 text-lg font-bold shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105\",\n                                                        children: submitting ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BookOpen_Building_CheckCircle_CreditCard_FileUp_GraduationCap_Loader2_MapPin_MessageSquare_Phone_School_User_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                    className: \"mr-3 h-6 w-6 animate-spin\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                    lineNumber: 869,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                \"Submitting Application...\"\n                                                            ]\n                                                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BookOpen_Building_CheckCircle_CreditCard_FileUp_GraduationCap_Loader2_MapPin_MessageSquare_Phone_School_User_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                    className: \"mr-3 h-6 w-6\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                    lineNumber: 874,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                \"Submit Secondary School Application\"\n                                                            ]\n                                                        }, void 0, true)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                        lineNumber: 861,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                    lineNumber: 860,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                            lineNumber: 659,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                        lineNumber: 658,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_8__.TabsContent, {\n                                        value: \"university\",\n                                        className: \"space-y-8\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                            onSubmit: handleSubmit,\n                                            className: \"space-y-8\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-purple-50 p-6 rounded-lg border-2 border-purple-200 shadow-sm\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BookOpen_Building_CheckCircle_CreditCard_FileUp_GraduationCap_Loader2_MapPin_MessageSquare_Phone_School_User_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                className: \"h-6 w-6 text-purple-600 mr-3\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                lineNumber: 888,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-purple-800 font-semibold text-lg\",\n                                                                children: \"This form should be filled by the University Student directly\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                lineNumber: 889,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                        lineNumber: 887,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                    lineNumber: 886,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-8\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                                            className: \"border-2 border-purple-200 bg-purple-50/50 shadow-sm hover:shadow-md transition-shadow\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                                                    className: \"pb-4\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                                        className: \"text-xl font-bold text-gray-900 flex items-center\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BookOpen_Building_CheckCircle_CreditCard_FileUp_GraduationCap_Loader2_MapPin_MessageSquare_Phone_School_User_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                                className: \"h-6 w-6 mr-3 text-purple-600\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                lineNumber: 900,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            \"Personal Information\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                        lineNumber: 899,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                    lineNumber: 898,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                                                    className: \"space-y-4\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                                                    htmlFor: \"uni_full_name\",\n                                                                                    children: \"Full Name *\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                    lineNumber: 907,\n                                                                                    columnNumber: 25\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                                    id: \"uni_full_name\",\n                                                                                    value: universityFormData.full_name,\n                                                                                    onChange: (e)=>setUniversityFormData((prev)=>({\n                                                                                                ...prev,\n                                                                                                full_name: e.target.value\n                                                                                            })),\n                                                                                    placeholder: \"Enter your full name\",\n                                                                                    className: \"mt-1\",\n                                                                                    required: true\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                    lineNumber: 908,\n                                                                                    columnNumber: 25\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                            lineNumber: 906,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                                                    htmlFor: \"uni_age\",\n                                                                                    children: \"Age *\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                    lineNumber: 919,\n                                                                                    columnNumber: 25\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                                    id: \"uni_age\",\n                                                                                    type: \"number\",\n                                                                                    value: universityFormData.age,\n                                                                                    onChange: (e)=>setUniversityFormData((prev)=>({\n                                                                                                ...prev,\n                                                                                                age: e.target.value\n                                                                                            })),\n                                                                                    placeholder: \"Your age\",\n                                                                                    className: \"mt-1\",\n                                                                                    required: true\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                    lineNumber: 920,\n                                                                                    columnNumber: 25\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                            lineNumber: 918,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                                                    htmlFor: \"uni_phone_number\",\n                                                                                    children: \"Phone Number *\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                    lineNumber: 932,\n                                                                                    columnNumber: 25\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                                    id: \"uni_phone_number\",\n                                                                                    type: \"tel\",\n                                                                                    value: universityFormData.phone_number,\n                                                                                    onChange: (e)=>setUniversityFormData((prev)=>({\n                                                                                                ...prev,\n                                                                                                phone_number: e.target.value\n                                                                                            })),\n                                                                                    placeholder: \"Enter your phone number\",\n                                                                                    className: \"mt-1\",\n                                                                                    required: true\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                    lineNumber: 933,\n                                                                                    columnNumber: 25\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                            lineNumber: 931,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                                                    htmlFor: \"uni_email_address\",\n                                                                                    children: \"Email Address *\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                    lineNumber: 945,\n                                                                                    columnNumber: 25\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                                    id: \"uni_email_address\",\n                                                                                    type: \"email\",\n                                                                                    value: universityFormData.email_address,\n                                                                                    onChange: (e)=>setUniversityFormData((prev)=>({\n                                                                                                ...prev,\n                                                                                                email_address: e.target.value\n                                                                                            })),\n                                                                                    placeholder: \"Enter your email address\",\n                                                                                    className: \"mt-1\",\n                                                                                    required: true\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                    lineNumber: 946,\n                                                                                    columnNumber: 25\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                            lineNumber: 944,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                    lineNumber: 904,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                            lineNumber: 897,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                                            className: \"border-2 border-blue-200 bg-blue-50/50 shadow-sm hover:shadow-md transition-shadow\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                                                    className: \"pb-4\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                                        className: \"text-xl font-bold text-gray-900 flex items-center\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BookOpen_Building_CheckCircle_CreditCard_FileUp_GraduationCap_Loader2_MapPin_MessageSquare_Phone_School_User_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                                className: \"h-6 w-6 mr-3 text-blue-600\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                lineNumber: 963,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            \"Academic Information\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                        lineNumber: 962,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                    lineNumber: 961,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                                                    className: \"space-y-4\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                                                    htmlFor: \"uni_course_of_study\",\n                                                                                    children: \"Course of Study *\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                    lineNumber: 970,\n                                                                                    columnNumber: 25\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                                    id: \"uni_course_of_study\",\n                                                                                    value: universityFormData.course_of_study,\n                                                                                    onChange: (e)=>setUniversityFormData((prev)=>({\n                                                                                                ...prev,\n                                                                                                course_of_study: e.target.value\n                                                                                            })),\n                                                                                    placeholder: \"Enter your course of study\",\n                                                                                    className: \"mt-1\",\n                                                                                    required: true\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                    lineNumber: 971,\n                                                                                    columnNumber: 25\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                            lineNumber: 969,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                                                    htmlFor: \"uni_current_level\",\n                                                                                    children: \"Current Level *\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                    lineNumber: 982,\n                                                                                    columnNumber: 25\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.Select, {\n                                                                                    value: universityFormData.current_level,\n                                                                                    onValueChange: (value)=>setUniversityFormData((prev)=>({\n                                                                                                ...prev,\n                                                                                                current_level: value\n                                                                                            })),\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectTrigger, {\n                                                                                            className: \"mt-1\",\n                                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectValue, {\n                                                                                                placeholder: \"Select your current level\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                                lineNumber: 985,\n                                                                                                columnNumber: 29\n                                                                                            }, this)\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                            lineNumber: 984,\n                                                                                            columnNumber: 27\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectContent, {\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                                                    value: \"100L\",\n                                                                                                    children: \"100 Level\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                                    lineNumber: 988,\n                                                                                                    columnNumber: 29\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                                                    value: \"200L\",\n                                                                                                    children: \"200 Level\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                                    lineNumber: 989,\n                                                                                                    columnNumber: 29\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                                                    value: \"300L\",\n                                                                                                    children: \"300 Level\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                                    lineNumber: 990,\n                                                                                                    columnNumber: 29\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                                                    value: \"400L\",\n                                                                                                    children: \"400 Level\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                                    lineNumber: 991,\n                                                                                                    columnNumber: 29\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                                                    value: \"500L\",\n                                                                                                    children: \"500 Level\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                                    lineNumber: 992,\n                                                                                                    columnNumber: 29\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                                                    value: \"600L\",\n                                                                                                    children: \"600 Level\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                                    lineNumber: 993,\n                                                                                                    columnNumber: 29\n                                                                                                }, this)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                            lineNumber: 987,\n                                                                                            columnNumber: 27\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                    lineNumber: 983,\n                                                                                    columnNumber: 25\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                            lineNumber: 981,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                                                    htmlFor: \"uni_matriculation_number\",\n                                                                                    children: \"Matriculation Number *\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                    lineNumber: 999,\n                                                                                    columnNumber: 25\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                                    id: \"uni_matriculation_number\",\n                                                                                    value: universityFormData.matriculation_number,\n                                                                                    onChange: (e)=>setUniversityFormData((prev)=>({\n                                                                                                ...prev,\n                                                                                                matriculation_number: e.target.value\n                                                                                            })),\n                                                                                    placeholder: \"Enter your matriculation number\",\n                                                                                    className: \"mt-1\",\n                                                                                    required: true\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                    lineNumber: 1000,\n                                                                                    columnNumber: 25\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                            lineNumber: 998,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                    lineNumber: 967,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                            lineNumber: 960,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                    lineNumber: 895,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                                    className: \"border-2 border-orange-200 bg-orange-50/50 shadow-sm hover:shadow-md transition-shadow\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                                            className: \"pb-4\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                                className: \"text-xl font-bold text-gray-900 flex items-center\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BookOpen_Building_CheckCircle_CreditCard_FileUp_GraduationCap_Loader2_MapPin_MessageSquare_Phone_School_User_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                        className: \"h-6 w-6 mr-3 text-orange-600\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                        lineNumber: 1017,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    \"Required Documents\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                lineNumber: 1016,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                            lineNumber: 1015,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                                            className: \"space-y-6\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                                                    htmlFor: \"uni_student_id_card\",\n                                                                                    children: \"Upload Student ID Card *\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                    lineNumber: 1025,\n                                                                                    columnNumber: 25\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"mt-1\",\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                                        id: \"uni_student_id_card\",\n                                                                                        type: \"file\",\n                                                                                        accept: \"image/*,.pdf\",\n                                                                                        onChange: (e)=>{\n                                                                                            var _e_target_files;\n                                                                                            return handleFileChange('university', 'student_id_card', ((_e_target_files = e.target.files) === null || _e_target_files === void 0 ? void 0 : _e_target_files[0]) || null);\n                                                                                        },\n                                                                                        className: \"file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-purple-50 file:text-purple-700 hover:file:bg-purple-100\",\n                                                                                        required: true\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                        lineNumber: 1027,\n                                                                                        columnNumber: 27\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                    lineNumber: 1026,\n                                                                                    columnNumber: 25\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                            lineNumber: 1024,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                                                    htmlFor: \"uni_payment_evidence\",\n                                                                                    children: \"Upload Payment Evidence (Remita/Screenshot) *\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                    lineNumber: 1039,\n                                                                                    columnNumber: 25\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"mt-1\",\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                                        id: \"uni_payment_evidence\",\n                                                                                        type: \"file\",\n                                                                                        accept: \"image/*,.pdf\",\n                                                                                        onChange: (e)=>{\n                                                                                            var _e_target_files;\n                                                                                            return handleFileChange('university', 'payment_evidence', ((_e_target_files = e.target.files) === null || _e_target_files === void 0 ? void 0 : _e_target_files[0]) || null);\n                                                                                        },\n                                                                                        className: \"file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-purple-50 file:text-purple-700 hover:file:bg-purple-100\",\n                                                                                        required: true\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                        lineNumber: 1041,\n                                                                                        columnNumber: 27\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                    lineNumber: 1040,\n                                                                                    columnNumber: 25\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                            lineNumber: 1038,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                    lineNumber: 1023,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                                            htmlFor: \"uni_supporting_documents\",\n                                                                            children: \"Upload Supporting Documents (PDF, DOCX, JPG)\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                            lineNumber: 1054,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"mt-1\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                                id: \"uni_supporting_documents\",\n                                                                                type: \"file\",\n                                                                                accept: \".pdf,.docx,.doc,.jpg,.jpeg,.png\",\n                                                                                multiple: true,\n                                                                                onChange: (e)=>handleMultipleFileChange(e.target.files),\n                                                                                className: \"file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-purple-50 file:text-purple-700 hover:file:bg-purple-100\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                lineNumber: 1056,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                            lineNumber: 1055,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        universityFiles.supporting_documents.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"mt-2 space-y-2\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"text-sm text-gray-600\",\n                                                                                    children: \"Selected files:\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                    lineNumber: 1067,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                universityFiles.supporting_documents.map((file, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"flex items-center justify-between bg-gray-50 p-2 rounded\",\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                className: \"text-sm text-gray-700\",\n                                                                                                children: file.name\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                                lineNumber: 1070,\n                                                                                                columnNumber: 31\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                                                type: \"button\",\n                                                                                                variant: \"ghost\",\n                                                                                                size: \"sm\",\n                                                                                                onClick: ()=>removeFile(index),\n                                                                                                className: \"text-red-600 hover:text-red-800\",\n                                                                                                children: \"Remove\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                                lineNumber: 1071,\n                                                                                                columnNumber: 31\n                                                                                            }, this)\n                                                                                        ]\n                                                                                    }, index, true, {\n                                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                                        lineNumber: 1069,\n                                                                                        columnNumber: 29\n                                                                                    }, this))\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                            lineNumber: 1066,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                    lineNumber: 1053,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                            lineNumber: 1021,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                    lineNumber: 1014,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                                    className: \"border-2 border-indigo-200 bg-indigo-50/50 shadow-sm hover:shadow-md transition-shadow\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                                            className: \"pb-4\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                                className: \"text-xl font-bold text-gray-900 flex items-center\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BookOpen_Building_CheckCircle_CreditCard_FileUp_GraduationCap_Loader2_MapPin_MessageSquare_Phone_School_User_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                        className: \"h-6 w-6 mr-3 text-indigo-600\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                        lineNumber: 1092,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    \"Application Reason\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                lineNumber: 1091,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                            lineNumber: 1090,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                                        htmlFor: \"uni_reason_for_scholarship\",\n                                                                        children: \"Reason for the Scholarship *\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                        lineNumber: 1098,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_6__.Textarea, {\n                                                                        id: \"uni_reason_for_scholarship\",\n                                                                        value: universityFormData.reason_for_scholarship,\n                                                                        onChange: (e)=>setUniversityFormData((prev)=>({\n                                                                                    ...prev,\n                                                                                    reason_for_scholarship: e.target.value\n                                                                                })),\n                                                                        placeholder: \"Please explain why you need this scholarship\",\n                                                                        className: \"mt-1\",\n                                                                        rows: 4,\n                                                                        required: true\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                        lineNumber: 1099,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                lineNumber: 1097,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                            lineNumber: 1096,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                    lineNumber: 1089,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-center pt-8\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                        type: \"submit\",\n                                                        disabled: submitting || !validateForm(),\n                                                        size: \"lg\",\n                                                        className: \"bg-gradient-to-r from-purple-600 via-violet-600 to-indigo-600 hover:from-purple-700 hover:via-violet-700 hover:to-indigo-700 text-white px-12 py-4 text-lg font-bold shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105\",\n                                                        children: submitting ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BookOpen_Building_CheckCircle_CreditCard_FileUp_GraduationCap_Loader2_MapPin_MessageSquare_Phone_School_User_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                    className: \"mr-3 h-6 w-6 animate-spin\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                    lineNumber: 1121,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                \"Submitting Application...\"\n                                                            ]\n                                                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BookOpen_Building_CheckCircle_CreditCard_FileUp_GraduationCap_Loader2_MapPin_MessageSquare_Phone_School_User_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                    className: \"mr-3 h-6 w-6\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                                    lineNumber: 1126,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                \"Submit University Application\"\n                                                            ]\n                                                        }, void 0, true)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                        lineNumber: 1113,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                                    lineNumber: 1112,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                            lineNumber: 885,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                        lineNumber: 884,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                                lineNumber: 351,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                        lineNumber: 343,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                    lineNumber: 342,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n                lineNumber: 341,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\components\\\\scholarship\\\\ScholarshipApplicationPage.tsx\",\n        lineNumber: 325,\n        columnNumber: 5\n    }, this);\n}\n_s(ScholarshipApplicationPage, \"Sti8wAfvn58MX1lRHs4cA5vIto0=\", false, function() {\n    return [\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_9__.useToast\n    ];\n});\n_c = ScholarshipApplicationPage;\nvar _c;\n$RefreshReg$(_c, \"ScholarshipApplicationPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/scholarship/ScholarshipApplicationPage.tsx\n"));

/***/ })

});