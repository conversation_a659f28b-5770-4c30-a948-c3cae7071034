"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./app/dashboard/page.tsx":
/*!********************************!*\
  !*** ./app/dashboard/page.tsx ***!
  \********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DashboardPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./lib/api.ts\");\n/* harmony import */ var _barrel_optimize_names_ExternalLink_Heart_Loader2_LogOut_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ExternalLink,Heart,Loader2,LogOut,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_ExternalLink_Heart_Loader2_LogOut_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ExternalLink,Heart,Loader2,LogOut,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* harmony import */ var _barrel_optimize_names_ExternalLink_Heart_Loader2_LogOut_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ExternalLink,Heart,Loader2,LogOut,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/external-link.js\");\n/* harmony import */ var _barrel_optimize_names_ExternalLink_Heart_Loader2_LogOut_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=ExternalLink,Heart,Loader2,LogOut,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_ExternalLink_Heart_Loader2_LogOut_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=ExternalLink,Heart,Loader2,LogOut,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/log-out.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/dropdown-menu */ \"(app-pages-browser)/./components/ui/dropdown-menu.tsx\");\n/* harmony import */ var _components_ui_alert_dialog__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/alert-dialog */ \"(app-pages-browser)/./components/ui/alert-dialog.tsx\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var _hooks_useSettings__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/hooks/useSettings */ \"(app-pages-browser)/./hooks/useSettings.ts\");\n/* harmony import */ var _student_page__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./student/page */ \"(app-pages-browser)/./app/dashboard/student/page.tsx\");\n/* harmony import */ var _volunteer_page__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./volunteer/page */ \"(app-pages-browser)/./app/dashboard/volunteer/page.tsx\");\n/* harmony import */ var _partner_page__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./partner/page */ \"(app-pages-browser)/./app/dashboard/partner/page.tsx\");\n/* harmony import */ var _user_page__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./user/page */ \"(app-pages-browser)/./app/dashboard/user/page.tsx\");\n/* harmony import */ var _admin_page__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ./admin/page */ \"(app-pages-browser)/./app/dashboard/admin/page.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n// Dashboard components for different user types\n\n\n\n\n\nfunction DashboardPage() {\n    _s();\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [logoutLoading, setLogoutLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { settings } = (0,_hooks_useSettings__WEBPACK_IMPORTED_MODULE_8__.useSettings)();\n    // Get app name from settings or use default\n    const appName = (settings === null || settings === void 0 ? void 0 : settings.app_name) || 'Laravel NGO';\n    const appLogo = settings === null || settings === void 0 ? void 0 : settings.app_logo;\n    const handleLogout = async ()=>{\n        try {\n            setLogoutLoading(true);\n            await (0,_lib_api__WEBPACK_IMPORTED_MODULE_3__.logout)();\n            router.push('/auth/login');\n        } catch (error) {\n            console.error('Logout failed:', error);\n            // Even if API logout fails, clear local storage and redirect\n            localStorage.removeItem('authToken');\n            localStorage.removeItem('user');\n            router.push('/auth/login');\n        } finally{\n            setLogoutLoading(false);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"DashboardPage.useEffect\": ()=>{\n            // Check if user is authenticated\n            if (!(0,_lib_api__WEBPACK_IMPORTED_MODULE_3__.isAuthenticated)()) {\n                console.log('User not authenticated, redirecting to login');\n                router.push('/auth/login');\n                return;\n            }\n            // Get user data from localStorage first\n            const currentUser = (0,_lib_api__WEBPACK_IMPORTED_MODULE_3__.getCurrentUser)();\n            if (currentUser) {\n                console.log('Current user from localStorage:', currentUser);\n                setUser(currentUser);\n            }\n            // Fetch updated user profile from API\n            const fetchUserProfile = {\n                \"DashboardPage.useEffect.fetchUserProfile\": async ()=>{\n                    try {\n                        console.log('Fetching user profile from API...');\n                        const response = await _lib_api__WEBPACK_IMPORTED_MODULE_3__.apiClient.getProfile();\n                        console.log('Profile API response:', response);\n                        if (response.success && response.user) {\n                            // Handle the correct response structure from Laravel backend\n                            const userData = response.user;\n                            console.log('Extracted user data:', userData);\n                            setUser(userData);\n                            // Update localStorage with fresh data\n                            localStorage.setItem('user', JSON.stringify(userData));\n                        } else if (response.success && response.data) {\n                            // Fallback for different response structure\n                            const userData = response.data.user || response.data;\n                            console.log('Extracted user data (fallback):', userData);\n                            setUser(userData);\n                            localStorage.setItem('user', JSON.stringify(userData));\n                        } else {\n                            console.error('Failed to fetch profile - invalid response structure:', response);\n                            // If profile fetch fails but we have cached user, continue with cached data\n                            if (!currentUser) {\n                                router.push('/auth/login');\n                            }\n                        }\n                    } catch (error) {\n                        console.error('Failed to fetch user profile:', error);\n                        // If API call fails but we have cached user, continue with cached data\n                        if (!currentUser) {\n                            router.push('/auth/login');\n                        }\n                    } finally{\n                        setLoading(false);\n                    }\n                }\n            }[\"DashboardPage.useEffect.fetchUserProfile\"];\n            fetchUserProfile();\n        }\n    }[\"DashboardPage.useEffect\"], [\n        router\n    ]);\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center bg-gray-50\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ExternalLink_Heart_Loader2_LogOut_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                        className: \"h-8 w-8 animate-spin mx-auto mb-4 text-green-600\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 136,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: \"Loading your dashboard...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 137,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 135,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\page.tsx\",\n            lineNumber: 134,\n            columnNumber: 7\n        }, this);\n    }\n    if (!user) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center bg-gray-50\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-red-600\",\n                    children: \"Unable to load user data. Please try logging in again.\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 147,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 146,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\page.tsx\",\n            lineNumber: 145,\n            columnNumber: 7\n        }, this);\n    }\n    // Route to appropriate dashboard based on user role and preferences\n    const getUserType = ()=>{\n        var _user_preferences;\n        // Check user preferences first (from registration)\n        if ((_user_preferences = user.preferences) === null || _user_preferences === void 0 ? void 0 : _user_preferences.user_type) {\n            return user.preferences.user_type;\n        }\n        // Fall back to role-based routing\n        if (user.role === 'partner_organization') {\n            return 'partner';\n        } else if (user.role === 'volunteer') {\n            return 'volunteer';\n        } else if (user.role === 'admin') {\n            return 'admin';\n        } else if (user.role === 'student') {\n            return 'student';\n        } else {\n            // For regular users, check if they have specific preferences\n            // If user has partner_organization_id, they're linked to an institution\n            if (user.partner_organization_id) {\n                return 'partner';\n            }\n            return 'user';\n        }\n    };\n    const userType = getUserType();\n    // Add a header with logout functionality\n    const DashboardHeader = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white border-b border-gray-200 px-4 py-3 sm:px-6\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-between items-center max-w-7xl mx-auto\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-4\",\n                        children: [\n                            appLogo ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                src: appLogo,\n                                alt: \"\".concat(appName, \" Logo\"),\n                                className: \"h-8 w-auto object-contain\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 187,\n                                columnNumber: 13\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative h-8 w-8 overflow-hidden rounded-full bg-gradient-to-br from-green-600 to-green-700 shadow-lg\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ExternalLink_Heart_Loader2_LogOut_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                    className: \"absolute inset-0 m-auto h-5 w-5 text-white\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 194,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 193,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-xl sm:text-2xl font-bold text-gray-900\",\n                                children: [\n                                    appName,\n                                    \" Dashboard\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 197,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 185,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"hidden sm:block text-sm text-gray-600\",\n                                children: [\n                                    \"Welcome, \",\n                                    user.first_name\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 203,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_7___default()), {\n                                href: \"/\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    variant: \"outline\",\n                                    size: \"sm\",\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ExternalLink_Heart_Loader2_LogOut_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 210,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"hidden sm:inline\",\n                                            children: \"Visit Website\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 211,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"sm:hidden\",\n                                            children: \"Website\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 212,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 209,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 208,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_5__.DropdownMenu, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_5__.DropdownMenuTrigger, {\n                                        asChild: true,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            variant: \"ghost\",\n                                            size: \"sm\",\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-8 h-8 bg-green-600 rounded-full flex items-center justify-center text-white text-sm font-medium\",\n                                                    children: [\n                                                        user.first_name.charAt(0),\n                                                        user.last_name.charAt(0)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 219,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"hidden sm:inline\",\n                                                    children: user.first_name\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 222,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 218,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 217,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_5__.DropdownMenuContent, {\n                                        align: \"end\",\n                                        className: \"w-56\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"px-2 py-1.5 text-sm font-medium\",\n                                                children: [\n                                                    user.first_name,\n                                                    \" \",\n                                                    user.last_name\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 226,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"px-2 py-1.5 text-xs text-gray-500\",\n                                                children: user.email\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 229,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_5__.DropdownMenuSeparator, {}, void 0, false, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 232,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_5__.DropdownMenuItem, {\n                                                onClick: ()=>{\n                                                    // Navigate to profile settings (we'll implement this)\n                                                    const element = document.querySelector('[data-value=\"profile\"]');\n                                                    if (element) element.click();\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ExternalLink_Heart_Loader2_LogOut_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                        className: \"mr-2 h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 238,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    \"Profile Settings\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 233,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_5__.DropdownMenuSeparator, {}, void 0, false, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 241,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog__WEBPACK_IMPORTED_MODULE_6__.AlertDialog, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog__WEBPACK_IMPORTED_MODULE_6__.AlertDialogTrigger, {\n                                                        asChild: true,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_5__.DropdownMenuItem, {\n                                                            onSelect: (e)=>e.preventDefault(),\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ExternalLink_Heart_Loader2_LogOut_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                    className: \"mr-2 h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 245,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                \"Logout\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 244,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 243,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog__WEBPACK_IMPORTED_MODULE_6__.AlertDialogContent, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog__WEBPACK_IMPORTED_MODULE_6__.AlertDialogHeader, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog__WEBPACK_IMPORTED_MODULE_6__.AlertDialogTitle, {\n                                                                        children: \"Are you sure you want to logout?\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 251,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog__WEBPACK_IMPORTED_MODULE_6__.AlertDialogDescription, {\n                                                                        children: \"You will need to login again to access your dashboard.\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 252,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 250,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog__WEBPACK_IMPORTED_MODULE_6__.AlertDialogFooter, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog__WEBPACK_IMPORTED_MODULE_6__.AlertDialogCancel, {\n                                                                        children: \"Cancel\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 257,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog__WEBPACK_IMPORTED_MODULE_6__.AlertDialogAction, {\n                                                                        onClick: handleLogout,\n                                                                        disabled: logoutLoading,\n                                                                        className: \"bg-red-600 hover:bg-red-700\",\n                                                                        children: logoutLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ExternalLink_Heart_Loader2_LogOut_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                                    className: \"mr-2 h-4 w-4 animate-spin\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                                    lineNumber: 265,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                \"Logging out...\"\n                                                                            ]\n                                                                        }, void 0, true) : 'Logout'\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 258,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 256,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 249,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 242,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 225,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 216,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 202,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 184,\n                columnNumber: 7\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\page.tsx\",\n            lineNumber: 183,\n            columnNumber: 5\n        }, this);\n    // Render the appropriate dashboard component with header\n    const renderDashboard = ()=>{\n        switch(userType){\n            case 'student':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_student_page__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 286,\n                    columnNumber: 16\n                }, this);\n            case 'volunteer':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_volunteer_page__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 288,\n                    columnNumber: 16\n                }, this);\n            case 'partner':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_partner_page__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 290,\n                    columnNumber: 16\n                }, this);\n            case 'admin':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_admin_page__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 292,\n                    columnNumber: 16\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_user_page__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 294,\n                    columnNumber: 16\n                }, this);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DashboardHeader, {}, void 0, false, {\n                fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 300,\n                columnNumber: 7\n            }, this),\n            renderDashboard()\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\laragon\\\\www\\\\laravel-api-ngo\\\\front-end\\\\app\\\\dashboard\\\\page.tsx\",\n        lineNumber: 299,\n        columnNumber: 5\n    }, this);\n}\n_s(DashboardPage, \"+k7tylyzCuhPf6WaDKLSmdv9BFI=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _hooks_useSettings__WEBPACK_IMPORTED_MODULE_8__.useSettings\n    ];\n});\n_c = DashboardPage;\nvar _c;\n$RefreshReg$(_c, \"DashboardPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/dashboard/page.tsx\n"));

/***/ })

});